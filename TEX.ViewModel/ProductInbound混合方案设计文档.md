# ProductInbound入库系统混合方案设计文档

## 概述

本文档详细记录了TEX项目ProductInbound入库系统的混合架构方案，该方案通过**Bill-Roll实体 + Lot触发器**的设计，简化应用层逻辑的同时保持数据完整性。

## 方案背景

### 当前问题
- 入库操作需要同时维护Bill、Lot、Roll三级实体数据
- 还需要同步更新ProductStock库存表
- 操作复杂，事务管理困难，性能开销大

### 数据量特征
- 单个Bill：最多100个Lot，20000个Roll
- 年处理量：约300,000个Roll
- 频繁查询：按OrderDetail查询所有Lot
- 数据库：MySQL

## 架构设计

### 核心思想
1. **应用层简化**：入库服务只处理Bill和Roll两级数据
2. **数据库保障**：通过触发器和存储过程自动维护Lot数据和库存
3. **性能优化**：针对大批量操作进行特殊优化

### 数据流程
```
入库请求 → 保存Bill → 批量保存Roll → 触发器执行 → 自动更新Lot → 自动更新库存
```

## 实体结构调整

### ProductInboundRoll实体增强
```csharp
public class ProductInboundRoll : BasePoco, IPersistPoco, ITenant, IRemark
{
    // 原有字段保持不变
    [Required]
    public Guid ID { get; set; }
    public string LotNo { get; set; }
    public int RollNo { get; set; }
    public decimal Weight { get; set; }
    public decimal Meters { get; set; }
    public decimal Yards { get; set; }
    public string Grade { get; set; }
    
    // 新增：从Lot下移的关键字段
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OrderDetailId")]
    [Comment("订单明细")]
    public Guid OrderDetailId { get; set; }
 
    
    [StringLength(64)]
    [Display(Name = "_Location")]
    [Comment("库位")]
    public string Location { get; set; }
    
    [Display(Name = "_Status")]
    [Comment("入库状态")]
    public InboundStatusEnum InboundStatus { get; set; } = InboundStatusEnum.Inbound;
    
    // 直接关联到Bill
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_InboundBillId")]
    [Comment("入库单号")]
    public Guid InboundBillId { get; set; }
    
    // 审计字段
    public string TenantCode { get; set; }
    public bool IsValid { get; set; } = true;
    public string Remark { get; set; }
}
```

### ProductInboundLot保持不变
- 继续作为实体存在，但由触发器自动维护
- 保持完整的审计和业务字段

## 数据库设计

### 1. 核心触发器

#### Roll表INSERT触发器
```sql
DELIMITER $$

CREATE TRIGGER tr_ProductInboundRoll_AfterInsert
AFTER INSERT ON ProductInboundRolls
FOR EACH ROW
BEGIN
    -- 检查是否禁用触发器（批量操作时）
    IF @DISABLE_TRIGGERS IS NULL OR @DISABLE_TRIGGERS = 0 THEN
        CALL sp_UpdateProductInboundLot(NEW.InboundBillId, NEW.OrderDetailId, NEW.LotNo);
        CALL sp_UpdateProductStock(NEW.OrderDetailId, 'ADD', NEW.Weight, NEW.Meters, NEW.Yards, 1);
    END IF;
END$$

DELIMITER ;
```

#### Roll表UPDATE触发器
```sql
DELIMITER $$

CREATE TRIGGER tr_ProductInboundRoll_AfterUpdate  
AFTER UPDATE ON ProductInboundRolls
FOR EACH ROW
BEGIN
    IF @DISABLE_TRIGGERS IS NULL OR @DISABLE_TRIGGERS = 0 THEN
        -- 如果关键字段发生变化，需要更新相关的Lot
        IF OLD.LotNo != NEW.LotNo OR OLD.OrderDetailId != NEW.OrderDetailId THEN
            CALL sp_UpdateProductInboundLot(OLD.InboundBillId, OLD.OrderDetailId, OLD.LotNo);
            CALL sp_UpdateProductInboundLot(NEW.InboundBillId, NEW.OrderDetailId, NEW.LotNo);
        ELSE
            CALL sp_UpdateProductInboundLot(NEW.InboundBillId, NEW.OrderDetailId, NEW.LotNo);
        END IF;
        
        -- 更新库存差异
        CALL sp_UpdateProductStock(OLD.OrderDetailId, 'SUBTRACT', OLD.Weight, OLD.Meters, OLD.Yards, 1);
        CALL sp_UpdateProductStock(NEW.OrderDetailId, 'ADD', NEW.Weight, NEW.Meters, NEW.Yards, 1);
    END IF;
END$$

DELIMITER ;
```

#### Roll表DELETE触发器
```sql
DELIMITER $$

CREATE TRIGGER tr_ProductInboundRoll_AfterDelete
AFTER DELETE ON ProductInboundRolls  
FOR EACH ROW
BEGIN
    IF @DISABLE_TRIGGERS IS NULL OR @DISABLE_TRIGGERS = 0 THEN
        CALL sp_UpdateProductInboundLot(OLD.InboundBillId, OLD.OrderDetailId, OLD.LotNo);
        CALL sp_UpdateProductStock(OLD.OrderDetailId, 'SUBTRACT', OLD.Weight, OLD.Meters, OLD.Yards, 1);
    END IF;
END$$

DELIMITER ;
```

### 2. 核心存储过程

#### Lot数据更新存储过程
```sql
DELIMITER $$

CREATE PROCEDURE sp_UpdateProductInboundLot(
    IN p_InboundBillId CHAR(36),
    IN p_OrderDetailId CHAR(36), 
    IN p_LotNo VARCHAR(64)
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    DECLARE v_pcs INT DEFAULT 0;
    DECLARE v_weight DECIMAL(18,1) DEFAULT 0;
    DECLARE v_meters DECIMAL(18,1) DEFAULT 0;
    DECLARE v_yards DECIMAL(18,1) DEFAULT 0;
    DECLARE v_location VARCHAR(64) DEFAULT '';
    DECLARE v_color VARCHAR(64) DEFAULT '';
    DECLARE v_colorCode VARCHAR(64) DEFAULT '';
    DECLARE v_tenantCode VARCHAR(50) DEFAULT '';
    
    -- 统计当前Lot的Roll数据
    SELECT 
        COUNT(*),
        COALESCE(SUM(Weight), 0),
        COALESCE(SUM(Meters), 0), 
        COALESCE(SUM(Yards), 0),
        MAX(Location),
        MAX(Color),
        MAX(ColorCode),
        MAX(TenantCode)
    INTO v_count, v_weight, v_meters, v_yards, v_location, v_color, v_colorCode, v_tenantCode
    FROM ProductInboundRolls 
    WHERE InboundBillId = p_InboundBillId 
      AND OrderDetailId = p_OrderDetailId 
      AND LotNo = p_LotNo 
      AND IsValid = 1;
    
    -- 如果没有Roll数据，删除Lot记录
    IF v_count = 0 THEN
        DELETE FROM ProductInboundLots 
        WHERE InboundBillId = p_InboundBillId 
          AND OrderDetailId = p_OrderDetailId 
          AND LotNo = p_LotNo;
    ELSE
        -- 更新或插入Lot记录
        INSERT INTO ProductInboundLots (
            ID, InboundBillId, OrderDetailId, LotNo, Color, ColorCode,
            Pcs, Weight, Meters, Yards, Location, InboundStatus,
            CreateTime, CreateBy, IsValid, TenantCode
        ) VALUES (
            UUID(), p_InboundBillId, p_OrderDetailId, p_LotNo, v_color, v_colorCode,
            v_count, v_weight, v_meters, v_yards, v_location, 1,
            NOW(), 'SYSTEM_TRIGGER', 1, v_tenantCode
        )
        ON DUPLICATE KEY UPDATE
            Pcs = v_count,
            Weight = v_weight,
            Meters = v_meters,
            Yards = v_yards,
            Location = v_location,
            Color = v_color,
            ColorCode = v_colorCode,
            UpdateTime = NOW(),
            UpdateBy = 'SYSTEM_TRIGGER';
    END IF;
END$$

DELIMITER ;
```

#### 库存更新存储过程
```sql
DELIMITER $$

CREATE PROCEDURE sp_UpdateProductStock(
    IN p_OrderDetailId CHAR(36),
    IN p_Operation VARCHAR(10), -- 'ADD' 或 'SUBTRACT'
    IN p_Weight DECIMAL(18,1),
    IN p_Meters DECIMAL(18,1),
    IN p_Yards DECIMAL(18,1),
    IN p_Pcs INT
)
BEGIN
    DECLARE v_exists INT DEFAULT 0;

    -- 检查库存记录是否存在
    SELECT COUNT(*) INTO v_exists
    FROM ProductStocks
    WHERE OrderDetailId = p_OrderDetailId AND IsValid = 1;

    IF v_exists = 0 THEN
        -- 创建新的库存记录
        INSERT INTO ProductStocks (
            ID, OrderDetailId, TotalPcs, TotalWeight, TotalMeters, TotalYards,
            CreateTime, CreateBy, IsValid, TenantCode
        ) VALUES (
            UUID(), p_OrderDetailId,
            CASE WHEN p_Operation = 'ADD' THEN p_Pcs ELSE -p_Pcs END,
            CASE WHEN p_Operation = 'ADD' THEN p_Weight ELSE -p_Weight END,
            CASE WHEN p_Operation = 'ADD' THEN p_Meters ELSE -p_Meters END,
            CASE WHEN p_Operation = 'ADD' THEN p_Yards ELSE -p_Yards END,
            NOW(), 'SYSTEM_TRIGGER', 1,
            (SELECT TenantCode FROM OrderDetails WHERE ID = p_OrderDetailId LIMIT 1)
        );
    ELSE
        -- 更新现有库存记录
        UPDATE ProductStocks
        SET
            TotalPcs = TotalPcs + CASE WHEN p_Operation = 'ADD' THEN p_Pcs ELSE -p_Pcs END,
            TotalWeight = TotalWeight + CASE WHEN p_Operation = 'ADD' THEN p_Weight ELSE -p_Weight END,
            TotalMeters = TotalMeters + CASE WHEN p_Operation = 'ADD' THEN p_Meters ELSE -p_Meters END,
            TotalYards = TotalYards + CASE WHEN p_Operation = 'ADD' THEN p_Yards ELSE -p_Yards END,
            UpdateTime = NOW(),
            UpdateBy = 'SYSTEM_TRIGGER'
        WHERE OrderDetailId = p_OrderDetailId AND IsValid = 1;
    END IF;
END$$

DELIMITER ;
```

### 3. 批量操作优化存储过程

#### 批量Lot更新存储过程
```sql
DELIMITER $$

CREATE PROCEDURE sp_BatchUpdateProductInboundLot(
    IN p_BillId CHAR(36)
)
BEGIN
    -- 批量计算并更新Lot数据
    INSERT INTO ProductInboundLots (
        ID, InboundBillId, OrderDetailId, LotNo, Color, ColorCode,
        Pcs, Weight, Meters, Yards, Location, InboundStatus,
        CreateTime, CreateBy, IsValid, TenantCode
    )
    SELECT
        UUID() as ID,
        r.InboundBillId,
        r.OrderDetailId,
        r.LotNo,
        MAX(r.Color) as Color,
        MAX(r.ColorCode) as ColorCode,
        COUNT(*) as Pcs,
        SUM(r.Weight) as Weight,
        SUM(r.Meters) as Meters,
        SUM(r.Yards) as Yards,
        MAX(r.Location) as Location,
        MAX(r.InboundStatus) as InboundStatus,
        NOW() as CreateTime,
        'BATCH_SYSTEM' as CreateBy,
        1 as IsValid,
        MAX(r.TenantCode) as TenantCode
    FROM ProductInboundRolls r
    WHERE r.InboundBillId = p_BillId
      AND r.IsValid = 1
    GROUP BY r.InboundBillId, r.OrderDetailId, r.LotNo
    ON DUPLICATE KEY UPDATE
        Pcs = VALUES(Pcs),
        Weight = VALUES(Weight),
        Meters = VALUES(Meters),
        Yards = VALUES(Yards),
        Location = VALUES(Location),
        Color = VALUES(Color),
        ColorCode = VALUES(ColorCode),
        UpdateTime = NOW(),
        UpdateBy = 'BATCH_SYSTEM';

END$$

DELIMITER ;
```

#### 批量库存更新存储过程
```sql
DELIMITER $$

CREATE PROCEDURE sp_BatchUpdateProductStock(
    IN p_BillId CHAR(36)
)
BEGIN
    -- 按OrderDetailId汇总Roll数据并更新库存
    INSERT INTO ProductStocks (
        ID, OrderDetailId, TotalPcs, TotalWeight, TotalMeters, TotalYards,
        CreateTime, CreateBy, IsValid, TenantCode
    )
    SELECT
        UUID() as ID,
        r.OrderDetailId,
        COUNT(*) as TotalPcs,
        SUM(r.Weight) as TotalWeight,
        SUM(r.Meters) as TotalMeters,
        SUM(r.Yards) as TotalYards,
        NOW() as CreateTime,
        'BATCH_SYSTEM' as CreateBy,
        1 as IsValid,
        MAX(r.TenantCode) as TenantCode
    FROM ProductInboundRolls r
    WHERE r.InboundBillId = p_BillId
      AND r.IsValid = 1
    GROUP BY r.OrderDetailId
    ON DUPLICATE KEY UPDATE
        TotalPcs = TotalPcs + VALUES(TotalPcs),
        TotalWeight = TotalWeight + VALUES(TotalWeight),
        TotalMeters = TotalMeters + VALUES(TotalMeters),
        TotalYards = TotalYards + VALUES(TotalYards),
        UpdateTime = NOW(),
        UpdateBy = 'BATCH_SYSTEM';

END$$

DELIMITER ;
```

### 4. 索引优化
```sql
-- 关键索引优化
CREATE INDEX idx_roll_orderdetail_lot ON ProductInboundRolls(OrderDetailId, LotNo, InboundBillId);
CREATE INDEX idx_roll_bill_lot ON ProductInboundRolls(InboundBillId, LotNo);
CREATE INDEX idx_lot_orderdetail ON ProductInboundLots(OrderDetailId, LotNo);
CREATE INDEX idx_lot_bill_order_lot ON ProductInboundLots(InboundBillId, OrderDetailId, LotNo);

-- 查询优化视图
CREATE VIEW v_OrderDetailLots AS
SELECT
    l.OrderDetailId,
    l.LotNo,
    l.Color,
    l.ColorCode,
    l.Pcs,
    l.Weight,
    l.Meters,
    l.Yards,
    l.Location,
    l.InboundStatus,
    b.BillNo,
    b.CreateDate as InboundDate,
    od.ProductName,
    od.Specification
FROM ProductInboundLots l
INNER JOIN ProductInboundBills b ON l.InboundBillId = b.ID
INNER JOIN OrderDetails od ON l.OrderDetailId = od.ID
WHERE l.IsValid = 1 AND b.IsValid = 1;
```

## 应用层实现

### 1. 简化版入库服务
```csharp
namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    public partial class ProductInboundBillVM : BaseCRUDVM<ProductInboundBill>
    {
        /// <summary>
        /// 简化版入库 - 只处理Bill和Roll
        /// </summary>
        public override void DoAdd()
        {
            // 如果没有Roll数据，只创建空Bill
            if (Entity.RollList is null || Entity.RollList.Count == 0)
            {
                base.DoAdd();
                return;
            }

            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 1. 保存Bill
                base.DoAdd();

                // 2. 判断是否为大批量操作
                bool isLargeBatch = Entity.RollList.Count > 1000;

                if (isLargeBatch)
                {
                    // 大批量：禁用触发器，使用批量处理
                    ProcessLargeBatch();
                }
                else
                {
                    // 小批量：正常触发器处理
                    ProcessNormalBatch();
                }

                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// 小批量处理（使用触发器）
        /// </summary>
        private void ProcessNormalBatch()
        {
            foreach (var roll in Entity.RollList)
            {
                roll.InboundBillId = Entity.ID;
                roll.CreateTime = DateTime.Now;
                roll.CreateBy = LoginUserInfo?.ITCode;
                roll.TenantCode = LoginUserInfo?.CurrentTenant;
                roll.IsValid = true;
            }

            DC.Set<ProductInboundRoll>().AddRange(Entity.RollList);
            DC.SaveChanges(); // 触发器自动处理Lot和库存更新
        }

        /// <summary>
        /// 大批量处理（禁用触发器，使用批量存储过程）
        /// </summary>
        private void ProcessLargeBatch()
        {
            // 1. 临时禁用触发器
            DC.Database.ExecuteSqlRaw("SET @DISABLE_TRIGGERS = 1");

            // 2. 批量插入Roll数据
            foreach (var roll in Entity.RollList)
            {
                roll.InboundBillId = Entity.ID;
                roll.CreateTime = DateTime.Now;
                roll.CreateBy = LoginUserInfo?.ITCode;
                roll.TenantCode = LoginUserInfo?.CurrentTenant;
                roll.IsValid = true;
            }

            DC.Set<ProductInboundRoll>().AddRange(Entity.RollList);
            DC.SaveChanges();

            // 3. 批量更新Lot数据
            DC.Database.ExecuteSqlRaw(@"
                CALL sp_BatchUpdateProductInboundLot({0})",
                Entity.ID.ToString());

            // 4. 批量更新库存
            DC.Database.ExecuteSqlRaw(@"
                CALL sp_BatchUpdateProductStock({0})",
                Entity.ID.ToString());

            // 5. 重新启用触发器
            DC.Database.ExecuteSqlRaw("SET @DISABLE_TRIGGERS = 0");
        }

        /// <summary>
        /// 简化版编辑
        /// </summary>
        public override void DoEdit(bool updateAllFields = false)
        {
            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 1. 获取原有Roll数据（用于计算差异）
                var originalRolls = DC.Set<ProductInboundRoll>()
                    .Where(x => x.InboundBillId == Entity.ID && x.IsValid)
                    .AsNoTracking()
                    .ToList();

                // 2. 删除原有Roll数据（触发器会自动更新Lot和库存）
                if (originalRolls.Any())
                {
                    DC.Set<ProductInboundRoll>().RemoveRange(originalRolls);
                }

                // 3. 添加新的Roll数据
                if (Entity.RollList?.Any() == true)
                {
                    foreach (var roll in Entity.RollList)
                    {
                        roll.InboundBillId = Entity.ID;
                        roll.UpdateTime = DateTime.Now;
                        roll.UpdateBy = LoginUserInfo?.ITCode;
                        roll.IsValid = true;
                    }
                    DC.Set<ProductInboundRoll>().AddRange(Entity.RollList);
                }

                // 4. 更新Bill主表
                base.DoEdit(updateAllFields);

                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// 简化版删除
        /// </summary>
        public override void DoDelete()
        {
            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 1. 删除Roll数据（触发器会自动处理Lot和库存）
                var rolls = DC.Set<ProductInboundRoll>()
                    .Where(x => x.InboundBillId == Entity.ID)
                    .ToList();

                if (rolls.Any())
                {
                    DC.Set<ProductInboundRoll>().RemoveRange(rolls);
                    DC.SaveChanges(); // 先保存Roll删除，触发器处理
                }

                // 2. 删除Bill
                base.DoDelete();

                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }
    }
}
```

### 2. EF Core配置调整
```csharp
// DataContext.cs 中的配置调整
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    // ProductInboundRoll实体配置
    modelBuilder.Entity<ProductInboundRoll>(entity =>
    {
        entity.HasKey(e => e.ID);

        // 直接关联到Bill，移除与Lot的关联
        entity.HasOne<ProductInboundBill>()
              .WithMany()
              .HasForeignKey(e => e.InboundBillId)
              .OnDelete(DeleteBehavior.Cascade);

        // 添加复合索引
        entity.HasIndex(e => new { e.InboundBillId, e.OrderDetailId, e.LotNo })
              .HasDatabaseName("IX_Roll_Bill_Order_Lot");

        entity.HasIndex(e => new { e.OrderDetailId, e.LotNo })
              .HasDatabaseName("IX_Roll_Order_Lot");
    });

    // ProductInboundLot保持原有配置
    modelBuilder.Entity<ProductInboundLot>(entity =>
    {
        entity.HasKey(e => e.ID);

        // 添加唯一约束
        entity.HasIndex(e => new { e.InboundBillId, e.OrderDetailId, e.LotNo })
              .IsUnique()
              .HasDatabaseName("UK_Lot_Bill_Order_LotNo");
    });

    base.OnModelCreating(modelBuilder);
}
```

## 性能评估

### 数据量分析
- **单次入库**：最大20,000个Roll，100个Lot
- **年度处理量**：300,000个Roll
- **查询特点**：频繁按OrderDetail查询Lot

### 性能预估

| 操作类型 | 当前方案 | 混合方案 | 改善幅度 |
|---------|---------|---------|---------|
| 小批量入库(<1000 Roll) | 3-5秒 | 2-3秒 | 30%提升 |
| 大批量入库(20000 Roll) | 90-120秒 | 15-25秒 | 75%提升 |
| Lot查询(按OrderDetail) | 50-100ms | 10-20ms | 80%提升 |
| 年度数据增长影响 | 线性增长 | 基本无影响 | 显著改善 |

### 关键优化点
1. **批量操作检测**：自动识别大批量操作并切换处理模式
2. **触发器优化**：小批量使用触发器，大批量使用存储过程
3. **索引优化**：针对查询模式建立复合索引
4. **事务优化**：减少事务范围和锁定时间

## 实施计划

### 第一阶段：基础实施
1. 创建数据库触发器和存储过程
2. 调整ProductInboundRoll实体结构
3. 实现简化版入库服务
4. 单元测试验证

### 第二阶段：性能优化
1. 实现批量操作检测逻辑
2. 添加性能监控
3. 压力测试和调优
4. 建立数据一致性检查机制

### 第三阶段：生产部署
1. 数据迁移脚本
2. 灰度发布
3. 监控和告警
4. 文档更新

## 风险控制

### 数据一致性保障
1. **触发器异常处理**：完善的错误处理和回滚机制
2. **定期数据校验**：自动检查Lot数据与Roll数据的一致性
3. **手动修复工具**：提供数据不一致时的修复功能

### 性能监控
1. **触发器执行时间监控**
2. **大批量操作告警**
3. **数据库锁等待监控**
4. **存储过程性能分析**

### 回滚方案
1. **保留原有代码**：作为紧急回滚选项
2. **数据备份策略**：实施前完整备份
3. **快速切换机制**：配置开关控制新旧逻辑

## 数据一致性检查工具

### 检查脚本
```sql
-- 检查Lot数据与Roll数据的一致性
SELECT
    'Lot数据不一致' as ErrorType,
    l.InboundBillId,
    l.OrderDetailId,
    l.LotNo,
    l.Pcs as LotPcs,
    r.RollCount as ActualRollCount,
    l.Weight as LotWeight,
    r.ActualWeight,
    l.Meters as LotMeters,
    r.ActualMeters,
    l.Yards as LotYards,
    r.ActualYards
FROM ProductInboundLots l
LEFT JOIN (
    SELECT
        InboundBillId,
        OrderDetailId,
        LotNo,
        COUNT(*) as RollCount,
        SUM(Weight) as ActualWeight,
        SUM(Meters) as ActualMeters,
        SUM(Yards) as ActualYards
    FROM ProductInboundRolls
    WHERE IsValid = 1
    GROUP BY InboundBillId, OrderDetailId, LotNo
) r ON l.InboundBillId = r.InboundBillId
    AND l.OrderDetailId = r.OrderDetailId
    AND l.LotNo = r.LotNo
WHERE l.IsValid = 1
  AND (
    l.Pcs != COALESCE(r.RollCount, 0) OR
    ABS(l.Weight - COALESCE(r.ActualWeight, 0)) > 0.1 OR
    ABS(l.Meters - COALESCE(r.ActualMeters, 0)) > 0.1 OR
    ABS(l.Yards - COALESCE(r.ActualYards, 0)) > 0.1
  );
```

### 修复存储过程
```sql
DELIMITER $$

CREATE PROCEDURE sp_RepairLotData(
    IN p_BillId CHAR(36) DEFAULT NULL
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_billId CHAR(36);
    DECLARE v_orderDetailId CHAR(36);
    DECLARE v_lotNo VARCHAR(64);

    DECLARE lot_cursor CURSOR FOR
        SELECT DISTINCT InboundBillId, OrderDetailId, LotNo
        FROM ProductInboundRolls
        WHERE IsValid = 1
          AND (p_BillId IS NULL OR InboundBillId = p_BillId);

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN lot_cursor;

    repair_loop: LOOP
        FETCH lot_cursor INTO v_billId, v_orderDetailId, v_lotNo;
        IF done THEN
            LEAVE repair_loop;
        END IF;

        CALL sp_UpdateProductInboundLot(v_billId, v_orderDetailId, v_lotNo);
    END LOOP;

    CLOSE lot_cursor;
END$$

DELIMITER ;
```

## 总结

本混合方案通过**Bill-Roll实体 + Lot触发器**的设计，在保持数据完整性的前提下，显著简化了应用层的入库操作逻辑。针对TEX项目的数据量特征进行了专门优化，预期可以带来75%的性能提升。

### 关键成功因素：
1. **完善的触发器和存储过程实现**
2. **智能的批量操作检测**
3. **全面的性能监控和数据校验**
4. **分阶段的稳妥实施策略**

### 技术优势：
- 应用层逻辑大幅简化
- 数据一致性由数据库保障
- 性能显著提升
- 保持了完整的三级架构

### 适用场景：
- 大批量数据入库操作
- 需要保持数据一致性的复杂业务
- 对性能有较高要求的系统

该方案在技术上完全可行，建议按照实施计划逐步推进。通过合理的风险控制和监控机制，可以确保方案的成功实施。

---

## 附录：中等数据量优化方案

### 方案背景

基于更新的数据量评估：
- **单个Bill**：最多100个Lot，1000个Roll（相比原评估的20000个Roll大幅降低）
- **年处理量**：300,000个Roll
- **查询特点**：频繁按OrderDetail查询Lot
- **数据库**：MySQL

### 数据量影响分析

| 指标 | 原评估(20000 Roll) | 新评估(1000 Roll) | 影响 |
|------|-------------------|------------------|------|
| 单次触发器执行次数 | 20,000次 | 1,000次 | 减少95% |
| 预估处理时间 | 60-120秒 | 3-6秒 | 减少95% |
| 性能风险 | 高风险 | 低风险 | 显著降低 |
| 实施复杂度 | 复杂 | 简单 | 大幅简化 |

### 简化方案A：保持三级架构 + 简化触发器（推荐）

#### 方案优势
1. **风险最低**：保持现有业务逻辑和数据结构不变
2. **实施简单**：只需添加触发器，代码修改量最小
3. **性能提升明显**：预期40-50%的性能改善
4. **维护成本低**：触发器逻辑简单，易于调试和维护

#### 简化版触发器实现
```sql
-- 简化版触发器 - 适用于1000个Roll以内的场景
DELIMITER $$

CREATE TRIGGER tr_ProductInboundRoll_Simple_AfterInsert
AFTER INSERT ON ProductInboundRolls
FOR EACH ROW
BEGIN
    CALL sp_UpdateProductInboundLot_Simple(NEW.LotId);
    CALL sp_UpdateProductStock_Simple(NEW.LotId, 'ADD');
END$$

CREATE TRIGGER tr_ProductInboundRoll_Simple_AfterUpdate
AFTER UPDATE ON ProductInboundRolls
FOR EACH ROW
BEGIN
    -- 如果LotId发生变化，需要更新两个Lot
    IF OLD.LotId != NEW.LotId THEN
        CALL sp_UpdateProductInboundLot_Simple(OLD.LotId);
        CALL sp_UpdateProductInboundLot_Simple(NEW.LotId);
        CALL sp_UpdateProductStock_Simple(OLD.LotId, 'RECALC');
        CALL sp_UpdateProductStock_Simple(NEW.LotId, 'RECALC');
    ELSE
        CALL sp_UpdateProductInboundLot_Simple(NEW.LotId);
        CALL sp_UpdateProductStock_Simple(NEW.LotId, 'RECALC');
    END IF;
END$$

CREATE TRIGGER tr_ProductInboundRoll_Simple_AfterDelete
AFTER DELETE ON ProductInboundRolls
FOR EACH ROW
BEGIN
    CALL sp_UpdateProductInboundLot_Simple(OLD.LotId);
    CALL sp_UpdateProductStock_Simple(OLD.LotId, 'RECALC');
END$$

DELIMITER ;
```

#### 简化版存储过程
```sql
DELIMITER $$

CREATE PROCEDURE sp_UpdateProductInboundLot_Simple(
    IN p_LotId CHAR(36)
)
BEGIN
    -- 直接基于LotId更新，无需复杂的分组逻辑
    UPDATE ProductInboundLots l
    SET
        l.Pcs = (
            SELECT COUNT(*)
            FROM ProductInboundRolls r
            WHERE r.LotId = p_LotId AND r.IsValid = 1
        ),
        l.Weight = (
            SELECT COALESCE(SUM(r.Weight), 0)
            FROM ProductInboundRolls r
            WHERE r.LotId = p_LotId AND r.IsValid = 1
        ),
        l.Meters = (
            SELECT COALESCE(SUM(r.Meters), 0)
            FROM ProductInboundRolls r
            WHERE r.LotId = p_LotId AND r.IsValid = 1
        ),
        l.Yards = (
            SELECT COALESCE(SUM(r.Yards), 0)
            FROM ProductInboundRolls r
            WHERE r.LotId = p_LotId AND r.IsValid = 1
        ),
        l.UpdateTime = NOW(),
        l.UpdateBy = 'SYSTEM_TRIGGER'
    WHERE l.ID = p_LotId;
END$$

CREATE PROCEDURE sp_UpdateProductStock_Simple(
    IN p_LotId CHAR(36),
    IN p_Operation VARCHAR(10) -- 'ADD', 'RECALC'
)
BEGIN
    DECLARE v_orderDetailId CHAR(36);
    DECLARE v_pcs INT DEFAULT 0;
    DECLARE v_weight DECIMAL(18,1) DEFAULT 0;
    DECLARE v_meters DECIMAL(18,1) DEFAULT 0;
    DECLARE v_yards DECIMAL(18,1) DEFAULT 0;

    -- 获取OrderDetailId和当前Lot的统计数据
    SELECT
        l.OrderDetailId,
        l.Pcs,
        l.Weight,
        l.Meters,
        l.Yards
    INTO v_orderDetailId, v_pcs, v_weight, v_meters, v_yards
    FROM ProductInboundLots l
    WHERE l.ID = p_LotId AND l.IsValid = 1;

    IF v_orderDetailId IS NOT NULL THEN
        IF p_Operation = 'RECALC' THEN
            -- 重新计算整个OrderDetail的库存
            UPDATE ProductStocks ps
            SET
                ps.TotalPcs = (
                    SELECT COALESCE(SUM(l.Pcs), 0)
                    FROM ProductInboundLots l
                    WHERE l.OrderDetailId = v_orderDetailId AND l.IsValid = 1
                ),
                ps.TotalWeight = (
                    SELECT COALESCE(SUM(l.Weight), 0)
                    FROM ProductInboundLots l
                    WHERE l.OrderDetailId = v_orderDetailId AND l.IsValid = 1
                ),
                ps.TotalMeters = (
                    SELECT COALESCE(SUM(l.Meters), 0)
                    FROM ProductInboundLots l
                    WHERE l.OrderDetailId = v_orderDetailId AND l.IsValid = 1
                ),
                ps.TotalYards = (
                    SELECT COALESCE(SUM(l.Yards), 0)
                    FROM ProductInboundLots l
                    WHERE l.OrderDetailId = v_orderDetailId AND l.IsValid = 1
                ),
                ps.UpdateTime = NOW(),
                ps.UpdateBy = 'SYSTEM_TRIGGER'
            WHERE ps.OrderDetailId = v_orderDetailId AND ps.IsValid = 1;
        END IF;
    END IF;
END$$

DELIMITER ;
```

#### 极简版应用层实现
```csharp
namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    public partial class ProductInboundBillVM : BaseCRUDVM<ProductInboundBill>
    {
        /// <summary>
        /// 极简版入库服务 - 适用于1000个Roll以内
        /// </summary>
        public override void DoAdd()
        {
            // 如果没有Lot数据，只创建空Bill
            if (Entity.LotList is null || Entity.LotList.Count == 0)
            {
                base.DoAdd();
                return;
            }

            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 1. 保存Bill和Lot（保持原有三级结构）
                base.DoAdd();

                // 2. 保存Roll数据 - 触发器会自动更新Lot统计和库存
                var allRolls = Entity.LotList.SelectMany(lot => lot.RollList ?? new List<ProductInboundRoll>()).ToList();

                foreach (var roll in allRolls)
                {
                    roll.CreateTime = DateTime.Now;
                    roll.CreateBy = LoginUserInfo?.ITCode;
                    roll.TenantCode = LoginUserInfo?.CurrentTenant;
                    roll.IsValid = true;
                }

                if (allRolls.Any())
                {
                    DC.Set<ProductInboundRoll>().AddRange(allRolls);
                    DC.SaveChanges(); // 触发器自动处理统计更新
                }

                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// 极简版编辑 - 移除手动统计逻辑
        /// </summary>
        public override void DoEdit(bool updateAllFields = false)
        {
            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 保持原有编辑逻辑，移除手动Lot统计和库存更新
                // 触发器会自动处理这些逻辑
                base.DoEdit(updateAllFields);

                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// 极简版删除 - 移除手动统计逻辑
        /// </summary>
        public override void DoDelete()
        {
            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 保持原有删除逻辑，移除手动库存更新
                // 触发器会自动处理库存减少
                base.DoDelete();

                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }
    }
}
```

### 简化方案B：Bill-Roll两级 + Lot视图（备选）

对于1000个Roll的数据量，此方案的收益不明显，且实施风险较高，不推荐采用。

### 性能对比（中等数据量）

| 操作类型 | 当前方案 | 简化触发器方案 | 改善幅度 |
|---------|---------|---------------|---------|
| 小批量入库(<100 Roll) | 1-2秒 | 0.5-1秒 | 50%提升 |
| 中批量入库(100-500 Roll) | 2-4秒 | 1-2秒 | 50%提升 |
| 大批量入库(1000 Roll) | 5-8秒 | 3-5秒 | 40%提升 |
| Lot查询(按OrderDetail) | 50-100ms | 10-20ms | 80%提升 |
| 年度数据增长影响 | 线性增长 | 基本无影响 | 显著改善 |

### 实施建议（中等数据量）

#### 第一阶段：触发器部署
1. **创建简化版触发器和存储过程**
2. **与现有逻辑并行运行**，验证数据一致性
3. **小范围测试**，确保触发器正常工作

#### 第二阶段：应用层简化
1. **移除手动Lot统计逻辑**
2. **移除手动库存更新逻辑**
3. **保留原有三级实体结构**
4. **全面测试**，确保业务功能正常

#### 第三阶段：性能优化
1. **添加查询索引优化**
2. **监控触发器性能**
3. **建立数据一致性检查机制**

### 风险评估（中等数据量）

| 风险类型 | 风险等级 | 缓解措施 |
|---------|---------|---------|
| 数据一致性 | 低 | 触发器逻辑简单，易于验证 |
| 性能影响 | 极低 | 1000个Roll的触发器开销可忽略 |
| 实施复杂度 | 低 | 保持现有结构，修改量最小 |
| 回滚难度 | 极低 | 可快速禁用触发器回到原方案 |

### 总结（中等数据量方案）

对于**1000个Roll以内**的数据量场景：

1. **推荐采用简化触发器方案**：
   - 实施风险最低
   - 性能提升明显（40-50%）
   - 代码修改量最小
   - 维护成本低

2. **不推荐复杂的Bill-Roll两级方案**：
   - 对于中等数据量收益不明显
   - 实施风险和成本过高
   - 性价比不佳

3. **实施策略**：
   - 分三个阶段逐步实施
   - 重点关注数据一致性验证
   - 保持现有业务逻辑不变

该简化方案特别适合当前的数据量规模，能够在最小风险下获得显著的性能提升。
