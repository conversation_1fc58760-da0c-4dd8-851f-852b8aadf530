using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPVMs
{
    public partial class WIPTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<WIP>(x => x.BillNo);
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<WIP>(x => x.CreateDate);
        public ExcelPropety POrder_Excel = ExcelPropety.CreateProperty<WIP>(x => x.POrderId);
        public ExcelPropety Shipper_Excel = ExcelPropety.CreateProperty<WIP>(x => x.ShipperId);
        public ExcelPropety Receiver_Excel = ExcelPropety.CreateProperty<WIP>(x => x.ReceiverId);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<WIP>(x => x.Pcs);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<WIP>(x => x.Meters);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<WIP>(x => x.Weight);
        [Display(Name = "_Procedure")]
        public ExcelPropety Procedure_Excel = ExcelPropety.CreateProperty<WIP>(x => x.Procedure);
        [Display(Name = "_FinishingProcess")]
        public ExcelPropety FinishingProcess_Excel = ExcelPropety.CreateProperty<WIP>(x => x.FinishingProcess);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<WIP>(x => x.Remark);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<WIP>(x => x.AuditStatus);

	    protected override void InitVM()
        {
            POrder_Excel.DataType = ColumnDataType.ComboBox;
            POrder_Excel.ListItems = DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, y => y.OrderNo);
            Shipper_Excel.DataType = ColumnDataType.ComboBox;
            Shipper_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
            Receiver_Excel.DataType = ColumnDataType.ComboBox;
            Receiver_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class WIPImportVM : BaseImportVM<WIPTemplateVM, WIP>
    {

    }

}
