using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPVMs
{
    public partial class WIPBatchVM : BaseBatchVM<WIP, WIP_BatchEdit>
    {
        public WIPBatchVM()
        {
            ListVM = new WIPListVM();
            LinkedVM = new WIP_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class WIP_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
