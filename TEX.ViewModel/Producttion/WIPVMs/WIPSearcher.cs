using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPVMs
{
    public partial class WIPSearcher : BaseSearcher
    {
        [Display(Name = "_BillNo")]
        public String BillNo { get; set; }
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        [Display(Name = "_POrder")]
        public Guid? POrderId { get; set; }
        [Display(Name = "_Shipper")]
        public Guid? ShipperId { get; set; }
        [Display(Name = "_Receiver")]
        public Guid? ReceiverId { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; }

        protected override void InitVM()
        {
        }

    }
}
