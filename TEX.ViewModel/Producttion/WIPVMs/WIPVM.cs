using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPVMs
{
    public partial class WIPVM : BaseCRUDVM<WIP>
    {

        public WIPVM()
        {
            SetInclude(x => x.POrder);
            SetInclude(x => x.Shipper);
            SetInclude(x => x.Receiver);
            SetInclude(x => x.DetailList);

        }
        //设置唯一字段检查
        public override DuplicatedInfo<WIP> SetDuplicatedCheck()
        {
            var rv = CreateFieldsInfo(SimpleField(x => x.BillNo));
            return rv;
        }
        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
