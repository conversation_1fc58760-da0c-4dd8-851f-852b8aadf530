using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using TEX.ViewModel.Models.ProductVMs;


namespace TEX.ViewModel.Producttion.WIPVMs
{
    public partial class WIPListVM : BasePagedListVM<WIP_View, WIPSearcher>
    {

        protected override IEnumerable<IGridColumn<WIP_View>> InitGridHeader()
        {
            return new List<GridColumn<WIP_View>>{
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.Product_view),
                this.MakeGridHeader(x => x.Shipper_view),
                this.MakeGridHeader(x => x.Receiver_view),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Procedure),
                this.MakeGridHeader(x => x.FinishingProcess),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<WIP_View> GetSearchQuery()
        {
            var query = DC.Set<WIP>()
                .CheckContain(Searcher.BillNo, x=>x.BillNo)
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckEqual(Searcher.POrderId, x=>x.POrderId)
                .CheckEqual(Searcher.ShipperId, x=>x.ShipperId)
                .CheckEqual(Searcher.ReceiverId, x=>x.ReceiverId)
                .CheckEqual(Searcher.AuditStatus, x=>x.AuditStatus)
                .Select(x => new WIP_View
                {
				    ID = x.ID,
                    BillNo = x.BillNo,
                    CreateDate = x.CreateDate,
                    OrderNo_view = x.POrder.OrderNo,
                    Product_view=x.POrder.Product.ProductName,
                    Shipper_view = x.Shipper.CompanyName,
                    Receiver_view = x.Receiver.CompanyName,
                    Pcs = x.Pcs,
                    Meters = x.Meters,
                    Weight = x.Weight,
                    Procedure = x.Procedure,
                    FinishingProcess = x.FinishingProcess,
                    Remark = x.Remark,
                    AuditStatus = x.AuditStatus,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class WIP_View : WIP{
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_Product")]
        public String Product_view { get; set; }

        [Display(Name = "_Shipper")]
        public String Shipper_view { get; set; }
        [Display(Name = "_Receiver")]
        public String Receiver_view { get; set; }

    }
}
