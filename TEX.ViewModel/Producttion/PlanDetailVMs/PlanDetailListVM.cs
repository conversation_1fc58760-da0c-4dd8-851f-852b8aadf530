using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.PlanDetailVMs
{
    public partial class PlanDetailListVM : BasePagedListVM<PlanDetail_View, PlanDetailSearcher>
    {

        protected override IEnumerable<IGridColumn<PlanDetail_View>> InitGridHeader()
        {
            return new List<GridColumn<PlanDetail_View>>{
                this.MakeGridHeader(x => x.BillNo_view),
                this.MakeGridHeader(x => x.POrder),
                this.MakeGridHeader(x => x.Product),
                this.MakeGridHeader(x => x.Color_view),
                this.MakeGridHeader(x => x.Color),
                this.MakeGridHeader(x => x.ColorCode),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.AllocatedMeters),
                this.MakeGridHeader(x => x.AllocatedMetersPercent),
                this.MakeGridHeader(x => x.AllocatedWeight),
                this.MakeGridHeader(x => x.AllocatedWeightPercent),
                this.MakeGridHeader(x => x.DeliveryDate),
                this.MakeGridHeader(x => x.FinishingPrice),
                this.MakeGridHeader(x => x.GreigeBatch),
                this.MakeGridHeader(x => x.DyeingProcess),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<PlanDetail_View> GetSearchQuery()
        {
            var query = DC.Set<PlanDetail>()
                .CheckEqual(Searcher.DyeingPlanId, x => x.DyeingPlanId)
                .CheckEqual(Searcher.OrderDetailId, x => x.OrderDetailId)
                .CheckBetween(Searcher.DeliveryDate?.GetStartTime(), Searcher.DeliveryDate?.GetEndTime(), x => x.DeliveryDate, includeMax: false)
                .CheckContain(Searcher.GreigeBatch, x => x.GreigeBatch)
                .Select(x => new PlanDetail_View
                {
                    ID = x.ID,
                    BillNo_view = x.DyeingPlan.BillNo,
                    POrder = x.OrderDetail.PurchaseOrder.OrderNo,
                    Product = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    Color_view = x.OrderDetail.Color,
                    Color = x.Color,
                    ColorCode = x.OrderDetail.ColorCode,
                    Pcs = x.Pcs,
                    Meters = x.Meters,
                    Weight = x.Weight,
                    AllocatedMeters = x.LotAllocateList.Sum(y => y.Meters),
                    AllocatedWeight = x.LotAllocateList.Sum(y => y.Weight),
                    AllocatedMetersPercent = x.Meters == 0 ? 0 : x.LotAllocateList.Sum(y => y.Meters) / x.Meters ,
                    AllocatedWeightPercent = x.Weight == 0 ? 0 : x.LotAllocateList.Sum(y => y.Weight) / x.Weight ,
                    Qty = x.Qty,
                    QtyUnit = x.QtyUnit,
                    DeliveryDate = x.DeliveryDate,
                    FinishingPrice = x.FinishingPrice,
                    GreigeBatch = x.GreigeBatch,
                    DyeingProcess = x.DyeingProcess,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }
    }

    public class PlanDetail_View : PlanDetail
    {
        [Display(Name = "_BillNo")]
        public String BillNo_view { get; set; }
        [Display(Name = "_Color")]
        public String Color_view { get; set; }
        [Display(Name = "_PurchaseOrder")]
        public String POrder { get; set; }
        [Display(Name = "_Product")]
        public String Product { get; set; }

        [Display(Name = "AllocatedMeters")]
        public Decimal AllocatedMeters { get; set; }
        [Display(Name = "AllocatedWeight")]
        public Decimal AllocatedWeight { get; set; }

        [Display(Name = "AllocatedMetersPercent")]
        public Decimal AllocatedMetersPercent { get; set; }
        [Display(Name = "AllocatedWeightPercent")]
        public Decimal AllocatedWeightPercent { get; set; }
    }
}
