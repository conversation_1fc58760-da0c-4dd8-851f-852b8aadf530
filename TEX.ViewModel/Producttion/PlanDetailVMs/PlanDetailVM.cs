using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.PlanDetailVMs
{
    public partial class PlanDetailVM : BaseCRUDVM<PlanDetail>
    {

        public PlanDetailVM()
        {
            SetInclude(x => x.DyeingPlan);
            SetInclude(x => x.OrderDetail);
            SetInclude(x => x.LotAllocateList);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
