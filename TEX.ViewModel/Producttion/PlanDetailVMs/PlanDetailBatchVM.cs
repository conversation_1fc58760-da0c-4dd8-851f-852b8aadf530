using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.PlanDetailVMs
{
    public partial class PlanDetailBatchVM : BaseBatchVM<PlanDetail, PlanDetail_BatchEdit>
    {
        public PlanDetailBatchVM()
        {
            ListVM = new PlanDetailListVM();
            LinkedVM = new PlanDetail_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class PlanDetail_BatchEdit : BaseVM
    {
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_QtyUnit")]
        public AccountingUnitEnum? QtyUnit { get; set; }
        [Display(Name = "_DeliveryDate")]
        public DateTime? DeliveryDate { get; set; }
        [Display(Name = "_GreigeBatch")]
        public String GreigeBatch { get; set; }
        [Display(Name = "_DyeingProcess")]
        public String DyeingProcess { get; set; }

        protected override void InitVM()
        {
        }

    }

}
