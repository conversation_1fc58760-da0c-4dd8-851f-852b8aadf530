using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.PlanDetailVMs
{
    public partial class PlanDetailSearcher : BaseSearcher
    {
        public Guid? DyeingPlanId { get; set; }
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_DeliveryDate")]
        public DateRange DeliveryDate { get; set; }
        [Display(Name = "_GreigeBatch")]
        public String GreigeBatch { get; set; }

        protected override void InitVM()
        {
        }

    }
}
