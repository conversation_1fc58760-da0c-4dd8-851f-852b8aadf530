using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.PlanDetailVMs
{
    public partial class PlanDetailTemplateVM : BaseTemplateVM
    {
        public ExcelPropety DyeingPlan_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.DyeingPlanId);
        public ExcelPropety OrderDetail_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.OrderDetailId);
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.Color);
        [Display(Name = "_ColorCode")]
        public ExcelPropety ColorCode_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.ColorCode);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.Pcs);
        [Display(Name = "_Qty")]
        public ExcelPropety Qty_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.Qty);
        [Display(Name = "_QtyUnit")]
        public ExcelPropety QtyUnit_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.QtyUnit);
        [Display(Name = "_DeliveryDate")]
        public ExcelPropety DeliveryDate_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.DeliveryDate);
        [Display(Name = "_FinishingPrice")]
        public ExcelPropety FinishingPrice_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.FinishingPrice);
        [Display(Name = "_GreigeBatch")]
        public ExcelPropety GreigeBatch_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.GreigeBatch);
        [Display(Name = "_DyeingProcess")]
        public ExcelPropety DyeingProcess_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.DyeingProcess);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<PlanDetail>(x => x.Remark);

	    protected override void InitVM()
        {
            DyeingPlan_Excel.DataType = ColumnDataType.ComboBox;
            DyeingPlan_Excel.ListItems = DC.Set<DyeingPlan>().GetSelectListItems(Wtm, y => y.BillNo);
            OrderDetail_Excel.DataType = ColumnDataType.ComboBox;
            OrderDetail_Excel.ListItems = DC.Set<OrderDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class PlanDetailImportVM : BaseImportVM<PlanDetailTemplateVM, PlanDetail>
    {

    }

}
