using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.DyeingPlanVMs
{
    public partial class DyeingPlanTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.CreateDate);
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.BillNo);
        [Display(Name = "_Version")]
        public ExcelPropety Version_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.Version);
        [Display(Name = "_FinishingFactory")]
        public ExcelPropety FinishingFactory_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.FinishingFactoryId);
        [Display(Name = "_POrder")]
        public ExcelPropety POrder_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.POrderId);
        [Display(Name = "_PlanBatch")]
        public ExcelPropety PlanBatch_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.PlanBatch);
        [Display(Name = "_FinishingProcess")]
        public ExcelPropety FinishingProcess_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.FinishingProcess);
        //[Display(Name = "_Fabric")]
        //public ExcelPropety Fabric_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.FabricId);
        [Display(Name = "_Width")]
        public ExcelPropety Width_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.Width);
        [Display(Name = "_Gsm")]
        public ExcelPropety Gsm_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.GSM);
        [Display(Name = "_Light")]
        public ExcelPropety Light_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.Light);
        [Display(Name = "_Light2")]
        public ExcelPropety Light2_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.Light2);
        [Display(Name = "_GreigeBatch")]
        public ExcelPropety GreigeBatch_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.GreigeBatch);
        [Display(Name = "_GreigeVender")]
        public ExcelPropety GreigeVender_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.GreigeVenderId);
        [Display(Name = "_DyingDemand")]
        public ExcelPropety DyingDemand_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.DyeingDemand);
        [Display(Name = "_PackDemand")]
        public ExcelPropety PackDemand_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.PackDemand);
        [Display(Name = "_AdditionalDemend")]
        public ExcelPropety AdditionalDemend_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.AdditionalDemend);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.AuditStatus);
        [Display(Name = "_AuditedBy")]
        public ExcelPropety AuditedBy_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.AuditedBy);
        [Display(Name = "_AuditedComment")]
        public ExcelPropety AuditedComment_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.AuditedComment);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<DyeingPlan>(x => x.Remark);

	    protected override void InitVM()
        {
            FinishingFactory_Excel.DataType = ColumnDataType.ComboBox;
            FinishingFactory_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
            POrder_Excel.DataType = ColumnDataType.ComboBox;
            POrder_Excel.ListItems = DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, y => y.OrderNo);
            //Fabric_Excel.DataType = ColumnDataType.ComboBox;
            //Fabric_Excel.ListItems = DC.Set<Product>().GetSelectListItems(Wtm, y => y.ProductName);
            GreigeVender_Excel.DataType = ColumnDataType.ComboBox;
            GreigeVender_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class DyeingPlanImportVM : BaseImportVM<DyeingPlanTemplateVM, DyeingPlan>
    {

    }

}
