using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.DyeingPlanVMs
{
    public partial class DyeingPlanListVM : BasePagedListVM<DyeingPlan_View, DyeingPlanSearcher>
    {

        protected override IEnumerable<IGridColumn<DyeingPlan_View>> InitGridHeader()
        {
            return new List<GridColumn<DyeingPlan_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.FinishingFactoryName_view),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.PlanBatch),
                this.MakeGridHeader(x => x.ProductName_view),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.TotalQty),
                this.MakeGridHeader(x => x.TotalMeters),
                this.MakeGridHeader(x => x.TotalWeight),
                this.MakeGridHeader(x => x.Light),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<DyeingPlan_View> GetSearchQuery()
        {
            var query = DC.Set<DyeingPlan>()
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckEqual(Searcher.BillNo, x=>x.BillNo)
                .CheckEqual(Searcher.FinishingFactoryId, x=>x.FinishingFactoryId)
                .CheckEqual(Searcher.POrderId, x=>x.POrderId)
                .CheckEqual(Searcher.GreigeVenderId, x=>x.GreigeVenderId)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new DyeingPlan_View
                {
				    ID = x.ID,
                    CreateDate = x.CreateDate,
                    BillNo = x.BillNo,
                    FinishingFactoryName_view = x.FinishingFactory.CompanyName,
                    OrderNo_view = x.POrder.OrderNo,
                    PlanBatch = x.PlanBatch,
                    ProductName_view = x.POrder.Product.ProductName,
                    Pcs=x.Pcs,
                    TotalQty=x.TotalQty,
                    TotalMeters=x.TotalMeters,
                    TotalWeight=x.TotalWeight,
                    Light = x.Light,
                    AuditStatus = x.AuditStatus,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class DyeingPlan_View : DyeingPlan{
        [Display(Name = "_FinishingFactory")]
        public String FinishingFactoryName_view { get; set; }
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_ProductName")]
        public String ProductName_view { get; set; }

    }

    public class DyeingPlanWithDetails_TreeView : TopBasePoco
    {
        //public Guid ID { get; set; }
        public Guid? ParentID { get; set; }
        public string DisplayText { get; set; }
        public string Icon { get; set; } = "fa-solid fa-font-awesome";
        public bool IsActive { get; set; }

    }
}
