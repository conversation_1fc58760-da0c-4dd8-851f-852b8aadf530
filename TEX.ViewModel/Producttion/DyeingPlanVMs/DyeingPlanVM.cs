using TEX.Model.Models;
using WalkingTec.Mvvm.Core;


namespace TEX.ViewModel.Producttion.DyeingPlanVMs
{
    public partial class DyeingPlanVM : BaseCRUDVM<DyeingPlan>
    {

        public DyeingPlanVM()
        {
            //SetInclude(x => x.FinishingFactory);
            SetInclude(x => x.POrder);
            //SetInclude(x => x.Fabric);
            
            SetInclude(x => x.DetailList);
            //SetInclude(x => x.GreigeVender);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
