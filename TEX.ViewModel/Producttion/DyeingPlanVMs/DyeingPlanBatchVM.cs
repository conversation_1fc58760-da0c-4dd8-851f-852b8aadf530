using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.DyeingPlanVMs
{
    public partial class DyeingPlanBatchVM : BaseBatchVM<DyeingPlan, DyeingPlan_BatchEdit>
    {
        public DyeingPlanBatchVM()
        {
            ListVM = new DyeingPlanListVM();
            LinkedVM = new DyeingPlan_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class DyeingPlan_BatchEdit : BaseVM
    {
        [Display(Name = "_CreateDate")]
        public DateTime? CreateDate { get; set; }
        [Display(Name = "_FinishingFactory")]
        public Guid? FinishingFactoryId { get; set; }
        [Display(Name = "_FinishingProcess")]
        public String FinishingProcess { get; set; }
        [Display(Name = "_Fabric")]
        public Guid? FabricId { get; set; }
        [Display(Name = "_Light")]
        public LightEnum? Light { get; set; }
        [Display(Name = "_Light2")]
        public LightEnum? Light2 { get; set; }
        [Display(Name = "_GreigeBatch")]
        public String GreigeBatch { get; set; }
        [Display(Name = "_GreigeVender")]
        public Guid? GreigeVenderId { get; set; }

        protected override void InitVM()
        {
        }

    }

}
