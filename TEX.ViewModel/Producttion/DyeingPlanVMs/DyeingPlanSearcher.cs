using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.DyeingPlanVMs
{
    public partial class DyeingPlanSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        [Display(Name = "_BillNo")]
        public String BillNo { get; set; }
        [Display(Name = "_FinishingFactory")]
        public Guid? FinishingFactoryId { get; set; }
        [Display(Name = "_POrder")]
        public Guid? POrderId { get; set; }
        [Display(Name = "_Fabric")]
        public Guid? FabricId { get; set; }
        [Display(Name = "_GreigeVender")]
        public Guid? GreigeVenderId { get; set; }
        [Display(Name = "_Remark")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }
}
