using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using TEX.Model.Models;
using Elsa.Persistence.Specifications;
using System.ComponentModel.DataAnnotations;


namespace TEX.ViewModel.Producttion.WIPDetailVMs;

public partial class WIPDetailListVM2 : BasePagedListVM<LossRate_View, WIPDetailSearcher>
{


    public override IOrderedQueryable<LossRate_View> GetSearchQuery()
    {
        var q = from wips in DC.Set<WIP>()
                join wipdetails in DC.Set<WIPDetail>()
                on wips.ID equals wipdetails.WIPId
                select new
                {
                    wips.POrderId,
                    wips.POrder.OrderNo,
                    wips.Procedure,
                    wips.ShipperId,
                    ShipperName = wips.Shipper.CompanyName,
                    wips.ReceiverId,
                    ReceiverName = wips.Receiver.CompanyName,
                    wipdetails.Color,
                    wipdetails.LotNo,
                    wipdetails.Pcs,
                    wipdetails.Meters
                };

        var query = (from s in q
                     join r in q on
                     new { s.POrderId, s.Color, s.LotNo, ShipperId = s.ReceiverId } equals new { r.POrderId, r.Color, r.LotNo, r.ShipperId }
                     select new LossRate_View
                     {
                         ID = new(),
                         Procedure = s.Procedure,
                         OrderNo = s.OrderNo,
                         Shipper = s.ShipperName,
                         Receiver = s.ReceiverName,
                         LotNo = s.LotNo,
                         Color = s.Color,
                         ShipperPcs = s.Pcs,
                         ShipperMeters = s.Meters,
                         ReceiverPcs = r.Pcs,
                         ReceiverMeters = r.Meters,
                         LossRate = (r.Meters - s.Meters) / s.Meters,
                         POrderId = s.POrderId,
                         ShipperId = s.ShipperId
                     })
                     .OrderBy(x=>x.Procedure);
                    
        return query;
    }
     
}

public class LossRate_View: WIPDetail
{
    [Display(Name = "_Procedure")]
    public ProcedureEnum Procedure { get; set; }
    [Display(Name = "_Shipper")]
    public String Shipper { get; set; }
    [Display(Name = "_Receiver")]
    public String Receiver { get; set; }
    [Display(Name = "_OrderNo")]
    public String OrderNo { get; set; }
    [Display(Name = "_ShipperPcs")]
    public int ShipperPcs { get; set; }
    [Display(Name = "_ShipperMeters")]
    public Decimal ShipperMeters { get; set; }
    [Display(Name = "_ReceiverPcs")]
    public int ReceiverPcs { get; set; }
    [Display(Name = "_ReceiverMeters")]
    public Decimal ReceiverMeters { get; set; }
    [Display(Name = "_POrderId")]
    public Guid POrderId { get; set; }
    [Display(Name = "_ShipperId")]
    public Guid ShipperId { get; set; }
    [Display(Name = "_LossRate")]
    public Decimal LossRate { get; set; }
}
