using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPDetailVMs
{
    public partial class WIPDetailSearcher : BaseSearcher
    {
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_LotNo")]
        public String LotNo { get; set; }

        protected override void InitVM()
        {
        }

    }
}
