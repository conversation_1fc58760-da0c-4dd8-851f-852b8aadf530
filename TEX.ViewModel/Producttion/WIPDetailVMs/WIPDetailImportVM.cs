using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPDetailVMs
{
    public partial class WIPDetailTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<WIPDetail>(x => x.Color);
        [Display(Name = "_LotNo")]
        public ExcelPropety LotNo_Excel = ExcelPropety.CreateProperty<WIPDetail>(x => x.LotNo);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<WIPDetail>(x => x.Pcs);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<WIPDetail>(x => x.Meters);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<WIPDetail>(x => x.Weight);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<WIPDetail>(x => x.Remark);

	    protected override void InitVM()
        {
        }

    }

    public class WIPDetailImportVM : BaseImportVM<WIPDetailTemplateVM, WIPDetail>
    {

    }

}
