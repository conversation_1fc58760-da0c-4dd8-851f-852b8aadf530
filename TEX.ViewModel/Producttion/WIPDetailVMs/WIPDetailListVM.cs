using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using NPOI.OpenXmlFormats.Wordprocessing;
using Elsa.Persistence.Specifications;
using TEX.ViewModel.Producttion.WIPVMs;


namespace TEX.ViewModel.Producttion.WIPDetailVMs
{
    public partial class WIPDetailListVM : BasePagedListVM<WIPDetail_View, WIPDetailSearcher>
    {

        protected override IEnumerable<IGridColumn<WIPDetail_View>> InitGridHeader()
        {
            return new List<GridColumn<WIPDetail_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.WIPNo),
                this.MakeGridHeader(x => x.OrderNo),
                this.MakeGridHeader(x => x.ProductName),
                this.MakeGridHeader(x => x.Color),
                this.MakeGridHeader(x => x.LotNo),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Shipper),
                this.MakeGridHeader(x => x.Receiver),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<WIPDetail_View> GetSearchQuery()
        {
           // var q = from wips in DC.Set<WIP>()
           //         join wipdetails in DC.Set<WIPDetail>()
           //         on wips.ID equals wipdetails.WIPId
           //         select new
           //         {
           //             wips.POrderId,
           //             wips.POrder.OrderNo,
           //             wips.Procedure,
           //             wips.ShipperId,
           //             ShipperName = wips.Shipper.CompanyName,
           //             wips.ReceiverId,
           //             ReceiverName = wips.Receiver.CompanyName,
           //             wipdetails.Color,
           //             wipdetails.LotNo,
           //             wipdetails.Pcs,
           //             wipdetails.Meters
           //         };

           // var q2 = (from s in q
           //               //join r in q on new { s.POrderId, s.ShipperId, s.LotNo, s.Color } equals new { r.POrderId, r.ReceiverId, r.LotNo, r.Color }
           //           join r in q on
           //           //s.ShipperId equals r.ReceiverId
           //           new { s.POrderId, s.Color, s.LotNo, ShipperId = s.ReceiverId } equals new { r.POrderId, r.Color, r.LotNo, r.ShipperId }
           //           select new
           //           {
           //               s.Procedure,
           //               s.ShipperName,
           //               s.ReceiverName,
           //               s.LotNo,
           //               s.Color,
           //               ShipperPcs = s.Pcs,
           //               ShipperMeters = s.Meters,
           //               ReceiverPcs = r.Pcs,
           //               ReceiverMeters = r.Meters,
           //               s.POrderId,
           //               s.ShipperId,
           //           })
           //          .ToList();

           // var q3 = q2;
           // var outbound = (from wips in DC.Set<WIP>()
           //                 join wipdetails in DC.Set<WIPDetail>() on wips.ID equals wipdetails.WIPId
           //                 select new
           //                 {
           //                     wips.POrderId,
           //                     wips.ShipperId,
           //                     wips.ReceiverId,
           //                     wipdetails.Color,
           //                     wipdetails.LotNo,
           //                     wipdetails.Pcs,
           //                     wipdetails.Meters
           //                 })
           //.GroupBy(x => new { x.POrderId, x.Color, x.LotNo, x.ReceiverId, x.ShipperId })
           //.Select(g => new
           //{
           //    g.Key.POrderId,
           //    g.Key.ReceiverId,
           //    g.Key.ShipperId,
           //    g.Key.Color,
           //    g.Key.LotNo,
           //    Pcs = g.Sum(x => x.Pcs),
           //    Meters = g.Sum(x => x.Meters)
           //});
           // var r1 = from o in outbound
           //          group o by new { o.POrderId, o.Color, o.LotNo, o.ShipperId } into g
           //          select new
           //          {
           //              g.Key.POrderId,
           //              g.Key.ShipperId,
           //              g.Key.Color,
           //              g.Key.LotNo,
           //              Pcs = g.Sum(x => x.Pcs),
           //              ShipperMeters = g.Sum(x => x.Meters)
           //          };
           // var r2 = from o in outbound
           //          group o by new { o.POrderId, o.Color, o.LotNo, o.ReceiverId } into g
           //          select new
           //          {
           //              g.Key.POrderId,
           //              g.Key.ReceiverId,
           //              g.Key.Color,
           //              g.Key.LotNo,
           //              Pcs = g.Sum(x => x.Pcs),
           //              ReceiverMeters = g.Sum(x => x.Meters)
           //          };





            var query = DC.Set<WIPDetail>()
                .CheckContain(Searcher.Color, x => x.Color)
                .CheckContain(Searcher.LotNo, x => x.LotNo)
                .Select(x => new WIPDetail_View
                {
                    ID = x.ID,
                    CreateDate=x.WIP.CreateDate,
                    WIPNo=x.WIP.BillNo,
                    Shipper=x.WIP.Shipper.CompanyName,
                    Receiver=x.WIP.Receiver.CompanyName,
                    OrderNo =x.OrderDetail.PurchaseOrder.OrderNo,
                    ProductName = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    Color = x.OrderDetail.Color,
                    LotNo = x.LotNo,
                    Pcs = x.Pcs,
                    Meters = x.Meters,
                    Weight = x.Weight,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class WIPDetail_View : WIPDetail
    {
        [Display(Name = "_ProductName")]
        public String ProductName { get; set; }
        [Display(Name = "_OrderNo")]
        public String OrderNo { get; set; }
        [Display(Name = "_WIPNo")]
        public String WIPNo { get; set; }
        [Display(Name = "_CreateDate")]
        public DateTime CreateDate { get; set; }
        [Display(Name = "_Shipper")]
        public String Shipper { get; set; }
        [Display(Name = "_Receiver")]
        public String Receiver { get; set; }
    }
}
