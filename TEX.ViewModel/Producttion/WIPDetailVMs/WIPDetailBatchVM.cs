using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.WIPDetailVMs
{
    public partial class WIPDetailBatchVM : BaseBatchVM<WIPDetail, WIPDetail_BatchEdit>
    {
        public WIPDetailBatchVM()
        {
            ListVM = new WIPDetailListVM();
            LinkedVM = new WIPDetail_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class WIPDetail_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
