using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.LotAllocateVMs
{
    public partial class LotAllocateSearcher : BaseSearcher
    {
        [Display(Name = "_PurchaseOrder")]
        public Guid? OrderId { get; set; }
        public Guid? PlanDetailId { get; set; }
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }

        [Display(Name = "_DyeingFactory")]
        public Guid? DyeingFacId { get; set; }
        [Display(Name = "_ProductName")]
        public String ProductName { get; set; }
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_ColorCode")]
        public String ColorCode { get; set; }
        [Display(Name = "_LotNo")]
        public String LotNo { get; set; }

        protected override void InitVM()
        {
        }

    }
}
