using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.LotAllocateVMs
{
    public partial class LotAllocateBatchVM : BaseBatchVM<LotAllocate, LotAllocate_BatchEdit>
    {
        public LotAllocateBatchVM()
        {
            ListVM = new LotAllocateListVM();
            LinkedVM = new LotAllocate_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class LotAllocate_BatchEdit : BaseVM
    {
        [Display(Name = "_CreateDate")]
        public DateTime? CreateDate { get; set; }
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_ColorCode")]
        public String ColorCode { get; set; }
        [Display(Name = "_LotNo")]
        public String LotNo { get; set; }

        protected override void InitVM()
        {
        }

    }

}
