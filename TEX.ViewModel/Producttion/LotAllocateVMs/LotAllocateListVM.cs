using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using Elsa.Persistence.Specifications;


namespace TEX.ViewModel.Producttion.LotAllocateVMs
{
    public partial class LotAllocateListVM : BasePagedListVM<LotAllocate_View, LotAllocateSearcher>
    {

        protected override IEnumerable<IGridColumn<LotAllocate_View>> InitGridHeader()
        {
            return new List<GridColumn<LotAllocate_View>>{
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.Product_View),
                this.MakeGridHeader(x => x.DyeingProductName),
                this.MakeGridHeader(x => x.Color),
                this.MakeGridHeader(x => x.ColorId),
                this.MakeGridHeader(x => x.ColorCode),
                this.MakeGridHeader(x => x.LotNo),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<LotAllocate_View> GetSearchQuery()
        {
            var query = DC.Set<LotAllocate>()
                .CheckEqual(Searcher.OrderId, x => x.PlanDetail.DyeingPlan.POrderId)
                .CheckEqual(Searcher.PlanDetailId, x => x.PlanDetailId)
                .CheckEqual(Searcher.DyeingFacId, x => x.PlanDetail.DyeingPlan.FinishingFactory.ID)
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckContain(Searcher.ProductName, x => x.ProductName)
                .CheckContain(Searcher.Color, x => x.Color)
                .CheckContain(Searcher.ColorCode, x => x.ColorCode)
                .CheckContain(Searcher.LotNo, x => x.LotNo)
                .Select(x => new LotAllocate_View
                {
                    ID = x.ID,
                    OrderId = x.PlanDetail.DyeingPlan.POrderId,
                    DyeingFac=x.PlanDetail.DyeingPlan.FinishingFactory.CompanyName,
                    OrderNo_view = x.PlanDetail.DyeingPlan.POrder.OrderNo,
                    CreateDate = x.CreateDate,
                    Product_View = x.PlanDetail.DyeingPlan.POrder.Product.ProductName,
                    DyeingProductName = x.PlanDetail.DyeingPlan.POrder.DyeingProductName,
                    ProductName = x.PlanDetail.DyeingPlan.POrder.Product.ProductName,
                    Color = x.PlanDetail.Color,
                    ColorId = x.PlanDetail.OrderDetailId,
                    ColorCode = x.ColorCode,
                    LotNo = x.LotNo,
                    Pcs = x.Pcs,
                    Meters = x.Meters,
                    Weight = x.Weight,
                    Remark = x.Remark,
                })
                //.CheckEqual(Searcher.OrderId, x => x.OrderId)
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class LotAllocate_View : LotAllocate
    {
        [Display(Name = "_OrderNo")]
        public string OrderNo_view { get; set; }
        [Display(Name = "_DyeingFactory")]
        public string DyeingFac { get; set; }
        [Display(Name = "_ProductName")]
        public string Product_View { get; set; }
        [Display(Name = "_DyeingProductName")]
        public string DyeingProductName { get; set; }
        public Guid OrderId { get; set; }
        public Guid ColorId { get; set; }//默认赋值OrderDetailId
    }
}
