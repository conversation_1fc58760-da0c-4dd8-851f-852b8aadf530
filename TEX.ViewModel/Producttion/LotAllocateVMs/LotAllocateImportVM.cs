using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Producttion.LotAllocateVMs
{
    public partial class LotAllocateTemplateVM : BaseTemplateVM
    {
        public ExcelPropety PlanDetail_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.PlanDetailId);
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.CreateDate);
        [Display(Name = "_ProductName")]
        public ExcelPropety ProductName_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.ProductName);
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.Color);
        [Display(Name = "_ColorCode")]
        public ExcelPropety ColorCode_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.ColorCode);
        [Display(Name = "_LotNo")]
        public ExcelPropety LotNo_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.LotNo);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.Pcs);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.Meters);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.Weight);
        //[Display(Name = "_DyeingProcess")]
        //public ExcelPropety DyeingProcess_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.DyeingProcess);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<LotAllocate>(x => x.Remark);

	    protected override void InitVM()
        {
            PlanDetail_Excel.DataType = ColumnDataType.ComboBox;
            PlanDetail_Excel.ListItems = DC.Set<PlanDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class LotAllocateImportVM : BaseImportVM<LotAllocateTemplateVM, LotAllocate>
    {

    }

}
