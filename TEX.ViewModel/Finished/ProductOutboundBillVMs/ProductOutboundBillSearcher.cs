using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundBillVMs
{
    public partial class ProductOutboundBillSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        [Display(Name = "_BillNo")]
        public String BillNo { get; set; }
        [Display(Name = "_Customer")]
        public Guid? CustomerId { get; set; }
        [Display(Name = "_Receiver")]
        public Guid? ReceiverId { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public String CustomerOrderNo { get; set; }
        [Display(Name = "_Remark")]
        public String Remark { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; }

        protected override void InitVM()
        {
        }

    }
}
