using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;


namespace TEX.ViewModel.Finished.ProductOutboundBillVMs
{
    public partial class ProductOutboundBillBatchVM : BaseBatchVM<ProductOutboundBill, ProductOutboundBill_BatchEdit>
    {
        public ProductOutboundBillBatchVM()
        {
            ListVM = new ProductOutboundBillListVM();
            LinkedVM = new ProductOutboundBill_BatchEdit();
        }


        /// <summary>
        /// 重写删除方法,同时软删除关联的Lot和Roll并更新库存
        /// {{ AURA-X: Modify - 增强DoBatchDelete方法，参考EditWithLotAndRoll实现三级联动软删除和库存更新. Confirmed via 寸止 }}
        /// </summary>
        /// <returns></returns>
        public override bool DoBatchDelete()
        {
            // {{ AURA-X: Add - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            var isInMemoryDatabase = base.DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : base.DC.BeginTransaction();

            try
            {
                var userCode = base.LoginUserInfo?.ITCode;

                foreach (var id in this.Ids)
                {
                    var billId = Guid.Parse(id);

                    // 1. 获取当前入库单的详细数据（包含Lot信息，用于库存更新）
                    var entity = DC.Set<ProductOutboundBill>()
                        .Include(x => x.LotList)
                        .AsNoTracking()
                        .Where(x => x.ID == billId)
                        .SingleOrDefault();

                    if (entity == null) continue;

                    // 2. 计算库存变更信息
                    var currentLotList = entity.LotList.GroupBy(y => y.OrderDetailId).Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                    // 3. 更新库存（增加库存，因为取消出库）
                    // {{ AURA-X: Fix - 使用Distinct去重后再ToDictionary，避免重复键异常同时保持性能. Confirmed via 寸止 }}
                    // {{ AURA-X: Fix - 出库删除时应该增加库存，而不是减少库存. Confirmed via 寸止 }}
                    var uniqueOrderDetailIds = currentLotList.Select(r => r.OrderDetailId).Distinct().ToList();
                    var productStocks = base.DC.Set<ProductStock>()
                        .Where(s => uniqueOrderDetailIds.Contains(s.OrderDetailId))
                        .ToDictionary(s => s.OrderDetailId); // 现在不会有重复键了

                    foreach (var r in currentLotList)
                    {
                        if (productStocks.TryGetValue(r.OrderDetailId, out var summary))
                        {
                            summary.TotalPcs += r.TotalPcs;
                            summary.TotalWeight += r.TotalWeight;
                            summary.TotalMeters += r.TotalMeters;
                            summary.TotalYards += r.TotalYards;
                            summary.UpdateTime = DateTime.Now;
                            summary.UpdateBy = userCode;
                        }
                        else
                        {
                            // {{ AURA-X: Add - 如果库存记录不存在，创建新的库存记录 }}
                            var newStock = new ProductStock
                            {
                                OrderDetailId = r.OrderDetailId,
                                TotalPcs = r.TotalPcs,
                                TotalWeight = r.TotalWeight,
                                TotalMeters = r.TotalMeters,
                                TotalYards = r.TotalYards,
                                CreateTime = DateTime.Now,
                                CreateBy = userCode,
                                TenantCode = base.LoginUserInfo?.CurrentTenant
                            };
                            base.DC.Set<ProductStock>().Add(newStock);
                        }
                    }

                    // 4. 软删除关联的Lot和Roll
                    // {{ AURA-X: Add - 参考ProductOutboundBillBatchVM和EditWithLotAndRoll的软删除实现模式 }}

                    // 4.1 获取需要软删除的Lot ID列表
                    var lotIds = base.DC.Set<ProductOutboundLot>()
                        .AsNoTracking()
                        .Where(x => x.OutboundBillId == billId && x.IsValid)
                        .Select(x => x.ID)
                        .ToList();

                    // 4.2 获取需要软删除的Roll ID列表
                    var rollIds = base.DC.Set<ProductOutboundRoll>()
                        .AsNoTracking()
                        .Where(x => lotIds.Contains(x.LotId) && x.IsValid)
                        .Select(x => x.ID)
                        .ToList();

                    // 4.3 批量软删除Lots
                    var lotsToUpdate = base.DC.Set<ProductOutboundLot>()
                        .Where(x => lotIds.Contains(x.ID))
                        .ToList();

                    foreach (var lot in lotsToUpdate)
                    {
                        lot.IsValid = false;
                        lot.UpdateTime = DateTime.Now;
                        lot.UpdateBy = userCode;
                    }

                    // 4.4 批量软删除Rolls
                    var rollsToUpdate = base.DC.Set<ProductOutboundRoll>()
                        .Where(x => rollIds.Contains(x.ID))
                        .ToList();

                    foreach (var roll in rollsToUpdate)
                    {
                        roll.IsValid = false;
                        roll.UpdateTime = DateTime.Now;
                        roll.UpdateBy = userCode;
                    }
                }

                // 5. 直接软删除入库单本身，避免实体跟踪冲突
                // {{ AURA-X: Modify - 直接实现Bill软删除，避免base.DoBatchDelete()的实体跟踪冲突问题. Confirmed via 寸止 }}
                var billsToUpdate = base.DC.Set<ProductOutboundBill>()
                    .Where(x => this.Ids.Contains(x.ID.ToString()))
                    .ToList();

                foreach (var bill in billsToUpdate)
                {
                    bill.IsValid = false;
                    bill.UpdateTime = DateTime.Now;
                    bill.UpdateBy = userCode;
                }

                // 6. 统一保存所有更改（库存更新 + Lot软删除 + Roll软删除 + Bill软删除）
                base.DC.SaveChanges();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Commit();
                }
                return true;
            }
            catch (Exception)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                throw;
            }
        }
    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class ProductOutboundBill_BatchEdit : BaseVM
    {
        public Guid? CustomerId { get; set; }
        public Guid? ReceiverId { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public String CustomerOrderNo { get; set; }

        protected override void InitVM()
        {
        }



    }

}
