using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;


namespace TEX.ViewModel.Finished.ProductOutboundBillVMs
{
    public partial class ProductOutboundBillListVM : BasePagedListVM<ProductOutboundBill_View, ProductOutboundBillSearcher>
    {

        protected override IEnumerable<IGridColumn<ProductOutboundBill_View>> InitGridHeader()
        {
            return new List<GridColumn<ProductOutboundBill_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.Customer_view),
                this.MakeGridHeader(x => x.Receiver_view),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Yards),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<ProductOutboundBill_View> GetSearchQuery()
        {
            var query = DC.Set<ProductOutboundBill>()
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckContain(Searcher.BillNo, x=>x.BillNo)
                .CheckEqual(Searcher.CustomerId, x=>x.CustomerId)
                .CheckEqual(Searcher.ReceiverId, x=>x.ReceiverId)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .CheckEqual(Searcher.AuditStatus, x=>x.AuditStatus)
                .Select(x => new ProductOutboundBill_View
                {
				    ID = x.ID,
                    CreateDate = x.CreateDate,
                    BillNo = x.BillNo,
                    Customer_view = x.Customer.CompanyName,
                    Receiver_view = x.Receiver.CompanyName,
                    Pcs = x.Pcs,
                    Weight = x.Weight,
                    Meters = x.Meters,
                    Yards = x.Yards,
                    Remark = x.Remark,
                    AuditStatus = x.AuditStatus,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class ProductOutboundBill_View : ProductOutboundBill{
        [Display(Name = "_Customer")]
        public String Customer_view { get; set; }
        [Display(Name = "_Receiver")]
        public String Receiver_view { get; set; }

        //供查询出库及明细用,每缸包含订单号,品名等信息
        public List<ProductOutboundLot_View> Details { get; set; }

    }
}
