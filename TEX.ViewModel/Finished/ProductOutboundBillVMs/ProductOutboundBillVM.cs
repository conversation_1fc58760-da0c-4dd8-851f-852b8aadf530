using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model.Finished;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using System.Reflection;
using WalkingTec.Mvvm.Core.Support.FileHandlers;
using Microsoft.EntityFrameworkCore;
using Elsa.Models;
using Newtonsoft.Json;


namespace TEX.ViewModel.Finished.ProductOutboundBillVMs
{
    public partial class ProductOutboundBillVM : BaseCRUDVM<ProductOutboundBill>
    {

        public ProductOutboundBillVM()
        {
            SetInclude(x => x.Customer);
            SetInclude(x => x.Receiver);
            //SetInclude(x => x.LotList);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {
            //运行LotList没有数据,只创建空Bill
            if (Entity.LotList is null || Entity.LotList.Count == 0)
            {
                base.DoAdd();
                return;
            }
            else
            {
                //Entity.LotList中每一条数据中的RollList的所有TenantCode改为当前用户的TenantCode
                //foreach (var lot in Entity.LotList)
                //{
                //    if (lot.RollList is not null)
                //    {
                //        foreach (var roll in lot.RollList)
                //        {
                //            roll.TenantCode = LoginUserInfo.CurrentTenant;
                //        }
                //    }
                //}

                Entity.LotList
                    .Where(lot => lot.RollList is not null) // 筛选 RollList 不为空的 Lot
                    .SelectMany(lot => lot.RollList)       // 展平所有 RollList
                    .ToList()                              // 转换为列表以便修改
                    .ForEach(roll => {
                        roll.TenantCode = LoginUserInfo.CurrentTenant;
                        roll.UpdateBy = LoginUserInfo.UserId;
                        roll.UpdateTime = DateTime.Now;
                        }); // 批量更新 TenantCode

            }

            using var transaction = base.DC.BeginTransaction();
            try
            {
                // 将入库数据按 OrderDetailId 汇总后更新库存
                var rv = Entity.LotList.GroupBy(x => x.OrderDetailId).Select(x => new
                {
                    OrderDetailId = x.First().OrderDetailId,
                    TotalPcs = x.Sum(y => y.Pcs),
                    TotalMeters = x.Sum(y => y.Meters),
                    TotalWeight = x.Sum(y => y.Weight),
                    TotalYards = x.Sum(y => y.Yards),
                }).ToList();

                // 一次性查询所有 ProductStock 记录
                var productStocks = base.DC.Set<ProductStock>()
                    .Where(s => rv.Select(r => r.OrderDetailId).Contains(s.OrderDetailId))
                    .ToDictionary(s => s.OrderDetailId);

                foreach (var r in rv)
                {
                    if (!productStocks.TryGetValue(r.OrderDetailId, out var summary))
                    {
                        summary = new ProductStock
                        {
                            OrderDetailId = r.OrderDetailId,
                            TotalPcs = -r.TotalPcs,
                            TotalMeters = -r.TotalMeters,
                            TotalWeight = -r.TotalWeight,
                            TotalYards = -r.TotalYards
                        };

                        if (!summary.CreateTime.HasValue)
                        {
                            summary.CreateTime = DateTime.Now;
                        }

                        if (string.IsNullOrEmpty(summary.CreateBy))
                        {
                            summary.CreateBy = base.LoginUserInfo?.ITCode;
                        }
                        base.DC.Set<ProductStock>().Add(summary);
                    }
                    else
                    {
                        summary.TotalPcs -= r.TotalPcs;
                        summary.TotalWeight -= r.TotalWeight;
                        summary.TotalMeters -= r.TotalMeters;
                        summary.TotalYards -= r.TotalYards;
                        if (!summary.CreateTime.HasValue)
                        {
                            summary.CreateTime = DateTime.Now;
                        }
                        if (!summary.UpdateTime.HasValue)
                        {
                            summary.UpdateTime = DateTime.Now;
                        }
                        if (string.IsNullOrEmpty(summary.CreateBy))
                        {
                            summary.CreateBy = base.LoginUserInfo?.ITCode;
                        }
                        if (string.IsNullOrEmpty(summary.UpdateBy))
                        {
                            summary.UpdateBy = base.LoginUserInfo?.ITCode;
                        }
                    }
                }

                base.DC.SaveChanges();
                base.DoAdd();
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }

        }

        /// <summary>
        /// 重写方法,同时更新库存
        /// </summary>
        /// <param name="updateAllFields"></param>
        public override void DoEdit(bool updateAllFields = false)
        {
            using var transaction = DC.BeginTransaction();
            try
            {
                #region 更新库存
                // 获取当前出库单的详细数据
                var currentLotList = new List<StockInfo>();
                if (Entity.LotList is not null)
                {
                    currentLotList = Entity.LotList
                        .GroupBy(x => x.OrderDetailId)
                        .Select(x => new StockInfo
                        {
                            OrderDetailId = x.Key,
                            TotalPcs = x.Sum(y => y.Pcs),
                            TotalMeters = x.Sum(y => y.Meters),
                            TotalWeight = x.Sum(y => y.Weight),
                            TotalYards = x.Sum(y => y.Yards),
                        }).ToList();
                }

                // 获取原始出库单的详细数据
                var originalLotList = DC.Set<ProductOutboundLot>()
                    .Where(x => x.OutboundBillId == Entity.ID && x.IsValid)
                    .GroupBy(x => x.OrderDetailId)
                    .Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                // 计算差异
                var differences = CalculateDifferences(originalLotList, currentLotList);

                // 一次性查询所有 ProductStock 记录
                var productStocks = DC.Set<ProductStock>()
                    .Where(s => differences.Select(r => r.OrderDetailId).Contains(s.OrderDetailId))
                    .ToDictionary(s => s.OrderDetailId);

                // 批量更新库存
                foreach (var diff in differences)
                {
                    if (!productStocks.TryGetValue(diff.OrderDetailId, out var stock))
                    {
                        // 如果不存在库存记录，创建新记录
                        stock = new ProductStock
                        {
                            OrderDetailId = diff.OrderDetailId,
                            TotalPcs = -diff.TotalPcs,
                            TotalMeters = -diff.TotalMeters,
                            TotalWeight = -diff.TotalWeight,
                            TotalYards = -diff.TotalYards,
                            CreateTime = DateTime.Now,
                            CreateBy = LoginUserInfo?.ITCode,
                            TenantCode = LoginUserInfo?.CurrentTenant
                        };
                        DC.Set<ProductStock>().Add(stock);
                    }
                    else
                    {
                        // 更新现有库存记录
                        stock.TotalPcs -= diff.TotalPcs;
                        stock.TotalMeters -= diff.TotalMeters;
                        stock.TotalWeight -= diff.TotalWeight;
                        stock.TotalYards -= diff.TotalYards;
                        stock.UpdateTime = DateTime.Now;
                        stock.UpdateBy = LoginUserInfo?.ITCode;
                    }
                }
                #endregion

                #region 更新出库单
                // 软删除原有的Lots和Rolls
                var existingLots = DC.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .Where(x => x.OutboundBillId == Entity.ID && x.IsValid)
                    .ToList();

                foreach (var lot in existingLots)
                {
                    lot.IsValid = false;
                    lot.UpdateTime = DateTime.Now;
                    lot.UpdateBy = LoginUserInfo?.ITCode;

                    foreach (var roll in lot.RollList.Where(r => r.IsValid))
                    {
                        roll.IsValid = false;
                        roll.UpdateTime = DateTime.Now;
                        roll.UpdateBy = LoginUserInfo?.ITCode;
                    }
                }

                // 添加新的Lots和Rolls
                if (Entity.LotList != null)
                {
                    foreach (var lot in Entity.LotList)
                    {
                        lot.OutboundBillId = Entity.ID;
                        lot.CreateTime = DateTime.Now;
                        lot.CreateBy = LoginUserInfo?.ITCode;
                        lot.IsValid = true;

                        if (lot.RollList != null)
                        {
                            foreach (var roll in lot.RollList)
                            {
                                roll.CreateTime = DateTime.Now;
                                roll.CreateBy = LoginUserInfo?.ITCode;
                                roll.IsValid = true;
                            }
                        }
                    }
                    DC.Set<ProductOutboundLot>().AddRange(Entity.LotList);
                }

                // 更新主表
                DC.UpdateEntity(Entity);
                #endregion

                DC.SaveChanges();
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }

        public override void DoDelete()
        {
            using var transaction = DC.BeginTransaction();
            try
            {
                // 获取当前出库单关联的所有有效LotList数据
                var existingLots = DC.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .Where(x => x.OutboundBillId == Entity.ID && x.IsValid)
                    .ToList();

                // 按OrderDetailId分组汇总数据用于更新库存
                var stockUpdates = existingLots
                    .GroupBy(x => x.OrderDetailId)
                    .Select(x => new
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                // 一次性查询所有相关的ProductStock记录
                var productStocks = DC.Set<ProductStock>()
                    .Where(s => stockUpdates.Select(r => r.OrderDetailId).Contains(s.OrderDetailId))
                    .ToDictionary(s => s.OrderDetailId);

                // 更新库存（加回库存）
                foreach (var update in stockUpdates)
                {
                    if (productStocks.TryGetValue(update.OrderDetailId, out var stock))
                    {
                        stock.TotalPcs += update.TotalPcs;
                        stock.TotalMeters += update.TotalMeters;
                        stock.TotalWeight += update.TotalWeight;
                        stock.TotalYards += update.TotalYards;
                        stock.UpdateTime = DateTime.Now;
                        stock.UpdateBy = LoginUserInfo?.ITCode;
                    }
                }

                // 软删除所有相关的Lots和Rolls
                foreach (var lot in existingLots)
                {
                    lot.IsValid = false;
                    lot.UpdateTime = DateTime.Now;
                    lot.UpdateBy = LoginUserInfo?.ITCode;

                    if (lot.RollList != null)
                    {
                        foreach (var roll in lot.RollList.Where(r => r.IsValid))
                        {
                            roll.IsValid = false;
                            roll.UpdateTime = DateTime.Now;
                            roll.UpdateBy = LoginUserInfo?.ITCode;
                        }
                    }
                }

                DC.SaveChanges();
                base.DoDelete();
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }

        private List<StockInfo> CalculateDifferences(List<StockInfo> originalLotList, List<StockInfo> currentLotList)
        {
            var differences = new List<StockInfo>();

            var originalDict = originalLotList.ToDictionary(x => x.OrderDetailId);
            var currentDict = currentLotList.ToDictionary(x => x.OrderDetailId);

            foreach (var current in currentLotList)
            {
                if (originalDict.TryGetValue(current.OrderDetailId, out StockInfo original))
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = current.OrderDetailId,
                        TotalPcs = current.TotalPcs - original.TotalPcs,
                        TotalMeters = current.TotalMeters - original.TotalMeters,
                        TotalWeight = current.TotalWeight - original.TotalWeight,
                        TotalYards = current.TotalYards - original.TotalYards
                    });
                }
                else
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = current.OrderDetailId,
                        TotalPcs = current.TotalPcs,
                        TotalMeters = current.TotalMeters,
                        TotalWeight = current.TotalWeight,
                        TotalYards = current.TotalYards
                    });
                }
            }

            foreach (var original in originalLotList)
            {
                if (!currentDict.ContainsKey(original.OrderDetailId))
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = original.OrderDetailId,
                        TotalPcs = -original.TotalPcs,
                        TotalMeters = -original.TotalMeters,
                        TotalWeight = -original.TotalWeight,
                        TotalYards = -original.TotalYards
                    });
                }
            }

            return differences;
        }


        private void DoEditPrepare_backup(bool updateAllFields)
        {
            // 如果实体实现了IBasePoco接口，更新UpdateTime和UpdateBy属性
            if (typeof(IBasePoco).IsAssignableFrom(typeof(ProductOutboundBill)))
            {
                IBasePoco obj = Entity as IBasePoco;
                obj.UpdateTime = DateTime.Now;
                obj.UpdateBy = base.LoginUserInfo?.ITCode;
            }

            // 获取实体的所有属性
            List<PropertyInfo> allProperties = typeof(ProductOutboundBill).GetAllProperties();

            // 处理实体的每个属性
            foreach (PropertyInfo item3 in allProperties)
            {
                // 如果属性不是泛型类型，跳过
                if (item3.PropertyType.GenericTypeArguments.Count() <= 0)
                {
                    continue;
                }

                // 获取属性的泛型类型参数
                Type type = item3.PropertyType.GenericTypeArguments.First();

                // 如果属性类型不是TopBasePoco的子类，跳过
                if (!type.IsSubclassOf(typeof(TopBasePoco)))
                {
                    continue;
                }

                // 获取外键名称
                string fkName = base.DC.GetFKName<ProductOutboundBill>(item3.Name);

                // 如果属性是IEnumerable<TopBasePoco>类型，并且有元素
                if (item3.GetValue(Entity) is IEnumerable<TopBasePoco> enumerable && enumerable.Count() > 0)
                {
                    // 获取子类实体的所有属性
                    List<PropertyInfo> allProperties2 = type.GetAllProperties();

                    // 遍历子类列表中的每个子类属性
                    foreach (TopBasePoco item4 in enumerable)
                    {
                        // 如果子类实体实现了IBasePoco接口，更新UpdateTime和UpdateBy属性
                        if (typeof(IBasePoco).IsAssignableFrom(item4.GetType()))
                        {
                            IBasePoco basePoco = item4 as IBasePoco;
                            if (!basePoco.UpdateTime.HasValue)
                            {
                                basePoco.UpdateTime = DateTime.Now;
                            }

                            if (string.IsNullOrEmpty(basePoco.UpdateBy))
                            {
                                basePoco.UpdateBy = base.LoginUserInfo?.ITCode;
                            }
                        }


                        // 遍历子类实体的属性
                        foreach (PropertyInfo item5 in allProperties2)
                        {
                            // 如果属性是TopBasePoco的子类，设置为null
                            if (item5.PropertyType.IsSubclassOf(typeof(TopBasePoco)))
                            {
                                item5.SetValue(item4, null);
                            }

                            // 如果属性是外键属性，设置外键值
                            if (!string.IsNullOrEmpty(fkName) && item5.Name.ToLower() == fkName.ToLower())
                            {
                                try
                                {
                                    item5.SetValue(item4, Entity.GetID());
                                }
                                catch
                                {
                                }
                            }
                        }

                        // 处理孙类
                        var grandChildProperties = item4.GetType().GetProperties()
                            .Where(p => p.PropertyType.IsGenericType
                                && typeof(IEnumerable<TopBasePoco>).IsAssignableFrom(p.PropertyType));

                        foreach (var grandChildProperty in grandChildProperties)
                        {
                            // 如果属性不是泛型类型，跳过
                            if (grandChildProperty.PropertyType.GenericTypeArguments.Count() <= 0)
                            {
                                continue;
                            }

                            // 获取孙类类型
                            var grandChildType = grandChildProperty.PropertyType.GenericTypeArguments.First();
                            if (!grandChildType.IsSubclassOf(typeof(TopBasePoco)))
                                continue;

                            // 获取外键名称
                            string grandChildFkName = base.DC.GetFKName(item4.GetType(), grandChildProperty.Name);

                            if (string.IsNullOrEmpty(grandChildFkName))
                                continue;

                            // 获取孙类实体的查询结果
                            IQueryable<TopBasePoco> grandChildQueryable = base.DC.GetType()
                                .GetMethod("Set", Type.EmptyTypes)
                                .MakeGenericMethod(grandChildType)
                                .Invoke(base.DC, null) as IQueryable<TopBasePoco>;

                            // 构建查询表达式
                            ParameterExpression grandChildParam = Expression.Parameter(grandChildType);
                            Expression grandChildFkExpr = Expression.MakeMemberAccess(grandChildParam, grandChildType.GetSingleProperty(grandChildFkName));
                            Expression grandChildRight = Expression.Constant(item4.GetID(), grandChildFkExpr.Type);
                            Expression grandChildBody = Expression.Equal(grandChildFkExpr, grandChildRight);
                            MethodCallExpression grandChildQuery = Expression.Call(
                                typeof(Queryable),
                                "Where",
                                new Type[1] { grandChildType },
                                grandChildQueryable.Expression,
                                Expression.Lambda(grandChildBody, grandChildParam));

                            // 执行查询并获取结果
                            var existingGrandChildren = (grandChildQueryable.Provider.CreateQuery(grandChildQuery) as IQueryable<TopBasePoco>)
                                .AsNoTracking()
                                .ToList();

                            // 获取当前的孙类列表
                            var currentGrandChildren = grandChildProperty.GetValue(item4) as IEnumerable<TopBasePoco>;
                            if (currentGrandChildren == null)
                                currentGrandChildren = Enumerable.Empty<TopBasePoco>();

                            // 检查孙类实体的差异
                            IEnumerable<TopBasePoco> grandChildToAdd = null;
                            IEnumerable<TopBasePoco> grandChildToRemove = null;
                            Utils.CheckDifference(existingGrandChildren, currentGrandChildren, out grandChildToRemove, out grandChildToAdd);

                            // 处理需要删除的孙类实体
                            foreach (TopBasePoco grandChild in grandChildToRemove)
                            {
                                if (grandChild is IPersistPoco)
                                {
                                    (grandChild as IPersistPoco).IsValid = false;
                                    if (grandChild is IBasePoco basePoco)
                                    {
                                        basePoco.UpdateTime = DateTime.Now;
                                        basePoco.UpdateBy = base.LoginUserInfo?.ITCode;
                                    }
                                    dynamic val = grandChild;
                                    base.DC.UpdateEntity(val);
                                }
                                else
                                {
                                    // 遍历孙类实体的属性，如果属性是TopBasePoco的子类，设置为null
                                    foreach (PropertyInfo prop in grandChildType.GetAllProperties())
                                    {
                                        if (prop.PropertyType.IsSubclassOf(typeof(TopBasePoco)))
                                        {
                                            prop.SetValue(grandChild, null);
                                        }
                                    }
                                    dynamic val = grandChild;
                                    base.DC.DeleteEntity(val);
                                }
                            }

                            // 处理需要添加的孙类实体
                            foreach (TopBasePoco grandChild in grandChildToAdd)
                            {
                                // 设置外键
                                grandChildType.GetProperty(grandChildFkName)?.SetValue(grandChild, item4.GetID());

                                if (grandChild is IBasePoco basePoco)
                                {
                                    if (!basePoco.CreateTime.HasValue)
                                        basePoco.CreateTime = DateTime.Now;
                                    if (string.IsNullOrEmpty(basePoco.CreateBy))
                                        basePoco.CreateBy = base.LoginUserInfo?.ITCode;
                                }

                                if (grandChild is ITenant tenant)
                                    tenant.TenantCode = base.LoginUserInfo?.CurrentTenant;

                                dynamic val = grandChild;
                                base.DC.AddEntity(val);
                            }
                        }
                    }

                    // 获取子类实体的查询结果
                    IQueryable<TopBasePoco> queryable = base.DC.GetType().GetMethod("Set", Type.EmptyTypes).MakeGenericMethod(type)
                        .Invoke(base.DC, null) as IQueryable<TopBasePoco>;

                    // 构建查询表达式
                    ParameterExpression parameterExpression = Expression.Parameter(type);
                    Expression expression = Expression.MakeMemberAccess(parameterExpression, type.GetSingleProperty(fkName));
                    Expression right = Expression.Constant(Entity.GetID(), expression.Type);
                    Expression body = Expression.Equal(expression, right);
                    MethodCallExpression expression2 = Expression.Call(typeof(Queryable), "Where", new Type[1] { type }, queryable.Expression, Expression.Lambda(body, parameterExpression));

                    // 执行查询并获取结果
                    IEnumerable<TopBasePoco> enumerable2 = (queryable.Provider.CreateQuery(expression2) as IQueryable<TopBasePoco>).AsNoTracking().ToList();

                    // 检查子类实体的差异，确定需要添加、删除或更新的子类实体
                    IEnumerable<TopBasePoco> ToAdd = null;
                    IEnumerable<TopBasePoco> ToRemove = null;
                    Utils.CheckDifference(enumerable2, enumerable, out ToRemove, out ToAdd);

                    // 遍历需要删除的子类实体
                    foreach (TopBasePoco item9 in ToRemove)
                    {
                        // 如果子类实体实现了IPersistPoco接口，设置IsValid为false
                        if (typeof(IPersistPoco).IsAssignableFrom(type))
                        {
                            (item9 as IPersistPoco).IsValid = false;

                            // 如果子类实体实现了IBasePoco接口，更新UpdateTime和UpdateBy属性
                            if (typeof(IBasePoco).IsAssignableFrom(type))
                            {
                                (item9 as IBasePoco).UpdateTime = DateTime.Now;
                                (item9 as IBasePoco).UpdateBy = base.LoginUserInfo?.ITCode;
                            }

                            // 更新子类实体
                            dynamic val2 = item9;
                            base.DC.UpdateEntity(val2);
                            continue;
                        }

                        // 遍历子类实体的属性，如果属性是TopBasePoco的子类，设置为null
                        foreach (PropertyInfo item10 in allProperties2)
                        {
                            if (item10.PropertyType.IsSubclassOf(typeof(TopBasePoco)))
                            {
                                item10.SetValue(item9, null);
                            }
                        }

                        // 删除子类实体
                        dynamic val3 = item9;
                        base.DC.DeleteEntity(val3);
                    }

                    // 遍历需要添加的子类实体
                    foreach (TopBasePoco item11 in ToAdd)
                    {
                        // 如果子类实体实现了IBasePoco接口，更新CreateTime和CreateBy属性
                        if (typeof(IBasePoco).IsAssignableFrom(item11.GetType()))
                        {
                            IBasePoco basePoco2 = item11 as IBasePoco;
                            if (!basePoco2.CreateTime.HasValue)
                            {
                                basePoco2.CreateTime = DateTime.Now;
                            }

                            if (string.IsNullOrEmpty(basePoco2.CreateBy))
                            {
                                basePoco2.CreateBy = base.LoginUserInfo?.ITCode;
                            }
                        }

                        // 如果子类实体实现了ITenant接口，设置TenantCode
                        if (typeof(ITenant).IsAssignableFrom(item11.GetType()))
                        {
                            (item11 as ITenant).TenantCode = base.LoginUserInfo?.CurrentTenant;
                        }

                        // 添加子类实体
                        //base.DC.AddEntity(item11);
                        base.DC.Set<ProductOutboundLot>().Add(item11 as ProductOutboundLot);

                    }
                }
                else
                {
                    // 如果属性是IEnumerable<TopBasePoco>类型，但没有元素，并且外键名称不为空
                    if (!(item3.GetValue(Entity) is IEnumerable<TopBasePoco> enumerable3) || enumerable3 == null || enumerable3.Count() != 0 || string.IsNullOrEmpty(fkName))
                    {
                        continue;
                    }

                    // 获取子类实体的所有属性
                    List<PropertyInfo> allProperties3 = type.GetAllProperties();

                    // 获取子类实体的查询结果
                    IQueryable<TopBasePoco> queryable2 = base.DC.GetType().GetMethod("Set", Type.EmptyTypes).MakeGenericMethod(type)
                        .Invoke(base.DC, null) as IQueryable<TopBasePoco>;

                    // 构建查询表达式
                    ParameterExpression parameterExpression2 = Expression.Parameter(type);
                    Expression expression3 = Expression.MakeMemberAccess(parameterExpression2, type.GetSingleProperty(fkName));
                    Expression right2 = Expression.Constant(Entity.GetID(), expression3.Type);
                    Expression body2 = Expression.Equal(expression3, right2);
                    MethodCallExpression expression4 = Expression.Call(typeof(Queryable), "Where", new Type[1] { type }, queryable2.Expression, Expression.Lambda(body2, parameterExpression2));

                    // 执行查询并获取结果
                    foreach (TopBasePoco item12 in (IEnumerable<TopBasePoco>)(queryable2.Provider.CreateQuery(expression4) as IQueryable<TopBasePoco>).AsNoTracking().ToList())
                    {
                        // 如果子类实体实现了IPersistPoco接口，设置IsValid为false
                        if (typeof(IPersistPoco).IsAssignableFrom(type))
                        {
                            (item12 as IPersistPoco).IsValid = false;

                            // 如果子类实体实现了IBasePoco接口，更新UpdateTime和UpdateBy属性
                            if (typeof(IBasePoco).IsAssignableFrom(type))
                            {
                                (item12 as IBasePoco).UpdateTime = DateTime.Now;
                                (item12 as IBasePoco).UpdateBy = base.LoginUserInfo?.ITCode;
                            }

                            // 更新子类实体
                            dynamic val4 = item12;
                            base.DC.UpdateEntity(val4);
                            continue;
                        }

                        // 遍历子类实体的属性，如果属性是TopBasePoco的子类，设置为null
                        foreach (PropertyInfo item13 in allProperties3)
                        {
                            if (item13.PropertyType.IsSubclassOf(typeof(TopBasePoco)))
                            {
                                item13.SetValue(item12, null);
                            }
                        }

                        // 删除子类实体
                        dynamic val5 = item12;
                        base.DC.DeleteEntity(val5);
                    }
                }
            }

            // 如果不是更新所有字段
            if (!updateAllFields)
            {
                // 如果实体是TreePoco类型，处理ParentId
                if (typeof(TreePoco).IsAssignableFrom(typeof(ProductOutboundBill)))
                {
                    object iD = Entity.GetID();
                    object parentID = Entity.GetParentID();
                    if (iD != null && parentID != null && iD.ToString() == parentID.ToString())
                    {
                        string text4 = base.FC.Keys.Where((string x) => x.ToLower() == "entity.parentid").FirstOrDefault();
                        if (!string.IsNullOrEmpty(text4))
                        {
                            base.FC.Remove(text4);
                        }
                    }
                }

                // 遍历实体的属性，更新需要更新的属性
                foreach (string key2 in base.FC.Keys)
                {
                    string text5 = key2.ToLower();
                    if (!text5.StartsWith(GetParentStr().ToLower() + "entity.") || text5.Contains("["))
                    {
                        continue;
                    }

                    string name = text5.Replace(GetParentStr().ToLower() + "entity.", "");
                    try
                    {
                        PropertyInfo propertyInfo2 = allProperties.Where((PropertyInfo x) => x.Name.ToLower() == name).FirstOrDefault();
                        NotMappedAttribute customAttribute3 = propertyInfo2.GetCustomAttribute<NotMappedAttribute>();
                        CanNotEditAttribute customAttribute4 = propertyInfo2.GetCustomAttribute<CanNotEditAttribute>();
                        if (propertyInfo2.Name != "ID" && customAttribute3 == null && !propertyInfo2.PropertyType.IsList() && customAttribute4 == null)
                        {
                            base.DC.UpdateProperty(Entity, propertyInfo2.Name);
                        }
                    }
                    catch (Exception)
                    {
                    }
                }

                // 如果实体实现了IBasePoco接口，更新UpdateTime和UpdateBy属性
                if (typeof(IBasePoco).IsAssignableFrom(typeof(ProductOutboundBill)))
                {
                    try
                    {
                        base.DC.UpdateProperty(Entity, "UpdateTime");
                        base.DC.UpdateProperty(Entity, "UpdateBy");
                        return;
                    }
                    catch (Exception)
                    {
                        return;
                    }
                }

                return;
            }

            // 如果更新所有字段
            if (typeof(TreePoco).IsAssignableFrom(typeof(ProductOutboundBill)))
            {
                object iD2 = Entity.GetID();
                object parentID2 = Entity.GetParentID();
                if (iD2 != null && parentID2 != null && iD2.ToString() == parentID2.ToString())
                {
                    PropertyInfo singleProperty = Entity.GetType().GetSingleProperty("ParentId");
                    if (singleProperty != null)
                    {
                        try
                        {
                            singleProperty.SetValue(Entity, null);
                        }
                        catch
                        {
                        }
                    }
                }
            }

            // 更新实体
            base.DC.UpdateEntity(Entity);
        }


        /// <summary>
        /// 处理出库单及其子孙类的修改
        /// </summary>
        private void ProcessOutboundBillEdit()
        {
            // 1. 处理主实体 ProductOutboundBill
            if (Entity is IBasePoco basePoco)
            {
                basePoco.UpdateTime = DateTime.Now;
                basePoco.UpdateBy = base.LoginUserInfo?.ITCode;
            }

            DC.UpdateEntity(Entity);//更新ProductOutboundBill本身

            // 2. 处理子类 ProductOutboundLot
            var existingLots = DC.Set<ProductOutboundLot>()
                .Include(x => x.RollList)  // 预加载孙类
                .Where(x => x.OutboundBillId == Entity.ID)
                .AsNoTracking()
                .ToList();

            var currentLots = Entity.LotList ?? new List<ProductOutboundLot>();
            //foreach(var lot in currentLots)
            //{
            //    lot.OutboundBillId = Entity.ID;
            //}

            // 检查差异
            IEnumerable<ProductOutboundLot> lotsToAdd = null;
            IEnumerable<ProductOutboundLot> lotsToRemove = null;
            Utils.CheckDifference(existingLots, currentLots, out lotsToRemove, out lotsToAdd);

            //var settings = new JsonSerializerSettings
            //{
            //    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            //};

            //var existingLotsJson = JsonConvert.SerializeObject(existingLots, settings);

            //var currentLotsJson = JsonConvert.SerializeObject(currentLots, settings);

            //Console.WriteLine(existingLotsJson);
            //Console.WriteLine(currentLotsJson);

            var lotsToUpdate = existingLots
                .Join(currentLots,
                    existing => existing.ID,
                    current => current.ID,
                    (existing, current) => new { existing, current })
                .Where(pair => LotHasChanges(pair.existing, pair.current))
                .Select(pair => pair.existing);
            // 2.1 处理需要删除的Lots
            foreach (var lot in lotsToRemove)
            {
                if (lot is IPersistPoco persistPoco)
                {
                    // 软删除
                    persistPoco.IsValid = false;
                    lot.UpdateTime = DateTime.Now;
                    lot.UpdateBy = base.LoginUserInfo?.ITCode;
                    DC.UpdateEntity(lot);

                    // 同时软删除关联的Rolls
                    if (lot.RollList != null)
                    {
                        foreach (var roll in lot.RollList)
                        {
                            if (roll is IPersistPoco rollPoco)
                            {
                                rollPoco.IsValid = false;
                                roll.UpdateTime = DateTime.Now;
                                roll.UpdateBy = base.LoginUserInfo?.ITCode;
                                DC.UpdateEntity(roll);
                            }
                        }
                    }
                }
                else
                {
                    // 硬删除
                    DC.DeleteEntity(lot);
                }
            }

            // 2.2 处理需要添加的Lots
            foreach (var lot in lotsToAdd)
            {
                lot.OutboundBillId = Entity.ID;
                if (!lot.CreateTime.HasValue)
                    lot.CreateTime = DateTime.Now;
                if (string.IsNullOrEmpty(lot.CreateBy))
                    lot.CreateBy = base.LoginUserInfo?.ITCode;

                if (lot is ITenant lotTenant)
                    lotTenant.TenantCode = base.LoginUserInfo?.CurrentTenant;

                DC.AddEntity(lot);

                // 同时添加关联的Rolls
                if (lot.RollList != null)
                {
                    foreach (var roll in lot.RollList)
                    {
                        roll.LotId = lot.ID;
                        if (!roll.CreateTime.HasValue)
                            roll.CreateTime = DateTime.Now;
                        if (string.IsNullOrEmpty(roll.CreateBy))
                            roll.CreateBy = base.LoginUserInfo?.ITCode;

                        if (roll is ITenant rollTenant)
                            rollTenant.TenantCode = base.LoginUserInfo?.CurrentTenant;

                        DC.AddEntity(roll);
                    }
                }
            }

            // 2.3 处理需要更新的Lots

            foreach (var lot in lotsToUpdate)
            {
                var updatedLot = currentLots.First(x => x.ID == lot.ID);

                // 更新Lot基本信息
                lot.UpdateTime = DateTime.Now;
                lot.UpdateBy = base.LoginUserInfo?.ITCode;
                lot.OrderDetailId = updatedLot.OrderDetailId;
                lot.Pcs = updatedLot.Pcs;
                lot.Meters = updatedLot.Meters;
                lot.Weight = updatedLot.Weight;
                lot.Yards = updatedLot.Yards;

                DC.UpdateEntity(lot);

                // 3. 处理孙类 ProductOutboundRoll
                var existingRolls = DC.Set<ProductOutboundRoll>()
                    .Where(x => x.LotId == lot.ID)
                    .AsNoTracking()
                    .ToList();

                var currentRolls = updatedLot.RollList ?? new List<ProductOutboundRoll>();

                // 检查Roll的差异
                IEnumerable<ProductOutboundRoll> rollsToAdd = null;
                IEnumerable<ProductOutboundRoll> rollsToRemove = null;
                Utils.CheckDifference(existingRolls, currentRolls, out rollsToRemove, out rollsToAdd);

                // 3.1 处理需要删除的Rolls
                foreach (var roll in rollsToRemove)
                {
                    if (roll is IPersistPoco persistPoco)
                    {
                        // 软删除
                        persistPoco.IsValid = false;
                        roll.UpdateTime = DateTime.Now;
                        roll.UpdateBy = base.LoginUserInfo?.ITCode;
                        DC.UpdateEntity(roll);
                    }
                    else
                    {
                        // 硬删除
                        DC.DeleteEntity(roll);
                    }
                }

                // 3.2 处理需要添加的Rolls
                foreach (var roll in rollsToAdd)
                {
                    roll.LotId = lot.ID;
                    if (!roll.CreateTime.HasValue)
                        roll.CreateTime = DateTime.Now;
                    if (string.IsNullOrEmpty(roll.CreateBy))
                        roll.CreateBy = base.LoginUserInfo?.ITCode;

                    if (roll is ITenant rollTenant)
                        rollTenant.TenantCode = base.LoginUserInfo?.CurrentTenant;

                    DC.AddEntity(roll);
                }

                // 3.3 处理需要更新的Rolls
                var rollsToUpdate = existingRolls
                    .Join(currentRolls,
                        existing => existing.ID,
                        current => current.ID,
                        (existing, current) => new { existing, current })
                    .Where(pair => HasChanges(pair.existing, pair.current))
                    .Select(pair => pair.existing);
                foreach (var roll in rollsToUpdate)
                {
                    var updatedRoll = currentRolls.First(x => x.ID == roll.ID);

                    roll.UpdateTime = DateTime.Now;
                    roll.UpdateBy = base.LoginUserInfo?.ITCode;
                    roll.RollNo = updatedRoll.RollNo;
                    roll.LotId = updatedRoll.LotId;
                    roll.Meters = updatedRoll.Meters;
                    roll.Weight = updatedRoll.Weight;
                    roll.Yards = updatedRoll.Yards;

                    DC.UpdateEntity(roll);
                }
            }
        }

        /// <summary>
        /// 检查实体是否有变化
        /// </summary>
        private bool HasChanges<T>(T existing, T updated) where T : TopBasePoco
        {
            var properties = typeof(T).GetProperties()
                .Where(p =>
                    p.Name != "ID" &&
                    p.Name != "ProductOutboundBill" &&
                    p.Name != "ProductOutboundBillId" &&
                    p.Name != "ID" &&
                    p.Name != "LotId" &&
                    p.Name != "CreateTime" &&
                    p.Name != "CreateBy" &&
                    p.Name != "UpdateTime" &&
                    p.Name != "UpdateBy" &&
                    p.Name != "RollList" &&  // 跳过子集合
                    p.Name != "Lot" &&       // 跳过导航属性
                    p.Name != "LotList" &&       // 跳过导航属性
                    p.GetCustomAttribute<NotMappedAttribute>() == null &&
                    p.GetCustomAttribute<CanNotEditAttribute>() == null);

            foreach (var prop in properties)
            {
                var existingValue = prop.GetValue(existing);
                var updatedValue = prop.GetValue(updated);

                if (!Equals(existingValue, updatedValue))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 专门检查 ProductOutboundLot 是否有变化
        /// </summary>
        private bool LotHasChanges(ProductOutboundLot existing, ProductOutboundLot updated)
        {
            if (existing == null || updated == null)
                return false;

            return existing.LotNo != updated.LotNo ||
                   existing.OrderDetailId != updated.OrderDetailId ||
                   existing.Pcs != updated.Pcs ||
                   existing.Weight != updated.Weight ||
                   existing.Meters != updated.Meters ||
                   existing.Yards != updated.Yards;
        }
        /// <summary>
        /// 专门检查 ProductOutboundRoll 是否有变化
        /// </summary>
        private bool RollHasChanges(ProductOutboundRoll existing, ProductOutboundRoll updated)
        {
            if (existing == null || updated == null)
                return false;

            return existing.RollNo != updated.RollNo ||
                   existing.Weight != updated.Weight ||
                   existing.Meters != updated.Meters ||
                   existing.Yards != updated.Yards;
        }

    }
}
// 自定义比较器，用于比较ProductOutboundLot实体
public class ProductOutboundLotComparer : IEqualityComparer<ProductOutboundLot>
{
    public bool Equals(ProductOutboundLot x, ProductOutboundLot y)
    {
        if (x == null && y == null)
            return true;
        if (x == null || y == null)
            return false;
        return x.ID == y.ID;
    }

    public int GetHashCode(ProductOutboundLot obj)
    {
        return obj.ID.GetHashCode();
    }
}

// 自定义比较器，用于比较ProductOutboundRoll实体
public class ProductOutboundRollComparer : IEqualityComparer<ProductOutboundRoll>
{
    public bool Equals(ProductOutboundRoll x, ProductOutboundRoll y)
    {
        if (x == null && y == null)
            return true;
        if (x == null || y == null)
            return false;
        return x.ID == y.ID;
    }

    public int GetHashCode(ProductOutboundRoll obj)
    {
        return obj.ID.GetHashCode();
    }
}