using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundBillVMs
{
    public partial class ProductOutboundBillTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.CreateDate);
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.BillNo);
        public ExcelPropety Customer_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.CustomerId);
        public ExcelPropety Receiver_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.ReceiverId);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.Pcs);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.Yards);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<ProductOutboundBill>(x => x.Remark);

	    protected override void InitVM()
        {
            Customer_Excel.DataType = ColumnDataType.ComboBox;
            Customer_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
            Receiver_Excel.DataType = ColumnDataType.ComboBox;
            Receiver_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class ProductOutboundBillImportVM : BaseImportVM<ProductOutboundBillTemplateVM, ProductOutboundBill>
    {

    }

}
