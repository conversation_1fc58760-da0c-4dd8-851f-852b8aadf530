using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectedRollVMs
{
    public partial class InspectedRollListVM : BasePagedListVM<InspectedRoll_View, InspectedRollSearcher>
    {
        protected override IEnumerable<IGridColumn<InspectedRoll_View>> InitGridHeader()
        {
            return new List<GridColumn<InspectedRoll_View>>{
                this.MakeGridHeader(x => x.OrderNo),
                this.MakeGridHeader(x => x.OrderDetailId),
                this.MakeGridHeader(x => x.ProductName),
                this.MakeGridHeader(x => x.Color_view),
                this.MakeGridHeader(x => x.ColorCode),
                this.MakeGridHeader(x => x.Spec),
                this.MakeGridHeader(x => x.GSM),
                this.MakeGridHeader(x => x.Width),
                this.MakeGridHeader(x => x.InspectedLot),
                this.MakeGridHeader(x => x.RollNo),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.GW),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Yards),
                this.MakeGridHeader(x => x.FreeYards),
                this.MakeGridHeader(x => x.Score),
                this.MakeGridHeader(x => x.TotalScore),
                this.MakeGridHeader(x => x.Grade),
                this.MakeGridHeader(x => x.ProcessName),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.DefectsRecord),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeader(x => x.CreateTime),
                this.MakeGridHeader(x => x.CreateBy),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<InspectedRoll_View> GetSearchQuery()
        {
            var query = DC.Set<InspectedRoll>()
                .CheckBetween(Searcher.CreateTime?.GetStartTime(), Searcher.CreateTime?.GetEndTime(), x => x.CreateTime, includeMax: false)
                .CheckEqual(Searcher.LotNo, x => x.InspectedLot)
                .CheckEqual(Searcher.OrderDetailId, x => x.OrderDetailId)
                .CheckEqual(Searcher.OrderId, x => x.OrderDetail.PurchaseOrder.ID)
                .CheckEqual(Searcher.ProductId, x => x.OrderDetail.PurchaseOrder.ProductId)
                .CheckEqual(Searcher.CustomerId, x => x.OrderDetail.PurchaseOrder.CustomerId)
                .CheckEqual(Searcher.Score, x => x.Score)
                .CheckContain(Searcher.Grade, x => x.Grade)
                .CheckEqual(Searcher.AuditStatus, x => x.AuditStatus)
                .Select(x => new InspectedRoll_View
                {
                    ID = x.ID,
                    OrderDetailId = x.OrderDetailId,
                    CreateBy = x.CreateBy,
                    OrderNo = x.OrderDetail.PurchaseOrder.OrderNo,
                    ProductName = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    Color_view = x.OrderDetail.Color,
                    GSM = x.OrderDetail.PurchaseOrder.Product.GSM,
                    ColorCode = x.OrderDetail.ColorCode,
                    Width = x.OrderDetail.PurchaseOrder.Product.Width,
                    Spec = x.OrderDetail.PurchaseOrder.Product.Spec,
                    InspectedLot = x.InspectedLot,
                    RollNo = x.RollNo,
                    Weight = x.Weight,
                    GW = x.GW,
                    Meters = x.Meters,
                    Yards = x.Yards,
                    FreeYards = x.FreeYards,
                    Score = x.Score,
                    TotalScore = x.TotalScore,
                    Grade = x.Grade,
                    ProcessName = x.ProcessName,
                    AuditStatus = x.AuditStatus,
                    DefectsRecord = x.DefectsRecord,
                    Remark = x.Remark,
                    CreateTime = x.CreateTime
                }).OrderBy(x => x.OrderNo).ThenBy(x => x.ProductName).ThenBy(x => x.Color_view).ThenBy(x => x.InspectedLot).ThenBy(x => x.RollNo);


           
            return query;
        }

    }

    public class InspectedRoll_View : InspectedRoll{
        [Display(Name = "_Color")]
        public String Color_view { get; set; }
        [Display(Name = "_OrderNo")]
        public string OrderNo { get; set; }
        [Display(Name = "_ProductName")]
        public string ProductName { get; set; }
        [Display(Name = "_Spec")]
        public string Spec { get; set; }
        [Display(Name = "_GSM")]
        public int? GSM { get; set; }
        [Display(Name = "_ColorCode")]
        public string ColorCode { get; set; }
        [Display(Name = "_Width")]
        public int? Width { get; set; }
        //[Display(Name = "_CreateDate")]
        //public DateTime? CreateTime { get; set; }
    }

    public class InspectLot
    {
        public string OrderNo { get; set; }
        public string LotNO { get; set; }
        public int RollsCount { get; set; }
        public decimal TotalMeters { get; set; }
        public decimal TotalWeight { get; set; }
    }
}
