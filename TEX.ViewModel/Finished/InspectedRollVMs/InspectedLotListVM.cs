using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using System.Diagnostics.Metrics;
using TEX.ViewModel.Models.OrderDetailVMs;


namespace TEX.ViewModel.Finished.InspectedRollVMs
{
    public partial class InspectedLotListVM : BasePagedListVM<InspectedLot_View, InspectedLotSearcher>
    {

        protected override IEnumerable<IGridColumn<InspectedLot_View>> InitGridHeader()
        {
            return new List<GridColumn<InspectedLot_View>>{
                this.MakeGridHeader(x => x.Customer),
                this.MakeGridHeader(x => x.OrderNo),
                this.MakeGridHeader(x => x.ProductName),
                this.MakeGridHeader(x => x.OrderDetailId),
                this.MakeGridHeader(x => x.Color),
                this.MakeGridHeader(x => x.Spec),
                this.MakeGridHeader(x => x.LotNo),
                this.MakeGridHeader(x => x.RollCount),
                this.MakeGridHeader(x => x.TotalWeight),
                this.MakeGridHeader(x => x.TotalMeters),
                this.MakeGridHeader(x => x.TotalYards),

                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<InspectedLot_View> GetSearchQuery()
        {
            var q = DC.Set<InspectedRoll>().CheckEqual(Searcher.OrderDetailId, x => x.OrderDetailId);

            var groupedQuery = from ir in q
                                   //where ir.IsValid //框架会自动过滤掉无效的记录
                               group ir by new { ir.OrderDetailId, ir.InspectedLot } into g
                               select new
                               {
                                   OrderDetailId = g.Key.OrderDetailId,
                                   InspectedLot = g.Key.InspectedLot,
                                   RollCount = g.Count(),
                                   TotalWeight = g.Sum(ir => ir.Weight),
                                   TotalMeters = g.Sum(ir => ir.Meters),
                                   TotalYards = g.Sum(ir => ir.Yards)
                               };

            var query1 = from g in groupedQuery
                         join od in DC.Set<OrderDetail>() on g.OrderDetailId equals od.ID
                         join po in DC.Set<PurchaseOrder>() on od.PurchaseOrderId equals po.ID
                         join c in DC.Set<Company>() on po.CustomerId equals c.ID
                         join p in DC.Set<Product>() on po.ProductId equals p.ID
                         select new InspectedLot_View
                         {
                             ID = Guid.NewGuid(),
                             OrderDetailId=g.OrderDetailId,
                             Customer = c.CompanyName,
                             OrderNo = po.OrderNo,
                             Color = od.Color,
                             ProductName = p.ProductName,
                             LotNo = g.InspectedLot,
                             RollCount = g.RollCount,
                             TotalWeight = g.TotalWeight,
                             TotalMeters = g.TotalMeters,
                             TotalYards = g.TotalYards
                         };

            var query = query1.OrderBy(x => x.OrderNo).ThenBy(x => x.Color).ThenBy(x => x.LotNo);
            //var query = from product in DC.Set<Product>()
            //            join purchaseOrder in DC.Set<PurchaseOrder>() on product.ID equals purchaseOrder.ProductId
            //            join orderDetail in DC.Set<OrderDetail>() on purchaseOrder.ID equals orderDetail.PurchaseOrderId
            //            join inspectedRoll in DC.Set<InspectedRoll>() on orderDetail.ID equals inspectedRoll.OrderDetailId
            //            group inspectedRoll by new
            //            {
            //                product.ProductName,
            //                purchaseOrder.OrderNo,
            //                orderDetail.Color,
            //                inspectedRoll.InspectedLot
            //            } into g
            //            select new InspectedLot_View
            //            {
            //                ProductName = g.Key.ProductName,
            //                OrderNo = g.Key.OrderNo,
            //                Color = g.Key.Color,
            //                LotNo = g.Key.InspectedLot,
            //                RollCount = g.Count(),
            //                TotalWeight = g.Sum(ir => ir.Weight),
            //                TotalMeters = g.Sum(ir => ir.Meters)
            //            };
            return query;
        }
    }

    public class InspectedLot_View : BasePoco
    {
        [Display(Name = "_Customer")]
        public string Customer { get; set; }
        [Display(Name = "_OrderNo")]
        public string OrderNo { get; set; }
        [Display(Name = "_ProductName")]
        public string ProductName { get; set; }
        [Display(Name = "_Spec")]
        public string Spec { get; set; }

        public Guid OrderDetailId { get; set; }
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_LotNo")]
        [Comment("缸号")]
        public string LotNo { get; set; }
        [Display(Name = "_RollsCount")]
        [Comment("件数")]
        public int RollCount { get; set; }
        [Display(Name = "_Meters")]
        [Comment("米数")]
        public decimal TotalMeters { get; set; }
        [Display(Name = "_Yards")]
        [Comment("码数")]
        public decimal TotalYards { get; set; }
        [Display(Name = "_Weight")]
        [Comment("重量")]
        public decimal TotalWeight { get; set; }
        public List<InspectedRoll> RollList { get; set; }
    }
}
