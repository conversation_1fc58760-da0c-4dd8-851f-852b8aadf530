using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectedRollVMs
{
    public partial class InspectedRollTemplateVM : BaseTemplateVM
    {
        public ExcelPropety OrderDetail_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.OrderDetailId);
        [Display(Name = "_InboundLot")]
        public ExcelPropety InspectedLot_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.InspectedLot);
        [Display(Name = "_RollNo")]
        public ExcelPropety RollNo_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.RollNo);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.Yards);
        [Display(Name = "_Score")]
        public ExcelPropety Score_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.Score);
        [Display(Name = "_Totalscroe")]
        public ExcelPropety Totalscroe_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.TotalScore);
        [Display(Name = "_Grade")]
        public ExcelPropety Grade_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.Grade);
        [Display(Name = "_Processname")]
        public ExcelPropety Processname_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.ProcessName);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.AuditStatus);
        [Display(Name = "_AuditedBy")]
        public ExcelPropety AuditedBy_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.AuditedBy);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<InspectedRoll>(x => x.Remark);

	    protected override void InitVM()
        {
            OrderDetail_Excel.DataType = ColumnDataType.ComboBox;
            OrderDetail_Excel.ListItems = DC.Set<OrderDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class InspectedRollImportVM : BaseImportVM<InspectedRollTemplateVM, InspectedRoll>
    {

    }

}
