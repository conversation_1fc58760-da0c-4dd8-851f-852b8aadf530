using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectedRollVMs
{
    public partial class InspectedRollVM : BaseCRUDVM<InspectedRoll>
    {

        public InspectedRollVM()
        {
            SetInclude(x => x.OrderDetail);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }
        public override DuplicatedInfo<InspectedRoll> SetDuplicatedCheck()
        {
            //检查所属订单明细中的缸号卷号是否重复
            var rv = CreateFieldsInfo(SimpleField(x => x.OrderDetailId), SimpleField(x => x.InspectedLot), SimpleField(x => x.RollNo));
            return rv;
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
