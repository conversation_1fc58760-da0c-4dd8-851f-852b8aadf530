using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectedRollVMs
{
    public partial class InspectedRollBatchVM : BaseBatchVM<InspectedRoll, InspectedRoll_BatchEdit>
    {
        public InspectedRollBatchVM()
        {
            ListVM = new InspectedRollListVM();
            LinkedVM = new InspectedRoll_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class InspectedRoll_BatchEdit : BaseVM
    {
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_InboundLot")]
        public String InspectedLot { get; set; }
        [Display(Name = "_Grade")]
        public String Grade { get; set; }
        [Display(Name = "_Processname")]
        public String Processname { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; }
        protected override void InitVM()
        {
        }

    }

}
