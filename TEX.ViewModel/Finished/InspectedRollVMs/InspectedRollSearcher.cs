using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectedRollVMs
{
    public partial class InspectedRollSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateTime { get; set; }
        [Display(Name = "_Customer")]
        public Guid? CustomerId { get; set; }
        [Display(Name = "_ProductName")]
        public Guid? ProductId { get; set; }
        [Display(Name = "_OrderNo")]
        public Guid? OrderId { get; set; }
        [Display(Name = "_Color")]
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "LotNo")]
        public string LotNo { get; set; }

        [Display(Name = "_Score")]
        public Double? Score { get; set; }
        [Display(Name = "_Grade")]
        public String Grade { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; }

        protected override void InitVM()
        {
        }

    }
    public partial class InspectedLotSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateTime { get; set; }
        [Display(Name = "_Customer")]
        public Guid? CustomerId { get; set; }
        [Display(Name = "_ProductName")]
        public Guid? ProductId { get; set; }
        [Display(Name = "_OrderNo")]
        public Guid? OrderId { get; set; }
        [Display(Name = "_Color")]
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_Grade")]
        public String Grade { get; set; }

    }
}
