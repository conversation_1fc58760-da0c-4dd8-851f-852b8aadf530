using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;


namespace TEX.ViewModel.Finished.ProductInboundLotVMs
{
    public partial class ProductInboundLotVM : BaseCRUDVM<ProductInboundLot>
    {

        public ProductInboundLotVM()
        {
            SetInclude(x => x.InboundBill);
            SetInclude(x => x.OrderDetail);
            SetInclude(x => x.RollList);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {
            if (Entity.RollList is not null && Entity.RollList.Any())
            {
                Entity.Meters = Entity.RollList.Sum(x => x.Meters);
                Entity.Weight = Entity.RollList.Sum(x => x.Weight);
                Entity.Yards = Entity.RollList.Sum(x => x.Yards);
                Entity.Pcs = Entity.RollList.Count;
            }


            var existingStock =  DC.Set<ProductStock>()
                        .FirstOrDefault(s => s.OrderDetailId ==Entity.OrderDetailId);

            if (existingStock != null)
            {
                // {{ AURA-X: Modify - 入库库存调整：原库存加上差异值（差异为正表示增加入库，需要增加库存） }}
                existingStock.TotalPcs += Entity.Pcs;
                existingStock.TotalWeight += Entity.Weight;
                existingStock.TotalMeters += Entity.Meters;
                existingStock.TotalYards += Entity.Yards;
                existingStock.UpdateTime = DateTime.Now;
                existingStock.UpdateBy = Wtm.LoginUserInfo.ITCode;
                // 不需要调用Update，EF Core会自动跟踪变更
            }
            else if (Entity.Pcs > 0 || Entity.Meters > 0 ||
                     Entity.Weight > 0 || Entity.Yards > 0)
            {
                // {{ AURA-X: Add - 创建新的库存记录（仅当差异为正值时） }}
                var newStock = new ProductStock
                {
                    OrderDetailId = Entity.OrderDetailId,
                    TotalPcs = Entity.Pcs,
                    TotalMeters = Entity.Meters,
                    TotalWeight = Entity.Weight,
                    TotalYards = Entity.Yards,
                    CreateTime = DateTime.Now,
                    CreateBy = Wtm.LoginUserInfo.ITCode,
                };
                DC.Set<ProductStock>().Add(newStock);
            }
            base.DoAdd();
            var bill=DC.Set<ProductInboundBill>().Include(x => x.LotList).FirstOrDefault(x => x.ID == Entity.InboundBillId);
            if (bill is not null)
            {
                bill.Pcs = bill.LotList.Sum(x => x.Pcs);
                bill.Meters = bill.LotList.Sum(x => x.Meters);
                bill.Weight = bill.LotList.Sum(x => x.Weight);
                bill.Yards = bill.LotList.Sum(x => x.Yards);
                bill.UpdateTime = DateTime.Now;
                bill.UpdateBy = Wtm.LoginUserInfo.ITCode;
            }

            DC.SaveChanges();

        }

        public override void DoEdit(bool updateAllFields = false)
        {
            //DoEditAsync(updateAllFields).Wait();
            var isInMemoryDatabase = DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : DC.BeginTransaction();

            try
            {
                var lot = Entity;
                var tenantCode = LoginUserInfo?.CurrentTenant;
                var userCode = LoginUserInfo?.ITCode;

                if (lot.RollList is null)
                {
                    lot.RollList = [];
                }

                // 1. 更新现有Lot的统计信息（基于Roll汇总）
                if (lot.RollList.Any())
                {
                    lot.Pcs = lot.RollList.Count;
                    lot.Meters = lot.RollList.Sum(x => x.Meters);
                    lot.Weight = lot.RollList.Sum(x => x.Weight);
                    lot.Yards = lot.RollList.Sum(x => x.Yards);
                }

                // 2. 清理ChangeTracker避免实体跟踪冲突
                var dc = DC as DbContext;
                if (!isInMemoryDatabase)
                {

                    dc.ChangeTracker.Clear();
                }

                // 3. 查询数据库中现有的完整实体（包含Roll关联）
                var existingLot = DC.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lot.ID);

                if (existingLot == null)
                {
                    MSD.AddModelError("", "入库批次不存在");
                    return;
                }

                // 4. 计算原始库存统计（用于后续库存差异计算）
                var originalStockInfo = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = existingLot.Pcs,
                    TotalMeters = existingLot.Meters,
                    TotalWeight = existingLot.Weight,
                    TotalYards = existingLot.Yards
                };

                // 5. 处理Roll的增删改操作
                var newRollList = new List<ProductInboundRoll>();
                var updateRollList = new List<ProductInboundRoll>();
                var deleteRollList = new List<ProductInboundRoll>();

                // 5.1 处理传入的Roll数据
                foreach (var inputRoll in lot.RollList)
                {
                    var existingRoll = existingLot.RollList.FirstOrDefault(x => x.ID == inputRoll.ID);
                    if (existingRoll == null)
                    {
                        // 新增Roll
                        inputRoll.LotId = lot.ID;
                        inputRoll.LotNo = lot.LotNo; // 入库Roll需要设置LotNo
                        inputRoll.CreateTime = DateTime.Now;
                        inputRoll.CreateBy = userCode;
                        inputRoll.TenantCode = tenantCode;
                        inputRoll.IsValid = true;
                        newRollList.Add(inputRoll);
                    }
                    else
                    {
                        // 修改Roll
                        existingRoll.RollNo = inputRoll.RollNo;
                        existingRoll.Weight = inputRoll.Weight;
                        existingRoll.Meters = inputRoll.Meters;
                        existingRoll.Yards = inputRoll.Yards;
                        existingRoll.Grade = inputRoll.Grade;
                        existingRoll.InboundStatus = inputRoll.InboundStatus;
                        existingRoll.Remark = inputRoll.Remark;
                        existingRoll.UpdateTime = DateTime.Now;
                        existingRoll.UpdateBy = userCode;
                        updateRollList.Add(existingRoll);
                    }
                }

                // 5.2 找出需要删除的Roll（软删除）
                deleteRollList = existingLot.RollList.Where(x => lot.RollList.All(y => y.ID != x.ID)).ToList();
                foreach (var roll in deleteRollList)
                {
                    roll.IsValid = false;
                    roll.UpdateTime = DateTime.Now;
                    roll.UpdateBy = userCode;
                }

                // 合并删除列表到更新列表
                updateRollList.AddRange(deleteRollList);

                // 6. 使用EF Core原生批量操作
                // 批量添加新Roll
                if (newRollList.Any())
                {
                    dc.Set<ProductInboundRoll>().AddRange(newRollList);
                }

                // 批量更新Roll（这些实体已经被跟踪，EF Core会自动检测更改）
                // updateRollList中的实体来自existingLot，已经被跟踪，无需调用UpdateRange

                // 更新Lot主表（existingLot已经被跟踪）
                existingLot.Color = lot.Color;
                existingLot.ColorCode = lot.ColorCode;
                existingLot.LotNo = lot.LotNo;
                existingLot.Pcs = existingLot.RollList.Where(x => x.IsValid).Count();
                existingLot.Meters = existingLot.RollList.Where(x => x.IsValid).Sum(x => x.Meters);
                existingLot.Weight = existingLot.RollList.Where(x => x.IsValid).Sum(x => x.Weight);
                existingLot.Yards = existingLot.RollList.Where(x => x.IsValid).Sum(x => x.Yards);
                existingLot.Location = lot.Location;
                existingLot.InboundStatus = lot.InboundStatus;
                existingLot.Remark = lot.Remark;
                existingLot.UpdateTime = DateTime.Now;
                existingLot.UpdateBy = userCode;

                // 7. 计算库存差异并更新库存
                var newStockInfo = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = existingLot.Pcs,
                    TotalMeters = existingLot.Meters,
                    TotalWeight = existingLot.Weight,
                    TotalYards = existingLot.Yards
                };

                // 计算库存差异（新入库 - 原入库 = 差异）
                var stockDifference = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = newStockInfo.TotalPcs - originalStockInfo.TotalPcs,
                    TotalMeters = newStockInfo.TotalMeters - originalStockInfo.TotalMeters,
                    TotalWeight = newStockInfo.TotalWeight - originalStockInfo.TotalWeight,
                    TotalYards = newStockInfo.TotalYards - originalStockInfo.TotalYards
                };

                // 只有存在差异时才更新库存
                if (stockDifference.TotalPcs != 0 || stockDifference.TotalMeters != 0 ||
                    stockDifference.TotalWeight != 0 || stockDifference.TotalYards != 0)
                {
                    var existingStock = dc.Set<ProductStock>()
                        .FirstOrDefault(s => s.OrderDetailId == stockDifference.OrderDetailId);

                    if (existingStock != null)
                    {
                        // {{ AURA-X: Modify - 入库库存调整：原库存加上差异值（差异为正表示增加入库，需要增加库存） }}
                        existingStock.TotalPcs += stockDifference.TotalPcs;
                        existingStock.TotalWeight += stockDifference.TotalWeight;
                        existingStock.TotalMeters += stockDifference.TotalMeters;
                        existingStock.TotalYards += stockDifference.TotalYards;
                        existingStock.UpdateTime = DateTime.Now;
                        existingStock.UpdateBy = userCode;
                        // 不需要调用Update，EF Core会自动跟踪变更
                    }
                    else if (stockDifference.TotalPcs > 0 || stockDifference.TotalMeters > 0 ||
                             stockDifference.TotalWeight > 0 || stockDifference.TotalYards > 0)
                    {
                        // {{ AURA-X: Add - 创建新的库存记录（仅当差异为正值时） }}
                        var newStock = new ProductStock
                        {
                            OrderDetailId = stockDifference.OrderDetailId,
                            TotalPcs = stockDifference.TotalPcs,
                            TotalMeters = stockDifference.TotalMeters,
                            TotalWeight = stockDifference.TotalWeight,
                            TotalYards = stockDifference.TotalYards,
                            CreateTime = DateTime.Now,
                            CreateBy = userCode,
                            TenantCode = tenantCode
                        };
                        dc.Set<ProductStock>().Add(newStock);
                    }
                }


                // 8. 更新关联的Bill统计字段
                var inboundBill = dc.Set<ProductInboundBill>()
                    .FirstOrDefault(x => x.ID == existingLot.InboundBillId);

                if (inboundBill != null)
                {

                    inboundBill.Pcs += stockDifference.TotalPcs;
                    inboundBill.Meters += stockDifference.TotalMeters;
                    inboundBill.Weight += stockDifference.TotalWeight;
                    inboundBill.Yards += stockDifference.TotalYards;
                    inboundBill.UpdateTime = DateTime.Now;
                    inboundBill.UpdateBy = userCode;

                }

                // 统一保存所有更改
                dc.SaveChanges();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Commit();
                }
            }
            catch (Exception ex)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                MSD.AddModelError("", ex.Message);
                throw;
            }

        }
        /// <summary>
        /// 两级联动修改（Lot-Roll）及库存更新 - 参考ProductInboundBillController.EditWithLotAndRoll实现
        /// </summary>
        public override async Task DoEditAsync(bool updateAllFields = false)
        {
            //if (!MSD.IsValid)
            //{
            //    return;
            //}

            // {{ AURA-X: Fix - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
