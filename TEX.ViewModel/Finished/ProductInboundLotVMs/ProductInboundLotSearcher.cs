using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundLotVMs
{
    public partial class ProductInboundLotSearcher : BaseSearcher
    {
        [Display(Name = "_InboundBill")]
        public Guid? InboundBillId { get; set; }
        [Display(Name = "_Color")]
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_LotNo")]
        public String LotNo { get; set; }
        [Display(Name = "_Location")]
        public String Location { get; set; }

        protected override void InitVM()
        {
        }

    }
}
