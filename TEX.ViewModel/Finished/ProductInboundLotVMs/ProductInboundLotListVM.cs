using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using TEX.ViewModel.Models.ProductVMs;


namespace TEX.ViewModel.Finished.ProductInboundLotVMs
{
    public partial class ProductInboundLotListVM : BasePagedListVM<ProductInboundLot_View, ProductInboundLotSearcher>
    {

        protected override IEnumerable<IGridColumn<ProductInboundLot_View>> InitGridHeader()
        {
            return new List<GridColumn<ProductInboundLot_View>>{
                this.MakeGridHeader(x => x.Customer_view),
                this.MakeGridHeader(x => x.CustomerOrderNo_view),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.Product_view),
                this.MakeGridHeader(x => x.OrderDetailId),
                this.MakeGridHeader(x => x.Spec_view),
                this.MakeGridHeader(x => x.BillNo_view),
                this.MakeGridHeader(x => x.Color_view),
                this.MakeGridHeader(x => x.Color),
                this.MakeGridHeader(x => x.ColorCode),
                this.MakeGridHeader(x => x.LotNo),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Yards),
                this.MakeGridHeader(x => x.Location),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<ProductInboundLot_View> GetSearchQuery()
        {
            var query = DC.Set<ProductInboundLot>()
                .CheckEqual(Searcher.InboundBillId, x => x.InboundBillId)
                .CheckEqual(Searcher.OrderDetailId, x => x.OrderDetailId)
                .CheckContain(Searcher.LotNo, x => x.LotNo)
                .CheckContain(Searcher.Location, x => x.Location)
                .Select(x => new ProductInboundLot_View
                {
                    ID = x.ID,
                    Customer_view = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                    OrderDetailId = x.OrderDetailId,
                    CustomerOrderNo_view = x.OrderDetail.PurchaseOrder.CustomerOrderNo,
                    OrderNo_view = x.OrderDetail.PurchaseOrder.OrderNo,
                    Product_view = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    Spec_view = x.OrderDetail.PurchaseOrder.Product.GSM + "GSM - " + x.OrderDetail.PurchaseOrder.Product.Width + "CM",
                    //BillNo_view = x.InboundBill.BillNo,
                    Color_view = x.OrderDetail.Color,
                    Color = x.OrderDetail.Color,
                    ColorCode = x.OrderDetail.ColorCode,
                    LotNo = x.LotNo,
                    Pcs = x.Pcs,
                    Weight = x.Weight,
                    Meters = x.Meters,
                    Yards = x.Yards,
                    Location = x.Location,
                    Remark = x.Remark,
                    CreateTime=x.CreateTime
                })
                .OrderByDescending(x => x.CreateTime);
            var l = query.ToList();
            return query;
        }

    }

    public class ProductInboundLot_View : ProductInboundLot
    {
        [Display(Name = "_Customer")]
        public string Customer_view { get; set; }
        [Display(Name = "_OrderNo")]
        public string OrderNo_view { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public string CustomerOrderNo_view { get; set; }
        [Display(Name = "_Product")]
        public string Product_view { get; set; }
        [Display(Name = "_Spec")]
        public string Spec_view { get; set; }
        [Display(Name = "_BillNo")]
        public string BillNo_view { get; set; }
        [Display(Name = "_Color")]
        public string Color_view { get; set; }

    }
}
