using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundLotVMs
{
    public partial class ProductInboundLotTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_InboundBill")]
        public ExcelPropety InboundBill_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.InboundBillId);
        public ExcelPropety OrderDetail_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.OrderDetailId);
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Color);
        [Display(Name = "_ColorCode")]
        public ExcelPropety ColorCode_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.ColorCode);
        [Display(Name = "_LotNo")]
        public ExcelPropety LotNo_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.LotNo);
        [Display(Name = "_RollsCount")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Pcs);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Yards);
        [Display(Name = "_Location")]
        public ExcelPropety Location_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Location);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<ProductInboundLot>(x => x.Remark);

	    protected override void InitVM()
        {
            InboundBill_Excel.DataType = ColumnDataType.ComboBox;
            InboundBill_Excel.ListItems = DC.Set<ProductInboundBill>().GetSelectListItems(Wtm, y => y.BillNo);
            OrderDetail_Excel.DataType = ColumnDataType.ComboBox;
            OrderDetail_Excel.ListItems = DC.Set<OrderDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class ProductInboundLotImportVM : BaseImportVM<ProductInboundLotTemplateVM, ProductInboundLot>
    {

    }

}
