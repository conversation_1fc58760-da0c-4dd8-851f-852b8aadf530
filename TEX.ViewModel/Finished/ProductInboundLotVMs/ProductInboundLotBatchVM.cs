using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model.Finished;
using Microsoft.EntityFrameworkCore;


namespace TEX.ViewModel.Finished.ProductInboundLotVMs
{
    public partial class ProductInboundLotBatchVM : BaseBatchVM<ProductInboundLot, ProductInboundLot_BatchEdit>
    {
        public ProductInboundLotBatchVM()
        {
            ListVM = new ProductInboundLotListVM();
            LinkedVM = new ProductInboundLot_BatchEdit();
        }

        /// <summary>
        /// 重写删除方法，同时软删除关联的Roll、更新库存和Bill统计字段
        /// {{ AURA-X: Modify - 实现ProductInboundLotBatchVM.DoBatchDelete方法，参考ProductOutboundLotBatchVM实现模式. Confirmed via 寸止 }}
        /// </summary>
        /// <returns></returns>
        public override bool DoBatchDelete()
        {
            // {{ AURA-X: Add - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            var isInMemoryDatabase = base.DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : base.DC.BeginTransaction();

            try
            {
                var userCode = base.LoginUserInfo?.ITCode;

                // 1. 获取要删除的Lot数据（包含Roll信息，用于库存更新和Bill更新）
                var lotIds = this.Ids.Select(id => Guid.Parse(id)).ToList();
                var lotsToDelete = base.DC.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .AsNoTracking()
                    .Where(x => lotIds.Contains(x.ID) && x.IsValid)
                    .ToList();

                if (!lotsToDelete.Any())
                {
                    transaction?.Commit();
                    return true;
                }

                // 2. 计算库存减少信息（按OrderDetailId分组）
                // {{ AURA-X: Add - 入库删除时需要减少库存，与出库删除相反. Confirmed via 寸止 }}
                var stockReduceList = lotsToDelete
                    .GroupBy(x => x.OrderDetailId)
                    .Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                // 3. 更新库存（减少库存，因为取消入库）
                // {{ AURA-X: Fix - 使用Distinct去重后再ToDictionary，避免重复键异常同时保持性能. Confirmed via 寸止 }}
                var uniqueOrderDetailIds = stockReduceList.Select(r => r.OrderDetailId).Distinct().ToList();
                var productStocks = base.DC.Set<ProductStock>()
                    .Where(s => uniqueOrderDetailIds.Contains(s.OrderDetailId))
                    .ToDictionary(s => s.OrderDetailId); // 现在不会有重复键了

                foreach (var stockReduce in stockReduceList)
                {
                    if (productStocks.TryGetValue(stockReduce.OrderDetailId, out var existingStock))
                    {
                        // {{ AURA-X: Modify - 入库删除时减少库存（与入库时增加库存相反） }}
                        existingStock.TotalPcs -= stockReduce.TotalPcs;
                        existingStock.TotalWeight -= stockReduce.TotalWeight;
                        existingStock.TotalMeters -= stockReduce.TotalMeters;
                        existingStock.TotalYards -= stockReduce.TotalYards;
                        existingStock.UpdateTime = DateTime.Now;
                        existingStock.UpdateBy = userCode;
                    }
                    else
                    {
                        // {{ AURA-X: Add - 如果库存记录不存在，创建负数库存记录 }}
                        var newStock = new ProductStock
                        {
                            OrderDetailId = stockReduce.OrderDetailId,
                            TotalPcs = -stockReduce.TotalPcs,
                            TotalWeight = -stockReduce.TotalWeight,
                            TotalMeters = -stockReduce.TotalMeters,
                            TotalYards = -stockReduce.TotalYards,
                            CreateTime = DateTime.Now,
                            CreateBy = userCode,
                            TenantCode = base.LoginUserInfo?.CurrentTenant
                        };
                        base.DC.Set<ProductStock>().Add(newStock);
                    }
                }

                // 4. 软删除关联的Roll
                // {{ AURA-X: Add - 参考ProductOutboundLotBatchVM的软删除实现模式 }}
                var rollIds = lotsToDelete
                    .SelectMany(x => x.RollList ?? new List<ProductInboundRoll>())
                    .Where(x => x.IsValid)
                    .Select(x => x.ID)
                    .ToList();

                if (rollIds.Any())
                {
                    DC.Set<ProductInboundRoll>()
                       .Where(b => rollIds.Contains(b.ID))
                        .ExecuteUpdateAsync(setters => setters
                            .SetProperty(r => r.IsValid, false)
                            .SetProperty(r => r.UpdateTime, DateTime.Now)
                            .SetProperty(r => r.UpdateBy, userCode));
                }

                // 5. 软删除Lot（避免使用base.DoBatchDelete()以防实体跟踪冲突）
                // {{ AURA-X: Modify - 直接实现Lot软删除，避免base.DoBatchDelete()的实体跟踪冲突问题. Confirmed via 寸止 }}
                if (lotIds.Any())
                {
                    DC.Set<ProductInboundLot>()
                       .Where(l => lotIds.Contains(l.ID))
                       .ExecuteUpdateAsync(setters => setters
                           .SetProperty(l => l.IsValid, false)
                           .SetProperty(l => l.UpdateTime, DateTime.Now)
                           .SetProperty(l => l.UpdateBy, userCode));
                }

                // 6. 更新受影响的Bill统计字段
                // {{ AURA-X: Add - 重新计算受影响Bill的统计字段，参考ProductInboundLotVM的Bill更新逻辑 }}
                var affectedBillIds = lotsToDelete.Select(x => x.InboundBillId).Distinct().ToList();
                var billsToUpdate = base.DC.Set<ProductInboundBill>()
                    .Include(x => x.LotList)
                    .Where(x => affectedBillIds.Contains(x.ID))
                    .ToList();

                foreach (var bill in billsToUpdate)
                {
                    // 重新计算Bill的统计字段（基于有效的Lot）
                    var validLots = bill.LotList.Where(x => x.IsValid).ToList();
                    bill.Pcs = validLots.Sum(x => x.Pcs);
                    bill.Meters = validLots.Sum(x => x.Meters);
                    bill.Weight = validLots.Sum(x => x.Weight);
                    bill.Yards = validLots.Sum(x => x.Yards);
                    bill.UpdateTime = DateTime.Now;
                    bill.UpdateBy = userCode;
                }

                // 7. 统一保存所有更改（库存更新 + Roll软删除 + Lot软删除 + Bill统计更新）
                base.DC.SaveChanges();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                transaction?.Commit();
                return true;
            }
            catch (Exception)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                transaction?.Rollback();
                throw;
            }
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class ProductInboundLot_BatchEdit : BaseVM
    {
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_LotNo")]
        public String LotNo { get; set; }
        [Display(Name = "_Location")]
        public String Location { get; set; }

        protected override void InitVM()
        {
        }

    }

}
