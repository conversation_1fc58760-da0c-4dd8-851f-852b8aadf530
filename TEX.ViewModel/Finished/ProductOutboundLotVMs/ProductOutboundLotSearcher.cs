using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundLotVMs
{
    public partial class ProductOutboundLotSearcher : BaseSearcher
    {
        [Display(Name = "_BillNo")]
        public Guid? OutboundBillId { get; set; }
        [Display(Name = "_Color")]
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_ColorCode")]
        public String ColorCode { get; set; }
        [Display(Name = "_LotNo")]
        public String LotNo { get; set; }

        protected override void InitVM()
        {
        }

    }
}
