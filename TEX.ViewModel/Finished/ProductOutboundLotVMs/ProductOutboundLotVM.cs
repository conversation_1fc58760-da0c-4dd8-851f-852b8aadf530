using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;


namespace TEX.ViewModel.Finished.ProductOutboundLotVMs
{
    public partial class ProductOutboundLotVM : BaseCRUDVM<ProductOutboundLot>
    {

        public ProductOutboundLotVM()
        {
            SetInclude(x => x.OutboundBill);
            SetInclude(x => x.OrderDetail);
            SetInclude(x => x.RollList);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {
            //如果RollList为空，则直接调用基类DoAdd

            if (Entity.RollList is not null && Entity.RollList.Any())
            {
                Entity.Meters = Entity.RollList.Sum(x => x.Meters);
                Entity.Weight = Entity.RollList.Sum(x => x.Weight);
                Entity.Yards = Entity.RollList.Sum(x => x.Yards);
                Entity.Pcs = Entity.RollList.Count;
            }
            else {
                Entity.Pcs = 0;
                Entity.Meters = 0;
                Entity.Weight = 0;
                Entity.Yards = 0;
            }


                var existingStock = DC.Set<ProductStock>()
                            .FirstOrDefault(s => s.OrderDetailId == Entity.OrderDetailId);

            if (existingStock != null)
            {
                // {{ AURA-X: Modify - 入库库存调整：原库存加上差异值（差异为正表示增加入库，需要增加库存） }}
                existingStock.TotalPcs -= Entity.Pcs;
                existingStock.TotalWeight -= Entity.Weight;
                existingStock.TotalMeters -= Entity.Meters;
                existingStock.TotalYards -= Entity.Yards;
                existingStock.UpdateTime = DateTime.Now;
                existingStock.UpdateBy = Wtm.LoginUserInfo.ITCode;
                // 不需要调用Update，EF Core会自动跟踪变更
            }
            else if (Entity.Pcs > 0 || Entity.Meters > 0 ||
                     Entity.Weight > 0 || Entity.Yards > 0)
            {
                // {{ AURA-X: Add - 创建新的库存记录（仅当差异为正值时） }}
                var newStock = new ProductStock
                {
                    OrderDetailId = Entity.OrderDetailId,
                    TotalPcs = -Entity.Pcs,
                    TotalMeters = -Entity.Meters,
                    TotalWeight = -Entity.Weight,
                    TotalYards = -Entity.Yards,
                    CreateTime = DateTime.Now,
                    CreateBy = Wtm.LoginUserInfo.ITCode,
                };
                DC.Set<ProductStock>().Add(newStock);
            }
            base.DoAdd();
            var bill = DC.Set<ProductOutboundBill>().Include(x => x.LotList).FirstOrDefault(x => x.ID == Entity.OutboundBillId);
            if (bill is not null)
            {
                bill.Pcs = bill.LotList.Sum(x => x.Pcs);
                bill.Meters = bill.LotList.Sum(x => x.Meters);
                bill.Weight = bill.LotList.Sum(x => x.Weight);
                bill.Yards = bill.LotList.Sum(x => x.Yards);
                bill.UpdateTime = DateTime.Now;
                bill.UpdateBy = Wtm.LoginUserInfo.ITCode;
            }

            DC.SaveChanges();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            var isInMemoryDatabase = DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : DC.BeginTransaction();

            try
            {
                var lot = Entity;
                var tenantCode = LoginUserInfo?.CurrentTenant;
                var userCode = LoginUserInfo?.ITCode;

                if (lot.RollList is null)
                {
                    lot.RollList = [];
                }

                // 1. 更新现有Lot的统计信息（基于Roll汇总）
                if (lot.RollList.Any())
                {
                    lot.Pcs = lot.RollList.Count;
                    lot.Meters = lot.RollList.Sum(x => x.Meters);
                    lot.Weight = lot.RollList.Sum(x => x.Weight);
                    lot.Yards = lot.RollList.Sum(x => x.Yards);
                }

                // 2. 清理ChangeTracker避免实体跟踪冲突
                var dc = DC as DbContext;
                dc.ChangeTracker.Clear();

                // 3. 查询数据库中现有的完整实体（包含Roll关联）
                var existingLot =  DC.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lot.ID);

                if (existingLot == null)
                {
                    MSD.AddModelError("", "出库缸号不存在");
                    return;
                }

                // 4. 计算原始出库统计（用于后续出库差异计算）
                var originalStockInfo = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = existingLot.Pcs,
                    TotalMeters = existingLot.Meters,
                    TotalWeight = existingLot.Weight,
                    TotalYards = existingLot.Yards
                };

                // 5. 处理Roll的增删改操作
                var newRollList = new List<ProductOutboundRoll>();
                var updateRollList = new List<ProductOutboundRoll>();
                var deleteRollList = new List<ProductOutboundRoll>();

                // 5.1 处理传入的Roll数据
                foreach (var inputRoll in lot.RollList)
                {
                    var existingRoll = existingLot.RollList.FirstOrDefault(x => x.ID == inputRoll.ID);
                    if (existingRoll == null)
                    {
                        // 新增Roll
                        inputRoll.LotId = lot.ID;
                        inputRoll.CreateTime = DateTime.Now;
                        inputRoll.CreateBy = userCode;
                        inputRoll.TenantCode = tenantCode;
                        inputRoll.IsValid = true;
                        newRollList.Add(inputRoll);
                    }
                    else
                    {
                        // 修改Roll
                        existingRoll.RollNo = inputRoll.RollNo;
                        existingRoll.Weight = inputRoll.Weight;
                        existingRoll.Meters = inputRoll.Meters;
                        existingRoll.Yards = inputRoll.Yards;
                        existingRoll.Grade = inputRoll.Grade;
                        existingRoll.Remark = inputRoll.Remark;
                        existingRoll.UpdateTime = DateTime.Now;
                        existingRoll.UpdateBy = userCode;
                        updateRollList.Add(existingRoll);
                    }
                }

                

                // 5.2 找出需要删除的Roll（软删除）
                deleteRollList = existingLot.RollList.Where(x => lot.RollList.All(y => y.ID != x.ID)).ToList();


                foreach (var roll in deleteRollList)
                {
                    roll.IsValid = false;
                    roll.UpdateTime = DateTime.Now;
                    roll.UpdateBy = userCode;
                }

                // 合并删除列表到更新列表
                updateRollList.AddRange(deleteRollList);

                // 6. 使用EF Core原生批量操作
                // 批量添加新Roll
                if (newRollList.Any())
                {
                    dc.Set<ProductOutboundRoll>().AddRange(newRollList);
                }

                // 批量更新Roll（这些实体已经被跟踪，EF Core会自动检测更改）
                // updateRollList中的实体来自existingLot，已经被跟踪，无需调用UpdateRange

                // 更新Lot主表（existingLot已经被跟踪）
                existingLot.Color = lot.Color;
                existingLot.ColorCode = lot.ColorCode;
                existingLot.LotNo = lot.LotNo;


                var existingLotRollList = existingLot.RollList.Where(x => x.IsValid).ToList();
                //如果RollList为空,不更新统计字段
                if (existingLotRollList.Any())
                {
                    existingLot.Pcs = existingLotRollList.Count;
                    existingLot.Meters = existingLotRollList.Sum(x => x.Meters);
                    existingLot.Weight = existingLotRollList.Sum(x => x.Weight);
                    existingLot.Yards = existingLotRollList.Sum(x => x.Yards);
                }
                else
                {

                    existingLot.Pcs = 0;
                    existingLot.Meters= 0;
                    existingLot.Weight= 0;
                    existingLot.Yards= 0;
                }


                    existingLot.Remark = lot.Remark;
                existingLot.UpdateTime = DateTime.Now;
                existingLot.UpdateBy = userCode;

                // 7. 计算出库差异并更新库存
                var newStockInfo = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = existingLot.Pcs,
                    TotalMeters = existingLot.Meters,
                    TotalWeight = existingLot.Weight,
                    TotalYards = existingLot.Yards
                };

                // 计算出库差异（原出库 - 新出库 = 差异）
                var stockDifference = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = originalStockInfo.TotalPcs - newStockInfo.TotalPcs,
                    TotalMeters = originalStockInfo.TotalMeters - newStockInfo.TotalMeters,
                    TotalWeight = originalStockInfo.TotalWeight - newStockInfo.TotalWeight,
                    TotalYards = originalStockInfo.TotalYards - newStockInfo.TotalYards
                };

                // 只有存在差异时才更新库存
                if (stockDifference.TotalPcs != 0 || stockDifference.TotalMeters != 0 ||
                    stockDifference.TotalWeight != 0 || stockDifference.TotalYards != 0)
                {
                    var existingStock =  dc.Set<ProductStock>()
                        .FirstOrDefault(s => s.OrderDetailId == stockDifference.OrderDetailId);

                    if (existingStock != null)
                    {
                        // {{ AURA-X: Modify - 出库库存调整：原库存加上差异值（差异为正表示减少出库，需要增加库存） }}
                        existingStock.TotalPcs += stockDifference.TotalPcs;
                        existingStock.TotalWeight += stockDifference.TotalWeight;
                        existingStock.TotalMeters += stockDifference.TotalMeters;
                        existingStock.TotalYards += stockDifference.TotalYards;
                        existingStock.UpdateTime = DateTime.Now;
                        existingStock.UpdateBy = userCode;
                        // 不需要调用Update，EF Core会自动跟踪变更
                    }
                }

                // 8. 更新关联的Bill统计字段
                var outboundBill =  dc.Set<ProductOutboundBill>()
                    .FirstOrDefault(x => x.ID == existingLot.OutboundBillId);

                if (outboundBill != null)
                {
                    // 更新Bill的统计字段
                    outboundBill.Pcs -= stockDifference.TotalPcs;
                    outboundBill.Meters -= stockDifference.TotalMeters;
                    outboundBill.Weight -= stockDifference.TotalWeight;
                    outboundBill.Yards -= stockDifference.TotalYards;
                    outboundBill.UpdateTime = DateTime.Now;
                    outboundBill.UpdateBy = userCode;
                }

                // 统一保存所有更改
                 dc.SaveChanges();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Commit();
                }
            }
            catch (Exception ex)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                MSD.AddModelError("", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 两级联动修改（Lot-Roll）及库存更新 - 参考ProductOutboundBillController.EditWithLotAndRoll实现
        /// </summary>
        public override async Task DoEditAsync(bool updateAllFields = false)
        {
            //if (!MSD.IsValid)
            //{
            //    return;
            //}

            // {{ AURA-X: Fix - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            var isInMemoryDatabase = DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : DC.BeginTransaction();

            try
            {
                var lot = Entity;
                var tenantCode = LoginUserInfo?.CurrentTenant;
                var userCode = LoginUserInfo?.ITCode;

                if (lot.RollList is null)
                {
                    lot.RollList = [];
                }

                // 1. 更新现有Lot的统计信息（基于Roll汇总）
                if (lot.RollList.Any())
                {
                    lot.Pcs = lot.RollList.Count;
                    lot.Meters = lot.RollList.Sum(x => x.Meters);
                    lot.Weight = lot.RollList.Sum(x => x.Weight);
                    lot.Yards = lot.RollList.Sum(x => x.Yards);
                }

                // 2. 清理ChangeTracker避免实体跟踪冲突
                var dc = DC as DbContext;
                dc.ChangeTracker.Clear();

                // 3. 查询数据库中现有的完整实体（包含Roll关联）
                var existingLot = await DC.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefaultAsync(x => x.ID == lot.ID);

                if (existingLot == null)
                {
                    MSD.AddModelError("", "出库缸号不存在");
                    return;
                }

                // 4. 计算原始库存统计（用于后续库存差异计算）
                var originalStockInfo = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = existingLot.Pcs,
                    TotalMeters = existingLot.Meters,
                    TotalWeight = existingLot.Weight,
                    TotalYards = existingLot.Yards
                };

                // 5. 处理Roll的增删改操作
                var newRollList = new List<ProductOutboundRoll>();
                var updateRollList = new List<ProductOutboundRoll>();
                var deleteRollList = new List<ProductOutboundRoll>();

                // 5.1 处理传入的Roll数据
                foreach (var inputRoll in lot.RollList)
                {
                    var existingRoll = existingLot.RollList.FirstOrDefault(x => x.ID == inputRoll.ID);
                    if (existingRoll == null)
                    {
                        // 新增Roll
                        inputRoll.LotId = lot.ID;
                        inputRoll.CreateTime = DateTime.Now;
                        inputRoll.CreateBy = userCode;
                        inputRoll.TenantCode = tenantCode;
                        inputRoll.IsValid = true;
                        newRollList.Add(inputRoll);
                    }
                    else
                    {
                        // 修改Roll
                        existingRoll.RollNo = inputRoll.RollNo;
                        existingRoll.Weight = inputRoll.Weight;
                        existingRoll.Meters = inputRoll.Meters;
                        existingRoll.Yards = inputRoll.Yards;
                        existingRoll.Grade = inputRoll.Grade;
                        existingRoll.Remark = inputRoll.Remark;
                        existingRoll.UpdateTime = DateTime.Now;
                        existingRoll.UpdateBy = userCode;
                        updateRollList.Add(existingRoll);
                    }
                }

                // 5.2 找出需要删除的Roll（软删除）
                deleteRollList = existingLot.RollList.Where(x => lot.RollList.All(y => y.ID != x.ID)).ToList();
                foreach (var roll in deleteRollList)
                {
                    roll.IsValid = false;
                    roll.UpdateTime = DateTime.Now;
                    roll.UpdateBy = userCode;
                }

                // 合并删除列表到更新列表
                updateRollList.AddRange(deleteRollList);

                // 6. 使用EF Core原生批量操作
                // 批量添加新Roll
                if (newRollList.Any())
                {
                    dc.Set<ProductOutboundRoll>().AddRange(newRollList);
                }

                // 批量更新Roll（这些实体已经被跟踪，EF Core会自动检测更改）
                // updateRollList中的实体来自existingLot，已经被跟踪，无需调用UpdateRange

                // 更新Lot主表（existingLot已经被跟踪）
                existingLot.Color = lot.Color;
                existingLot.ColorCode = lot.ColorCode;
                existingLot.LotNo = lot.LotNo;
                existingLot.Pcs = lot.Pcs;
                existingLot.Meters = lot.Meters;
                existingLot.Weight = lot.Weight;
                existingLot.Yards = lot.Yards;
                existingLot.Remark = lot.Remark;
                existingLot.UpdateTime = DateTime.Now;
                existingLot.UpdateBy = userCode;

                // 7. 计算出库差异并更新库存
                var newStockInfo = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = existingLot.Pcs,
                    TotalMeters = existingLot.Meters,
                    TotalWeight = existingLot.Weight,
                    TotalYards = existingLot.Yards
                };

                // 计算出库差异（原出库 - 新出库 = 差异）
                var stockDifference = new StockInfo
                {
                    OrderDetailId = existingLot.OrderDetailId,
                    TotalPcs = originalStockInfo.TotalPcs - newStockInfo.TotalPcs,
                    TotalMeters = originalStockInfo.TotalMeters - newStockInfo.TotalMeters,
                    TotalWeight = originalStockInfo.TotalWeight - newStockInfo.TotalWeight,
                    TotalYards = originalStockInfo.TotalYards - newStockInfo.TotalYards
                };

                // 只有存在差异时才更新库存
                if (stockDifference.TotalPcs != 0 || stockDifference.TotalMeters != 0 ||
                    stockDifference.TotalWeight != 0 || stockDifference.TotalYards != 0)
                {
                    var existingStock = await dc.Set<ProductStock>()
                        .FirstOrDefaultAsync(s => s.OrderDetailId == stockDifference.OrderDetailId);

                    if (existingStock != null)
                    {
                        // {{ AURA-X: Modify - 出库库存调整：原库存加上差异值（差异为正表示减少出库，需要增加库存） }}
                        existingStock.TotalPcs += stockDifference.TotalPcs;
                        existingStock.TotalWeight += stockDifference.TotalWeight;
                        existingStock.TotalMeters += stockDifference.TotalMeters;
                        existingStock.TotalYards += stockDifference.TotalYards;
                        existingStock.UpdateTime = DateTime.Now;
                        existingStock.UpdateBy = userCode;
                        // 不需要调用Update，EF Core会自动跟踪变更
                    }
                }

                // 8. 更新关联的Bill统计字段
                var outboundBill = await dc.Set<ProductOutboundBill>()
                    .FirstOrDefaultAsync(x => x.ID == existingLot.OutboundBillId);

                if (outboundBill != null)
                {
                    // 更新Bill的统计字段
                    outboundBill.Pcs += stockDifference.TotalPcs;
                    outboundBill.Meters += stockDifference.TotalWeight;
                    outboundBill.Weight += stockDifference.TotalWeight;
                    outboundBill.Yards += stockDifference.TotalYards;
                    outboundBill.UpdateTime = DateTime.Now;
                    outboundBill.UpdateBy = userCode;
                }

                // 统一保存所有更改
                await dc.SaveChangesAsync();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Commit();
                }
            }
            catch (Exception ex)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                MSD.AddModelError("", ex.Message);
                throw;
            }
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
