using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundLotVMs
{
    public partial class ProductOutboundLotTemplateVM : BaseTemplateVM
    {
        public ExcelPropety OutboundBill_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.OutboundBillId);
        public ExcelPropety OrderDetail_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.OrderDetailId);
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.Color);
        [Display(Name = "_ColorCode")]
        public ExcelPropety ColorCode_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.ColorCode);
        [Display(Name = "_LotNo")]
        public ExcelPropety LotNo_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.LotNo);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.Pcs);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.Yards);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<ProductOutboundLot>(x => x.Remark);

	    protected override void InitVM()
        {
            OutboundBill_Excel.DataType = ColumnDataType.ComboBox;
            OutboundBill_Excel.ListItems = DC.Set<ProductOutboundBill>().GetSelectListItems(Wtm, y => y.BillNo);
            OrderDetail_Excel.DataType = ColumnDataType.ComboBox;
            OrderDetail_Excel.ListItems = DC.Set<OrderDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class ProductOutboundLotImportVM : BaseImportVM<ProductOutboundLotTemplateVM, ProductOutboundLot>
    {

    }

}
