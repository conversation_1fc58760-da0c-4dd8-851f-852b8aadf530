using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundLotVMs
{
    public partial class ProductOutboundLotListVM : BasePagedListVM<ProductOutboundLot_View, ProductOutboundLotSearcher>
    {

        protected override IEnumerable<IGridColumn<ProductOutboundLot_View>> InitGridHeader()
        {
            return new List<GridColumn<ProductOutboundLot_View>>{
                this.MakeGridHeader(x => x.BillNo_view),
                this.MakeGridHeader(x => x.OutboundBillId),
                this.MakeGridHeader(x => x.OrderDetailId),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.Product_view),
                this.MakeGridHeader(x => x.Customer_view),
                this.MakeGridHeader(x => x.Color_view),
                this.MakeGridHeader(x => x.Color),
                this.MakeGridHeader(x => x.ColorCode),
                this.MakeGridHeader(x => x.LotNo),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Yards),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<ProductOutboundLot_View> GetSearchQuery()
        {
            var query = DC.Set<ProductOutboundLot>()
                .CheckEqual(Searcher.OutboundBillId, x=>x.OutboundBillId)
                .CheckEqual(Searcher.OrderDetailId, x=>x.OrderDetailId)
                .CheckContain(Searcher.Color, x=>x.Color)
                .CheckContain(Searcher.ColorCode, x=>x.ColorCode)
                .CheckContain(Searcher.LotNo, x=>x.LotNo)
                .Select(x => new ProductOutboundLot_View
                {
				    ID = x.ID,
                    BillNo_view = x.OutboundBill.BillNo,
                    OutboundBillId=x.OutboundBillId,
                    OrderDetailId =x.OrderDetailId,
                    Customer_view = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                    OrderNo_view =x.OrderDetail.PurchaseOrder.OrderNo,
                    Product_view = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    Color_view = x.OrderDetail.Color,
                    Color = x.Color,
                    ColorCode = x.ColorCode,
                    LotNo = x.LotNo,
                    Pcs = x.Pcs,
                    Weight = x.Weight,
                    Meters = x.Meters,
                    Yards = x.Yards,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class ProductOutboundLot_View : ProductOutboundLot{
        [Display(Name = "_BillNo")]
        public String BillNo_view { get; set; }
        [Display(Name = "_Customer")]
        public String Customer_view { get; set; }
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_ProductName")]
        public String Product_view { get; set; }
        [Display(Name = "_Color")]
        public String Color_view { get; set; }

        public string Spec_view { get; set; }

    }

    public class LotSummary
    {
        public Guid? OutboundBillId { get; set; }
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_BillNo")]
        public String BillNo_view { get; set; }
        [Display(Name = "_Customer")]
        public String Customer_view { get; set; }
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_ProductName")]
        public String Product_view { get; set; }
        [Display(Name = "_Color")]
        public String Color_view { get; set; }
        [Display(Name = "_Spec")]
        public string Spec_view { get; set; }
        [Display(Name = "_TotalLot")]
        public int TotalLot { get; set; }
        [Display(Name = "_TotalPcs")]
        public int TotalPcs { get; set; }
        [Display(Name = "_TotalWeight")]
        public decimal TotalWeight { get; set; }
        [Display(Name = "_TotalMeters")]
        public decimal TotalMeters { get; set; }
        [Display(Name = "_TotalYards")]
        public decimal TotalYards { get; set; }

    }
}
