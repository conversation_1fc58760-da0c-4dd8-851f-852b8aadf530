using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Elsa.Models;
using TEX.Model.Finished;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;


namespace TEX.ViewModel.Finished.ProductOutboundLotVMs
{
    public partial class ProductOutboundLotBatchVM : BaseBatchVM<ProductOutboundLot, ProductOutboundLot_BatchEdit>
    {
        public ProductOutboundLotBatchVM()
        {
            ListVM = new ProductOutboundLotListVM();
            LinkedVM = new ProductOutboundLot_BatchEdit();
        }

        /// <summary>
        /// 重写删除方法，同时软删除关联的Roll、更新库存和Bill统计字段
        /// {{ AURA-X: Modify - 实现ProductOutboundLotBatchVM.DoBatchDelete方法，参考ProductInboundLotBatchVM实现模式. Confirmed via 寸止 }}
        /// </summary>
        /// <returns></returns>
        public override bool DoBatchDelete()
        {
            // {{ AURA-X: Add - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
            var isInMemoryDatabase = base.DC.Database.ProviderName?.Contains("InMemory") == true;
            var transaction = isInMemoryDatabase ? null : base.DC.BeginTransaction();

            try
            {
                var userCode = base.LoginUserInfo?.ITCode;

                // 1. 获取要删除的Lot数据（包含Roll信息，用于库存恢复和Bill更新）
                var lotIds = this.Ids.Select(id => Guid.Parse(id)).ToList();
                var lotsToDelete = base.DC.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .AsNoTracking()
                    .Where(x => lotIds.Contains(x.ID) && x.IsValid)
                    .ToList();

                if (!lotsToDelete.Any())
                {
                    transaction?.Commit();
                    return true;
                }

                // 2. 计算库存恢复信息（按OrderDetailId分组）
                // {{ AURA-X: Add - 出库删除时需要增加库存，与入库删除相反. Confirmed via 寸止 }}
                var stockRestoreList = lotsToDelete
                    .GroupBy(x => x.OrderDetailId)
                    .Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                // 3. 更新库存（增加库存，因为取消出库）
                // {{ AURA-X: Fix - 使用Distinct去重后再ToDictionary，避免重复键异常同时保持性能. Confirmed via 寸止 }}
                var uniqueOrderDetailIds = stockRestoreList.Select(r => r.OrderDetailId).Distinct().ToList();
                var productStocks = base.DC.Set<ProductStock>()
                    .Where(s => uniqueOrderDetailIds.Contains(s.OrderDetailId))
                    .ToDictionary(s => s.OrderDetailId); // 现在不会有重复键了

                foreach (var stockRestore in stockRestoreList)
                {
                    if (productStocks.TryGetValue(stockRestore.OrderDetailId, out var existingStock))
                    {
                        // {{ AURA-X: Modify - 出库删除时增加库存（与出库时扣减库存相反） }}
                        existingStock.TotalPcs += stockRestore.TotalPcs;
                        existingStock.TotalWeight += stockRestore.TotalWeight;
                        existingStock.TotalMeters += stockRestore.TotalMeters;
                        existingStock.TotalYards += stockRestore.TotalYards;
                        existingStock.UpdateTime = DateTime.Now;
                        existingStock.UpdateBy = userCode;
                    }
                    else
                    {
                        // {{ AURA-X: Add - 如果库存记录不存在，创建新的库存记录 }}
                        var newStock = new ProductStock
                        {
                            OrderDetailId = stockRestore.OrderDetailId,
                            TotalPcs = stockRestore.TotalPcs,
                            TotalWeight = stockRestore.TotalWeight,
                            TotalMeters = stockRestore.TotalMeters,
                            TotalYards = stockRestore.TotalYards,
                            CreateTime = DateTime.Now,
                            CreateBy = userCode,
                            TenantCode = base.LoginUserInfo?.CurrentTenant
                        };
                        base.DC.Set<ProductStock>().Add(newStock);
                    }
                }

                // 4. 软删除关联的Roll
                // {{ AURA-X: Add - 参考ProductInboundBillBatchVM的软删除实现模式 }}
                var rollIds = lotsToDelete
                    .SelectMany(x => x.RollList ?? new List<ProductOutboundRoll>())
                    .Where(x => x.IsValid)
                    .Select(x => x.ID)
                    .ToList();

                if (rollIds.Any())
                {
                    if (isInMemoryDatabase)
                    {
                        var rollsToUpdate = base.DC.Set<ProductOutboundRoll>()
                            .Where(x => rollIds.Contains(x.ID))
                            .ToList();
                        foreach (var roll in rollsToUpdate)
                        {
                            roll.IsValid = false;
                            roll.UpdateTime = DateTime.Now;
                            roll.UpdateBy = userCode;
                        }
                    }
                    else
                    {
                        DC.Set<ProductOutboundRoll>()
                                                   .Where(b => rollIds.Contains(b.ID))
                                                    .ExecuteUpdateAsync(setters => setters
                                                        .SetProperty(r => r.IsValid, false)
                                                        .SetProperty(r => r.UpdateTime, DateTime.Now)
                                                        .SetProperty(r => r.UpdateBy, userCode));
                    }

                }

                // 5. 软删除Lot（避免使用base.DoBatchDelete()以防实体跟踪冲突）
                // {{ AURA-X: Modify - 直接实现Lot软删除，避免base.DoBatchDelete()的实体跟踪冲突问题. Confirmed via 寸止 }}
                
                if (lotIds.Any())
                {
                    if (isInMemoryDatabase)
                    {
                        var lotsToUpdate = base.DC.Set<ProductOutboundLot>()
                            .Where(x => lotIds.Contains(x.ID))
                            .ToList();

                        foreach (var lot in lotsToUpdate)
                        {
                            lot.IsValid = false;
                            lot.UpdateTime = DateTime.Now;
                            lot.UpdateBy = userCode;
                        }
                    }
                    else
                    {
                        DC.Set<ProductOutboundLot>()
                                               .Where(l => lotIds.Contains(l.ID))
                                               .ExecuteUpdateAsync(setters => setters
                                                   .SetProperty(l => l.IsValid, false)
                                                   .SetProperty(l => l.UpdateTime, DateTime.Now)
                                                   .SetProperty(l => l.UpdateBy, userCode));
                    }
                }
                        

                // 6. 更新受影响的Bill统计字段
                // {{ AURA-X: Add - 重新计算受影响Bill的统计字段，参考ProductInboundLotVM的Bill更新逻辑 }}
                var affectedBillIds = lotsToDelete.Select(x => x.OutboundBillId).Distinct().ToList();
                var billsToUpdate = base.DC.Set<ProductOutboundBill>()
                    .Include(x => x.LotList)
                    .Where(x => affectedBillIds.Contains(x.ID))
                    .ToList();

                foreach (var bill in billsToUpdate)
                {
                    // 重新计算Bill的统计字段（基于有效的Lot）
                    var validLots = bill.LotList.Where(x => x.IsValid).ToList();
                    bill.Pcs = validLots.Sum(x => x.Pcs);
                    bill.Meters = validLots.Sum(x => x.Meters);
                    bill.Weight = validLots.Sum(x => x.Weight);
                    bill.Yards = validLots.Sum(x => x.Yards);
                    bill.UpdateTime = DateTime.Now;
                    bill.UpdateBy = userCode;
                }

                // 7. 统一保存所有更改（库存更新 + Roll软删除 + Lot软删除 + Bill统计更新）
                base.DC.SaveChanges();

                // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Commit();
                }
                return true;
            }
            catch (Exception)
            {
                // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                throw;
            }
        }
    }

    /// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class ProductOutboundLot_BatchEdit : BaseVM
    {
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_Color")] public String Color { get; set; }
        [Display(Name = "_ColorCode")] public String ColorCode { get; set; }

        protected override void InitVM()
        {
        }

    }

}
