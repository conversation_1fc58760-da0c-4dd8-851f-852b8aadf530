using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundRollVMs
{
    public partial class ProductInboundRollBatchVM : BaseBatchVM<ProductInboundRoll, ProductInboundRoll_BatchEdit>
    {
        public ProductInboundRollBatchVM()
        {
            ListVM = new ProductInboundRollListVM();
            LinkedVM = new ProductInboundRoll_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class ProductInboundRoll_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
