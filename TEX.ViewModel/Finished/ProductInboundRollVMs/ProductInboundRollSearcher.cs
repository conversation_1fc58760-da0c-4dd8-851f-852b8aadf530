using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundRollVMs
{
    public partial class ProductInboundRollSearcher : BaseSearcher
    {
        [Display(Name = "_Lot")]
        public Guid? LotId { get; set; }
        [Display(Name = "_Grade")]
        public String Grade { get; set; }
        [Display(Name = "_Admin.Remark")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }
}
