using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundRollVMs
{
    public partial class ProductInboundRollTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_Lot")]
        public ExcelPropety Lot_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.LotId);
        [Display(Name = "_RollNo")]
        public ExcelPropety RollNo_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.RollNo);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.Yards);
        [Display(Name = "_Grade")]
        public ExcelPropety Grade_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.Grade);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<ProductInboundRoll>(x => x.Remark);

	    protected override void InitVM()
        {
            Lot_Excel.DataType = ColumnDataType.ComboBox;
            Lot_Excel.ListItems = DC.Set<ProductInboundLot>().GetSelectListItems(Wtm, y => y.LotNo);
        }

    }

    public class ProductInboundRollImportVM : BaseImportVM<ProductInboundRollTemplateVM, ProductInboundRoll>
    {

    }

}
