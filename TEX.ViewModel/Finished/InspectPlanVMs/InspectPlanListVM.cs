using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectPlanVMs
{
    public partial class InspectPlanListVM : BasePagedListVM<InspectPlan_View, InspectPlanSearcher>
    {

        protected override IEnumerable<IGridColumn<InspectPlan_View>> InitGridHeader()
        {
            return new List<GridColumn<InspectPlan_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.Color_view),
                this.MakeGridHeader(x => x.PlanNo),
                this.MakeGridHeader(x => x.Customer),
                this.MakeGridHeader(x => x.OrderNo),
                this.MakeGridHeader(x => x.OrderDetailId),
                this.MakeGridHeader(x => x.ProductName),
                this.MakeGridHeader(x => x.ProductSpec),
                this.MakeGridHeader(x => x.GSM),
                this.MakeGridHeader(x => x.Width),
                this.MakeGridHeader(x => x.ColorCode),
                this.MakeGridHeader(x => x.PlanBatch),
                this.MakeGridHeader(x => x.InspectionStandard),
                this.MakeGridHeader(x => x.PlanQty),
                this.MakeGridHeader(x => x.QtyUnit),
                this.MakeGridHeader(x => x.InspectStatus),
                this.MakeGridHeader(x => x.PlanFinishDate),
                this.MakeGridHeader(x => x.LabelContent),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<InspectPlan_View> GetSearchQuery()
        {
            var query = DC.Set<InspectPlan>()
                .CheckContain(Searcher.OrderNo, x => x.OrderDetail.PurchaseOrder.OrderNo)
                .CheckContain(Searcher.ProductName, x => x.OrderDetail.PurchaseOrder.Product.ProductName)
                .CheckContain(Searcher.Color, x => x.OrderDetail.Color)
                .CheckContain(Searcher.ColorCode, x => x.OrderDetail.ColorCode)
                .CheckEqual(Searcher.InspectionStandard, x => x.InspectionStandard)
                .CheckEqual(Searcher.InspectStatus, x => x.InspectStatus)
                .Select(x => new InspectPlan_View
                {
                    ID = x.ID,
                    CreateDate = x.CreateDate,
                    Color_view = x.OrderDetail.Color,
                    OrderDetailId = x.OrderDetailId,
                    PlanNo = x.PlanNo,
                    Customer = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                    OrderNo = x.OrderDetail.PurchaseOrder.OrderNo,
                    ProductName = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    ProductSpec = x.OrderDetail.PurchaseOrder.Product.Spec,
                    GSM = x.OrderDetail.PurchaseOrder.Product.GSM,
                    Width = x.OrderDetail.PurchaseOrder.Product.Width,
                    ColorCode = x.OrderDetail.ColorCode,
                    PlanBatch = x.PlanBatch,
                    InspectionStandard = x.InspectionStandard,
                    PlanQty = x.PlanQty,
                    QtyUnit = x.QtyUnit,
                    InspectStatus = x.InspectStatus,
                    PlanFinishDate = x.PlanFinishDate,
                    LabelContent = x.LabelContent,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class InspectPlan_View : InspectPlan
    {
        [Display(Name = "_Color")]
        public String Color_view { get; set; }
        [Display(Name = "_Customer")]
        public String Customer { get; set; }
        [Display(Name = "_OrderNo")]
        public string OrderNo { get; set; }
        [Display(Name = "_ProductName")]
        public string ProductName { get; set; }
        [Display(Name = "_ColorCode")]
        public string ColorCode { get; set; }
        [Display(Name = "_ProductSpec")]
        public string ProductSpec { get; set; }
        [Display(Name = "_GSM")]
        public int? GSM { get; set; }
        [Display(Name = "_Width")]
        public int? Width { get; set; }
    }
}
