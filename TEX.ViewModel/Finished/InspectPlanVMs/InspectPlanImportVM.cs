using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectPlanVMs
{
    public partial class InspectPlanTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.CreateDate);
        public ExcelPropety OrderDetail_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.OrderDetailId);
        [Display(Name = "_PlanNo")]
        public ExcelPropety PlanNo_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.PlanNo);
        [Display(Name = "_OrderNo")]
        public ExcelPropety OrderNo_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.OrderDetail.PurchaseOrder.OrderNo);
        [Display(Name = "_ProductName")]
        public ExcelPropety ProductName_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.OrderDetail.PurchaseOrder.Product.ProductName);
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.OrderDetail.Color);
        [Display(Name = "_ColorCode")]
        public ExcelPropety ColorCode_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.OrderDetail.ColorCode);
        [Display(Name = "_Batch")]
        public ExcelPropety PlanBatch_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.PlanBatch);
        [Display(Name = "_InspectionStandard")]
        public ExcelPropety InspectionStandard_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.InspectionStandard);
        [Display(Name = "_PlanQty")]
        public ExcelPropety PlanQty_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.PlanQty);
        [Display(Name = "_Unit")]
        public ExcelPropety QtyUnit_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.QtyUnit);
        [Display(Name = "_InspectStatus")]
        public ExcelPropety InspectStatus_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.InspectStatus);
        [Display(Name = "_PlanFinishedDate")]
        public ExcelPropety PlanFinishDate_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.PlanFinishDate);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<InspectPlan>(x => x.Remark);

	    protected override void InitVM()
        {
            OrderDetail_Excel.DataType = ColumnDataType.ComboBox;
            OrderDetail_Excel.ListItems = DC.Set<OrderDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class InspectPlanImportVM : BaseImportVM<InspectPlanTemplateVM, InspectPlan>
    {

    }

}
