using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.InspectPlanVMs
{
    public partial class InspectPlanBatchVM : BaseBatchVM<InspectPlan, InspectPlan_BatchEdit>
    {
        public InspectPlanBatchVM()
        {
            ListVM = new InspectPlanListVM();
            LinkedVM = new InspectPlan_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class InspectPlan_BatchEdit : BaseVM
    {
        [Display(Name = "_CreateDate")]
        public DateTime? CreateDate { get; set; }
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_ProductName")]
        public String ProductName { get; set; }
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_InspectStatus")]
        public InspectStatusEnum? InspectStatus { get; set; }

        protected override void InitVM()
        {
        }

    }

}
