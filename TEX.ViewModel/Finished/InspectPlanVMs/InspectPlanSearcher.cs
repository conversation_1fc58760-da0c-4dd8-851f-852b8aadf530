using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Finished;


namespace TEX.ViewModel.Finished.InspectPlanVMs
{
    public partial class InspectPlanSearcher : BaseSearcher
    {
        [Display(Name = "_OrderNo")]
        public String OrderNo { get; set; }
        [Display(Name = "_ProductName")]
        public String ProductName { get; set; }
        [Display(Name = "_Color")]
        public String Color { get; set; }
        [Display(Name = "_ColorCode")]
        public String ColorCode { get; set; }
        [Display(Name = "_InspectionStandard")]
        public InspectionStandardEnum? InspectionStandard { get; set; }
        [Display(Name = "_InspectStatus")]
        public InspectStatusEnum? InspectStatus { get; set; }

        protected override void InitVM()
        {
        }

    }
}
