using WalkingTec.Mvvm.Core;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;
using System.Linq;
using Elsa.Models;
using NPOI.SS.Formula.Functions;
using WalkingTec.Mvvm.Core.Extensions;
using System;
using System.Collections.Generic;


namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    public partial class ProductInboundBillVM : BaseCRUDVM<ProductInboundBill>
    {

        public ProductInboundBillVM()
        {
            SetInclude(x => x.POrder);
            SetInclude(x => x.POrder.Product);//包含多层级
            SetInclude(x => x.FinishingFactory);
            SetInclude(x => x.LotList);
            //SetInclude(x => x.LotList.Select(y => y.RollList)); //无效,不能包含第三级实体
        }
        public override DuplicatedInfo<ProductInboundBill> SetDuplicatedCheck()
        {
            var rv = CreateFieldsInfo(SimpleField(x => x.BillNo));
            return rv;
        }
        protected override void InitVM()
        {
        }

        /// <summary>
        /// 重写方法,同时更新库存
        /// </summary>
        public override void DoAdd()
        {

            //TODO:三级实体只有两级有租户号,最后一级没有租户号,导致查询失败

            //运行LotList没有数据,只创建空Bill
            if (Entity.LotList is null || Entity.LotList.Count == 0)
            {
                base.DoAdd();
                return;
            }

            using var transaction = base.DC.BeginTransaction();
            try
            {
                base.DoAdd();

                // 将入库数据按 OrderDetailId 汇总后加入库存
                var rv = Entity.LotList.GroupBy(x => x.OrderDetailId).Select(x => new
                {
                    OrderDetailId = x.First().OrderDetailId,
                    TotalPcs = x.Sum(y => y.Pcs),
                    TotalMeters = x.Sum(y => y.Meters),
                    TotalWeight = x.Sum(y => y.Weight),
                    TotalYards = x.Sum(y => y.Yards),
                }).ToList();

                // 一次性查询所有 ProductStock 记录
                var productStocks = base.DC.Set<ProductStock>()
                    .Where(s => rv.Select(r => r.OrderDetailId).Contains(s.OrderDetailId))
                    .ToDictionary(s => s.OrderDetailId);

                foreach (var r in rv)
                {
                    if (!productStocks.TryGetValue(r.OrderDetailId, out var summary))
                    {
                        summary = new ProductStock
                        {
                            OrderDetailId = r.OrderDetailId,
                            TotalPcs = r.TotalPcs,
                            TotalMeters = r.TotalMeters,
                            TotalWeight = r.TotalWeight,
                            TotalYards = r.TotalYards,
                            TenantCode=DC.TenantCode
                        };

                        if (!summary.CreateTime.HasValue)
                        {
                            summary.CreateTime = DateTime.Now;
                        }

                        if (string.IsNullOrEmpty(summary.CreateBy))
                        {
                            summary.CreateBy = base.LoginUserInfo?.ITCode;
                        }
                        base.DC.Set<ProductStock>().Add(summary);
                    }
                    else
                    {
                        summary.TotalPcs += r.TotalPcs;
                        summary.TotalWeight += r.TotalWeight;
                        summary.TotalMeters += r.TotalMeters;
                        summary.TotalYards += r.TotalYards;
                        summary.TenantCode = DC.TenantCode;
                        if (!summary.UpdateTime.HasValue)
                        {
                            summary.UpdateTime = DateTime.Now;
                        }

                        if (string.IsNullOrEmpty(summary.UpdateBy))
                        {
                            summary.UpdateBy = base.LoginUserInfo?.ITCode;
                        }
                    }
                }

                base.DC.SaveChanges();
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }

        }

        /// <summary>
        /// 重写方法,同时更新库存
        /// </summary>
        /// <param name="updateAllFields"></param>
        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);

            //using var transaction = base.DC.BeginTransaction();
            //try
            //{
            //    // 获取当前入库单的详细数据
            //    var currentLotList = new List<StockInfo>();
            //    if (Entity.LotList is not null)
            //    {
            //        currentLotList = Entity.LotList.GroupBy(x => x.OrderDetailId).Select(x => new StockInfo
            //        {
            //            OrderDetailId = x.Key,
            //            TotalPcs = x.Sum(y => y.Pcs),
            //            TotalMeters = x.Sum(y => y.Meters),
            //            TotalWeight = x.Sum(y => y.Weight),
            //            TotalYards = x.Sum(y => y.Yards),
            //        }).ToList();
            //    }


            //    // 获取原始入库单的详细数据（假设你有一个方法来获取原始数据）
            //    var originalLotList = DC.Set<ProductInboundLot>().Where(x => x.InboundBillId == Entity.ID).GroupBy(x => x.OrderDetailId).Select(x => new StockInfo
            //    {
            //        OrderDetailId = x.Key,
            //        TotalPcs = x.Sum(y => y.Pcs),
            //        TotalMeters = x.Sum(y => y.Meters),
            //        TotalWeight = x.Sum(y => y.Weight),
            //        TotalYards = x.Sum(y => y.Yards),
            //    }).ToList();

            //    // 计算差异
            //    var differences = CalculateDifferences(originalLotList, currentLotList);

            //    // 一次性查询所有 ProductStock 记录
            //    var productStocks = base.DC.Set<ProductStock>()
            //        .Where(s => differences.Select(r => r.OrderDetailId).Contains(s.OrderDetailId))
            //        .ToDictionary(s => s.OrderDetailId);

            //    foreach (var r in differences)
            //    {
            //        if (!productStocks.TryGetValue(r.OrderDetailId, out var summary))
            //        {
            //            summary = new ProductStock
            //            {
            //                OrderDetailId = r.OrderDetailId,
            //                TotalPcs = r.TotalPcs,
            //                TotalMeters = r.TotalMeters,
            //                TotalWeight = r.TotalWeight,
            //                TotalYards = r.TotalYards
            //            };
            //            if (!summary.CreateTime.HasValue)
            //            {
            //                summary.CreateTime = DateTime.Now;
            //            }

            //            if (string.IsNullOrEmpty(summary.CreateBy))
            //            {
            //                summary.CreateBy = base.LoginUserInfo?.ITCode;
            //            }
            //            summary.TenantCode = base.LoginUserInfo?.CurrentTenant;
            //            base.DC.Set<ProductStock>().Add(summary);
            //        }
            //        else
            //        {
            //            summary.TotalPcs += r.TotalPcs;
            //            summary.TotalWeight += r.TotalWeight;
            //            summary.TotalMeters += r.TotalMeters;
            //            summary.TotalYards += r.TotalYards;
            //            if (!summary.UpdateTime.HasValue)
            //            {
            //                summary.UpdateTime = DateTime.Now;
            //            }

            //            if (string.IsNullOrEmpty(summary.UpdateBy))
            //            {
            //                summary.UpdateBy = base.LoginUserInfo?.ITCode;
            //            }
            //        }
            //    }

            //    //base.DC.SaveChanges();//DoEdit中有保存,不用重复
            //    base.DoEdit(updateAllFields);
            //    transaction.Commit();
            //}
            //catch (Exception)
            //{
            //    transaction.Rollback();
            //    throw;
            //}
        }
        private List<StockInfo> CalculateDifferences(List<StockInfo> originalLotList, List<StockInfo> currentLotList)
        {
            var differences = new List<StockInfo>();

            var originalDict = originalLotList.ToDictionary(x => x.OrderDetailId);
            var currentDict = currentLotList.ToDictionary(x => x.OrderDetailId);

            foreach (var current in currentLotList)
            {
                if (originalDict.TryGetValue(current.OrderDetailId, out StockInfo original))
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = current.OrderDetailId,
                        TotalPcs = current.TotalPcs - original.TotalPcs,
                        TotalMeters = current.TotalMeters - original.TotalMeters,
                        TotalWeight = current.TotalWeight - original.TotalWeight,
                        TotalYards = current.TotalYards - original.TotalYards
                    });
                }
                else
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = current.OrderDetailId,
                        TotalPcs = current.TotalPcs,
                        TotalMeters = current.TotalMeters,
                        TotalWeight = current.TotalWeight,
                        TotalYards = current.TotalYards
                    });
                }
            }

            foreach (var original in originalLotList)
            {
                if (!currentDict.ContainsKey(original.OrderDetailId))
                {
                    differences.Add(new StockInfo
                    {
                        OrderDetailId = original.OrderDetailId,
                        TotalPcs = -original.TotalPcs,
                        TotalMeters = -original.TotalMeters,
                        TotalWeight = -original.TotalWeight,
                        TotalYards = -original.TotalYards
                    });
                }
            }

            return differences;
        }

        

        /// <summary>
        /// 不走这里,走的BatchVM的DoBatchDelete
        /// </summary>
        public override void DoDelete()
        {
            using var transaction = base.DC.BeginTransaction();
            try
            {
                    // 获取当前入库单的详细数据
                    var currentLotList = Entity.LotList.GroupBy(y => y.OrderDetailId).Select(x => new StockInfo
                    {
                        OrderDetailId = x.Key,
                        TotalPcs = x.Sum(y => y.Pcs),
                        TotalMeters = x.Sum(y => y.Meters),
                        TotalWeight = x.Sum(y => y.Weight),
                        TotalYards = x.Sum(y => y.Yards),
                    }).ToList();

                    // 一次性查询所有 ProductStock 记录
                    var productStocks = base.DC.Set<ProductStock>()
                        .Where(s => currentLotList.Select(r => r.OrderDetailId).Contains(s.OrderDetailId))
                        .ToDictionary(s => s.OrderDetailId);

                    foreach (var r in currentLotList)
                    {
                        if (productStocks.TryGetValue(r.OrderDetailId, out var summary))
                        {
                            summary.TotalPcs -= r.TotalPcs;
                            summary.TotalWeight -= r.TotalWeight;
                            summary.TotalMeters -= r.TotalMeters;
                            summary.TotalYards -= r.TotalYards;
                            summary.UpdateTime = DateTime.Now;
                            summary.UpdateBy = base.LoginUserInfo?.ITCode;
                        }
                    }
                
                base.DC.SaveChanges();
                // 删除出库单数据
                base.DoDelete();
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }
    }
}
