using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    public partial class ProductInboundBillSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        public Guid? POrderId { get; set; }
        public Guid? FinishingFactoryId { get; set; }
        [Display(Name = "_Wearhouse")]
        public String Wearhouse { get; set; }

        protected override void InitVM()
        {
        }

    }
}
