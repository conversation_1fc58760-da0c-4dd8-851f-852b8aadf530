using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;
using TEX.Model.Statistics;

namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    /// <summary>
    /// 入库单统计查询ViewModel
    /// </summary>
    public class ProductInboundBillStatisticsVM : BaseVM
    {
        /// <summary>
        /// 获取入库统计数据
        /// </summary>
        /// <param name="searcher">搜索条件</param>
        /// <returns>统计结果</returns>
        public async Task<InboundStatisticsResult> GetStatisticsAsync(ProductInboundBillSearcher searcher)
        {
            var query = BuildBaseQuery(searcher);

            var result = await query
                .GroupBy(x => 1) // 全局分组
                .Select(g => new InboundStatisticsResult
                {
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .FirstOrDefaultAsync();

            if (result == null)
            {
                result = new InboundStatisticsResult();
            }

            // 设置日期范围显示
            if (searcher.CreateDate != null)
            {
                result.DateRange = $"{searcher.CreateDate.GetStartTime:yyyy-MM-dd} 至 {searcher.CreateDate.GetEndTime:yyyy-MM-dd}";
            }

            // 获取主要客户信息
            result.TopCustomers = await GetTopCustomersAsync(searcher);
            
            // 获取主要产品信息
            result.TopProducts = await GetTopProductsAsync(searcher);

            return result;
        }

        /// <summary>
        /// 获取分组统计数据
        /// </summary>
        /// <param name="searcher">搜索条件</param>
        /// <param name="groupType">分组类型</param>
        /// <returns>分组统计结果列表</returns>
        public async Task<List<GroupedStatisticsResult>> GetGroupedStatisticsAsync(
            ProductInboundBillSearcher searcher, 
            StatisticsGroupType groupType)
        {
            var query = BuildBaseQuery(searcher);

            switch (groupType)
            {
                case StatisticsGroupType.Customer:
                    return await GetCustomerGroupedStatistics(query);
                
                case StatisticsGroupType.Product:
                    return await GetProductGroupedStatistics(query);
                
                case StatisticsGroupType.OrderDetail:
                    return await GetOrderDetailGroupedStatistics(query);
                
                case StatisticsGroupType.Date:
                    return await GetDateGroupedStatistics(query);
                
                case StatisticsGroupType.Month:
                    return await GetMonthGroupedStatistics(query);
                
                case StatisticsGroupType.FinishingFactory:
                    return await GetFinishingFactoryGroupedStatistics(query);
                
                case StatisticsGroupType.Warehouse:
                    return await GetWarehouseGroupedStatistics(query);
                
                default:
                    return new List<GroupedStatisticsResult>();
            }
        }

        /// <summary>
        /// 构建基础查询
        /// </summary>
        private IQueryable<ProductInboundBill> BuildBaseQuery(ProductInboundBillSearcher searcher)
        {
            var query = DC.Set<ProductInboundBill>()
                .Include(x => x.LotList)
                .Include(x => x.POrder)
                .ThenInclude(x => x.Customer)
                .Include(x => x.POrder)
                .ThenInclude(x => x.Product)
                .Include(x => x.FinishingFactory)
                .CheckBetween(searcher.CreateDate?.GetStartTime(), searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckEqual(searcher.POrderId, x => x.POrderId)
                .CheckEqual(searcher.FinishingFactoryId, x => x.FinishingFactoryId)
                .CheckContain(searcher.Wearhouse, x => x.Warehouse);

            return query;
        }

        /// <summary>
        /// 按客户分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetCustomerGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            return await query
                .GroupBy(x => new { x.POrder.CustomerId, x.POrder.Customer.CompanyName })
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = g.Key.CustomerId.ToString(),
                    GroupName = g.Key.CompanyName,
                    GroupType = "Customer",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .OrderByDescending(x => x.TotalWeight)
                .ToListAsync();
        }

        /// <summary>
        /// 按产品分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetProductGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            return await query
                .GroupBy(x => new { x.POrder.Product.ID, x.POrder.Product.ProductName })
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = g.Key.ID.ToString(),
                    GroupName = g.Key.ProductName,
                    GroupType = "Product",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .OrderByDescending(x => x.TotalWeight)
                .ToListAsync();
        }

        /// <summary>
        /// 按订单明细分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetOrderDetailGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            // 需要通过Lot来获取OrderDetail信息
            var result = await DC.Set<ProductInboundLot>()
                .Include(x => x.InboundBill)
                .ThenInclude(x => x.POrder)
                .ThenInclude(x => x.Customer)
                .Include(x => x.OrderDetail)
                .Where(x => query.Select(q => q.ID).Contains(x.InboundBillId))
                .GroupBy(x => new { 
                    x.OrderDetailId, 
                    x.OrderDetail.Color,
                    x.InboundBill.POrder.Customer.CompanyName,
                    x.InboundBill.POrder.OrderNo
                })
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = g.Key.OrderDetailId.ToString(),
                    GroupName = $"{g.Key.CompanyName} - {g.Key.OrderNo} - {g.Key.Color}",
                    GroupType = "OrderDetail",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Select(x => x.InboundBillId).Distinct().Count(),
                    TotalLots = g.Count()
                })
                .OrderByDescending(x => x.TotalWeight)
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// 按日期分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetDateGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            return await query
                .GroupBy(x => x.CreateDate.Date)
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = g.Key.ToString("yyyy-MM-dd"),
                    GroupName = g.Key.ToString("yyyy年MM月dd日"),
                    GroupDate = g.Key,
                    GroupType = "Date",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .OrderBy(x => x.GroupDate)
                .ToListAsync();
        }

        /// <summary>
        /// 按月份分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetMonthGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            return await query
                .GroupBy(x => new { x.CreateDate.Year, x.CreateDate.Month })
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = $"{g.Key.Year}-{g.Key.Month:D2}",
                    GroupName = $"{g.Key.Year}年{g.Key.Month}月",
                    GroupDate = new DateTime(g.Key.Year, g.Key.Month, 1),
                    GroupType = "Month",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .OrderBy(x => x.GroupDate)
                .ToListAsync();
        }

        /// <summary>
        /// 按染整厂分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetFinishingFactoryGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            return await query
                .GroupBy(x => new { x.FinishingFactoryId, x.FinishingFactory.CompanyName })
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = g.Key.FinishingFactoryId.ToString(),
                    GroupName = g.Key.CompanyName,
                    GroupType = "FinishingFactory",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .OrderByDescending(x => x.TotalWeight)
                .ToListAsync();
        }

        /// <summary>
        /// 按仓库分组统计
        /// </summary>
        private async Task<List<GroupedStatisticsResult>> GetWarehouseGroupedStatistics(IQueryable<ProductInboundBill> query)
        {
            return await query
                .Where(x => !string.IsNullOrEmpty(x.Warehouse))
                .GroupBy(x => x.Warehouse)
                .Select(g => new GroupedStatisticsResult
                {
                    GroupKey = g.Key,
                    GroupName = g.Key,
                    GroupType = "Warehouse",
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalMeters = g.Sum(x => x.Meters),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalBills = g.Count(),
                    TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
                })
                .OrderByDescending(x => x.TotalWeight)
                .ToListAsync();
        }

        /// <summary>
        /// 获取主要客户信息
        /// </summary>
        private async Task<string> GetTopCustomersAsync(ProductInboundBillSearcher searcher)
        {
            var topCustomers = await BuildBaseQuery(searcher)
                .GroupBy(x => x.POrder.Customer.CompanyName)
                .Select(g => new { CustomerName = g.Key, TotalWeight = g.Sum(x => x.Weight) })
                .OrderByDescending(x => x.TotalWeight)
                .Take(3)
                .Select(x => x.CustomerName)
                .ToListAsync();

            return string.Join("、", topCustomers);
        }

        /// <summary>
        /// 获取主要产品信息
        /// </summary>
        private async Task<string> GetTopProductsAsync(ProductInboundBillSearcher searcher)
        {
            var topProducts = await BuildBaseQuery(searcher)
                .GroupBy(x => x.POrder.Product.ProductName)
                .Select(g => new { ProductName = g.Key, TotalWeight = g.Sum(x => x.Weight) })
                .OrderByDescending(x => x.TotalWeight)
                .Take(3)
                .Select(x => x.ProductName)
                .ToListAsync();

            return string.Join("、", topProducts);
        }
    }
}
