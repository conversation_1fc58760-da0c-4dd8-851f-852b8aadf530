using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    public partial class ProductInboundBillListVM : BasePagedListVM<ProductInboundBill_View, ProductInboundBillSearcher>
    {

        protected override IEnumerable<IGridColumn<ProductInboundBill_View>> InitGridHeader()
        {
            return new List<GridColumn<ProductInboundBill_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.Customer_view),
                this.MakeGridHeader(x => x.CustomerOrderNo_view),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.ProductName_view),
                this.MakeGridHeader(x => x.FinishingFactory_view),
                this.MakeGridHeader(x => x.Warehouse),
                this.MakeGridHeader(x => x.Pcs),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Yards),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.AuditedBy),
                this.MakeGridHeader(x => x.AuditedComment),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<ProductInboundBill_View> GetSearchQuery()
        {
            var query = DC.Set<ProductInboundBill>()
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckEqual(Searcher.POrderId, x => x.POrderId)
                .CheckEqual(Searcher.FinishingFactoryId, x => x.FinishingFactoryId)
                .CheckContain(Searcher.Wearhouse, x => x.Warehouse)
                .Select(x => new ProductInboundBill_View
                {
                    ID = x.ID,
                    CreateDate = x.CreateDate,
                    BillNo = x.BillNo,
                    Customer_view=x.POrder.Customer.CompanyName,
                    CustomerOrderNo_view = x.POrder.CustomerOrderNo,
                    OrderNo_view = x.POrder.OrderNo,
                    ProductName_view = x.POrder.Product.ProductName,
                    FinishingFactory_view = x.FinishingFactory.CompanyName,
                    Warehouse = x.Warehouse,
                    Pcs = x.Pcs,
                    Weight = x.Weight,
                    Meters = x.Meters,
                    Yards = x.Yards,
                    AuditStatus = x.AuditStatus,
                    AuditedBy = x.AuditedBy,
                    AuditedComment = x.AuditedComment,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class ProductInboundBill_View : ProductInboundBill{
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public String CustomerOrderNo_view { get; set; }
        [Display(Name = "_Customer")]
        public String Customer_view { get; set; }
        [Display(Name = "_FinishingFactory")]
        public String FinishingFactory_view { get; set; }

        [Display(Name = "_ProductName")]
        public String ProductName_view { get; set; }


    }
}
