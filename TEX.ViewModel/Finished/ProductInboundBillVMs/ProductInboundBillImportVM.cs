using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductInboundBillVMs
{
    public partial class ProductInboundBillTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.CreateDate);
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.BillNo);
        public ExcelPropety POrder_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.POrderId);
        public ExcelPropety FinishingFactory_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.FinishingFactoryId);
        [Display(Name = "_Wearhouse")]
        public ExcelPropety Wearhouse_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.Warehouse);
        [Display(Name = "_Pcs")]
        public ExcelPropety Pcs_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.Pcs);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.Yards);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.AuditStatus);
        [Display(Name = "_AuditedBy")]
        public ExcelPropety AuditedBy_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.AuditedBy);
        [Display(Name = "_AuditedComment")]
        public ExcelPropety AuditedComment_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.AuditedComment);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<ProductInboundBill>(x => x.Remark);

	    protected override void InitVM()
        {
            POrder_Excel.DataType = ColumnDataType.ComboBox;
            POrder_Excel.ListItems = DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, y => y.OrderNo);
            FinishingFactory_Excel.DataType = ColumnDataType.ComboBox;
            FinishingFactory_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class ProductInboundBillImportVM : BaseImportVM<ProductInboundBillTemplateVM, ProductInboundBill>
    {

    }

}
