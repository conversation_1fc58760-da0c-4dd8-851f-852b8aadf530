using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundRollVMs
{
    public partial class ProductOutboundRollBatchVM : BaseBatchVM<ProductOutboundRoll, ProductOutboundRoll_BatchEdit>
    {
        public ProductOutboundRollBatchVM()
        {
            ListVM = new ProductOutboundRollListVM();
            LinkedVM = new ProductOutboundRoll_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class ProductOutboundRoll_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
