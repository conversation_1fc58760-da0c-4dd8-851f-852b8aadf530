using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundRollVMs
{
    public partial class ProductOutboundRollListVM : BasePagedListVM<ProductOutboundRoll_View, ProductOutboundRollSearcher>
    {

        protected override IEnumerable<IGridColumn<ProductOutboundRoll_View>> InitGridHeader()
        {
            return new List<GridColumn<ProductOutboundRoll_View>>{
                this.MakeGridHeader(x => x.LotNo_view),
                this.MakeGridHeader(x => x.RollNo),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Meters),
                this.MakeGridHeader(x => x.Yards),
                this.MakeGridHeader(x => x.Grade),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<ProductOutboundRoll_View> GetSearchQuery()
        {
            var query = DC.Set<ProductOutboundRoll>()
                .CheckEqual(Searcher.LotId, x=>x.LotId)
                .CheckContain(Searcher.Grade, x=>x.Grade)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new ProductOutboundRoll_View
                {
				    ID = x.ID,
                    LotNo_view = x.Lot.LotNo,
                    RollNo = x.RollNo,
                    Weight = x.Weight,
                    Meters = x.Meters,
                    Yards = x.Yards,
                    Grade = x.Grade,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class ProductOutboundRoll_View : ProductOutboundRoll{
        [Display(Name = "_LotNo")]
        public String LotNo_view { get; set; }

    }
}
