using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductOutboundRollVMs
{
    public partial class ProductOutboundRollTemplateVM : BaseTemplateVM
    {
        public ExcelPropety Lot_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.LotId);
        [Display(Name = "_RollNo")]
        public ExcelPropety RollNo_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.RollNo);
        [Display(Name = "_Weight")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.Weight);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.Meters);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.Yards);
        [Display(Name = "_Grade")]
        public ExcelPropety Grade_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.Grade);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<ProductOutboundRoll>(x => x.Remark);

	    protected override void InitVM()
        {
            Lot_Excel.DataType = ColumnDataType.ComboBox;
            Lot_Excel.ListItems = DC.Set<ProductOutboundLot>().GetSelectListItems(Wtm, y => y.LotNo);
        }

    }

    public class ProductOutboundRollImportVM : BaseImportVM<ProductOutboundRollTemplateVM, ProductOutboundRoll>
    {

    }

}
