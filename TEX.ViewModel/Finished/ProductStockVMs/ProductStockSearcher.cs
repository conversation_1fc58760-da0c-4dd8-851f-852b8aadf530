using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductStockVMs
{
    public partial class ProductStockSearcher : BaseSearcher
    {
        [Display(Name = "_Customer")]
        public String Customer { get; set; }
        [Display(Name = "_OrderNo")]
        public String OrderNo { get; set; }
        public Guid POrderId { get; set; }
        public Guid CustomerId { get; set; }

        [Display(Name = "_ProductName")]
        public String ProductName { get; set; }

        [Display(Name = "_OrderColor")]
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_Wearhouse")]
        public String Wearhouse { get; set; }
        [Display(Name = "_Location")]
        public String Location { get; set; }

        protected override void InitVM()
        {
        }

    }
}
