using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductStockVMs
{
    public partial class ProductStockBatchVM : BaseBatchVM<ProductStock, ProductStock_BatchEdit>
    {
        public ProductStockBatchVM()
        {
            ListVM = new ProductStockListVM();
            LinkedVM = new ProductStock_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class ProductStock_BatchEdit : BaseVM
    {
        public Guid? OrderDetailId { get; set; }
        [Display(Name = "_Wearhouse")]
        public String Wearhouse { get; set; }
        [Display(Name = "_Location")]
        public String Location { get; set; }

        protected override void InitVM()
        {
        }

    }

}
