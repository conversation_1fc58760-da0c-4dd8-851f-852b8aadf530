using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using TEX.Model.Finished;
using TEX.Model.Models;
using TEX.Model.Statistics;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc.Admin.ViewModels.DataPrivilegeVMs;

namespace TEX.ViewModel.Finished.ProductStockVMs;

/// <summary>
/// 入库单统计查询ViewModel
/// </summary>
public class ProductStatisticsVM : BaseVM
{
    /// <summary>
    /// 获取入库统计数据
    /// </summary>
    /// <param name="searcher">搜索条件</param>
    /// <returns>统计结果</returns>

    public IOrderedQueryable<ProductStatisticsResult> GetSearchQuery(ProductStockSearcher searcher)
    {
        var query = DC.Set<ProductStock>()

            .Include(x => x.OrderDetail)
                .ThenInclude(x => x.PurchaseOrder)
                    .ThenInclude(x => x.Customer)
            .Include(x => x.OrderDetail)
                .ThenInclude(x => x.PurchaseOrder)
                    .ThenInclude(x => x.Product)

            .CheckEqual(searcher.OrderDetailId, x => x.OrderDetailId)
            .CheckEqual(searcher.POrderId, x => x.OrderDetail.PurchaseOrderId)
            .CheckEqual(searcher.CustomerId, x => x.OrderDetail.PurchaseOrder.CustomerId)
            .CheckContain(searcher.Wearhouse, x => x.Wearhouse)

            .Select(x => new ProductStatisticsResult
            {

                Customer_view = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                OrderNo_view = x.OrderDetail.PurchaseOrder.OrderNo,
                ProductName_view = x.OrderDetail.PurchaseOrder.Product.ProductName,

                Color_view = x.OrderDetail.Color,
                TotalPcs = x.TotalPcs,
                TotalWeight = x.TotalWeight,
                TotalMeters = x.TotalMeters,
                TotalYards = x.TotalYards,
            })
            .OrderBy(x => x.Customer_view);
        return query;
    }


    /// <summary>
    /// 构建基础查询
    /// </summary>
    private IQueryable<ProductStock> BuildBaseQuery(ProductStockSearcher searcher)
    {
        var query = DC.Set<ProductStock>()
            .Include(x => x.OrderDetail)
                .ThenInclude(x => x.PurchaseOrder)
                    .ThenInclude(x => x.Customer)
            .Include(x => x.OrderDetail)
                .ThenInclude(x => x.PurchaseOrder)
                    .ThenInclude(x => x.Product)

            .CheckEqual(searcher.OrderDetailId, x => x.OrderDetailId)
            .CheckEqual(searcher.POrderId, x => x.OrderDetail.PurchaseOrderId)
            .CheckEqual(searcher.CustomerId, x => x.OrderDetail.PurchaseOrder.CustomerId)
            .CheckContain(searcher.Wearhouse, x => x.Wearhouse);

        return query;
    }

    /// <summary>
    /// 按客户分组统计
    /// </summary>
    private async Task<List<GroupedStatisticsResult>> GetCustomerGroupedStatistics(IQueryable<ProductInboundBill> query)
    {
        return await query
            .GroupBy(x => new { x.POrder.CustomerId, x.POrder.Customer.CompanyName })
            .Select(g => new GroupedStatisticsResult
            {
                GroupKey = g.Key.CustomerId.ToString(),
                GroupName = g.Key.CompanyName,
                GroupType = "Customer",
                TotalPcs = g.Sum(x => x.Pcs),
                TotalWeight = g.Sum(x => x.Weight),
                TotalMeters = g.Sum(x => x.Meters),
                TotalYards = g.Sum(x => x.Yards),
                TotalBills = g.Count(),
                TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
            })
            .OrderByDescending(x => x.TotalWeight)
            .ToListAsync();
    }

    /// <summary>
    /// 按产品分组统计
    /// </summary>
    private async Task<List<GroupedStatisticsResult>> GetProductGroupedStatistics(IQueryable<ProductInboundBill> query)
    {
        return await query
            .GroupBy(x => new { x.POrder.Product.ID, x.POrder.Product.ProductName })
            .Select(g => new GroupedStatisticsResult
            {
                GroupKey = g.Key.ID.ToString(),
                GroupName = g.Key.ProductName,
                GroupType = "Product",
                TotalPcs = g.Sum(x => x.Pcs),
                TotalWeight = g.Sum(x => x.Weight),
                TotalMeters = g.Sum(x => x.Meters),
                TotalYards = g.Sum(x => x.Yards),
                TotalBills = g.Count(),
                TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
            })
            .OrderByDescending(x => x.TotalWeight)
            .ToListAsync();
    }

    /// <summary>
    /// 按订单明细分组统计
    /// </summary>
    private async Task<List<GroupedStatisticsResult>> GetOrderDetailGroupedStatistics(IQueryable<ProductInboundBill> query)
    {
        // 需要通过Lot来获取OrderDetail信息
        var result = await DC.Set<ProductInboundLot>()
            .Include(x => x.InboundBill)
            .ThenInclude(x => x.POrder)
            .ThenInclude(x => x.Customer)
            .Include(x => x.OrderDetail)
            .Where(x => query.Select(q => q.ID).Contains(x.InboundBillId))
            .GroupBy(x => new
            {
                x.OrderDetailId,
                x.OrderDetail.Color,
                x.InboundBill.POrder.Customer.CompanyName,
                x.InboundBill.POrder.OrderNo
            })
            .Select(g => new GroupedStatisticsResult
            {
                GroupKey = g.Key.OrderDetailId.ToString(),
                GroupName = $"{g.Key.CompanyName} - {g.Key.OrderNo} - {g.Key.Color}",
                GroupType = "OrderDetail",
                TotalPcs = g.Sum(x => x.Pcs),
                TotalWeight = g.Sum(x => x.Weight),
                TotalMeters = g.Sum(x => x.Meters),
                TotalYards = g.Sum(x => x.Yards),
                TotalBills = g.Select(x => x.InboundBillId).Distinct().Count(),
                TotalLots = g.Count()
            })
            .OrderByDescending(x => x.TotalWeight)
            .ToListAsync();

        return result;
    }

    /// <summary>
    /// 按日期分组统计
    /// </summary>
    private async Task<List<GroupedStatisticsResult>> GetDateGroupedStatistics(IQueryable<ProductInboundBill> query)
    {
        return await query
            .GroupBy(x => x.CreateDate.Date)
            .Select(g => new GroupedStatisticsResult
            {
                GroupKey = g.Key.ToString("yyyy-MM-dd"),
                GroupName = g.Key.ToString("yyyy年MM月dd日"),
                GroupDate = g.Key,
                GroupType = "Date",
                TotalPcs = g.Sum(x => x.Pcs),
                TotalWeight = g.Sum(x => x.Weight),
                TotalMeters = g.Sum(x => x.Meters),
                TotalYards = g.Sum(x => x.Yards),
                TotalBills = g.Count(),
                TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
            })
            .OrderBy(x => x.GroupDate)
            .ToListAsync();
    }

    /// <summary>
    /// 按月份分组统计
    /// </summary>
    private async Task<List<GroupedStatisticsResult>> GetMonthGroupedStatistics(IQueryable<ProductInboundBill> query)
    {
        return await query
            .GroupBy(x => new { x.CreateDate.Year, x.CreateDate.Month })
            .Select(g => new GroupedStatisticsResult
            {
                GroupKey = $"{g.Key.Year}-{g.Key.Month:D2}",
                GroupName = $"{g.Key.Year}年{g.Key.Month}月",
                GroupDate = new DateTime(g.Key.Year, g.Key.Month, 1),
                GroupType = "Month",
                TotalPcs = g.Sum(x => x.Pcs),
                TotalWeight = g.Sum(x => x.Weight),
                TotalMeters = g.Sum(x => x.Meters),
                TotalYards = g.Sum(x => x.Yards),
                TotalBills = g.Count(),
                TotalLots = g.Sum(x => x.LotList.Count(l => l.IsValid))
            })
            .OrderBy(x => x.GroupDate)
            .ToListAsync();
    }



}