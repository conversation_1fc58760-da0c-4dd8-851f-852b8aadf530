using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductStockVMs
{
    public partial class ProductStockListVM : BasePagedListVM<ProductStock_View, ProductStockSearcher>
    {

        protected override IEnumerable<IGridColumn<ProductStock_View>> InitGridHeader()
        {
            return new List<GridColumn<ProductStock_View>>{
                this.MakeGridHeader(x => x.Customer_view),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.ProductName_view),
                this.MakeGridHeader(x => x.ProductSpec_view),
                this.MakeGridHeader(x => x.Color_view),
                this.MakeGridHeader(x => x.TotalPcs),
                this.MakeGridHeader(x => x.TotalWeight),
                this.MakeGridHeader(x => x.TotalMeters),
                this.MakeGridHeader(x => x.TotalYards),
                this.MakeGridHeader(x => x.Wearhouse),
                this.MakeGridHeader(x => x.Location),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<ProductStock_View> GetSearchQuery()
        {
            var query = DC.Set<ProductStock>()
                .CheckEqual(Searcher.OrderDetailId, x => x.OrderDetailId)
                .CheckContain(Searcher.Wearhouse, x => x.Wearhouse)
                .CheckContain(Searcher.Location, x => x.Location)
                .Select(x => new ProductStock_View
                {
                    ID = x.ID,
                    Customer_view = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                    OrderNo_view = x.OrderDetail.PurchaseOrder.OrderNo,
                    ProductName_view = x.OrderDetail.PurchaseOrder.Product.ProductName,
                    ProductSpec_view = x.OrderDetail.PurchaseOrder.Product.Spec,

                    Color_view = x.OrderDetail.Color,
                    TotalPcs = x.TotalPcs,
                    TotalWeight = x.TotalWeight,
                    TotalMeters = x.TotalMeters,
                    TotalYards = x.TotalYards,
                    Wearhouse = x.Wearhouse,
                    Location = x.Location,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class ProductStock_View : ProductStock{
        [Display(Name = "_Color")]
        public String Color_view { get; set; }
        [Display(Name = "_Customer")]
        public String Customer_view { get; set; }
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_ProductName")]
        public String ProductName_view { get; set; }
        [Display(Name = "_Spec")]
        public String ProductSpec_view { get; set; }

    }
}
