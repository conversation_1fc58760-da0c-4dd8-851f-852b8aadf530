using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.ViewModel.Finished.ProductStockVMs
{
    public partial class ProductStockTemplateVM : BaseTemplateVM
    {
        public ExcelPropety OrderDetail_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.OrderDetailId);
        [Display(Name = "_Pcs")]
        public ExcelPropety TotalPcs_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.TotalPcs);
        [Display(Name = "_Weight")]
        public ExcelPropety TotalWeight_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.TotalWeight);
        [Display(Name = "_Meters")]
        public ExcelPropety TotalMeters_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.TotalMeters);
        [Display(Name = "_Yards")]
        public ExcelPropety TotalYards_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.TotalYards);
        [Display(Name = "_Wearhouse")]
        public ExcelPropety Wearhouse_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.Wearhouse);
        [Display(Name = "_Location")]
        public ExcelPropety Location_Excel = ExcelPropety.CreateProperty<ProductStock>(x => x.Location);

	    protected override void InitVM()
        {
            OrderDetail_Excel.DataType = ColumnDataType.ComboBox;
            OrderDetail_Excel.ListItems = DC.Set<OrderDetail>().GetSelectListItems(Wtm, y => y.Color);
        }

    }

    public class ProductStockImportVM : BaseImportVM<ProductStockTemplateVM, ProductStock>
    {

    }

}
