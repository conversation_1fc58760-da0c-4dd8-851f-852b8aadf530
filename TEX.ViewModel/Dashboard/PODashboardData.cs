using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TEX.ViewModel.Models;

public class PODashboardData
{

    /// <summary>
    /// 当年统计
    /// </summary>
    public Decimal POYearTotalAmount { get; init; } = 0;
    public Decimal POYearTotalWeight { get; init; } = 0;

    /// <summary>
    /// 当月统计
    /// </summary>
    public Decimal POMonthTotalAmount { get; init; } = 0;
    public Decimal POMonthTotalWeight { get; init; } = 0;

    /// <summary>
    /// 当日统计
    /// </summary>
    public Decimal POWeekTotalAmount { get; init; } = 0;
    public Decimal POWeekTotalWeight { get; init; } = 0;


    /// <summary>
    /// 月度统计列表
    /// </summary>
    public List<MonthlyTotalWeight> MonthlyTotalWeight { get; init; } = new();
    public List<MonthlyTotalAmount> MonthlyTotalAmount { get; init; } = new();
}

public class InspectDashboardData
{

    /// <summary>
    /// 本年度统计
    /// </summary>
    public Decimal InspectYearTotalMeters { get; init; } = 0;
    public Decimal InspectYearTotalYards { get; init; } = 0;
    public Decimal InspectYearTotalWeight { get; init; } = 0;

    /// <summary>
    /// 本月统计
    /// </summary>
    public Decimal InspectMonthTotalMeters { get; init; } = 0;
    public Decimal InspectMonthTotalYards { get; init; } = 0;
    public Decimal InspectMonthTotalWeight { get; init; } = 0;

    /// <summary>
    /// 本周统计
    /// </summary>
    public Decimal InspectWeekTotalMeters { get; init; } = 0;
    public Decimal InspectWeekTotalYards { get; init; } = 0;
    public Decimal InspectWeekTotalWeight { get; init; } = 0;

    /// <summary>
    /// 当日统计
    /// </summary>
    public Decimal InspectTodayTotalMeters { get; init; } = 0;
    public Decimal InspectTodayTotalYards { get; init; } = 0;
    public Decimal InspectTodayTotalWeight { get; init; } = 0;


    /// <summary>
    /// 月度统计列表
    /// </summary>
    public List<InspectMonthlyTotalWeight> InspectMonthlyTotalWeight { get; init; } = new();
    public List<InspectMonthlyTotalYards> InspectMonthlyTotalYards { get; init; } = new();
    public List<InspectMonthlyTotalMeters> InspectMonthlyTotalMeters { get; init; } = new();
}
public class MonthlyTotalWeight
{
    public DateTime YearMonth { get; set; } = DateTime.Now;
    public Decimal TotalWeight { get; set; } = 0;
}
public class MonthlyTotalAmount
{
    public DateTime YearMonth { get; set; } = DateTime.Now;
    public Decimal TotalAmount { get; set; } = 0;
}

public class InspectMonthlyTotalWeight
{
    //public DateTime YearMonth { get; set; } = DateTime.Now;
    public int Year { get; set; }
    public int Month { get; set; }
    public Decimal MonthlyTotalWeight { get; set; } = 0;
}
public class InspectMonthlyTotalMeters
{
    //public DateTime YearMonth { get; set; } = DateTime.Now;
    public int Year { get; set; }
    public int Month { get; set; }
    public Decimal MonthlyTotalMeters { get; set; } = 0;
}
public class InspectMonthlyTotalYards
{
    //public DateTime YearMonth { get; set; } = DateTime.Now;
    public int Year { get; set; }
    public int Month { get; set; }
    public Decimal MonthlyTotalYards { get; set; } = 0;
}