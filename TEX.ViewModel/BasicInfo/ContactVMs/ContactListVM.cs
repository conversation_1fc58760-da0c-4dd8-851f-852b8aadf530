using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.BasicInfo.ContactVMs
{
    public partial class ContactListVM : BasePagedListVM<Contact_View, ContactSearcher>
    {

        protected override IEnumerable<IGridColumn<Contact_View>> InitGridHeader()
        {
            return new List<GridColumn<Contact_View>>{
                this.MakeGridHeader(x => x.ContactName),
                this.MakeGridHeader(x => x.CompanyName_view),
                this.MakeGridHeader(x => x.PositionTitle),
                this.MakeGridHeader(x => x.MobilePhone),
                this.MakeGridHeader(x => x.Address),
                this.MakeGridHeader(x => x.Phone),
                this.MakeGridHeader(x => x.Email),
                this.MakeGridHeader(x => x.WeChat),
                this.MakeGridHeader(x => x.QQ),
                this.MakeGridHeader(x => x.Fax),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<Contact_View> GetSearchQuery()
        {
            var query = DC.Set<Contact>()
                .CheckContain(Searcher.ContactName, x=>x.ContactName)
                .CheckEqual(Searcher.AffiliationCompanyId, x=>x.AffiliationCompanyId)
                .CheckContain(Searcher.MobilePhone, x=>x.MobilePhone)
                //.CheckEqual(Searcher.AuditStatus, x=>x.AuditStatus)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new Contact_View
                {
				    ID = x.ID,
                    ContactName = x.ContactName,
                    CompanyName_view = x.AffiliationCompany.CompanyName,
                    PositionTitle = x.PositionTitle,
                    MobilePhone = x.MobilePhone,
                    Address = x.Address,
                    Phone = x.Phone,
                    Email = x.Email,
                    WeChat = x.WeChat,
                    QQ = x.QQ,
                    Fax = x.Fax,
                    AuditStatus = x.AuditStatus,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class Contact_View : Contact{
        [Display(Name = "_CompanyName")]
        public String CompanyName_view { get; set; }

    }
}
