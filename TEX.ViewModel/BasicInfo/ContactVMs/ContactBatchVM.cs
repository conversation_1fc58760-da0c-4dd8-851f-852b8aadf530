using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.BasicInfo.ContactVMs
{
    public partial class ContactBatchVM : BaseBatchVM<Contact, Contact_BatchEdit>
    {
        public ContactBatchVM()
        {
            ListVM = new ContactListVM();
            LinkedVM = new Contact_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class Contact_BatchEdit : BaseVM
    {
        [Display(Name = "_AffiliationCompany")]
        public Guid? AffiliationCompanyId { get; set; }
        [Display(Name = "_PositionTitle")]
        public String PositionTitle { get; set; }
        
        protected override void InitVM()
        {
        }

    }

}
