using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.BasicInfo.ContactVMs
{
    public partial class ContactSearcher : BaseSearcher
    {
        [Display(Name = "_ContactName")]
        public String ContactName { get; set; }
        [Display(Name = "_AffiliationCompany")]
        public Guid? AffiliationCompanyId { get; set; }
        [Display(Name = "_MobilePhone")]
        public String MobilePhone { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum AuditStatus { get; set; }
        [Display(Name = "_Admin.Remark")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }
}
