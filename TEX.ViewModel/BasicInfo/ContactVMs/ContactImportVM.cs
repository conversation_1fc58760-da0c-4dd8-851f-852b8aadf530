using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.BasicInfo.ContactVMs
{
    public partial class ContactTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_ContactName")]
        public ExcelPropety ContactName_Excel = ExcelPropety.CreateProperty<Contact>(x => x.ContactName);
        [Display(Name = "_AffiliationCompany")]
        public ExcelPropety AffiliationCompany_Excel = ExcelPropety.CreateProperty<Contact>(x => x.AffiliationCompanyId);
        [Display(Name = "_PositionTitle")]
        public ExcelPropety PositionTitle_Excel = ExcelPropety.CreateProperty<Contact>(x => x.PositionTitle);
        [Display(Name = "_MobilePhone")]
        public ExcelPropety MobilePhone_Excel = ExcelPropety.CreateProperty<Contact>(x => x.MobilePhone);
        [Display(Name = "_Address")]
        public ExcelPropety Address_Excel = ExcelPropety.CreateProperty<Contact>(x => x.Address);
        [Display(Name = "_Phone")]
        public ExcelPropety Phone_Excel = ExcelPropety.CreateProperty<Contact>(x => x.Phone);
        [Display(Name = "_Email")]
        public ExcelPropety Email_Excel = ExcelPropety.CreateProperty<Contact>(x => x.Email);
        [Display(Name = "_WeChat")]
        public ExcelPropety WeChat_Excel = ExcelPropety.CreateProperty<Contact>(x => x.WeChat);
        [Display(Name = "_QQ")]
        public ExcelPropety QQ_Excel = ExcelPropety.CreateProperty<Contact>(x => x.QQ);
        [Display(Name = "_Fax")]
        public ExcelPropety Fax_Excel = ExcelPropety.CreateProperty<Contact>(x => x.Fax);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<Contact>(x => x.Remark);

	    protected override void InitVM()
        {
            AffiliationCompany_Excel.DataType = ColumnDataType.ComboBox;
            AffiliationCompany_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class ContactImportVM : BaseImportVM<ContactTemplateVM, Contact>
    {

    }

}
