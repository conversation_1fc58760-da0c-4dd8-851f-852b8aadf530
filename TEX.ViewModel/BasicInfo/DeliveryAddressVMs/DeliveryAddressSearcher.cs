using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using TEX.Model.Models;
using WalkingTec.Mvvm.Core;



namespace TEX.ViewModel.BasicInfo.DeliveryAddressVMs
{
    public partial class DeliveryAddressSearcher : BaseSearcher
    {
        [Display(Name = "_CompanyName")]
        public String CompanyName { get; set; }
        [Display(Name = "_ContactName")]
        public String ContactName { get; set; }
        [Display(Name = "_Address")]
        public String Address { get; set; }
        [Display(Name = "_AffiliationCompany")]
        public Guid? AffiliationCompanyId { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum AuditStatus { get; set; }
        [Display(Name = "_Admin.Remark")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }
}
