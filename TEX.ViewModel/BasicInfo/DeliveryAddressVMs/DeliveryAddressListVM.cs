using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;



namespace TEX.ViewModel.BasicInfo.DeliveryAddressVMs
{
    public partial class DeliveryAddressListVM : BasePagedListVM<DeliveryAddress_View, DeliveryAddressSearcher>
    {

        protected override IEnumerable<IGridColumn<DeliveryAddress_View>> InitGridHeader()
        {
            return new List<GridColumn<DeliveryAddress_View>>{
                this.MakeGridHeader(x => x.CompanyName),
                this.MakeGridHeader(x => x.ContactName),
                this.MakeGridHeader(x => x.Phone),
                this.MakeGridHeader(x => x.Address),
                this.MakeGridHeader(x => x.CompanyName_view),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<DeliveryAddress_View> GetSearchQuery()
        {
            var query = DC.Set<DeliveryAddress>()
                .CheckContain(Searcher.CompanyName, x=>x.CompanyName)
                .CheckContain(Searcher.ContactName, x=>x.ContactName)
                .CheckContain(Searcher.Address, x=>x.Address)
                .CheckEqual(Searcher.AffiliationCompanyId, x=>x.AffiliationCompanyId)
                //.CheckEqual(Searcher.AuditStatus, x => x.AuditStatus)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new DeliveryAddress_View
                {
				    ID = x.ID,
                    CompanyName = x.CompanyName,
                    ContactName = x.ContactName,
                    Phone = x.Phone,
                    Address = x.Address,
                    CompanyName_view = x.AffiliationCompany.CompanyName,
                    AuditStatus = x.AuditStatus,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class DeliveryAddress_View : DeliveryAddress{
        [Display(Name = "_CompanyName")]
        public String CompanyName_view { get; set; }

    }
}
