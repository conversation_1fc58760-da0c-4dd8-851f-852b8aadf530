using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;



namespace TEX.ViewModel.BasicInfo.DeliveryAddressVMs
{
    public partial class DeliveryAddressBatchVM : BaseBatchVM<DeliveryAddress, DeliveryAddress_BatchEdit>
    {
        public DeliveryAddressBatchVM()
        {
            ListVM = new DeliveryAddressListVM();
            LinkedVM = new DeliveryAddress_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class DeliveryAddress_BatchEdit : BaseVM
    {
        [Display(Name = "_AffiliationCompany")]
        public Guid? AffiliationCompanyId { get; set; }
       
        

        protected override void InitVM()
        {
        }

    }

}
