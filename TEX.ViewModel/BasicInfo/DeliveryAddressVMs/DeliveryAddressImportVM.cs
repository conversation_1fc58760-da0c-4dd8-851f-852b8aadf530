using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;



namespace TEX.ViewModel.BasicInfo.DeliveryAddressVMs
{
    public partial class DeliveryAddressTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CompanyName")]
        public ExcelPropety CompanyName_Excel = ExcelPropety.CreateProperty<DeliveryAddress>(x => x.CompanyName);
        [Display(Name = "_ContactName")]
        public ExcelPropety ContactName_Excel = ExcelPropety.CreateProperty<DeliveryAddress>(x => x.ContactName);
        [Display(Name = "_Phone")]
        public ExcelPropety Phone_Excel = ExcelPropety.CreateProperty<DeliveryAddress>(x => x.Phone);
        [Display(Name = "_Address")]
        public ExcelPropety Address_Excel = ExcelPropety.CreateProperty<DeliveryAddress>(x => x.Address);
        [Display(Name = "_AffiliationCompany")]
        public ExcelPropety AffiliationCompany_Excel = ExcelPropety.CreateProperty<DeliveryAddress>(x => x.AffiliationCompanyId);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<DeliveryAddress>(x => x.Remark);

	    protected override void InitVM()
        {
            AffiliationCompany_Excel.DataType = ColumnDataType.ComboBox;
            AffiliationCompany_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class DeliveryAddressImportVM : BaseImportVM<DeliveryAddressTemplateVM, DeliveryAddress>
    {

    }

}
