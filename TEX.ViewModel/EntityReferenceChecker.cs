using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;

namespace TEX.ViewModel
{
    public class EntityReferenceCheckerVM:BaseVM
    {
        //通过指定实体检查是否有外键使用过数据,使用过则不能删除
        public bool IsEntityReferenced<TEntity>(Guid id, params Type[] typesToCheck) where TEntity : class
        {
            var entityType = typeof(TEntity);

            foreach (var type in typesToCheck)
            {
                
                        // 使用反射来调用DC.Set<T>()方法
                        var method = DC.GetType().GetMethod("Set").MakeGenericMethod(type);
                        var dbSet = method.Invoke(DC, null) as IEnumerable;
                // 如果DbSet中有实体的外键值与我们要检查的id相同，返回true
                //if (dbSet.Any(e => (Guid)e.GetType().GetProperty(nameof(type)).GetValue(e) == id)) // 替换"ForeignKeyPropertyName"为你的外键属性名
                //{
                //    return true;
                //}

            }

            // 如果没有实体引用我们要检查的实体，返回false
            return false;
        }
    }
}
