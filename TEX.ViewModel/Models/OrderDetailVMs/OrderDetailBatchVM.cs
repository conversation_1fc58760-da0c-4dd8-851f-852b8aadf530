
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailBatchVM : BaseBatchVM<OrderDetail, OrderDetail_BatchEdit>
    {
        public OrderDetailBatchVM()
        {
            ListVM = new OrderDetailListVM();
            LinkedVM = new OrderDetail_BatchEdit();
        }

        public override bool DoBatchEdit()
        {
            
            return base.DoBatchEdit();
        }
    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class OrderDetail_BatchEdit : BaseVM
    {

        
        [Display(Name = "_PurchaseOrder")]
        public Guid? PurchaseOrderId { get; set; }
        [Display(Name = "_Color")]
        public string Color { get; set; }
        [Display(Name = "_ColorCode")]
        public string ColorCode { get; set; }
        [Display(Name = "_Meters")]
        public decimal? Meters { get; set; }
        [Display(Name = "_KG")]
        public decimal? KG { get; set; }
        [Display(Name = "_Yards")]
        public decimal? Yards { get; set; }
        [Display(Name = "_AccountUnit")]
        public AccountingUnitEnum? AccountUnit { get; set; }
        [Display(Name = "_PriceUnit")]
        public CurrencyEnum? PriceUnit { get; set; }
        [Display(Name = "_Price")]
        public decimal? Price { get; set; }
        [Display(Name = "_Amount")]
        public decimal? Amount { get; set; }

        protected override void InitVM()
        {
           
        }
    }

}