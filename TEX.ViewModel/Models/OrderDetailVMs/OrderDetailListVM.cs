using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailListVM : BasePagedListVM<OrderDetail_View, OrderDetailSearcher>
    {
        // 判断角色是否匹配
        private bool HasMatchingRole => base.Wtm.LoginUserInfo.Roles.Any(x => x.RoleCode.StartsWith("8") || x.RoleCode.Contains("001"));
        protected override IEnumerable<IGridColumn<OrderDetail_View>> InitGridHeader()
        {
            var columns = new List<GridColumn<OrderDetail_View>>
                {
                    this.MakeGridHeader(x => x.OrderDetail_Customer),
                    this.MakeGridHeader(x => x.OrderDetail_PurchaseOrder),
                    this.MakeGridHeader(x => x.OrderDetail_ProductName),
                    this.MakeGridHeader(x => x.OrderDetail_ProductSpec),
                    this.MakeGridHeader(x => x.Color),
                    this.MakeGridHeader(x => x.EngColor),
                    this.MakeGridHeader(x => x.ColorCode),
                    this.MakeGridHeader(x => x.Meters),
                    this.MakeGridHeader(x => x.KG),
                    this.MakeGridHeader(x => x.Yards),
                    this.MakeGridHeader(x => x.OrderDetail_GSM),
                    this.MakeGridHeader(x => x.OrderDetail_Width),
                    this.MakeGridHeader(x => x.OrderDetail_AccountUnit),
                    this.MakeGridHeader(x => x.OrderDetail_PriceUnit),
                    this.MakeGridHeader(x => x.CreateTime),
                    this.MakeGridHeader(x => x.UpdateTime),
                    this.MakeGridHeader(x => x.CreateBy),
                    this.MakeGridHeader(x => x.UpdateBy),
                    this.MakeGridHeader(x => x.IsValid),
                    this.MakeGridHeaderAction(width: 200)
                };

            // 如果角色匹配，添加 Price 和 Amount 列
            if (HasMatchingRole)
            {
                columns.Add(this.MakeGridHeader(x => x.Price));
                columns.Add(this.MakeGridHeader(x => x.Amount));
            }

            return columns;
        }


        public override IOrderedQueryable<OrderDetail_View> GetSearchQuery()
        {
            var query = DC.Set<OrderDetail>()

                .CheckEqual(Searcher.PurchaseOrderId, x => x.PurchaseOrderId)
                .CheckEqual(Searcher.OrderDetailId, x => x.ID)
                .CheckEqual(Searcher.CustomerId, x => x.PurchaseOrder.CustomerId)
                .CheckEqual(Searcher.ProductId, x => x.PurchaseOrder.ProductId)
                .CheckContain(Searcher.Color, x => x.Color)
                .CheckContain(Searcher.ColorCode, x => x.ColorCode)
                .Select(x => new OrderDetail_View
                {
                    ID = x.ID,
                    OrderDetail_Customer = x.PurchaseOrder.Customer.CompanyName,
                    OrderDetail_PurchaseOrder = x.PurchaseOrder.OrderNo,
                    OrderDetail_ProductName = x.PurchaseOrder.Product.ProductName,
                    OrderDetail_ProductSpec = x.PurchaseOrder.Product.GSM + "GSM " + x.PurchaseOrder.Product.Width + "CM",
                    Color = x.Color,
                    EngColor = x.EngColor,
                    ColorCode = x.ColorCode,
                    Meters = x.Meters,
                    KG = x.KG,
                    Yards = x.Yards,
                    OrderDetail_GSM = x.PurchaseOrder.Product.GSM,
                    OrderDetail_Width = x.PurchaseOrder.Product.Width,
                    OrderDetail_AccountUnit = x.PurchaseOrder.AccountUnit,
                    OrderDetail_PriceUnit = x.PurchaseOrder.PriceUnit,
                    //Price = x.Price,
                    //Amount = x.Amount,
                    Price = HasMatchingRole ? x.Price : (decimal?)null, // 如果角色不匹配，Price 为 null
                    Amount = HasMatchingRole ? x.Amount : (decimal?)null, // 如果角色不匹配，Amount 为 null
                    CreateTime = x.CreateTime,
                    //OrderDetail_UpdateTime = x.UpdateTime,
                    //OrderDetail_CreateBy = x.CreateBy,
                    //OrderDetail_UpdateBy = x.UpdateBy,
                    //OrderDetail_IsValid = x.IsValid,
                })
                .OrderBy(x => x.OrderDetail_PurchaseOrder).ThenBy(x => x.OrderDetail_ProductName).ThenBy(x => x.Color);
            return query;
        }

    }
    public class OrderDetail_View : OrderDetail
    {
        [Display(Name = "_Customer")]
        public string OrderDetail_Customer { get; set; }
        [Display(Name = "_PurchaseOrder")]
        public string OrderDetail_PurchaseOrder { get; set; }
        [Display(Name = "_ProductName")]
        public string OrderDetail_ProductName { get; set; }
        [Display(Name = "_ProductSpec")]
        public string OrderDetail_ProductSpec { get; set; }
        public int? OrderDetail_GSM { get; set; }
        public int? OrderDetail_Width { get; set; }
        public AccountingUnitEnum? OrderDetail_AccountUnit { get; set; }
        public CurrencyEnum? OrderDetail_PriceUnit { get; set; }
    }

}