using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;

using TEX.ViewModel.Models.PurchaseOrderVMs;
using TEX.Model.Models;
using TEX.Model;
namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailVM : BaseCRUDVM<OrderDetail>
    {
        

        public OrderDetailVM()
        {
            
            SetInclude(x => x.PurchaseOrder);

        }

        protected override void InitVM()
        {
            

        }

        public override DuplicatedInfo<OrderDetail> SetDuplicatedCheck()
        {
            return null;

        }

        public override async Task DoAddAsync()        
        {
            
            await base.DoAddAsync();

        }

        public override async Task DoEditAsync(bool updateAllFields = false)
        {
            
            await base.DoEditAsync();

        }

        public override async Task DoDeleteAsync()
        {
            await base.DoDeleteAsync();

        }
    }
}
