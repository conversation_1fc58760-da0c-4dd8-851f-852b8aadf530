
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailTemplateVM : BaseTemplateVM
    {
        
        [Display(Name = "_PurchaseOrder")]
        public ExcelPropety PurchaseOrder_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.PurchaseOrderId);
        [Display(Name = "_Color")]
        public ExcelPropety Color_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.Color);
        [Display(Name = "_ColorCode")]
        public ExcelPropety ColorCode_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.ColorCode);
        [Display(Name = "_Meters")]
        public ExcelPropety Meters_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.Meters);
        [Display(Name = "_KG")]
        public ExcelPropety KG_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.KG);
        [Display(Name = "_Yards")]
        public ExcelPropety Yards_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.Yards);
        [Display(Name = "_Price")]
        public ExcelPropety Price_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.Price);
        [Display(Name = "_Amount")]
        public ExcelPropety Amount_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.Amount);
        [Display(Name = "_CreateTime")]
        public ExcelPropety CreateTime_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.CreateTime, true);
        [Display(Name = "_UpdateTime")]
        public ExcelPropety UpdateTime_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.UpdateTime, true);
        [Display(Name = "_CreateBy")]
        public ExcelPropety CreateBy_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.CreateBy);
        [Display(Name = "_UpdateBy")]
        public ExcelPropety UpdateBy_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.UpdateBy);
        [Display(Name = "_IsValid")]
        public ExcelPropety IsValid_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.IsValid);
        [Display(Name = "_TenantCode")]
        public ExcelPropety TenantCode_Excel = ExcelPropety.CreateProperty<OrderDetail>(x => x.TenantCode);

	    protected override void InitVM()
        {
            
            PurchaseOrder_Excel.DataType = ColumnDataType.ComboBox;
            PurchaseOrder_Excel.ListItems = DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, y => y.TenantCode.ToString(), SortByName: false);

        }

    }

    public class OrderDetailImportVM : BaseImportVM<OrderDetailTemplateVM, OrderDetail>
    {
            //import

    }

}