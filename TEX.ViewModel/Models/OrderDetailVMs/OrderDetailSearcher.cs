
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;
namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailSearcher : BaseSearcher
    {
        
        [Display(Name = "_PurchaseOrder")]
        public Guid? PurchaseOrderId { get; set; }
        [Display(Name = "_OrderDetail")]
        public Guid? OrderDetailId { get; set; }

        [Display(Name = "_Customer")]
        public Guid? CustomerId { get; set; }

        [Display(Name = "_Product")]
        public Guid? ProductId { get; set; }

        [Display(Name = "_Color")]
        public string Color { get; set; }
        [Display(Name = "_ColorCode")]
        public string ColorCode { get; set; }

        protected override void InitVM()
        {
            

        }
    }

}