
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.PurchaseOrderVMs
{
    public partial class PurchaseOrderBatchVM : BaseBatchVM<PurchaseOrder, PurchaseOrder_BatchEdit>
    {
        public PurchaseOrderBatchVM()
        {
            ListVM = new PurchaseOrderListVM();
            LinkedVM = new PurchaseOrder_BatchEdit();
        }

        public override bool DoBatchEdit()
        {
            
            return base.DoBatchEdit();
        }
    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class PurchaseOrder_BatchEdit : BaseVM
    {

        
        [Display(Name = "_CreateDate")]
        public DateTime? CreateDate { get; set; }
        [Display(Name = "_DeliveryDate")]
        public DateTime? DeliveryDate { get; set; }
        [Display(Name = "_Customer")]
        public Guid? CustomerId { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public string CustomerOrderNo { get; set; }
        [Display(Name = "_Merchandiser")]
        public string Merchandiser { get; set; }
        [Display(Name = "_OrderType")]
        public OrderTypeEnum? OrderType { get; set; }
        [Display(Name = "_Product")]
        public Guid? ProductId { get; set; }
        [Display(Name = "_Light")]
        public LightEnum? Light { get; set; }
        [Display(Name = "_Light2")]
        public LightEnum? Light2 { get; set; }
        [Display(Name = "_TotalMeters")]
        public double? TotalMeters { get; set; }
        [Display(Name = "_TotalYards")]
        public double? TotalYards { get; set; }
        [Display(Name = "_TotalWeight")]
        public double? TotalWeight { get; set; }
        [Display(Name = "_TotalAmount")]
        public decimal? TotalAmount { get; set; }

        protected override void InitVM()
        {
           
        }
    }

}