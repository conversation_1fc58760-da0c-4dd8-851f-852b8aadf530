
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;

namespace TEX.ViewModel.Models.PurchaseOrderVMs
{
    public partial class PurchaseOrderSearcher : BaseSearcher
    {
        
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        [Display(Name = "_DeliveryDate")]
        public DateRange DeliveryDate { get; set; }
        [Display(Name = "_Customer")]
        public Guid? CustomerId { get; set; }
        [Display(Name = "_OrderNo")]
        public Guid? OrderId { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public string CustomerOrderNo { get; set; }
        [Display(Name = "_Merchandiser")]
        public string Merchandiser { get; set; }
        [Display(Name = "_OrderType")]
        public OrderTypeEnum? OrderType { get; set; }
        [Display(Name = "_Product")]
        public Guid? ProductId { get; set; }
        [Display(Name = "_Light")]
        public LightEnum? Light { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; } //必须标记为可空,否则默认枚举=0,查询结果错误

        protected override void InitVM()
        {
            

        }
    }

}