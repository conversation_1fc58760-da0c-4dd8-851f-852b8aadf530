using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core.Support.Json;

namespace TEX.ViewModel.Models.PurchaseOrderVMs
{
    public partial class PurchaseOrderListVM : BasePagedListVM<PurchaseOrder_View, PurchaseOrderSearcher>
    {
        protected override IEnumerable<IGridColumn<PurchaseOrder_View>> InitGridHeader()
        {
            return new List<GridColumn<PurchaseOrder_View>>{

                this.MakeGridHeader(x => x.PurchaseOrder_CreateDate),
                this.MakeGridHeader(x => x.PurchaseOrder_Customer),
                this.MakeGridHeader(x => x.PurchaseOrder_OrderNo),
                this.MakeGridHeader(x => x.PurchaseOrder_CustomerOrderNo),
                this.MakeGridHeader(x => x.PurchaseOrder_Merchandiser),
                this.MakeGridHeader(x => x.PurchaseOrder_OrderType),
                this.MakeGridHeader(x => x.PurchaseOrder_Product),
                this.MakeGridHeader(x => x.PurchaseOrder_Light),
                this.MakeGridHeader(x => x.PurchaseOrder_TotalMeters),
                this.MakeGridHeader(x => x.PurchaseOrder_TotalYards),
                this.MakeGridHeader(x => x.PurchaseOrder_TotalWeight),
                this.MakeGridHeader(x => x.AccountUnit),
                //前端要显示列,这里必须先定义,SetTitle可以不需要,前端组件中优先显示制定名称
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        
        public override IOrderedQueryable<PurchaseOrder_View> GetSearchQuery()
        {
            IOrderedQueryable<PurchaseOrder_View> q;
            List<string> roleids = base.Wtm.LoginUserInfo.Roles?.Select((SimpleRole x) => "r:" + x.ID).ToList();
            List<string> groupids = base.Wtm.LoginUserInfo.Groups?.Select((SimpleGroup x) => "g:" + x.ID).ToList();
            string tenantCode = base.Wtm.LoginUserInfo.TenantCode;

            //var l = base.Wtm.LoginUserInfo.Roles.Where(x => x.RoleCode.StartsWith("8") || x.RoleCode.Contains("001")).ToList();
            bool hasMatchingRole = base.Wtm.LoginUserInfo.Roles.Any(x => x.RoleCode.StartsWith("8") || x.RoleCode.Contains("001"));

            var query = DC.Set<PurchaseOrder>()
                .CheckEqual(Searcher.AuditStatus, x => x.AuditStatus)
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckBetween(Searcher.DeliveryDate?.GetStartTime(), Searcher.DeliveryDate?.GetEndTime(), x => x.DeliveryDate, includeMax: false)
                .CheckEqual(Searcher.CustomerId, x => x.CustomerId)
                //Searcher.OrderId必须设置成可空类型,否则查询为空
                .CheckEqual(Searcher.OrderId, x => x.ID)
                .CheckContain(Searcher.CustomerOrderNo, x => x.CustomerOrderNo)
                //.CheckContain(Searcher.Merchandiser, x=>x.Merchandiser.ContactName)
                .CheckEqual(Searcher.OrderType, x => x.OrderType)
                .CheckEqual(Searcher.ProductId, x => x.ProductId)
                .CheckEqual(Searcher.Light, x => x.Light);

            if (hasMatchingRole)
            {


                 q = query.Select(x => new PurchaseOrder_View
                {
                    ID = x.ID,
                    PurchaseOrder_CreateDate = x.CreateDate,
                    PurchaseOrder_Customer = x.Customer.CompanyName,
                    PurchaseOrder_OrderNo = x.OrderNo,
                    PurchaseOrder_CustomerOrderNo = x.CustomerOrderNo,
                    PurchaseOrder_Merchandiser = x.Merchandiser.ContactName,
                    PurchaseOrder_OrderType = x.OrderType,
                    PurchaseOrder_Product = x.Product.ProductName,
                    PurchaseOrder_Light = x.Light,
                    PurchaseOrder_TotalMeters = x.TotalMeters,
                    PurchaseOrder_TotalYards = x.TotalYards,
                    PurchaseOrder_TotalWeight = x.TotalWeight,
                    AccountUnit = x.AccountUnit,
                    AuditStatus = x.AuditStatus,
                })
                    .OrderByDescending(x => x.PurchaseOrder_CreateDate);
            }
            else
            {
                q = query.Select(x => new PurchaseOrder_View
                {
                    ID = x.ID,
                    PurchaseOrder_CreateDate = x.CreateDate,
                    PurchaseOrder_Customer = x.Customer.CompanyName,
                    PurchaseOrder_OrderNo = x.OrderNo,
                    PurchaseOrder_CustomerOrderNo = x.CustomerOrderNo,
                    PurchaseOrder_Merchandiser = x.Merchandiser.ContactName,
                    PurchaseOrder_OrderType = x.OrderType,
                    PurchaseOrder_Product = x.Product.ProductName,
                    PurchaseOrder_Light = x.Light,
                    PurchaseOrder_TotalMeters = x.TotalMeters,
                    PurchaseOrder_TotalYards = x.TotalYards,
                    PurchaseOrder_TotalWeight = x.TotalWeight,
                    AccountUnit = x.AccountUnit,
                    AuditStatus = x.AuditStatus,
                })
                    .OrderByDescending(x => x.PurchaseOrder_CreateDate);
            }

            return q;
        }

    }
    public class PurchaseOrder_View: PurchaseOrder
    {
        [Display(Name = "_CreateDate")]
        public DateTime? PurchaseOrder_CreateDate { get; set; }
        [Display(Name = "_Customer")]
        public string PurchaseOrder_Customer { get; set; }
        [Display(Name = "_OrderNo")]
        public string PurchaseOrder_OrderNo { get; set; }
        [Display(Name = "_CustomerOrderNo")]
        public string PurchaseOrder_CustomerOrderNo { get; set; }
        [Display(Name = "_Merchandiser")]
        public string PurchaseOrder_Merchandiser { get; set; }
        [Display(Name = "_OrderType")]
        public OrderTypeEnum? PurchaseOrder_OrderType { get; set; }
        [Display(Name = "_Product")]
        public string PurchaseOrder_Product { get; set; }
        [Display(Name = "_Light")]
        public LightEnum? PurchaseOrder_Light { get; set; }
        [Display(Name = "_TotalMeters")]
        public decimal? PurchaseOrder_TotalMeters { get; set; }
        [Display(Name = "_TotalYards")]
        public decimal? PurchaseOrder_TotalYards { get; set; }
        [Display(Name = "_TotalWeight")]
        public decimal? PurchaseOrder_TotalWeight { get; set; }
        
    }

}