using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;

using TEX.ViewModel.Models.CompanyVMs;
using TEX.ViewModel.Models.ProductVMs;
using TEX.ViewModel.Models.OrderDetailVMs;
using TEX.Model.Models;

namespace TEX.ViewModel.Models.PurchaseOrderVMs
{
    public partial class PurchaseOrderVM : BaseCRUDVM<PurchaseOrder>
    {
        

        public PurchaseOrderVM()
        {
            
            SetInclude(x => x.Customer);
            SetInclude(x => x.Product);
            SetInclude(x => x.Merchandiser);
            SetInclude(x => x.OrderDetailList);

        }

        protected override void InitVM()
        {
            //新建订单不走这里
        }

        //设置唯一字段检查
        public override DuplicatedInfo<PurchaseOrder> SetDuplicatedCheck()
        {
            var rv = CreateFieldsInfo(SimpleField(x => x.OrderNo));
            return rv;
        }

        public override async Task DoAddAsync()        
        {
            
            await base.DoAddAsync();

        }

        public override async Task DoEditAsync(bool updateAllFields = false)
        {
            await base.DoEditAsync();
        }

        public override async Task DoDeleteAsync()
        {
            await base.DoDeleteAsync();

        }

        public async Task UpdateAuditField(Guid id)
        {
            
                // 根据id查询Order表中的一条记录
                var order = await DC.Set<PurchaseOrder>().FirstOrDefaultAsync(o => o.ID == id);

                if (order != null)
                {
                    // 将Audit字段的值设置为1
                    order.AuditStatus = AuditStatusEnum.AuditedApproved;

                    // 提交更改到数据库
                    await DC.SaveChangesAsync();
                }
            
        }
    }
}
