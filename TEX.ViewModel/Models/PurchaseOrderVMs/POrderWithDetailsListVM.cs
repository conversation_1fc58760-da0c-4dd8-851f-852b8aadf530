using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;
using Microsoft.EntityFrameworkCore;
using NPOI.POIFS.Properties;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace TEX.ViewModel.Models.PurchaseOrderVMs
{
    public partial class PurchaseOrderWithDetailsListVM : BasePagedListVM<PurchaseOrderWithDetails_TreeView, PurchaseOrderSearcher>
    {



        public override IOrderedQueryable<PurchaseOrderWithDetails_TreeView> GetSearchQuery()
        {

            var pOrders = DC.Set<PurchaseOrder>().OrderByDescending(x => x.CreateDate).Select(pOrder => new PurchaseOrderWithDetails_TreeView
            {
                ID = pOrder.ID,
                Text = pOrder.OrderNo + " - " + pOrder.Product.ProductName,
            }).ToList();

            var pOrderDetails = DC.Set<OrderDetail>().Select(d => new PurchaseOrderWithDetails_TreeView
            {
                ID = d.ID,
                ParentId = d.PurchaseOrderId,
                Text = d.Color,
            }).ToList();


            var query = pOrders.Union(pOrderDetails).AsQueryable().OrderByDescending(x => x.Text);


            return query;
        }

    }
    public class PurchaseOrderWithDetails_TreeView : TopBasePoco
    {
        //public Guid ID { get; set; }
        public Guid? ParentId { get; set; }
        public string Text { get; set; }
        public string Icon { get; set; } = "fa-solid fa-font-awesome";
        public bool IsActive { get; set; }

    }


}