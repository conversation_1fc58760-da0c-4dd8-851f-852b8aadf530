
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;



namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailPurchaseOrderDetailListVM1 : BasePagedListVM<OrderDetail, OrderDetailDetailSearcher1>
    {
        
        protected override List<GridAction> InitGridAction()
        {
            return new List<GridAction>
            {
                this.MakeStandardAction("OrderDetail", GridActionStandardTypesEnum.AddRow, "新建","", dialogWidth: 800),
                this.MakeStandardAction("OrderDetail", GridActionStandardTypesEnum.RemoveRow, "删除","", dialogWidth: 800),
            };
        }
 
        protected override IEnumerable<IGridColumn<OrderDetail>> InitGridHeader()
        {
            return new List<GridColumn<OrderDetail>>{
                
                this.MakeGridHeaderAction(width: 200)
            };
        }

        
        public override IOrderedQueryable<OrderDetail> GetSearchQuery()
        {
                
            var id = (Guid?)Searcher.PurchaseOrderId.ConvertValue(typeof(Guid?));
            if (id == null)
                return new List<OrderDetail>().AsQueryable().OrderBy(x => x.ID);
            var query = DC.Set<OrderDetail>()
                .Where(x => id == x.PurchaseOrderId)

                .OrderBy(x => x.ID);
            return query;
        }

    }

    public partial class OrderDetailDetailSearcher1 : BaseSearcher
    {
        
        [Display(Name = "_PurchaseOrder")]
        public string PurchaseOrderId { get; set; }
    }

}

