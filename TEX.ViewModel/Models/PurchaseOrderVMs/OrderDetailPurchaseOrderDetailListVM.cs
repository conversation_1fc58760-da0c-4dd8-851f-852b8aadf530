
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;



namespace TEX.ViewModel.Models.OrderDetailVMs
{
    public partial class OrderDetailPurchaseOrderDetailListVM : BasePagedListVM<OrderDetail, OrderDetailDetailSearcher>
    {
        
        protected override List<GridAction> InitGridAction()
        {
            return new List<GridAction>
            {
                this.MakeStandardAction("OrderDetail", GridActionStandardTypesEnum.AddRow, "新建","", dialogWidth: 800),
                this.MakeStandardAction("OrderDetail", GridActionStandardTypesEnum.RemoveRow, "删除","", dialogWidth: 800),
            };
        }
 
        protected override IEnumerable<IGridColumn<OrderDetail>> InitGridHeader()
        {
            return new List<GridColumn<OrderDetail>>{
                
                this.MakeGridHeader(x => x.Color).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.颜色"].Value),
                this.MakeGridHeader(x => x.ColorCode).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.色号"].Value),
                this.MakeGridHeader(x => x.Meters).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.米数"].Value),
                this.MakeGridHeader(x => x.KG).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.重量"].Value),
                this.MakeGridHeader(x => x.Yards).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.码数"].Value),
                this.MakeGridHeader(x => x.Price).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.单价"].Value),
                this.MakeGridHeader(x => x.Amount).SetEditType(EditTypeEnum.TextBox).SetTitle(@Localizer["Page.金额"].Value),

                this.MakeGridHeaderAction(width: 200)
            };
        }

        
        public override IOrderedQueryable<OrderDetail> GetSearchQuery()
        {
                
            var id = (Guid?)Searcher.PurchaseOrderId.ConvertValue(typeof(Guid?));
            if (id == null)
                return new List<OrderDetail>().AsQueryable().OrderBy(x => x.ID);
            var query = DC.Set<OrderDetail>()
                .Where(x => id == x.PurchaseOrderId)

                .OrderBy(x => x.ID);
            return query;
        }

    }

    public partial class OrderDetailDetailSearcher : BaseSearcher
    {
        
        [Display(Name = "_PurchaseOrder")]
        public string PurchaseOrderId { get; set; }
    }

}

