
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.PurchaseOrderVMs
{
    public partial class PurchaseOrderTemplateVM : BaseTemplateVM
    {
        
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.CreateDate, true);
        [Display(Name = "_DeliveryDate")]
        public ExcelPropety DeliveryDate_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.DeliveryDate, true);
        [Display(Name = "_Customer")]
        public ExcelPropety Customer_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.CustomerId);
        [Display(Name = "_OrderNo")]
        public ExcelPropety OrderNo_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.OrderNo);
        [Display(Name = "_CustomerOrderNo")]
        public ExcelPropety CustomerOrderNo_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.CustomerOrderNo);
        [Display(Name = "_Merchandiser")]
        public ExcelPropety Merchandiser_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.Merchandiser);
        [Display(Name = "_OrderType")]
        public ExcelPropety OrderType_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.OrderType);
        [Display(Name = "_Product")]
        public ExcelPropety Product_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.ProductId);
        [Display(Name = "_Light")]
        public ExcelPropety Light_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.Light);
        [Display(Name = "_Light2")]
        public ExcelPropety Light2_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.Light2);
        [Display(Name = "_TotalMeters")]
        public ExcelPropety TotalMeters_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.TotalMeters);
        [Display(Name = "_TotalYards")]
        public ExcelPropety TotalYards_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.TotalYards);
        [Display(Name = "_TotalWeight")]
        public ExcelPropety TotalWeight_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.TotalWeight);
        [Display(Name = "_TotalAmount")]
        public ExcelPropety TotalAmount_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.TotalAmount);
        [Display(Name = "_CreateTime")]
        public ExcelPropety CreateTime_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.CreateTime, true);
        [Display(Name = "_UpdateTime")]
        public ExcelPropety UpdateTime_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.UpdateTime, true);
        [Display(Name = "_CreateBy")]
        public ExcelPropety CreateBy_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.CreateBy);
        [Display(Name = "_UpdateBy")]
        public ExcelPropety UpdateBy_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.UpdateBy);
        [Display(Name = "_IsValid")]
        public ExcelPropety IsValid_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.IsValid);
        [Display(Name = "_TenantCode")]
        public ExcelPropety TenantCode_Excel = ExcelPropety.CreateProperty<PurchaseOrder>(x => x.TenantCode);

	    protected override void InitVM()
        {
            
            Customer_Excel.DataType = ColumnDataType.ComboBox;
            Customer_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.TenantCode.ToString());
            Product_Excel.DataType = ColumnDataType.ComboBox;
            Product_Excel.ListItems = DC.Set<Product>().GetSelectListItems(Wtm, y => y.TenantCode.ToString());

        }

    }

    public class PurchaseOrderImportVM : BaseImportVM<PurchaseOrderTemplateVM, PurchaseOrder>
    {
            //import

    }

}