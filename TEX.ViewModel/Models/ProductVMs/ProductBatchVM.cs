
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.ProductVMs
{
    public partial class ProductBatchVM : BaseBatchVM<Product, Product_BatchEdit>
    {
        public ProductBatchVM()
        {
            ListVM = new ProductListVM();
            LinkedVM = new Product_BatchEdit();
        }

        public override bool DoBatchEdit()
        {
            
            return base.DoBatchEdit();
        }

        //重写CheckIfCanDelete,检查是否作为外键已使用,如使用则不能删除
        protected override bool CheckIfCanDelete(object id, out string errorMessage)
        {
            var erc = new EntityReferenceCheckerVM();

            Guid guid = Guid.Parse(id.ToString());
            var o = DC.Set<PurchaseOrder>().Where(x => x.ProductId == guid).Any();
            
            var g = DC.Set<DyeingPlan>().Where(x => x.GreigeVenderId == guid).Any();

            if (o || g)
            {
                errorMessage = "数据已被使用,不允许删除!";
                return false;
            }
            else
            {
                return base.CheckIfCanDelete(id, out errorMessage);
            }
        }
    }
    
    /// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class Product_BatchEdit : BaseVM
    {

        
        [Display(Name = "_ProductName")]
        public string ProductName { get; set; }
        [Display(Name = "_Category")]
        public FabricCategoryEnum? Category { get; set; }
        [Display(Name = "_Contents")]
        public string Contents { get; set; }
        [Display(Name = "_Spec")]
        public string Spec { get; set; }
        [Display(Name = "_GSM")]
        public int? GSM { get; set; }
        [Display(Name = "_Width")]
        public int? Width { get; set; }
        [Display(Name = "_PileLength")]
        public int? PileLength { get; set; }
        [Display(Name = "_DyeingProcess")]
        public string DyeingProcess { get; set; }
        [Display(Name = "_KnittingProcess")]
        public string KnittingProcess { get; set; }
        [Display(Name = "_FinishingProcess")]
        public string FinishingProcess { get; set; }

        protected override void InitVM()
        {
           
        }
    }

}