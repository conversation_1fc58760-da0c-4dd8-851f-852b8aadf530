
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.ProductVMs
{
    public partial class ProductTemplateVM : BaseTemplateVM
    {
        
        [Display(Name = "_ProductCode")]
        public ExcelPropety ProductCode_Excel = ExcelPropety.CreateProperty<Product>(x => x.ProductCode);
        [Display(Name = "_ProductName")]
        public ExcelPropety ProductName_Excel = ExcelPropety.CreateProperty<Product>(x => x.ProductName);
        [Display(Name = "_Category")]
        public ExcelPropety Category_Excel = ExcelPropety.CreateProperty<Product>(x => x.Category);
        [Display(Name = "_Contents")]
        public ExcelPropety Contents_Excel = ExcelPropety.CreateProperty<Product>(x => x.Contents);
        [Display(Name = "_Spec")]
        public ExcelPropety Spec_Excel = ExcelPropety.CreateProperty<Product>(x => x.Spec);
        [Display(Name = "_GSM")]
        public ExcelPropety GSM_Excel = ExcelPropety.CreateProperty<Product>(x => x.GSM);
        [Display(Name = "_Width")]
        public ExcelPropety Width_Excel = ExcelPropety.CreateProperty<Product>(x => x.Width);
        [Display(Name = "_PileLength")]
        public ExcelPropety PileLength_Excel = ExcelPropety.CreateProperty<Product>(x => x.PileLength);
        [Display(Name = "_DyeingProcess")]
        public ExcelPropety DyeingProcess_Excel = ExcelPropety.CreateProperty<Product>(x => x.DyeingProcess);
        [Display(Name = "_KnittingProcess")]
        public ExcelPropety KnittingProcess_Excel = ExcelPropety.CreateProperty<Product>(x => x.KnittingProcess);
        [Display(Name = "_FinishingProcess")]
        public ExcelPropety FinishingProcess_Excel = ExcelPropety.CreateProperty<Product>(x => x.FinishingProcess);
        [Display(Name = "_Parent")]
        public ExcelPropety Parent_Excel = ExcelPropety.CreateProperty<Product>(x => x.ParentId);
      

	    protected override void InitVM()
        {
            
            Parent_Excel.DataType = ColumnDataType.ComboBox;
            Parent_Excel.ListItems = DC.Set<Product>().GetSelectListItems(Wtm, y => y.ProductName);

        }

    }

    public class ProductImportVM : BaseImportVM<ProductTemplateVM, Product>
    {
            //import

    }

}