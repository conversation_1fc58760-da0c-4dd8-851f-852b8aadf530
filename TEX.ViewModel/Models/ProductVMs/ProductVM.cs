using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;

using TEX.ViewModel.Models.ProductVMs;
using TEX.Model.Models;
using TEX.Model;
namespace TEX.ViewModel.Models.ProductVMs
{
    public partial class ProductVM : BaseCRUDVM<Product>
    {
        

        public ProductVM()
        {
            
            SetInclude(x => x.Photo);
            SetInclude(x => x.Parent);

        }

        protected override void InitVM()
        {
            

        }

        public override DuplicatedInfo<Product> SetDuplicatedCheck()
        {
            var rv = CreateFieldsInfo(SimpleField(x => x.ProductCode));
            return rv;

        }

        public override async Task DoAddAsync()        
        {
            
            await base.DoAddAsync();

        }

        public override async Task DoEditAsync(bool updateAllFields = false)
        {
            
            await base.DoEditAsync();

        }

        public override async Task DoDeleteAsync()
        {
            await base.DoDeleteAsync();

        }
    }
}
