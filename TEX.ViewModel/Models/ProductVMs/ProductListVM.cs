using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.ProductVMs
{
    public partial class ProductListVM : BasePagedListVM<Product_View, ProductSearcher>
    {
        
        protected override IEnumerable<IGridColumn<Product_View>> InitGridHeader()
        {
            return new List<GridColumn<Product_View>>{
                
                this.MakeGridHeader(x => x.Product_ProductCode),
                this.MakeGridHeader(x => x.Product_ProductName),
                this.MakeGridHeader(x => x.Product_Category),
                this.MakeGridHeader(x => x.Product_Contents),
                this.MakeGridHeader(x => x.Product_Spec),
                this.MakeGridHeader(x => x.Product_GSM),
                this.MakeGridHeader(x => x.Product_Width),          
                this.MakeGridHeader(x => x.Product_AuditStatus),          
                this.MakeGridHeader(x => x.Product_Parent),          
                this.MakeGridHeaderAction(width: 200)
            };
        }

        
        public override IOrderedQueryable<Product_View> GetSearchQuery()
        {
            var query = DC.Set<Product>()
                
                .CheckContain(Searcher.ProductCode, x=>x.ProductCode)
                .CheckContain(Searcher.ProductName, x=>x.ProductName)
                .CheckEqual(Searcher.CategoryId, x=>x.CategoryId)
                .CheckContain(Searcher.Contents, x=>x.Contents)
                .CheckContain(Searcher.Spec, x=>x.Spec)
                .CheckEqual(Searcher.GSM, x=>x.GSM)
                .CheckEqual(Searcher.GSM, x=>x.GSM)
                //.CheckEqual(Searcher.AuditStatus, x => x.AuditStatus)
                .CheckEqual(Searcher.Width, x=>x.Width)
                .Select(x => new Product_View
                {
				    ID = x.ID,
                    
                    Product_ProductCode = x.ProductCode,
                    Product_ProductName = x.ProductName,
                    Product_Category = x.CategoryId,
                    Product_Contents = x.Contents,
                    Product_Spec = x.Spec,
                    Product_GSM = x.GSM,
                    Product_Width = x.Width,
                    Product_AuditStatus = x.AuditStatus,
                    Product_Parent=x.Parent.ProductName,

                })
                .OrderBy(x => x.ID);
            return query;
        }

    }
    public class Product_View: Product
    {
        
        public string Product_ProductCode { get; set; }
        public string Product_ProductName { get; set; }
        public int Product_Category { get; set; }
        public string Product_Contents { get; set; }
        public string Product_Spec { get; set; }
        public int? Product_GSM { get; set; }
        public int? Product_Width { get; set; }
        public int? Product_PileLength { get; set; }
        public string Product_DyeingProcess { get; set; }
        public string Product_KnittingProcess { get; set; }
        public string Product_FinishingProcess { get; set; }
        public string Product_Parent { get; set; }
        public DateTime? Product_CreateTime { get; set; }
        public DateTime? Product_UpdateTime { get; set; }
        public string Product_CreateBy { get; set; }
        public string Product_UpdateBy { get; set; }
        public bool Product_IsValid { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum Product_AuditStatus { get; set; }



    }

}