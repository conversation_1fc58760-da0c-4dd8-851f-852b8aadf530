
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;
namespace TEX.ViewModel.Models.ProductVMs
{
    public partial class ProductSearcher : BaseSearcher
    {
        
        [Display(Name = "_ProductCode")]
        public string ProductCode { get; set; }
        [Display(Name = "_ProductName")]
        public string ProductName { get; set; }
        [Display(Name = "_Category")]
        public int? CategoryId { get; set; }
        [Display(Name = "_Contents")]
        public string Contents { get; set; }
        [Display(Name = "_Spec")]
        public string Spec { get; set; }
        [Display(Name = "_GSM")]
        public int? GSM { get; set; }
        [Display(Name = "_Width")]
        public int? Width { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum AuditStatus { get; set; }

        protected override void InitVM()
        {
            
        }
    }

}