using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictItemVMs
{
    public partial class DictItemListVM : BasePagedListVM<DictItem_View, DictItemSearcher>
    {

        protected override IEnumerable<IGridColumn<DictItem_View>> InitGridHeader()
        {
            return new List<GridColumn<DictItem_View>>{
                this.MakeGridHeader(x => x.Name_view),
                this.MakeGridHeader(x => x.ItemName),
                this.MakeGridHeader(x => x.Description),
                this.MakeGridHeader(x => x.DictOrder),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<DictItem_View> GetSearchQuery()
        {
            var query = DC.Set<DictItem>()
                .CheckEqual(Searcher.DictTypeId, x=>x.DictTypeId)
                .CheckContain(Searcher.ItemName, x=>x.ItemName)
                .CheckContain(Searcher.Description, x=>x.Description)
                .Select(x => new DictItem_View
                {
				    ID = x.ID,
                    Name_view = x.DictType.Name,
                    ItemName = x.ItemName,
                    Description = x.Description,
                    DictOrder = x.DictOrder,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class DictItem_View : DictItem{
        [Display(Name = "_DictName")]
        public String Name_view { get; set; }

    }
}
