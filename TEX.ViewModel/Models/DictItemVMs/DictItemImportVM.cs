using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictItemVMs
{
    public partial class DictItemTemplateVM : BaseTemplateVM
    {
        public ExcelPropety DictType_Excel = ExcelPropety.CreateProperty<DictItem>(x => x.DictTypeId);
        [Display(Name = "_DictItemName")]
        public ExcelPropety ItemName_Excel = ExcelPropety.CreateProperty<DictItem>(x => x.ItemName);
        [Display(Name = "_Description")]
        public ExcelPropety Description_Excel = ExcelPropety.CreateProperty<DictItem>(x => x.Description);
        [Display(Name = "_DictItemOrder")]
        public ExcelPropety OrderNo_Excel = ExcelPropety.CreateProperty<DictItem>(x => x.DictOrder);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<DictItem>(x => x.Remark);

	    protected override void InitVM()
        {
            DictType_Excel.DataType = ColumnDataType.ComboBox;
            DictType_Excel.ListItems = DC.Set<DictType>().GetSelectListItems(Wtm, y => y.Name);
        }

    }

    public class DictItemImportVM : BaseImportVM<DictItemTemplateVM, DictItem>
    {

    }

}
