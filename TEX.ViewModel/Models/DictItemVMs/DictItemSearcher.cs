using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictItemVMs
{
    public partial class DictItemSearcher : BaseSearcher
    {
        public int? DictTypeId { get; set; }
        [Display(Name = "_DictItemName")]
        public String ItemName { get; set; }
        [Display(Name = "_Description")]
        public String Description { get; set; }

        protected override void InitVM()
        {
        }

    }
}
