using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictItemVMs
{
    public partial class DictItemBatchVM : BaseBatchVM<DictItem, DictItem_BatchEdit>
    {
        public DictItemBatchVM()
        {
            ListVM = new DictItemListVM();
            LinkedVM = new DictItem_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class DictItem_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
