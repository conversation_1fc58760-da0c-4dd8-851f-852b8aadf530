
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.CompanyVMs
{
    public partial class CompanyBatchVM : BaseBatchVM<Company, Company_BatchEdit>
    {
        public CompanyBatchVM()
        {
            ListVM = new CompanyListVM();
            LinkedVM = new Company_BatchEdit();
        }

        public override bool DoBatchEdit()
        {
            
            return base.DoBatchEdit();
        }

        //重写CheckIfCanDelete,检查是否作为外键已使用,如使用则不能删除
        protected override bool CheckIfCanDelete(object id, out string errorMessage)
        {
            var erc = new EntityReferenceCheckerVM();

            Guid guid= Guid.Parse(id.ToString());
            var o=DC.Set<PurchaseOrder>().Where(x=>x.CustomerId== guid).Any();
            var f=DC.Set<DyeingPlan>().Where(x=>x.FinishingFactoryId== guid).Any();
            var g=DC.Set<DyeingPlan>().Where(x=>x.GreigeVenderId== guid).Any();

            if(o||f||g) { 
                errorMessage = "数据已被使用,不允许删除!";
                return false;
            }
            else {
            return base.CheckIfCanDelete(id, out errorMessage);
            }
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class Company_BatchEdit : BaseVM
    {

        
        [Display(Name = "_CompanyFullName")]
        public string CompanyFullName { get; set; }
        [Display(Name = "_CompanyType")]
        public CompanyTypeEnum? CompanyType { get; set; }
        [Display(Name = "_Relationship")]
        public RelationshipEnum? Relationship { get; set; }
        [Display(Name = "_ContactPhone")]
        public string ContactPhone { get; set; }
        [Display(Name = "_Adress")]
        public string Adress { get; set; }
        [Display(Name = "_TaxNO")]
        public string TaxNO { get; set; }
        [Display(Name = "_InvoiceInfo")]
        public string InvoiceInfo { get; set; }

        protected override void InitVM()
        {
           
        }
    }

}