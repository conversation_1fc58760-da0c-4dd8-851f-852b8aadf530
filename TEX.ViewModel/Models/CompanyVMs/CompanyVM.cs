using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;

using TEX.Model.Models;
using TEX.Model;
namespace TEX.ViewModel.Models.CompanyVMs
{
    public partial class CompanyVM : BaseCRUDVM<Company>
    {
        
        public CompanyVM()
        {
            
        }

        protected override void InitVM()
        {
            
        }

        public override DuplicatedInfo<Company> SetDuplicatedCheck()
        {
            var rv = CreateFieldsInfo(SimpleField(x => x.CompanyCode));
            rv.AddGroup(SimpleField(x => x.CompanyName));
            return rv;

        }

        public override async Task DoAddAsync()        
        {
            
            await base.DoAddAsync();

        }

        public override async Task DoEditAsync(bool updateAllFields = false)
        {
            
            await base.DoEditAsync();

        }

        public override async Task DoDeleteAsync()
        {
            await base.DoDeleteAsync();

        }
    }
}
