
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.CompanyVMs
{
    public partial class CompanyTemplateVM : BaseTemplateVM
    {
        
        [Display(Name = "_CompanyCode")]
        public ExcelPropety CompanyCode_Excel = ExcelPropety.CreateProperty<Company>(x => x.CompanyCode);
        [Display(Name = "_CompanyName")]
        public ExcelPropety CompanyName_Excel = ExcelPropety.CreateProperty<Company>(x => x.CompanyName);
        [Display(Name = "_CompanyFullName")]
        public ExcelPropety CompanyFullName_Excel = ExcelPropety.CreateProperty<Company>(x => x.CompanyFullName);
        [Display(Name = "_CompanyType")]
        public ExcelPropety CompanyType_Excel = ExcelPropety.CreateProperty<Company>(x => x.CompanyType);
        [Display(Name = "_Relationship")]
        public ExcelPropety Relationship_Excel = ExcelPropety.CreateProperty<Company>(x => x.Relationship);
        [Display(Name = "_ContactPhone")]
        public ExcelPropety ContactPhone_Excel = ExcelPropety.CreateProperty<Company>(x => x.ContactPhone);
        [Display(Name = "_Adress")]
        public ExcelPropety Adress_Excel = ExcelPropety.CreateProperty<Company>(x => x.Adress);
        [Display(Name = "_TaxNO")]
        public ExcelPropety TaxNO_Excel = ExcelPropety.CreateProperty<Company>(x => x.TaxNO);
        [Display(Name = "_InvoiceInfo")]
        public ExcelPropety InvoiceInfo_Excel = ExcelPropety.CreateProperty<Company>(x => x.InvoiceInfo);
        [Display(Name = "_CreateTime")]
        public ExcelPropety CreateTime_Excel = ExcelPropety.CreateProperty<Company>(x => x.CreateTime, true);
        [Display(Name = "_UpdateTime")]
        public ExcelPropety UpdateTime_Excel = ExcelPropety.CreateProperty<Company>(x => x.UpdateTime, true);
        [Display(Name = "_CreateBy")]
        public ExcelPropety CreateBy_Excel = ExcelPropety.CreateProperty<Company>(x => x.CreateBy);
        [Display(Name = "_UpdateBy")]
        public ExcelPropety UpdateBy_Excel = ExcelPropety.CreateProperty<Company>(x => x.UpdateBy);
        [Display(Name = "_IsValid")]
        public ExcelPropety IsValid_Excel = ExcelPropety.CreateProperty<Company>(x => x.IsValid);
        [Display(Name = "_TenantCode")]
        public ExcelPropety TenantCode_Excel = ExcelPropety.CreateProperty<Company>(x => x.TenantCode);

	    protected override void InitVM()
        {
            
        }

    }

    public class CompanyImportVM : BaseImportVM<CompanyTemplateVM, Company>
    {
            //import

    }

}