
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;
namespace TEX.ViewModel.Models.CompanyVMs
{
    public partial class CompanySearcher : BaseSearcher
    {
        
        [Display(Name = "_CompanyCode")]
        public string CompanyCode { get; set; }
        [Display(Name = "_CompanyName")]
        public string CompanyName { get; set; }
        [Display(Name = "_CompanyType")]
        public CompanyTypeEnum? CompanyType { get; set; }
        [Display(Name = "_Relationship")]
        public RelationshipEnum? Relationship { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum AuditStatus { get; set; }
        protected override void InitVM()
        {
            
        }
    }

}