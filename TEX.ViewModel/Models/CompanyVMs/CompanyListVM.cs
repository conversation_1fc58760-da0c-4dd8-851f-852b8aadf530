using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;
using TEX.Model;

namespace TEX.ViewModel.Models.CompanyVMs
{
    public partial class CompanyListVM : BasePagedListVM<Company_View, CompanySearcher>
    {
        
        protected override IEnumerable<IGridColumn<Company_View>> InitGridHeader()
        {
            return new List<GridColumn<Company_View>>{
                
                this.MakeGridHeader(x => x.Company_CompanyCode).SetTitle(@Localizer["Page.公司代码"].Value),
                this.MakeGridHeader(x => x.Company_CompanyName).SetTitle(@Localizer["Page.公司名称"].Value),
                this.MakeGridHeader(x => x.Company_CompanyFullName).SetTitle(@Localizer["Page.公司全称"].Value),
                this.MakeGridHeader(x => x.Company_CompanyType).SetTitle(@Localizer["Page.公司类型"].Value),
                this.MakeGridHeader(x => x.Company_Relationship).SetTitle(@Localizer["Page.往来关系"].Value),
                this.MakeGridHeader(x => x.Company_ContactPhone).SetTitle(@Localizer["Page.电话"].Value),
                this.MakeGridHeader(x => x.Company_Adress).SetTitle(@Localizer["_Admin.Address"].Value),
                this.MakeGridHeader(x => x.Company_TaxNO).SetTitle(@Localizer["Page.税号"].Value),
                this.MakeGridHeader(x => x.Company_InvoiceInfo).SetTitle(@Localizer["Page.开票资料"].Value),
                this.MakeGridHeader(x => x.Company_AuditStatus),

                this.MakeGridHeaderAction(width: 200)
            };
        }

        
        public override IOrderedQueryable<Company_View> GetSearchQuery()
        {
            var query = DC.Set<Company>()
                
                .CheckContain(Searcher.CompanyCode, x=>x.CompanyCode)
                .CheckContain(Searcher.CompanyName, x=>x.CompanyName)
                .CheckEqual(Searcher.CompanyType, x=>x.CompanyType)
                .CheckEqual(Searcher.Relationship, x=>x.Relationship)
                //.CheckEqual(Searcher.AuditStatus, x => x.AuditStatus)
                .Select(x => new Company_View
                {
				    ID = x.ID,
                    
                    Company_CompanyCode = x.CompanyCode,
                    Company_CompanyName = x.CompanyName,
                    Company_CompanyFullName = x.CompanyFullName,
                    Company_CompanyType = x.CompanyType,
                    Company_Relationship = x.Relationship,
                    Company_ContactPhone = x.ContactPhone,
                    Company_Adress = x.Adress,
                    Company_TaxNO = x.TaxNO,
                    Company_InvoiceInfo = x.InvoiceInfo,
                    Company_AuditStatus = x.AuditStatus,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }
    public class Company_View: Company
    {
        
        public string Company_CompanyCode { get; set; }
        public string Company_CompanyName { get; set; }
        public string Company_CompanyFullName { get; set; }
        public CompanyTypeEnum? Company_CompanyType { get; set; }
        public RelationshipEnum? Company_Relationship { get; set; }
        public string Company_ContactPhone { get; set; }
        public string Company_Adress { get; set; }
        public string Company_TaxNO { get; set; }
        public string Company_InvoiceInfo { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum Company_AuditStatus { get; set; }

    }

}