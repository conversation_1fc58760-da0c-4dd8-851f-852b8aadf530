using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictTypeVMs
{
    public partial class DictTypeTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_DictName")]
        public ExcelPropety Name_Excel = ExcelPropety.CreateProperty<DictType>(x => x.Name);
        [Display(Name = "_Description")]
        public ExcelPropety Description_Excel = ExcelPropety.CreateProperty<DictType>(x => x.Description);
        [Display(Name = "_DictOrder")]
        public ExcelPropety DictOrder_Excel = ExcelPropety.CreateProperty<DictType>(x => x.DictOrder);

	    protected override void InitVM()
        {
        }

    }

    public class DictTypeImportVM : BaseImportVM<DictTypeTemplateVM, DictType>
    {

    }

}
