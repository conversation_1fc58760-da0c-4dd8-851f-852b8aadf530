using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictTypeVMs
{
    public partial class DictTypeBatchVM : BaseBatchVM<DictType, DictType_BatchEdit>
    {
        public DictTypeBatchVM()
        {
            ListVM = new DictTypeListVM();
            LinkedVM = new DictType_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class DictType_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
