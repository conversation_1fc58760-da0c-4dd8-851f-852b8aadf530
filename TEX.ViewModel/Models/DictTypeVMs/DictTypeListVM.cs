using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictTypeVMs
{
    public partial class DictTypeListVM : BasePagedListVM<DictType_View, DictTypeSearcher>
    {

        protected override IEnumerable<IGridColumn<DictType_View>> InitGridHeader()
        {
            return new List<GridColumn<DictType_View>>{
                this.MakeGridHeader(x => x.Name),
                this.MakeGridHeader(x => x.Description),
                this.MakeGridHeader(x => x.DictOrder),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<DictType_View> GetSearchQuery()
        {
            var query = DC.Set<DictType>()
                .CheckContain(Searcher.Name, x=>x.Name)
                .CheckContain(Searcher.Description, x=>x.Description)
                .Select(x => new DictType_View
                {
				    ID = x.ID,
                    Name = x.Name,
                    Description = x.Description,
                    DictOrder = x.DictOrder,
                })
                .OrderBy(x => x.DictOrder);
            return query;
        }

    }

    public class DictType_View : DictType{

    }
}
