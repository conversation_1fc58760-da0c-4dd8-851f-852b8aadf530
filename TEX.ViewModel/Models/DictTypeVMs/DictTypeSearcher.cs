using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Models.DictTypeVMs
{
    public partial class DictTypeSearcher : BaseSearcher
    {
        [Display(Name = "_DictName")]
        public String Name { get; set; }
        [Display(Name = "_Description")]
        public String Description { get; set; }

        protected override void InitVM()
        {
        }

    }
}
