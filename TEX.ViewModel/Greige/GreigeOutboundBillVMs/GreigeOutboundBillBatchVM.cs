using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeOutboundBillVMs
{
    public partial class GreigeOutboundBillBatchVM : BaseBatchVM<GreigeOutboundBill, GreigeOutboundBill_BatchEdit>
    {
        public GreigeOutboundBillBatchVM()
        {
            ListVM = new GreigeOutboundBillListVM();
            LinkedVM = new GreigeOutboundBill_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class GreigeOutboundBill_BatchEdit : BaseVM
    {
        [Display(Name = "出库日期")]
        public DateTime? OutboundDate { get; set; }
        [Display(Name = "出库用途")]
        public String Purpose { get; set; }
        public Guid? ReceiverId { get; set; }
        public Guid? DyeingFactoryId { get; set; }
        [Display(Name = "_Warehouse")]
        public String Warehouse { get; set; }
        [Display(Name = "_TransportMethod")]
        public String TransportMethod { get; set; }

        protected override void InitVM()
        {
        }

    }

}
