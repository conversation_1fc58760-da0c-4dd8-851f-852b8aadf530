using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeOutboundBillVMs
{
    public partial class GreigeOutboundBillListVM : BasePagedListVM<GreigeOutboundBill_View, GreigeOutboundBillSearcher>
    {

        protected override IEnumerable<IGridColumn<GreigeOutboundBill_View>> InitGridHeader()
        {
            return new List<GridColumn<GreigeOutboundBill_View>>{
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.OutboundDate),
                this.MakeGridHeader(x => x.Purpose),
                this.MakeGridHeader(x => x.CompanyName_view),
                this.MakeGridHeader(x => x.CompanyName_view2),
                this.MakeGridHeader(x => x.Warehouse),
                this.MakeGridHeader(x => x.TransportMethod),
                this.MakeGridHeader(x => x.DeliveryAddress),
                this.MakeGridHeader(x => x.TotalRolls),
                this.MakeGridHeader(x => x.TotalWeight),
                this.MakeGridHeader(x => x.TotalMeters),
                this.MakeGridHeader(x => x.TotalYards),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.AuditedBy),
                this.MakeGridHeader(x => x.AuditedComment),
                this.MakeGridHeader(x => x.TenantCode),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<GreigeOutboundBill_View> GetSearchQuery()
        {
            var query = DC.Set<GreigeOutboundBill>()
                .CheckContain(Searcher.BillNo, x=>x.BillNo)
                .CheckBetween(Searcher.OutboundDate?.GetStartTime(), Searcher.OutboundDate?.GetEndTime(), x => x.OutboundDate, includeMax: false)
                .CheckContain(Searcher.Purpose, x=>x.Purpose)
                .CheckEqual(Searcher.ReceiverId, x=>x.ReceiverId)
                .CheckEqual(Searcher.DyeingFactoryId, x=>x.DyeingFactoryId)
                .CheckContain(Searcher.Warehouse, x=>x.Warehouse)
                .CheckContain(Searcher.TransportMethod, x=>x.TransportMethod)
                .CheckContain(Searcher.DeliveryAddress, x=>x.DeliveryAddress)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new GreigeOutboundBill_View
                {
				    ID = x.ID,
                    BillNo = x.BillNo,
                    OutboundDate = x.OutboundDate,
                    Purpose = x.Purpose,
                    CompanyName_view = x.Receiver.CompanyName,
                    CompanyName_view2 = x.DyeingFactory.CompanyName,
                    Warehouse = x.Warehouse,
                    TransportMethod = x.TransportMethod,
                    DeliveryAddress = x.DeliveryAddress,
                    TotalRolls = x.TotalRolls,
                    TotalWeight = x.TotalWeight,
                    TotalMeters = x.TotalMeters,
                    TotalYards = x.TotalYards,
                    AuditStatus = x.AuditStatus,
                    AuditedBy = x.AuditedBy,
                    AuditedComment = x.AuditedComment,
                    TenantCode = x.TenantCode,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class GreigeOutboundBill_View : GreigeOutboundBill{
        [Display(Name = "_CompanyName")]
        public String CompanyName_view { get; set; }
        [Display(Name = "_CompanyName")]
        public String CompanyName_view2 { get; set; }

    }
}
