using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeOutboundBillVMs
{
    public partial class GreigeOutboundBillTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.BillNo);
        [Display(Name = "出库日期")]
        public ExcelPropety OutboundDate_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.OutboundDate);
        [Display(Name = "出库用途")]
        public ExcelPropety Purpose_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.Purpose);
        public ExcelPropety Receiver_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.ReceiverId);
        public ExcelPropety DyeingFactory_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.DyeingFactoryId);
        [Display(Name = "_Warehouse")]
        public ExcelPropety Warehouse_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.Warehouse);
        [Display(Name = "_TransportMethod")]
        public ExcelPropety TransportMethod_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.TransportMethod);
        [Display(Name = "_DeliveryAddress")]
        public ExcelPropety DeliveryAddress_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.DeliveryAddress);
        [Display(Name = "总卷数")]
        public ExcelPropety TotalRolls_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.TotalRolls);
        [Display(Name = "总重量")]
        public ExcelPropety TotalWeight_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.TotalWeight);
        [Display(Name = "总米数")]
        public ExcelPropety TotalMeters_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.TotalMeters);
        [Display(Name = "总码数")]
        public ExcelPropety TotalYards_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.TotalYards);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.AuditStatus);
        [Display(Name = "_AuditedBy")]
        public ExcelPropety AuditedBy_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.AuditedBy);
        [Display(Name = "_AuditedComment")]
        public ExcelPropety AuditedComment_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.AuditedComment);
        [Display(Name = "_TenantCode")]
        public ExcelPropety TenantCode_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.TenantCode);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<GreigeOutboundBill>(x => x.Remark);

	    protected override void InitVM()
        {
            Receiver_Excel.DataType = ColumnDataType.ComboBox;
            Receiver_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
            DyeingFactory_Excel.DataType = ColumnDataType.ComboBox;
            DyeingFactory_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class GreigeOutboundBillImportVM : BaseImportVM<GreigeOutboundBillTemplateVM, GreigeOutboundBill>
    {

    }

}
