using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeOutboundBillVMs
{
    public partial class GreigeOutboundBillSearcher : BaseSearcher
    {
        [Display(Name = "_BillNo")]
        public String BillNo { get; set; }
        [Display(Name = "出库日期")]
        public DateRange OutboundDate { get; set; }
        [Display(Name = "出库用途")]
        public String Purpose { get; set; }
        public Guid? ReceiverId { get; set; }
        public Guid? DyeingFactoryId { get; set; }
        [Display(Name = "_Warehouse")]
        public String Warehouse { get; set; }
        [Display(Name = "_TransportMethod")]
        public String TransportMethod { get; set; }
        [Display(Name = "_DeliveryAddress")]
        public String DeliveryAddress { get; set; }
        [Display(Name = "_Admin.Remark")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }
}
