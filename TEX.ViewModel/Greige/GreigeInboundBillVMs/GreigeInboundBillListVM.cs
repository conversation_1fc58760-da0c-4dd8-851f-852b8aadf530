using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeInboundBillVMs
{
    public partial class GreigeInboundBillListVM : BasePagedListVM<GreigeInboundBill_View, GreigeInboundBillSearcher>
    {

        protected override IEnumerable<IGridColumn<GreigeInboundBill_View>> InitGridHeader()
        {
            return new List<GridColumn<GreigeInboundBill_View>>{
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.InboundDate),
                this.MakeGridHeader(x => x.CompanyName_view),
                this.MakeGridHeader(x => x.Warehouse),
                this.MakeGridHeader(x => x.TotalRolls),
                this.MakeGridHeader(x => x.TotalWeight),
                this.MakeGridHeader(x => x.TotalMeters),
                this.MakeGridHeader(x => x.TotalYards),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.AuditedBy),
                this.MakeGridHeader(x => x.AuditedComment),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<GreigeInboundBill_View> GetSearchQuery()
        {
            var query = DC.Set<GreigeInboundBill>()
                .CheckContain(Searcher.BillNo, x=>x.BillNo)
                .CheckBetween(Searcher.InboundDate?.GetStartTime(), Searcher.InboundDate?.GetEndTime(), x => x.InboundDate, includeMax: false)
                .CheckEqual(Searcher.KnittingFactoryId, x=>x.KnittingFactoryId)
                .CheckContain(Searcher.Warehouse, x=>x.Warehouse)
                .CheckEqual(Searcher.AuditStatus, x=>x.AuditStatus)
                .Select(x => new GreigeInboundBill_View
                {
				    ID = x.ID,
                    BillNo = x.BillNo,
                    InboundDate = x.InboundDate,
                    CompanyName_view = x.KnittingFactory.CompanyName,
                    Warehouse = x.Warehouse,
                    TotalRolls = x.TotalRolls,
                    TotalWeight = x.TotalWeight,
                    TotalMeters = x.TotalMeters,
                    TotalYards = x.TotalYards,
                    AuditStatus = x.AuditStatus,
                    AuditedBy = x.AuditedBy,
                    AuditedComment = x.AuditedComment,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class GreigeInboundBill_View : GreigeInboundBill{
        [Display(Name = "_CompanyName")]
        public String CompanyName_view { get; set; }

    }
}
