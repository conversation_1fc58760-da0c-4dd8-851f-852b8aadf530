using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeInboundBillVMs
{
    public partial class GreigeInboundBillBatchVM : BaseBatchVM<GreigeInboundBill, GreigeInboundBill_BatchEdit>
    {
        public GreigeInboundBillBatchVM()
        {
            ListVM = new GreigeInboundBillListVM();
            LinkedVM = new GreigeInboundBill_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class GreigeInboundBill_BatchEdit : BaseVM
    {
        public Guid? KnittingFactoryId { get; set; }

        protected override void InitVM()
        {
        }

    }

}
