using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeInboundBillVMs
{
    public partial class GreigeInboundBillSearcher : BaseSearcher
    {
        [Display(Name = "_BillNo")]
        public String BillNo { get; set; }
        [Display(Name = "入库日期")]
        public DateRange InboundDate { get; set; }
        public Guid? KnittingFactoryId { get; set; }
        [Display(Name = "_Warehouse")]
        public String Warehouse { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; }

        protected override void InitVM()
        {
        }

    }
}
