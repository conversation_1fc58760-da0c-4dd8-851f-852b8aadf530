using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeInboundBillVMs
{
    public partial class GreigeInboundBillTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.BillNo);
        [Display(Name = "入库日期")]
        public ExcelPropety InboundDate_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.InboundDate);
        public ExcelPropety KnittingFactory_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.KnittingFactoryId);
        [Display(Name = "_Warehouse")]
        public ExcelPropety Warehouse_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.Warehouse);
        [Display(Name = "总卷数")]
        public ExcelPropety TotalRolls_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.TotalRolls);
        [Display(Name = "总重量")]
        public ExcelPropety TotalWeight_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.TotalWeight);
        [Display(Name = "总米数")]
        public ExcelPropety TotalMeters_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.TotalMeters);
        [Display(Name = "总码数")]
        public ExcelPropety TotalYards_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.TotalYards);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.AuditStatus);
        [Display(Name = "_AuditedBy")]
        public ExcelPropety AuditedBy_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.AuditedBy);
        [Display(Name = "_AuditedComment")]
        public ExcelPropety AuditedComment_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.AuditedComment);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<GreigeInboundBill>(x => x.Remark);

	    protected override void InitVM()
        {
            KnittingFactory_Excel.DataType = ColumnDataType.ComboBox;
            KnittingFactory_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
        }

    }

    public class GreigeInboundBillImportVM : BaseImportVM<GreigeInboundBillTemplateVM, GreigeInboundBill>
    {

    }

}
