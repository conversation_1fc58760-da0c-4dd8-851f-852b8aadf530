using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternDetailVMs
{
    public partial class PatternDetailListVM : BasePagedListVM<PatternDetail_View, PatternDetailSearcher>
    {

        protected override IEnumerable<IGridColumn<PatternDetail_View>> InitGridHeader()
        {
            return new List<GridColumn<PatternDetail_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.PatternName_view),
                this.MakeGridHeader(x => x.Customer_view),
                this.MakeGridHeader(x => x.PatternCode),
                this.MakeGridHeader(x => x.PatternVersion),
                this.MakeGridHeader(x => x.FabricCategory),
                this.MakeGridHeader(x => x.Pattern<PERSON>epeatWidth),
                this.MakeGridHeader(x => x.PatternRepeatHeight),
                this.MakeGridHeader(x => x.RequiredFullWidth),
                this.MakeGridHeader(x => x.RequiredGsm),
                this.MakeGridHeader(x => x.RequiredCuttableWidth),
                this.MakeGridHeader(x => x.MachineInch),
                this.MakeGridHeader(x => x.MachineTotalNeedles),
                this.MakeGridHeader(x => x.MachineSpec),
                this.MakeGridHeader(x => x.JacquardFeed),
                this.MakeGridHeader(x => x.PatternWeftPoint),
                this.MakeGridHeader(x => x.PatternWarpPoint),
                this.MakeGridHeader(x => x.GreigeRepeatWidth),
                this.MakeGridHeader(x => x.GreigeRepeatHeight),
                this.MakeGridHeader(x => x.GreigeWidth),
                this.MakeGridHeader(x => x.GreigeGsm),
                this.MakeGridHeader(x => x.FabricRepeatWidth),
                this.MakeGridHeader(x => x.FabricRepeatHeight),
                this.MakeGridHeader(x => x.FabricFullWidth),
                this.MakeGridHeader(x => x.FabricGsm),
                this.MakeGridHeader(x => x.VersionModified),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<PatternDetail_View> GetSearchQuery()
        {
            var query = DC.Set<PatternDetail>()
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckEqual(Searcher.PatternId, x=>x.PatternId)
                .CheckEqual(Searcher.PatternCode, x=>x.PatternCode)
                .CheckContain(Searcher.Customer, x=>x.Pattern.Customer)
                .CheckEqual(Searcher.PatternVersion, x=>x.PatternVersion)
                .CheckContain(Searcher.FabricCategory, x=>x.FabricCategory)
                .CheckEqual(Searcher.RequiredFullWidth, x=>x.RequiredFullWidth)
                .CheckEqual(Searcher.RequiredGsm, x=>x.RequiredGsm)
                .CheckEqual(Searcher.MachineTotalNeedles, x=>x.MachineTotalNeedles)
                .CheckEqual(Searcher.JacquardFeed, x=>x.JacquardFeed)
                .CheckContain(Searcher.VersionModified, x=>x.VersionModified)
                .Select(x => new PatternDetail_View
                {
				    ID = x.ID,
                    CreateDate = x.CreateDate,
                    PatternName_view = x.Pattern.PatternName,
                    Customer_view = x.Pattern.Customer,
                    PatternCode = x.PatternCode,
                    PatternVersion = x.PatternVersion,
                    FabricCategory = x.FabricCategory,
                    PatternRepeatWidth = x.PatternRepeatWidth,
                    PatternRepeatHeight = x.PatternRepeatHeight,
                    RequiredFullWidth = x.RequiredFullWidth,
                    RequiredGsm = x.RequiredGsm,
                    RequiredCuttableWidth = x.RequiredCuttableWidth,
                    MachineInch = x.MachineInch,
                    MachineTotalNeedles = x.MachineTotalNeedles,
                    MachineSpec = x.MachineSpec,
                    JacquardFeed = x.JacquardFeed,
                    PatternWeftPoint = x.PatternWeftPoint,
                    PatternWarpPoint = x.PatternWarpPoint,
                    GreigeRepeatWidth = x.GreigeRepeatWidth,
                    GreigeRepeatHeight = x.GreigeRepeatHeight,
                    GreigeWidth = x.GreigeWidth,
                    GreigeGsm = x.GreigeGsm,
                    FabricRepeatWidth = x.FabricRepeatWidth,
                    FabricRepeatHeight = x.FabricRepeatHeight,
                    FabricFullWidth = x.FabricFullWidth,
                    FabricGsm = x.FabricGsm,
                    VersionModified = x.VersionModified,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class PatternDetail_View : PatternDetail{
        [Display(Name = "_PictureName")]
        public String PatternName_view { get; set; }
        [Display(Name = "_Customer")]
        public String Customer_view { get; set; }

    }
}
