using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternDetailVMs
{
    public partial class PatternDetailBatchVM : BaseBatchVM<PatternDetail, PatternDetail_BatchEdit>
    {
        public PatternDetailBatchVM()
        {
            ListVM = new PatternDetailListVM();
            LinkedVM = new PatternDetail_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class PatternDetail_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
