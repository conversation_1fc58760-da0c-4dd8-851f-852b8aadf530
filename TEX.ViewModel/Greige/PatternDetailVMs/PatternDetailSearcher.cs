using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternDetailVMs
{
    public partial class PatternDetailSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        [Display(Name = "_PatternId")]
        public Guid? PatternId { get; set; }
        [Display(Name = "_Customer")]
        public string Customer { get; set; }
        [Display(Name = "_PatternCode")]
        public Int32? PatternCode { get; set; }
        [Display(Name = "_PatternVersion")]
        public Int32? PatternVersion { get; set; }
        [Display(Name = "_FabricCategory")]
        public String FabricCategory { get; set; }
        [Display(Name = "_RequiredFullWidth")]
        public Int32? RequiredFullWidth { get; set; }
        [Display(Name = "_RequiredGsm")]
        public Int32? RequiredGsm { get; set; }
        [Display(Name = "_MachineTotalNeedles")]
        public Int32? MachineTotalNeedles { get; set; }
        [Display(Name = "_JacquardFeed")]
        public Int32? JacquardFeed { get; set; }
        [Display(Name = "_VersionModified")]
        public String VersionModified { get; set; }

        protected override void InitVM()
        {
        }

    }
}
