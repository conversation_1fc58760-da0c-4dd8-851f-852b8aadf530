using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternDetailVMs
{
    public partial class PatternDetailTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.CreateDate);
        public ExcelPropety Pattern_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternId);
        [Display(Name = "_PatternCode")]
        public ExcelPropety PatternCode_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternCode);
        [Display(Name = "_PatternVersion")]
        public ExcelPropety PatternVersion_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternVersion);
        [Display(Name = "_FabricCategory")]
        public ExcelPropety FabricCategory_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.FabricCategory);
        [Display(Name = "_PatternRepeatWidth")]
        public ExcelPropety PatternRepeatWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternRepeatWidth);
        [Display(Name = "_PatternRepeatHeight")]
        public ExcelPropety PatternRepeatHeight_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternRepeatHeight);
        [Display(Name = "_RequiredFullWidth")]
        public ExcelPropety RequiredFullWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.RequiredFullWidth);
        [Display(Name = "_RequiredGsm")]
        public ExcelPropety RequiredGsm_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.RequiredGsm);
        [Display(Name = "_RequiredCuttableWidth")]
        public ExcelPropety RequiredCuttableWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.RequiredCuttableWidth);
        [Display(Name = "_MachineInch")]
        public ExcelPropety MachineInch_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.MachineInch);
        [Display(Name = "_MachineTotalNeedles")]
        public ExcelPropety MachineTotalNeedles_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.MachineTotalNeedles);
        [Display(Name = "_MachineSpec")]
        public ExcelPropety MachineSpec_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.MachineSpec);
        [Display(Name = "_JacquardFeed")]
        public ExcelPropety JacquardFeed_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.JacquardFeed);
        [Display(Name = "_PatternWeftPoint")]
        public ExcelPropety PatternWeftPoint_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternWeftPoint);
        [Display(Name = "_PatternWarpPoint")]
        public ExcelPropety PatternWarpPoint_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.PatternWarpPoint);
        [Display(Name = "_GreigeRepeatWidth")]
        public ExcelPropety GreigeRepeatWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.GreigeRepeatWidth);
        [Display(Name = "_GreigeRepeatHeight")]
        public ExcelPropety GreigeRepeatHeight_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.GreigeRepeatHeight);
        [Display(Name = "_GreigeWidth")]
        public ExcelPropety GreigeWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.GreigeWidth);
        [Display(Name = "_GreigeGsm")]
        public ExcelPropety GreigeGsm_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.GreigeGsm);
        [Display(Name = "_FabricRepeatWidth")]
        public ExcelPropety FabricRepeatWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.FabricRepeatWidth);
        [Display(Name = "_FabricRepeatHeight")]
        public ExcelPropety FabricRepeatHeight_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.FabricRepeatHeight);
        [Display(Name = "_FabricFullWidth")]
        public ExcelPropety FabricFullWidth_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.FabricFullWidth);
        [Display(Name = "_FabricGsm")]
        public ExcelPropety FabricGsm_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.FabricGsm);
        [Display(Name = "_VersionModified")]
        public ExcelPropety VersionModified_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.VersionModified);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<PatternDetail>(x => x.Remark);

	    protected override void InitVM()
        {
            Pattern_Excel.DataType = ColumnDataType.ComboBox;
            Pattern_Excel.ListItems = DC.Set<Pattern>().GetSelectListItems(Wtm, y => y.PatternName);
        }

    }

    public class PatternDetailImportVM : BaseImportVM<PatternDetailTemplateVM, PatternDetail>
    {

    }

}
