using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingPlanVMs
{
    public partial class KnittingPlanBatchVM : BaseBatchVM<KnittingPlan, KnittingPlan_BatchEdit>
    {
        public KnittingPlanBatchVM()
        {
            ListVM = new KnittingPlanListVM();
            LinkedVM = new KnittingPlan_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class KnittingPlan_BatchEdit : BaseVM
    {
        [Display(Name = "日期")]
        public DateTime? PlanDate { get; set; }
        public Guid? PurchaseOrderId { get; set; }
        public Guid? KnittingFactoryId { get; set; }
        public Guid? KnittingProcessId { get; set; }
        [Display(Name = "要求")]
        public String Requirements { get; set; }
        [Display(Name = "_AuditStatus")]
        public AuditStatusEnum? AuditStatus { get; set; }

        protected override void InitVM()
        {
        }

    }

}
