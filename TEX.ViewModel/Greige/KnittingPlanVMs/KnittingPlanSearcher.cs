using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingPlanVMs
{
    public partial class KnittingPlanSearcher : BaseSearcher
    {
        [Display(Name = "日期")]
        public DateRange PlanDate { get; set; }
        [Display(Name = "_PurchaseOrder")]
        public Guid? PurchaseOrderId { get; set; }
        [Display(Name = "_KnittingFactory")]
        public Guid? KnittingFactoryId { get; set; }
        [Display(Name = "_ProductName")]
        public String ProductName { get; set; }
        [Display(Name = "_KnittingProcess")]
        public Guid? KnittingProcessId { get; set; }

        protected override void InitVM()
        {
        }

    }
}
