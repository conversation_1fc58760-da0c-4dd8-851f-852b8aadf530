using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingPlanVMs
{
    public partial class KnittingPlanTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_BillNo")]
        public ExcelPropety BillNo_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.BillNo);
        [Display(Name = "日期")]
        public ExcelPropety PlanDate_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.PlanDate);
        public ExcelPropety PurchaseOrder_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.PurchaseOrderId);
        public ExcelPropety KnittingFactory_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.KnittingFactoryId);
        [Display(Name = "品种")]
        public ExcelPropety ProductName_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.ProductName);
        public ExcelPropety KnittingProcess_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.KnittingProcessId);
        [Display(Name = "匹数")]
        public ExcelPropety PieceCount_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.PieceCount);
        [Display(Name = "重量")]
        public ExcelPropety TotalWeight_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.TotalWeight);
        [Display(Name = "单匹重")]
        public ExcelPropety WeightPerPiece_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.WeightPerPiece);
        [Display(Name = "批次")]
        public ExcelPropety BatchNo_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.BatchNo);
        [Display(Name = "交期")]
        public ExcelPropety DeliveryDate_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.DeliveryDate);
        [Display(Name = "要求")]
        public ExcelPropety Requirements_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.Requirements);
        [Display(Name = "_AuditStatus")]
        public ExcelPropety AuditStatus_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.AuditStatus);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<KnittingPlan>(x => x.Remark);

	    protected override void InitVM()
        {
            PurchaseOrder_Excel.DataType = ColumnDataType.ComboBox;
            PurchaseOrder_Excel.ListItems = DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, y => y.OrderNo);
            KnittingFactory_Excel.DataType = ColumnDataType.ComboBox;
            KnittingFactory_Excel.ListItems = DC.Set<Company>().GetSelectListItems(Wtm, y => y.CompanyName);
            KnittingProcess_Excel.DataType = ColumnDataType.ComboBox;
            KnittingProcess_Excel.ListItems = DC.Set<KnittingProcess>().GetSelectListItems(Wtm, y => y.ProcessCode);
        }

    }

    public class KnittingPlanImportVM : BaseImportVM<KnittingPlanTemplateVM, KnittingPlan>
    {

    }

}
