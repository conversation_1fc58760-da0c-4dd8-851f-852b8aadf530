using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingPlanVMs
{
    public partial class KnittingPlanListVM : BasePagedListVM<KnittingPlan_View, KnittingPlanSearcher>
    {

        protected override IEnumerable<IGridColumn<KnittingPlan_View>> InitGridHeader()
        {
            return new List<GridColumn<KnittingPlan_View>>{
                this.MakeGridHeader(x => x.BillNo),
                this.MakeGridHeader(x => x.PlanDate),
                this.MakeGridHeader(x => x.OrderNo_view),
                this.MakeGridHeader(x => x.KnittingFactory_view),
                this.MakeGridHeader(x => x.ProductName),
                this.MakeGridHeader(x => x.ProcessCode_view),
                this.MakeGridHeader(x => x.PieceCount),
                this.MakeGridHeader(x => x.TotalWeight),
                this.MakeGridHeader(x => x.WeightPerPiece),
                this.MakeGridHeader(x => x.BatchNo),
                this.MakeGridHeader(x => x.DeliveryDate),
                this.MakeGridHeader(x => x.Requirements),
                this.MakeGridHeader(x => x.AuditStatus),
                this.MakeGridHeader(x => x.AuditedBy),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<KnittingPlan_View> GetSearchQuery()
        {
            var query = DC.Set<KnittingPlan>()
                .CheckBetween(Searcher.PlanDate?.GetStartTime(), Searcher.PlanDate?.GetEndTime(), x => x.PlanDate, includeMax: false)
                .CheckEqual(Searcher.PurchaseOrderId, x=>x.PurchaseOrderId)
                .CheckEqual(Searcher.KnittingFactoryId, x=>x.KnittingFactoryId)
                .CheckContain(Searcher.ProductName, x=>x.ProductName)
                .CheckEqual(Searcher.KnittingProcessId, x=>x.KnittingProcessId)
                .Select(x => new KnittingPlan_View
                {
				    ID = x.ID,
                    BillNo = x.BillNo,
                    PlanDate = x.PlanDate,
                    OrderNo_view = x.PurchaseOrder.OrderNo,
                    KnittingFactory_view = x.KnittingFactory.CompanyName,
                    ProductName = x.ProductName,
                    ProcessCode_view = x.KnittingProcess.ProcessCode,
                    PieceCount = x.PieceCount,
                    TotalWeight = x.TotalWeight,
                    WeightPerPiece = x.WeightPerPiece,
                    BatchNo = x.BatchNo,
                    DeliveryDate = x.DeliveryDate,
                    Requirements = x.Requirements,
                    AuditStatus = x.AuditStatus,
                    //AuditedBy = x.AuditedBy,
                    //Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class KnittingPlan_View : KnittingPlan{
        [Display(Name = "_OrderNo")]
        public String OrderNo_view { get; set; }
        [Display(Name = "_KnittingFactory")]
        public String KnittingFactory_view { get; set; }
        [Display(Name = "织造工艺代码")]
        public String ProcessCode_view { get; set; }

    }
}
