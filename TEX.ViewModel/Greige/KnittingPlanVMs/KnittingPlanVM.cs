using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingPlanVMs
{
    public partial class KnittingPlanVM : BaseCRUDVM<KnittingPlan>
    {

        public KnittingPlanVM()
        {
            SetInclude(x => x.PurchaseOrder);
            SetInclude(x => x.KnittingFactory);
            SetInclude(x => x.KnittingProcess);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
