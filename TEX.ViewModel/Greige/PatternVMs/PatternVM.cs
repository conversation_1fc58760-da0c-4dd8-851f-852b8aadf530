using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternVMs
{
    public partial class PatternVM : BaseCRUDVM<Pattern>
    {

        public PatternVM()
        {
            SetInclude(x => x.DetailList);
            SetInclude(x => x.Images);
        }

        protected override void InitVM()
        {

        }
        
        public override void DoAdd()
        {
            base.DoAdd();
        }

        public override DuplicatedInfo<Pattern> SetDuplicatedCheck()
        {
            base.SetDuplicatedCheck();
            var rv = new DuplicatedInfo<Pattern>();

            var i=DC.Set<PatternDetail>().Where(x => x.PatternCode == Entity.CodeNo).Any();
            var k = DC.Set<Pattern>().Where(x => x.CodeNo == Entity.CodeNo).Any();
            if (i || k)
            {
                var duplicatedField = new DuplicatedField<Pattern>(x => x.CodeNo);
                rv.AddGroup(duplicatedField);
            }

            return rv;
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
