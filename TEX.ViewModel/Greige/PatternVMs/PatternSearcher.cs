using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternVMs
{
    public partial class PatternSearcher : BaseSearcher
    {
        [Display(Name = "_CreateDate")]
        public DateRange CreateDate { get; set; }
        [Display(Name = "_CodeNo")]
        public int? CodeNo { get; set; }//如果不为空,数字类型默认值为0,导致查询不到数据
        [Display(Name = "_PictureName")]
        public String PatternName { get; set; }
        [Display(Name = "_Customer")]
        public String Customer { get; set; }
        [Display(Name = "_Requirements")]
        public String Requirements { get; set; }

        protected override void InitVM()
        {
        }

    }
}
