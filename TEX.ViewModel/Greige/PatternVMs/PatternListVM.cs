using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternVMs
{
    public partial class PatternListVM : BasePagedListVM<Pattern_View, PatternSearcher>
    {

        protected override IEnumerable<IGridColumn<Pattern_View>> InitGridHeader()
        {
            return new List<GridColumn<Pattern_View>>{
                this.MakeGridHeader(x => x.CreateDate),
                this.MakeGridHeader(x => x.CodeNo),
                this.MakeGridHeader(x => x.DisplayName),
                this.MakeGridHeader(x => x.PatternName),
                this.MakeGridHeader(x => x.Customer),
                this.MakeGridHeader(x => x.Description),
                this.MakeGridHeader(x => x.Requirements),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<Pattern_View> GetSearchQuery()
        {
            var query = DC.Set<Pattern>()
                .CheckBetween(Searcher.CreateDate?.GetStartTime(), Searcher.CreateDate?.GetEndTime(), x => x.CreateDate, includeMax: false)
                .CheckEqual(Searcher.CodeNo, x => x.CodeNo)//如果不为空,数字类型默认值为0,导致查询不到数据
                .CheckContain(Searcher.PatternName, x => x.PatternName)
                .CheckContain(Searcher.Customer, x => x.Customer)
                .CheckContain(Searcher.Requirements, x => x.Requirements)
                .Select(x => new Pattern_View
                {
                    ID = x.ID,
                    CreateDate = x.CreateDate,
                    DisplayName = x.CodeNo + " - " + x.PatternName,
                    PatternName = x.PatternName,
                    CodeNo = x.CodeNo,
                    Customer = x.Customer,
                    Description = x.Description,
                    Requirements = x.Requirements,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class Pattern_View : Pattern
    {

        public string DisplayName { get; set; }

    }
}