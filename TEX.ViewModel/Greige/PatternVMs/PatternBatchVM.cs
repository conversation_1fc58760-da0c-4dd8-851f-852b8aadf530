using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternVMs
{
    public partial class PatternBatchVM : BaseBatchVM<Pattern, Pattern_BatchEdit>
    {
        public PatternBatchVM()
        {
            ListVM = new PatternListVM();
            LinkedVM = new Pattern_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class Pattern_BatchEdit : BaseVM
    {

        protected override void InitVM()
        {
        }

    }

}
