using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.PatternVMs
{
    public partial class PatternTemplateVM : BaseTemplateVM
    {
        [Display(Name = "_CreateDate")]
        public ExcelPropety CreateDate_Excel = ExcelPropety.CreateProperty<Pattern>(x => x.CreateDate);
        [Display(Name = "_PictureName")]
        public ExcelPropety PatternName_Excel = ExcelPropety.CreateProperty<Pattern>(x => x.PatternName);
        [Display(Name = "_Customer")]
        public ExcelPropety Customer_Excel = ExcelPropety.CreateProperty<Pattern>(x => x.Customer);
        [Display(Name = "_Description")]
        public ExcelPropety Description_Excel = ExcelPropety.CreateProperty<Pattern>(x => x.Description);
        [Display(Name = "_Requirements")]
        public ExcelPropety Requirements_Excel = ExcelPropety.CreateProperty<Pattern>(x => x.Requirements);
        [Display(Name = "_Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<Pattern>(x => x.Remark);

	    protected override void InitVM()
        {
        }

    }

    public class PatternImportVM : BaseImportVM<PatternTemplateVM, Pattern>
    {

    }

}
