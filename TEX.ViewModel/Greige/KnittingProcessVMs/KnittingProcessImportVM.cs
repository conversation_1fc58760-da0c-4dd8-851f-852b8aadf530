using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingProcessVMs
{
    public partial class KnittingProcessTemplateVM : BaseTemplateVM
    {
        [Display(Name = "工艺编码")]
        public ExcelPropety ProcessCode_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.ProcessCode);
        [Display(Name = "机型信息")]
        public ExcelPropety MachineInfoJson_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.MachineInfoJson);
        [Display(Name = "下机克重")]
        public ExcelPropety Weight_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.Weight);
        [Display(Name = "下机门幅")]
        public ExcelPropety Width_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.Width);
        [Display(Name = "下机毛高")]
        public ExcelPropety PileHeight_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.PileLength);
        [Display(Name = "成品克重")]
        public ExcelPropety FinishedWeight_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.FinishedWeight);
        [Display(Name = "成品门幅")]
        public ExcelPropety FinishedWidth_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.FinishedWidth);
        [Display(Name = "成品毛高")]
        public ExcelPropety FinishedPileHeight_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.FinishedPileHeight);
        [Display(Name = "纱支信息")]
        public ExcelPropety YarnInfoJson_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.YarnInfoJson);
        [Display(Name = "_Admin.Remark")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<KnittingProcess>(x => x.Remark);

	    protected override void InitVM()
        {
        }

    }

    public class KnittingProcessImportVM : BaseImportVM<KnittingProcessTemplateVM, KnittingProcess>
    {

    }

}
