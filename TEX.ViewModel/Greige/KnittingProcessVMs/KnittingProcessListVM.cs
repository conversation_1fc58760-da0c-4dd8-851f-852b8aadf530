using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingProcessVMs
{
    public partial class KnittingProcessListVM : BasePagedListVM<KnittingProcess_View, KnittingProcessSearcher>
    {

        protected override IEnumerable<IGridColumn<KnittingProcess_View>> InitGridHeader()
        {
            return new List<GridColumn<KnittingProcess_View>>{
                this.MakeGridHeader(x => x.ProcessCode),
                this.MakeGridHeader(x => x.Category_view),
                this.MakeGridHeader(x => x.MachineInfoJson),
                this.MakeGridHeader(x => x.Weight),
                this.MakeGridHeader(x => x.Width),
                this.MakeGridHeader(x => x.<PERSON>th),
                this.MakeGridHeader(x => x.Finished<PERSON>eight),
                this.MakeGridHeader(x => x.FinishedWidth),
                this.MakeGridHeader(x => x.FinishedPileHeight),
                this.MakeGridHeader(x => x.YarnInfoJson),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeader(x => x.CreateTime),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<KnittingProcess_View> GetSearchQuery()
        {
            var query = DC.Set<KnittingProcess>()
                .CheckContain(Searcher.ProcessCode, x=>x.ProcessCode)
                .CheckEqual(Searcher.FinishedWeight, x=>x.FinishedWeight)
                .CheckEqual(Searcher.FinishedWidth, x=>x.FinishedWidth)
                .CheckEqual(Searcher.FinishedPileHeight, x=>x.FinishedPileHeight)
                .Select(x => new KnittingProcess_View
                {
				    ID = x.ID,
                    ProcessCode = x.ProcessCode,
                    Category_view=x.Category.Description,
                    MachineInfoJson = x.MachineInfoJson,
                    Weight = x.Weight,
                    Width = x.Width,
                    PileLength = x.PileLength,
                    FinishedWeight = x.FinishedWeight,
                    FinishedWidth = x.FinishedWidth,
                    FinishedPileHeight = x.FinishedPileHeight,
                    YarnInfoJson = x.YarnInfoJson,
                    Remark = x.Remark,
                    CreateTime=x.CreateTime
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class KnittingProcess_View : KnittingProcess{
        [Display(Name = "_Category")]
        public string Category_view { get; set; }
    }
}
