using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingProcessVMs
{
    public partial class KnittingProcessBatchVM : BaseBatchVM<KnittingProcess, KnittingProcess_BatchEdit>
    {
        public KnittingProcessBatchVM()
        {
            ListVM = new KnittingProcessListVM();
            LinkedVM = new KnittingProcess_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class KnittingProcess_BatchEdit : BaseVM
    {
        [Display(Name = "_Admin.Remark")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }

}
