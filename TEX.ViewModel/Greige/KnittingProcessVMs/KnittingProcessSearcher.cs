using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.KnittingProcessVMs
{
    public partial class KnittingProcessSearcher : BaseSearcher
    {
        [Display(Name = "工艺编码")]
        public String ProcessCode { get; set; }
        [Display(Name = "成品克重")]
        public Decimal? FinishedWeight { get; set; }
        [Display(Name = "成品门幅")]
        public Decimal? FinishedWidth { get; set; }
        [Display(Name = "成品毛高")]
        public Decimal? FinishedPileHeight { get; set; }

        protected override void InitVM()
        {
        }

    }
}
