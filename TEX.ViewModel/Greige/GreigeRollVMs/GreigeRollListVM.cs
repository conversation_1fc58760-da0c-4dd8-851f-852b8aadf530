using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeRollVMs
{
    public partial class GreigeRollListVM : BasePagedListVM<GreigeRoll_View, GreigeRollSearcher>
    {

        protected override IEnumerable<IGridColumn<GreigeRoll_View>> InitGridHeader()
        {
            return new List<GridColumn<GreigeRoll_View>>{
                this.MakeGridHeader(x => x.ProductName_view),
                this.MakeGridHeader(x => x.BillNo_view),
                this.MakeGridHeader(x => x.BillNo_view2),
                this.MakeGridHeader(x => x.BillNo_view3),
                this.MakeGridHeader(x => x.OperationType),
                this.MakeGridHeader(x => x.OperationRemark),
                this.MakeGridHeader(x => x.BatchNo),
                this.MakeGridHeader(x => x.MachineId),
                this.MakeGridHeader(x => x.RollNo),
                this.MakeGridHeader(x => x.InboundWeight),
                this.MakeGridHeader(x => x.InboundMeters),
                this.MakeGridHeader(x => x.InboundYards),
                this.MakeGridHeader(x => x.InboundBillNo),
                this.MakeGridHeader(x => x.QcWeight),
                this.MakeGridHeader(x => x.QcMeters),
                this.MakeGridHeader(x => x.QcYards),
                this.MakeGridHeader(x => x.OutboundWeight),
                this.MakeGridHeader(x => x.OutboundMeters),
                this.MakeGridHeader(x => x.OutboundYards),
                this.MakeGridHeader(x => x.OutboundBillNo),
                this.MakeGridHeader(x => x.Grade),
                this.MakeGridHeader(x => x.Worker),
                this.MakeGridHeader(x => x.Inspector),
                this.MakeGridHeader(x => x.Status),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<GreigeRoll_View> GetSearchQuery()
        {
            var query = DC.Set<GreigeRoll>()
                .Select(x => new GreigeRoll_View
                {
				    ID = x.ID,
                    ProductName_view = x.Product.ProductName,
                    BillNo_view = x.KnittingPlan.BillNo,
                    BillNo_view2 = x.InboundBill.BillNo,
                    BillNo_view3 = x.OutboundBill.BillNo,
                    OperationType = x.OperationType,
                    OperationRemark = x.OperationRemark,
                    BatchNo = x.BatchNo,
                    MachineId = x.MachineId,
                    RollNo = x.RollNo,
                    InboundWeight = x.InboundWeight,
                    InboundMeters = x.InboundMeters,
                    InboundYards = x.InboundYards,
                    InboundBillNo = x.InboundBillNo,
                    QcWeight = x.QcWeight,
                    QcMeters = x.QcMeters,
                    QcYards = x.QcYards,
                    OutboundWeight = x.OutboundWeight,
                    OutboundMeters = x.OutboundMeters,
                    OutboundYards = x.OutboundYards,
                    OutboundBillNo = x.OutboundBillNo,
                    Grade = x.Grade,
                    Worker = x.Worker,
                    Inspector = x.Inspector,
                    Status = x.Status,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class GreigeRoll_View : GreigeRoll{
        [Display(Name = "_ProductName")]
        public String ProductName_view { get; set; }
        [Display(Name = "_BillNo")]
        public String BillNo_view { get; set; }
        [Display(Name = "_BillNo")]
        public String BillNo_view2 { get; set; }
        [Display(Name = "_BillNo")]
        public String BillNo_view3 { get; set; }

    }
}
