using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeRollVMs
{
    public partial class GreigeRollVM : BaseCRUDVM<GreigeRoll>
    {

        public GreigeRollVM()
        {
            SetInclude(x => x.Product);
            SetInclude(x => x.KnittingPlan);
            SetInclude(x => x.InboundBill);
            SetInclude(x => x.OutboundBill);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
