using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeRollVMs
{
    public partial class GreigeRollBatchVM : BaseBatchVM<GreigeRoll, GreigeRoll_BatchEdit>
    {
        public GreigeRollBatchVM()
        {
            ListVM = new GreigeRollListVM();
            LinkedVM = new GreigeRoll_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class GreigeRoll_BatchEdit : BaseVM
    {
        public Guid? ProductId { get; set; }
        public Guid? KnittingPlanId { get; set; }
        public Guid? InboundBillId { get; set; }
        public Guid? OutboundBillId { get; set; }
        [Display(Name = "_BatchNo")]
        public String BatchNo { get; set; }
        [Display(Name = "_Grade")]
        public String Grade { get; set; }
        [Display(Name = "_Worker")]
        public String Worker { get; set; }
        [Display(Name = "_Status")]
        public String Status { get; set; }

        protected override void InitVM()
        {
        }

    }

}
