using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using TEX.Model.Models;


namespace TEX.ViewModel.Greige.GreigeRollVMs
{
    public partial class GreigeRollTemplateVM : BaseTemplateVM
    {
        public ExcelPropety Product_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.ProductId);
        public ExcelPropety KnittingPlan_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.KnittingPlanId);
        public ExcelPropety InboundBill_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.InboundBillId);
        public ExcelPropety OutboundBill_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OutboundBillId);
        [Display(Name = "操作类型")]
        public ExcelPropety OperationType_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OperationType);
        [Display(Name = "操作备注")]
        public ExcelPropety OperationRemark_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OperationRemark);
        [Display(Name = "_BatchNo")]
        public ExcelPropety BatchNo_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.BatchNo);
        [Display(Name = "_MachineId")]
        public ExcelPropety MachineId_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.MachineId);
        [Display(Name = "_RollNo")]
        public ExcelPropety RollNo_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.RollNo);
        [Display(Name = "_InboundWeight")]
        public ExcelPropety InboundWeight_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.InboundWeight);
        [Display(Name = "_InboundMeters")]
        public ExcelPropety InboundMeters_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.InboundMeters);
        [Display(Name = "_InboundYards")]
        public ExcelPropety InboundYards_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.InboundYards);
        [Display(Name = "_InboundBillNo")]
        public ExcelPropety InboundBillNo_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.InboundBillNo);
        [Display(Name = "_QcWeight")]
        public ExcelPropety QcWeight_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.QcWeight);
        [Display(Name = "_QcMeters")]
        public ExcelPropety QcMeters_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.QcMeters);
        [Display(Name = "_QcYards")]
        public ExcelPropety QcYards_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.QcYards);
        [Display(Name = "_OutboundWeight")]
        public ExcelPropety OutboundWeight_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OutboundWeight);
        [Display(Name = "_OutboundMeters")]
        public ExcelPropety OutboundMeters_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OutboundMeters);
        [Display(Name = "_OutboundYards")]
        public ExcelPropety OutboundYards_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OutboundYards);
        [Display(Name = "_OutboundBillNo")]
        public ExcelPropety OutboundBillNo_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.OutboundBillNo);
        [Display(Name = "_Grade")]
        public ExcelPropety Grade_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.Grade);
        [Display(Name = "_Worker")]
        public ExcelPropety Worker_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.Worker);
        [Display(Name = "_Inspector")]
        public ExcelPropety Inspector_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.Inspector);
        [Display(Name = "_Status")]
        public ExcelPropety Status_Excel = ExcelPropety.CreateProperty<GreigeRoll>(x => x.Status);

	    protected override void InitVM()
        {
            Product_Excel.DataType = ColumnDataType.ComboBox;
            Product_Excel.ListItems = DC.Set<Product>().GetSelectListItems(Wtm, y => y.ProductName);
            KnittingPlan_Excel.DataType = ColumnDataType.ComboBox;
            KnittingPlan_Excel.ListItems = DC.Set<KnittingPlan>().GetSelectListItems(Wtm, y => y.BillNo);
            InboundBill_Excel.DataType = ColumnDataType.ComboBox;
            InboundBill_Excel.ListItems = DC.Set<GreigeInboundBill>().GetSelectListItems(Wtm, y => y.BillNo);
            OutboundBill_Excel.DataType = ColumnDataType.ComboBox;
            OutboundBill_Excel.ListItems = DC.Set<GreigeOutboundBill>().GetSelectListItems(Wtm, y => y.BillNo);
        }

    }

    public class GreigeRollImportVM : BaseImportVM<GreigeRollTemplateVM, GreigeRoll>
    {

    }

}
