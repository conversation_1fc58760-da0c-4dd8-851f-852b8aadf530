{
  "Logging": {
    "Console": {
      "IncludeScopes": true,
      "LogLevel": {
        "Default": "Information"
        
      }
    },
    "Debug": {
      "IncludeScopes": true,
      "LogLevel": {
        "Default": "Information",
        "Microsoft.AspNetCore": "Warning",
        "BootstrapBlazor.Localization": "Warning",
        "System.Net.Http.HttpClient.github": "Warning",
        "System.Net.Http.HttpClient.ApiClient.LogicalHandler": "Warning"
      }
    },
    "WTM": {
      "LogLevel": {
        "Default": "Information"
      }
    }
  },
  //浏览器F12调试给出详细错误信息,生产环境不能开启
  //"DetailedErrors": true,
  "Connections": [
    {
      "Key": "default",
      "DbContext": "DataContext",
      //"Value": "Server=localhost;User ID=root;Password=**************;Database=tex_db6",
      "Value": "Server=localhost;User ID=sean;Password=***********;Database=tex_db2025",
      "DBType": "mysql"
    }
  ],
  "CookiePre": "Textile", //cookie prefix
  "IsQuickDebug": false, //is debug mode
  "EnableTenant": true, //是否启动多租户
  "CorsOptions": {
    "EnableAll": true
  },
  "ErrorHandler": "/_Framework/Error",
  "Languages": "zh,en",
  "BlazorMode": "server", // server or wasm
  "UIOptions": {
    "DataTable": {
      "RPP": 100, //default records per page for all datagrid ,默认表格分页显示行数
      "ShowPrint": true,
      "ShowFilter": true
    },
    "ComboBox": {
      "DefaultEnableSearch": true
    },
    "DateTime": {
      "DefaultReadonly": true
    },
    "SearchPanel": {
      "DefaultExpand": false //是否展开搜索框
    }
  },
  "PageMode": "Tab", //display mode，Single or Tab
  "TabMode": "Simple", //Tab mode，Default or Simple
  "IsFilePublic": true, //Can download or view attachment file without login
  "FileUploadOptions": {
    "UploadLimit": 2097152000,
    "SaveFileMode": "local", //上传文件的保存方式，可选Database,local,oss
    "Settings": {
      "local": [
        {
          "GroupName": "default",
          "GroupLocation": ""
        }
      ],
      "oss": [
        {
          "GroupName": "default",
          "GroupLocation": "wtmimg",
          "ServerUrl": "",
          "Key": "",
          "Secret": ""
        }
      ]
    }
  },
  "JwtOptions": {
    "Issuer": "http://localhost",
    "Audience": "http://localhost",
    "Expires": 72000,
    "SecurityKey": "superSecretKey@185",
    "RefreshTokenExpires": 259200,
    "LoginPath": "/_Framework/Redirect401"
  },
  "CookieOptions": {
    "Issuer": "http://localhost",
    "Audience": "http://localhost",
    "Domain": "",
    "Expires": 72000,
    "SlidingExpiration": true,
    "SecurityKey": "superSecretKey@185",
    "RefreshTokenExpires": 259200,
    "LoginPath": "/Login/Login"
  },
  "Domains": {
    "server": { //Blazor Server模式下页面调用接口的内网地址
      "Address": "http://localhost:8888" 
        //发布后这里必须改成本地访问端口,框架默认5000,托管到IIS如果是8080,也需要改成对应的端口,要不然登陆不了,api访问不了
    },
    "serverpub": { //Blazor Server模式下页面调用接口的外网地址，可为空，为空表示api和页面部署在同一地址下
      "Address": ""

    },
    "mainhost": { //设置单点登录的主站，设置之后用户，角色和用户组将受控于主站

    },
    "github": {
      "Address": "https://api.github.com"
    }
  },
  "AppSettings": {
    "key1": "value1",
    "key2": "value2"
  }
}
