using System;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using TEX.Model.Models;
using TEX.ViewModel.Models;
using TEX.ViewModel.Models.PurchaseOrderVMs;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;

namespace TEX.Controllers;

[Area("Dashboard")]
[AuthorizeJwtWithCookie]
[ActionDescription("订单看板")]
[ApiController]
[Route("api/Dashboard/")]
public partial class DashboardController : BaseApiController
{
    [ActionDescription("Sys.Search")]
    [HttpGet("[action]")]
    public IActionResult GetOrderData()
    {
        //本年
        var thisYearTotalAmount = Wtm.DC.Set<PurchaseOrder>()
            .Where(x => x.CreateDate.Year == DateTime.Now.Year)
            .Sum(x => x.TotalAmount);
        var thisYearTotalWeight = Wtm.DC.Set<PurchaseOrder>()
            .Where(x => x.CreateDate.Year == DateTime.Now.Year)
            .Sum(x => x.TotalWeight);

        //本月
        var thisMonthTotalAmount = Wtm.DC.Set<PurchaseOrder>()
            .Where(x => x.CreateDate.Year == DateTime.Now.Year && x.CreateDate.Month == DateTime.Now.Month)
            .Sum(x => x.TotalAmount);
        var thisMonthTotalWeight = Wtm.DC.Set<PurchaseOrder>()
            .Where(x => x.CreateDate.Year == DateTime.Now.Year && x.CreateDate.Month == DateTime.Now.Month)
            .Sum(x => x.TotalWeight);

        //7天内
        var weekTotalAmount = Wtm.DC.Set<PurchaseOrder>()
            .Where(x => x.CreateDate <= DateTime.Now && x.CreateDate >= DateTime.Today.AddDays(-7))
            .Sum(x => x.TotalAmount);
        var weekTotalWeight = Wtm.DC.Set<PurchaseOrder>()
            .Where(x => x.CreateDate <= DateTime.Now && x.CreateDate >= DateTime.Today.AddDays(-7))
            .Sum(x => x.TotalWeight);


        var monthlyTotalWeights = Wtm.DC.Set<PurchaseOrder>()
                .GroupBy(po => new { po.CreateDate.Year, po.CreateDate.Month })
                .Select(g => new MonthlyTotalWeight
                {
                    YearMonth = new DateTime(g.Key.Year, g.Key.Month, 1),
                    TotalWeight = g.Sum(po => po.TotalWeight ?? 0)
                })
                .AsEnumerable()
                .OrderBy(x => x.YearMonth)
                //.OrderByDescending(x => x.YearMonth)
                .ToList();
        var monthlyTotalAmount = Wtm.DC.Set<PurchaseOrder>()
                .GroupBy(po => new { po.CreateDate.Year, po.CreateDate.Month })
                .Select(g => new MonthlyTotalAmount
                {
                    YearMonth = new DateTime(g.Key.Year, g.Key.Month, 1),
                    TotalAmount = g.Sum(po => po.TotalAmount ?? 0)
                })
                .ToList();
        return Ok(new PODashboardData
        {
            POYearTotalAmount = thisYearTotalAmount ?? 0,
            POMonthTotalAmount = thisMonthTotalAmount ?? 0,
            POWeekTotalAmount = weekTotalAmount ?? 0,
            POYearTotalWeight = thisYearTotalWeight ?? 0,
            POMonthTotalWeight = thisMonthTotalWeight ?? 0,
            POWeekTotalWeight = weekTotalWeight ?? 0,
            MonthlyTotalWeight = monthlyTotalWeights,
            MonthlyTotalAmount = monthlyTotalAmount
        });
    }
}

