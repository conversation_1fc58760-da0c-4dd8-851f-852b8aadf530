using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Producttion.WIPDetailVMs;
using TEX.Model.Models;
using NetBox.Extensions;
using SixLabors.ImageSharp.Drawing;


namespace TEX.Controllers;

[Area("Producttion")]
[AuthorizeJwt]
[ActionDescription("半成品明细")]
[ApiController]
[Route("api/WIPDetail")]
public partial class WIPDetailController : BaseApiController
{
    [ActionDescription("Sys.Search")]
    [HttpPost("Search")]
    public IActionResult Search(WIPDetailSearcher searcher)
    {
        if (ModelState.IsValid)
        {
            var vm = Wtm.CreateVM<WIPDetailListVM>();
            vm.Searcher = searcher;
            return Content(vm.GetJson(enumToString: false));
        }
        else
        {
            return BadRequest(ModelState.GetErrorJson());
        }
    }

    //查询损耗率
    [HttpPost("SearchLossRate")]
    public IActionResult SearchLossRate(WIPDetailSearcher searcher)
    {
        if (ModelState.IsValid)
        {
            //var vm = Wtm.CreateVM<WIPDetailListVM2>();
            ////vm.Searcher = searcher;
            //var q= Content(vm.GetJson(enumToString: false));
            //return q;
            var q = from wips in DC.Set<WIP>()
                    join wipdetails in DC.Set<WIPDetail>()
                    on wips.ID equals wipdetails.WIPId
                    select new
                    {
                        wips.POrderId,
                        wips.POrder.OrderNo,
                        wips.Procedure,
                        wips.ShipperId,
                        ShipperName = wips.Shipper.CompanyName,
                        wips.ReceiverId,
                        ReceiverName = wips.Receiver.CompanyName,
                        wipdetails.Color,
                        wipdetails.LotNo,
                        wipdetails.Pcs,
                        wipdetails.Meters
                    };

            var query = (from s in q
                         join r in q on
                         new { s.POrderId, s.Color, s.LotNo, ShipperId = s.ReceiverId } equals new { r.POrderId, r.Color, r.LotNo, r.ShipperId }
                         select new LossRate_View
                         {
                             ID = new(),
                             Procedure = s.Procedure,
                             OrderNo = s.OrderNo,
                             Shipper = s.ShipperName,
                             Receiver = s.ReceiverName,
                             LotNo = s.LotNo,
                             Color = s.Color,
                             ShipperPcs = s.Pcs,
                             ShipperMeters = s.Meters,
                             ReceiverPcs = r.Pcs,
                             ReceiverMeters = r.Meters,
                             LossRate = (r.Meters - s.Meters) / s.Meters,
                             POrderId = s.POrderId,
                             ShipperId = s.ShipperId
                         })
                         .OrderBy(x => x.Procedure);

            var qq = query.JsonSerialise();
            var q2 = qq.Substring(1, qq.Length - 2);
            
            
            return Content(q2);

        }
        else
        {
            return BadRequest(ModelState.GetErrorJson());
        }
    }

    [ActionDescription("Sys.Get")]
    [HttpGet("{id}")]
    public WIPDetailVM Get(string id)
    {
        var vm = Wtm.CreateVM<WIPDetailVM>(id);
        return vm;
    }

    [ActionDescription("Sys.Create")]
    [HttpPost("Add")]
    public IActionResult Add(WIPDetailVM vm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState.GetErrorJson());
        }
        else
        {
            vm.DoAdd();
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Entity);
            }
        }

    }

    [ActionDescription("Sys.Edit")]
    [HttpPut("Edit")]
    public IActionResult Edit(WIPDetailVM vm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState.GetErrorJson());
        }
        else
        {
            vm.DoEdit(false);
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Entity);
            }
        }
    }

    [HttpPost("BatchDelete")]
    [ActionDescription("Sys.Delete")]
    public IActionResult BatchDelete(string[] ids)
    {
        var vm = Wtm.CreateVM<WIPDetailBatchVM>();
        if (ids != null && ids.Count() > 0)
        {
            vm.Ids = ids;
        }
        else
        {
            return Ok();
        }
        if (!ModelState.IsValid || !vm.DoBatchDelete())
        {
            return BadRequest(ModelState.GetErrorJson());
        }
        else
        {
            return Ok(ids.Count());
        }
    }


    [ActionDescription("Sys.Export")]
    [HttpPost("ExportExcel")]
    public IActionResult ExportExcel(WIPDetailSearcher searcher)
    {
        var vm = Wtm.CreateVM<WIPDetailListVM>();
        vm.Searcher = searcher;
        vm.SearcherMode = ListVMSearchModeEnum.Export;
        return vm.GetExportData();
    }

    [ActionDescription("Sys.CheckExport")]
    [HttpPost("ExportExcelByIds")]
    public IActionResult ExportExcelByIds(string[] ids)
    {
        var vm = Wtm.CreateVM<WIPDetailListVM>();
        if (ids != null && ids.Count() > 0)
        {
            vm.Ids = new List<string>(ids);
            vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
        }
        return vm.GetExportData();
    }

    [ActionDescription("Sys.DownloadTemplate")]
    [HttpGet("GetExcelTemplate")]
    public IActionResult GetExcelTemplate()
    {
        var vm = Wtm.CreateVM<WIPDetailImportVM>();
        var qs = new Dictionary<string, string>();
        foreach (var item in Request.Query.Keys)
        {
            qs.Add(item, Request.Query[item]);
        }
        vm.SetParms(qs);
        var data = vm.GenerateTemplate(out string fileName);
        return File(data, "application/vnd.ms-excel", fileName);
    }

    [ActionDescription("Sys.Import")]
    [HttpPost("Import")]
    public ActionResult Import(WIPDetailImportVM vm)
    {
        if (vm != null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
        {
            return BadRequest(vm.GetErrorJson());
        }
        else
        {
            return Ok(vm?.EntityList?.Count ?? 0);
        }
    }


}
