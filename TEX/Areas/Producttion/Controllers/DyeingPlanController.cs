using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Producttion.DyeingPlanVMs;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using TEX.Model.Producttion;
using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Localization;
using System.Globalization;

namespace TEX.Controllers
{
    [Area("Producttion")]
    [AuthorizeJwt]
    [ActionDescription("染色计划")]
    [ApiController]
    [Route("api/DyeingPlan")]
    public partial class DyeingPlanController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
        public IActionResult Search(DyeingPlanSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<DyeingPlanListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public DyeingPlanVM Get(string id)
        {
            var vm = Wtm.CreateVM<DyeingPlanVM>(id);
            return vm;
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(DyeingPlanVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(DyeingPlanVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<DyeingPlanBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(DyeingPlanSearcher searcher)
        {
            var vm = Wtm.CreateVM<DyeingPlanListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<DyeingPlanListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<DyeingPlanImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(DyeingPlanImportVM vm)
        {
            if (vm != null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }


        [HttpGet("GetCompanys")]
        public ActionResult GetCompanys()
        {
            return Ok(DC.Set<Company>().GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetPurchaseOrders")]
        public ActionResult GetPurchaseOrders()
        {
            return Ok(DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, x => x.OrderNo));
        }

        [HttpGet("GetProducts")]
        public ActionResult GetProducts()
        {
            return Ok(DC.Set<Product>().GetSelectListItems(Wtm, x => x.ProductName));
        }

        [HttpGet("GetOrderNoFromDyeingPlanId/{id}")]
        public ActionResult GetOrderNoFromDyeingPlanId(string id)
        {
            var r = DC.Set<DyeingPlan>()
                .Where(x => x.ID == Guid.Parse(id))
                .Join(DC.Set<PurchaseOrder>(), od => od.POrderId, p => p.ID, (od, p) => new { od, p })
                .Select(x => new
                {
                    OrderNo = x.p.OrderNo,
                })
                .ToList();
            if (r.Count == 0)
            {
                return NotFound("没有找到对应的订单号!");
            }
            var orderNo = r[r.Count - 1].OrderNo;
            return Ok(orderNo);
        }

        [HttpPost("[action]")]
        //在参数前加[FromBody],Request Body为json参数
        //参数不加,则是query方式,即api/UpdateAuditField?id=xxxxxx-xxx-xxx-xxxxx方式,这种方式无法使用PostAsync来直接调用
        public ActionResult UpdateAuditField([FromBody] Guid id)
        {

            // 根据id查询Order表中的一条记录
            var entity = DC.Set<DyeingPlan>().FirstOrDefault(o => o.ID == id);

            if (entity != null)
            {
                //审核反审一次性解决
                entity.AuditStatus = entity.AuditStatus == AuditStatusEnum.AuditedApproved ? AuditStatusEnum.NotAudited : AuditStatusEnum.AuditedApproved;

                DC.SaveChangesAsync();
                return Ok();
            }
            else
            {
                throw new Exception("查询ID为空:" + id);
            }
        }

        [HttpGet("GetDyeingPlanTreeItem")]
        public ActionResult GetDyeingPlanTreeItem()
        {
            var pOrders = DC.Set<PurchaseOrder>().OrderByDescending(x => x.CreateDate).Select(pOrder => new DyeingPlanWithDetails_TreeView
            {
                ID = pOrder.ID,
                //ParentID=Guid.Empty,
                DisplayText = pOrder.OrderNo + " - " + pOrder.Product.ProductName,
            }).ToList();

            var plan = DC.Set<DyeingPlan>().OrderByDescending(x => x.CreateDate).Select(p => new DyeingPlanWithDetails_TreeView
            {
                ID = p.ID,
                ParentID = p.POrderId,
                DisplayText = p.BillNo,
            }).ToList();

            var details = DC.Set<PlanDetail>().Select(d => new DyeingPlanWithDetails_TreeView
            {
                ID = d.ID,
                ParentID = d.DyeingPlanId,
                DisplayText = d.Color,
            }).ToList();
            //不加ToList报错:can't parse JSON.:System.InvalidOperationException: Unable to translate set operation when matching columns on both sides have different store types.

            var query = pOrders.Concat(plan).Concat(details).AsQueryable();
            return Ok(query);
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("GetDyeingFacInDyeingPlan")]
        public ActionResult GetDyeingFacInDyeingPlan()
        {
            var facs = DC.Set<DyeingPlan>().Select(x => x.FinishingFactoryId);
            //在Company中查询出facs中的对应记录
            var dyeingFacs = DC.Set<Company>()
               .Where(x => facs.Contains(x.ID))
               .GetSelectListItems(Wtm,x => x.CompanyName);
               
            
            return Ok(dyeingFacs);
            
        }



        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            StringEscapeHandling = StringEscapeHandling.Default,
            Formatting = Formatting.None,
            NullValueHandling = NullValueHandling.Include,
            DateFormatString = "yyyy-MM-dd HH:mm:ss"
        };


        [HttpGet("GetDyeingPlanDTO/{id}")]
        public IActionResult GetDyeingPlanDTO(string id)
        {
            // 获取正确的 localizer，使用 Shared.Program 作为资源类型
            var localizer = Wtm.Localizer;

            var dto = DC.Set<DyeingPlan>()
                .Include(x => x.DetailList)
                .Include(x => x.POrder)
                .Include(x => x.FinishingFactory)
                .CheckID(id)
                .Select(x => new DyeingPlanDTO
                {
                    BillNo = x.BillNo,
                    OrderNO = x.POrder.OrderNo,
                    ProductName = x.POrder.Product.ProductName,
                    FinishingFactory = x.FinishingFactory.CompanyName,
                    CreateDate = x.CreateDate,
                    PlanBatch = x.PlanBatch ?? "",


                    Light = GetEnumDisplayName(x.Light, localizer),
                    Light2 = GetEnumDisplayName(x.Light2, localizer),
                    Width = x.Width,
                    GSM = x.GSM,



                    Pcs = x.Pcs,
                    TotalWeight = x.TotalWeight,
                    TotalMeters = x.TotalMeters,
                    GreigeBatch = x.GreigeBatch ?? "",

                    DyeingDemand=x.DyeingDemand ?? "",
                    PackDemand=x.PackDemand ?? "",

                    CreateBy = x.CreateBy ?? "",
                    AuditedBy = x.AuditedBy ?? "",
                    Remark = x.Remark ?? "",

                    PlanDetails = x.DetailList.Select(d => new PlanDetailDTO
                    {
                        Color = d.OrderDetail.Color,
                        ColorCode = d.OrderDetail.ColorCode,
                        Pcs = d.Pcs,
                        Meters = d.Meters,
                        GreigeBatch = d.GreigeBatch ?? "",
                        QtyUnit = GetEnumDisplayName(d.QtyUnit, localizer),
                        Qty = d.Qty,
                        Weight = d.Weight,
                        DeliveryDate = d.DeliveryDate,
                        Remark = d.Remark ?? "",

                    }).ToList()
                }).FirstOrDefault();

            if (dto == null)
                return NotFound();

            // 处理嵌套的 JSON 字符串中的枚举
            //dto.DyeingInfoJson = ConvertDyeingInfo(dto.DyeingInfoJson);
            //dto.ChemicalInfoJson = ConvertChemicalInfo(dto.ChemicalInfoJson);

            return Content(JsonConvert.SerializeObject(dto, JsonSettings), "application/json");
        }

        private static string GetEnumDisplayName(Enum enumValue, IStringLocalizer localizer)
        {
            //var cultureInfo = new CultureInfo("zh");
            //CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
            //CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;
            if (enumValue == null)
                return "";
            var enumType = enumValue.GetType();
            var enumValueString = enumValue.ToString();
            var memberInfo = enumType.GetField(enumValueString);

            string  displayname=string.Empty;

            if (memberInfo == null)
                return enumValueString;

            // 2. 尝试获取Display特性
            var displayAttribute = memberInfo.GetCustomAttributes(typeof(DisplayAttribute), false)
                .FirstOrDefault() as DisplayAttribute;
            if (displayAttribute != null && !string.IsNullOrEmpty(displayAttribute.Name))
            {
                displayname= displayAttribute.Name;
            }

            // 1. 尝试获取多语言配置
            var resourceKey = displayname;
            var localizedValue = localizer[resourceKey];
            //var localizedValue = localizer["_Enum._LightEnum._D65"];
            //var i = localizedValue.Value;
            //var l = localizer["_Product"].ToString();
            if (!localizedValue.ResourceNotFound && !string.IsNullOrEmpty(localizedValue.Value))
            {
                displayname = localizedValue.Value;
            }

            

            // 3. 返回枚举值的原始字符串
            return displayname ;
        }

        
    }
}
