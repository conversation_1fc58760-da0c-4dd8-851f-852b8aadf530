using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.Model;
using TEX.ViewModel.Models.ProductVMs;


namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    public partial class ProductController : BaseApiController
    {
                                                
        [ActionDescription("Sys.Search")]
        [HttpPost("[action]")]
        public IActionResult SearchProduct(TEX.ViewModel.Models.ProductVMs.ProductSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<TEX.ViewModel.Models.ProductVMs.ProductListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Export")]
        [HttpPost("[action]")]
        public IActionResult ProductExportExcel(TEX.ViewModel.Models.ProductVMs.ProductSearcher searcher)
        {
            var vm = Wtm.CreateVM<TEX.ViewModel.Models.ProductVMs.ProductListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("[action]")]
        public IActionResult ProductExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<TEX.ViewModel.Models.ProductVMs.ProductListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }
    
    }
}


