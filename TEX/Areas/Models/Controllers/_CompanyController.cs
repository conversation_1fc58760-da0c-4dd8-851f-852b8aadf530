using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Mvc;
using WalkingTec.Mvvm.Core.Extensions;
using System.Linq;
using System.Collections.Generic;
using TEX.Model.Models;
using TEX.ViewModel.Models.CompanyVMs;
using TEX.Model;

namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    [ActionDescription("_Model.Company")]
    [ApiController]
    [Route("/api/Models/Company")]
    public partial class CompanyController : BaseApiController
    {
        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public IActionResult Get(string id)
        {
            var vm = Wtm.CreateVM<CompanyVM>(id);
            return Ok(vm);
        }


        [HttpGet("GetCustomerCompanys")]
        public ActionResult GetCustomerCompanys()
        {
            return Ok(DC.Set<Company>().Where(x => x.Relationship == RelationshipEnum.Customer && x.AuditStatus == AuditStatusEnum.AuditedApproved).GetSelectListItems(Wtm, x => x.CompanyName + "-" + x.CompanyCode));
        }

        [HttpGet("GetDyeingFactorys")]
        public ActionResult GetDyeingFactorys()
        {
            return Ok(DC.Set<Company>().Where(x => x.CompanyType == CompanyTypeEnum.DyeingFactory).Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved).GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetFinishingFactorys")]
        public ActionResult GetFinishingFactorys()
        {
            return Ok(DC.Set<Company>().Where(x => x.CompanyType == CompanyTypeEnum.DyeingFactory || x.CompanyType == CompanyTypeEnum.FinishingFactory).Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved).GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetVenderCompanys")]
        public ActionResult GetVenderCompanys()
        {
            return Ok(DC.Set<Company>().Where(x => x.Relationship == RelationshipEnum.Vender && x.AuditStatus == AuditStatusEnum.AuditedApproved).GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetGreigeVenderCompanys")]
        public ActionResult GetGreigeVenderCompanys()
        {
            return Ok(DC.Set<Company>().Where(x => x.Relationship == RelationshipEnum.Vender && x.AuditStatus == AuditStatusEnum.AuditedApproved && x.CompanyType == CompanyTypeEnum.GreigeVender).GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetKnittingFactories")]
        public ActionResult GetKnittingFactories()
        {
            return Ok(DC.Set<Company>().Where(x => x.Relationship == RelationshipEnum.Vender && x.CompanyType == CompanyTypeEnum.KnittingFactory && x.AuditStatus == AuditStatusEnum.AuditedApproved).GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("[action]")]
        public async Task<IActionResult> Create(CompanyVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoAddAsync();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("[action]")]
        public async Task<IActionResult> Edit(CompanyVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoEditAsync(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [HttpPost("BatchEdit")]
        [ActionDescription("Sys.BatchEdit")]
        public ActionResult BatchEdit(CompanyBatchVM vm)
        {
            if (!ModelState.IsValid || !vm.DoBatchEdit())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Ids.Count());
            }
        }

        [HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<CompanyBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<CompanyImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(CompanyImportVM vm)
        {

            if (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData())
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm.EntityList.Count);
            }
        }

        [ActionDescription("_Audit")]
        [HttpPost("[action]")]
        public ActionResult UpdateAuditField([FromBody] Guid id)
        {
            // 根据id查询表中的一条记录
            var order = DC.Set<Company>().FirstOrDefault(o => o.ID == id);

            if (order != null)
            {
                //审核反审一次性解决
                order.AuditStatus = order.AuditStatus == AuditStatusEnum.AuditedApproved ? AuditStatusEnum.NotAudited : AuditStatusEnum.AuditedApproved;
                DC.SaveChangesAsync();

                return Ok();
            }
            else
            {
                throw new Exception("查询ID为空:" + id);
            }
        }


    }
}