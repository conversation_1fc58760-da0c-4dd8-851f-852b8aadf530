using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.Model;
using TEX.ViewModel.Models.CompanyVMs;


namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    public partial class CompanyController : BaseApiController
    {
                                                
        [ActionDescription("Sys.Search")]
        [HttpPost("[action]")]
        public IActionResult SearchCompany(TEX.ViewModel.Models.CompanyVMs.CompanySearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<TEX.ViewModel.Models.CompanyVMs.CompanyListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Export")]
        [HttpPost("[action]")]
        public IActionResult CompanyExportExcel(TEX.ViewModel.Models.CompanyVMs.CompanySearcher searcher)
        {
            var vm = Wtm.CreateVM<TEX.ViewModel.Models.CompanyVMs.CompanyListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("[action]")]
        public IActionResult CompanyExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<TEX.ViewModel.Models.CompanyVMs.CompanyListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }
    
    }
}


