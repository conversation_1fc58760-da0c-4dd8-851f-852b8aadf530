using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Mvc;
using WalkingTec.Mvvm.Core.Extensions;
using System.Linq;
using System.Collections.Generic;
using TEX.Model.Models;
using TEX.ViewModel.Models.ProductVMs;
using TEX.Model;

namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    [ActionDescription("_Model.Product")]
    [ApiController]
    [Route("/api/Models/Product")]
    public partial class ProductController : BaseApiController
    {
        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public IActionResult Get(string id)
        {
            var vm = Wtm.CreateVM<ProductVM>(id);
            return Ok(vm);
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("[action]")]
        public async Task<IActionResult> Create(ProductVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoAddAsync();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("[action]")]
        public async Task<IActionResult> Edit(ProductVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoEditAsync(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [HttpPost("BatchEdit")]
        [ActionDescription("Sys.BatchEdit")]
        public ActionResult BatchEdit(ProductBatchVM vm)
        {
            if (!ModelState.IsValid || !vm.DoBatchEdit())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Ids.Count());
            }
        }

		[HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<ProductBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<ProductImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(ProductImportVM vm)
        {

            if (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData())
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm.EntityList.Count);
            }
        }



        
        [HttpGet("GetProducts")]
        public ActionResult GetProducts(bool istree=false, bool istop=false)
        {
            var rv = DC.Set<Product>().Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved);
            if (istree == true){
                if(istop == false){
                    return Ok(rv.GetTreeSelectListItems(Wtm, x => x.ProductName));
                }
                else{
                    return Ok(rv.Where(x=>x.ParentId == null).GetTreeSelectListItems(Wtm, x => x.ProductName));
                }
            }
            else{
                if(istop == false){
                    return Ok(rv.GetSelectListItems(Wtm, x => x.ProductName));
                }
                else{
                    return Ok(rv.Where(x=>x.ParentId == null).GetSelectListItems(Wtm, x => x.ProductName));
                }
            }
        }
        [HttpPost("[action]")]
        public ActionResult Select_GetProductByProductId(List<string> id)
        {
            var rv = DC.Set<Product>().CheckIDs(id).GetSelectListItems(Wtm, x => x.ProductName);
            return Ok(rv);
        }

        [HttpPost("[action]")]
        public ActionResult Select_GetProductByProduct(List<string> id)
        {
            var rv = DC.Set<Product>().CheckIDs(id, x => x.ParentId).GetSelectListItems(Wtm,x=>x.ProductName);
            return Ok(rv);
        }


        [ActionDescription("_Audit")]
        [HttpPost("[action]")]
        public ActionResult UpdateAuditField([FromBody] Guid id)
        {
            // 根据id查询表中的一条记录
            var order = DC.Set<Product>().FirstOrDefault(o => o.ID == id);

            if (order != null)
            {
                //审核反审一次性解决
                order.AuditStatus = order.AuditStatus == AuditStatusEnum.AuditedApproved ? AuditStatusEnum.NotAudited : AuditStatusEnum.AuditedApproved;
                DC.SaveChangesAsync();
                return Ok();
            }
            else
            {
                throw new Exception("查询ID为空:" + id);
            }
        }
    }
}