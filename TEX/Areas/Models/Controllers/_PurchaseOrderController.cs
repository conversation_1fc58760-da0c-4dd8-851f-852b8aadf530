using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Mvc;
using WalkingTec.Mvvm.Core.Extensions;
using System.Linq;
using System.Collections.Generic;
using TEX.Model.Models;
using TEX.ViewModel.Models.PurchaseOrderVMs;
using TEX.Model;
using Microsoft.EntityFrameworkCore;
using BootstrapBlazor.Components;
using NPOI.SS.Formula.Functions;
using BootstrapBlazor.Shared;
using TEX.ViewModel.Models;

namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    [ActionDescription("_Model.PurchaseOrder")]
    [ApiController]
    [Route("/api/Models/PurchaseOrder")]
    public partial class PurchaseOrderController : BaseApiController
    {
        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public IActionResult Get(string id)
        {
            var vm = Wtm.CreateVM<PurchaseOrderVM>(id);
            return Ok(vm);
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("[action]")]
        public async Task<IActionResult> Create(PurchaseOrderVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoAddAsync();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("[action]")]
        public async Task<IActionResult> Edit(PurchaseOrderVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoEditAsync(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [HttpPost("BatchEdit")]
        [ActionDescription("Sys.BatchEdit")]
        public ActionResult BatchEdit(PurchaseOrderBatchVM vm)
        {
            if (!ModelState.IsValid || !vm.DoBatchEdit())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Ids.Length);
            }
        }

		[HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<PurchaseOrderBatchVM>();
            if (ids != null && ids.Length > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Length);
            }
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<PurchaseOrderImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(PurchaseOrderImportVM vm)
        {

            if (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData())
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm.EntityList.Count);
            }
        }


        [HttpPost("[action]")]
        public ActionResult Select_GetCompanyByCompanyId(List<string> id)
        {
            var rv = DC.Set<Company>().CheckIDs(id).GetSelectListItems(Wtm, x => x.CompanyName);
            return Ok(rv);
        }

        [HttpPost("[action]")]
        public ActionResult Select_GetPurchaseOrderByCompany(List<string> id)
        {
            var rv = DC.Set<PurchaseOrder>().CheckIDs(id, x => x.CustomerId).GetSelectListItems(Wtm,x=>x.OrderNo);
            return Ok(rv);
        }



        [HttpPost("[action]")]
        public ActionResult Select_GetProductByProductId(List<string> id)
        {
            var rv = DC.Set<Product>().CheckIDs(id).GetSelectListItems(Wtm, x => x.ProductName);
            return Ok(rv);
        }

        [HttpPost("[action]")]
        public ActionResult Select_GetPurchaseOrderByProduct(List<string> id)
        {
            var rv = DC.Set<PurchaseOrder>().CheckIDs(id, x => x.ProductId).GetSelectListItems(Wtm,x=>x.OrderNo);
            return Ok(rv);
        }

        [HttpPost("[action]")]
        //在参数前加[FromBody],Request Body为json参数
        //参数不加,则是query方式,即api/UpdateAuditField?id=xxxxxx-xxx-xxx-xxxxx方式,这种方式五法使用PostAsync来直接调用
        public ActionResult UpdateAuditField([FromBody] Guid id)
        {

            // 根据id查询Order表中的一条记录
            var order =  DC.Set<PurchaseOrder>().FirstOrDefault(o => o.ID == id);

            if (order != null)
            {
                //审核反审一次性解决
                order.AuditStatus = order.AuditStatus == AuditStatusEnum.AuditedApproved ? AuditStatusEnum.NotAudited : AuditStatusEnum.AuditedApproved;

                //DC.Set<PurchaseOrder>().SetPropertyValue("AuditStatus", AuditStatusEnum.AuditedApproved);
                // 提交更改到数据库
                DC.SaveChangesAsync();
                return Ok();
            }
            else
            {
                throw new Exception("查询ID为空:"+id);
            }
        }

        [HttpGet("GetPurchaseOrders")]
        public ActionResult GetPurchaseOrders()
        {
            return Ok(DC.Set<PurchaseOrder>().Where(x=>x.AuditStatus==AuditStatusEnum.AuditedApproved).OrderByDescending(x => x.CreateDate).GetSelectListItems(Wtm, x => x.OrderNo, SortByName:false));
        }

        [HttpGet("GetPOrderTreeItem")]
        public ActionResult GetPOrderTreeItem(string customerId)
        {

            var pOrders = DC.Set<PurchaseOrder>().Where(x => x.CustomerId == Guid.Parse(customerId) && x.AuditStatus == AuditStatusEnum.AuditedApproved).OrderByDescending(x => x.CreateDate).Select(pOrder => new PurchaseOrderWithDetails_TreeView
            {
                ID = pOrder.ID,
                //ParentID=Guid.Empty,
                Text = pOrder.OrderNo + " - " + pOrder.Product.ProductName,
            }).ToList();

            var pOrderDetails = DC.Set<OrderDetail>().Select(d => new PurchaseOrderWithDetails_TreeView
            {
                ID = d.ID,
                ParentId = d.PurchaseOrderId,
                Text = d.Color,
            }).ToList();
            //不加ToList报错:can't parse JSON.:System.InvalidOperationException: Unable to translate set operation when matching columns on both sides have different store types.

            var query = pOrders.Concat(pOrderDetails).AsQueryable();
            return Ok(query);
        }

        [HttpGet("GetCustomerPOTreeItem")]
        public ActionResult GetCustomerPOTreeItem()
        {
            // 获取在PurchaseOrder表中有关联记录的客户作为根节点
            var customerIds = DC.Set<PurchaseOrder>()
                .Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved)
                .Select(x => x.CustomerId)
                .Distinct();

            var customers = DC.Set<Company>()
                .Where(c => customerIds.Contains(c.ID))
                .OrderBy(x => x.CompanyName)
                .Select(customer => new PurchaseOrderWithDetails_TreeView
                {
                    ID = customer.ID,
                    ParentId = null, // 客户是根节点，没有父节点
                    Text = customer.CompanyName,
                    Icon = "customer-icon",
                    IsActive = true
                }).ToList();

            // 获取所有采购订单作为子节点
            var purchaseOrders = DC.Set<PurchaseOrder>()
                .Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved)
                .OrderByDescending(x => x.CreateDate)
                .Select(order => new PurchaseOrderWithDetails_TreeView
                {
                    ID = order.ID,
                    ParentId = order.CustomerId, // 将客户ID设置为父节点
                    Text = order.OrderNo + " - " + order.Product.ProductName,
                    Icon = "order-icon",
                    IsActive = true
                }).ToList();

            // 合并客户和订单数据
            var query = customers.Concat(purchaseOrders).ToList();
            return Ok(query);
        }

        [HttpGet("GetPOrderTreeItem/{id}")]
        public ActionResult GetPOrderTreeItemByCustomerId(string id)
        {
            if (!Guid.TryParse(id, out Guid customerId))
            {
                return Ok(new List<PurchaseOrderWithDetails_TreeView>());
            }
            //var pOrders = DC.Set<PurchaseOrder>()
            //    .Where(p => p.CustomerId == customerId)
            //    .OrderByDescending(x => x.CreateDate)
            //    .Select(pOrder => new PurchaseOrderWithDetails_TreeView
            //{
            //    ID = pOrder.ID,
            //    //ParentID=Guid.Empty,
            //    DisplayText = pOrder.OrderNo + " - " + pOrder.Product.ProductName,
            //}).ToList();

            //var pOrderDetails = DC.Set<OrderDetail>().Select(d => new PurchaseOrderWithDetails_TreeView
            //{
            //    ID = d.ID,
            //    ParentID = d.PurchaseOrderId,
            //    DisplayText = d.Color,
            //}).ToList();
            ////不加ToList报错:can't parse JSON.:System.InvalidOperationException: Unable to translate set operation when matching columns on both sides have different store types.

            //var query = pOrders.Concat(pOrderDetails).AsQueryable();

            // 一次性查询出所有需要的订单和订单明细
            var pOrders = DC.Set<PurchaseOrder>()
        .Where(p => p.CustomerId == customerId)
        .OrderByDescending(x => x.CreateDate)
        .Select(pOrder => new PurchaseOrderWithDetails_TreeView
        {
            ID = pOrder.ID,
            ParentId = null, // 订单没有父节点
            Text = pOrder.OrderNo + " - " + pOrder.Product.ProductName,
            Icon = "order-icon", // 假设有一个订单图标
            IsActive = true
        })
        .ToList();

            var pOrderDetails = DC.Set<OrderDetail>()
                .Where(d => d.PurchaseOrder.CustomerId == customerId)
                .Select(d => new PurchaseOrderWithDetails_TreeView
                {
                    ID = d.ID,
                    ParentId = d.PurchaseOrderId,
                    Text = d.Color,
                    Icon = "detail-icon", // 假设有一个订单明细图标
                    IsActive = true
                })
                .ToList();

            // 合并订单和订单明细
            var query = pOrders.Concat(pOrderDetails).ToList();

            return Ok(query);
        }


        [HttpPost("[action]")]
        public IActionResult SearchAuthPurchaseOrder(PurchaseOrderSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<PurchaseOrderListVM>();
                vm.Searcher = searcher;
                vm.Searcher.AuditStatus = AuditStatusEnum.AuditedApproved;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [HttpPost("[action]")]
        public IActionResult SearchPurchaseOrderByFinishedFactory(string id)
        {
            //var ids = DC.Set<DyeingPlan>().Where(dp => dp.FinishingFactoryId == Guid.Parse(id))
            //.Select(dp => dp.POrderId)
            //.Distinct();
            //var rv = DC.Set<PurchaseOrder>().Where(po => ids.Contains(po.ID));
            //return Ok(rv);
            if (!Guid.TryParse(id, out var factoryId))
            {
                return BadRequest("Invalid FinishingFactoryId.");
            }

            var purchaseOrders = DC.Set<DyeingPlan>()
                .Where(dp => dp.FinishingFactoryId == factoryId)
                .Select(dp => dp.POrderId)
                .Distinct()
                .Join(DC.Set<PurchaseOrder>().AsNoTracking(), dp => dp, po => po.ID, (dp, x) => new PurchaseOrder_View
                {
                    ID = x.ID,
                    PurchaseOrder_CreateDate = x.CreateDate,
                    PurchaseOrder_Customer = x.Customer.CompanyName,
                    PurchaseOrder_OrderNo = x.OrderNo,
                    PurchaseOrder_CustomerOrderNo = x.CustomerOrderNo,
                    PurchaseOrder_Merchandiser = x.Merchandiser.ContactName,
                    PurchaseOrder_OrderType = x.OrderType,
                    PurchaseOrder_Product = x.Product.ProductName,
                    PurchaseOrder_Light = x.Light,
                    PurchaseOrder_TotalMeters = x.TotalMeters,
                    PurchaseOrder_TotalYards = x.TotalYards,
                    PurchaseOrder_TotalWeight = x.TotalWeight,
                    AuditStatus = x.AuditStatus,
                });//.AsEnumerable();
            //QueryData<PurchaseOrder_View> data = new QueryData<PurchaseOrder_View>();
            
                //data.Items = purchaseOrders;
                //data.TotalCount = purchaseOrders.Count();
            
            return Ok(purchaseOrders);
        }

        [HttpGet("[action]")]
        public IActionResult GetOrderData()
        {
            //本年
            var thisYearTotalAmount = Wtm.DC.Set<PurchaseOrder>()
                .Where(x => x.CreateDate.Year == DateTime.Now.Year)
                .Sum(x => x.TotalAmount);
            var thisYearTotalWeight = Wtm.DC.Set<PurchaseOrder>()
                .Where(x => x.CreateDate.Year == DateTime.Now.Year)
                .Sum(x => x.TotalWeight);

            //本月
            var thisMonthTotalAmount = Wtm.DC.Set<PurchaseOrder>()
                .Where(x => x.CreateDate.Year == DateTime.Now.Year && x.CreateDate.Month == DateTime.Now.Month)
                .Sum(x => x.TotalAmount);
            var thisMonthTotalWeight = Wtm.DC.Set<PurchaseOrder>()
                .Where(x => x.CreateDate.Year == DateTime.Now.Year && x.CreateDate.Month == DateTime.Now.Month)
                .Sum(x => x.TotalWeight);

            //7天内
            var weekTotalAmount = Wtm.DC.Set<PurchaseOrder>()
                .Where(x => x.CreateDate <= DateTime.Now && x.CreateDate >= DateTime.Today.AddDays(-7))
                .Sum(x => x.TotalAmount);
            var weekTotalWeight = Wtm.DC.Set<PurchaseOrder>()
                .Where(x => x.CreateDate <= DateTime.Now && x.CreateDate >= DateTime.Today.AddDays(-7))
                .Sum(x => x.TotalWeight);


            var monthlyTotalWeights = Wtm.DC.Set<PurchaseOrder>()
                    .GroupBy(po => new { po.CreateDate.Year, po.CreateDate.Month })
                    .Select(g => new MonthlyTotalWeight
                    {
                        YearMonth = new DateTime(g.Key.Year, g.Key.Month, 1),
                        TotalWeight = g.Sum(po => po.TotalWeight ?? 0)
                    })
                    .AsEnumerable()
                    .OrderBy(x => x.YearMonth)
                    //.OrderByDescending(x => x.YearMonth)
                    .ToList();
            var monthlyTotalAmount = Wtm.DC.Set<PurchaseOrder>()
                    .GroupBy(po => new { po.CreateDate.Year, po.CreateDate.Month })
                    .Select(g => new MonthlyTotalAmount
                    {
                        YearMonth = new DateTime(g.Key.Year, g.Key.Month, 1),
                        TotalAmount = g.Sum(po => po.TotalAmount ?? 0)
                    })
                    .ToList();
            return Ok(new PODashboardData
            {
                POYearTotalAmount = thisYearTotalAmount ?? 0,
                POMonthTotalAmount = thisMonthTotalAmount ?? 0,
                POWeekTotalAmount = weekTotalAmount ?? 0,
                POYearTotalWeight = thisYearTotalWeight ?? 0,
                POMonthTotalWeight = thisMonthTotalWeight ?? 0,
                POWeekTotalWeight = weekTotalWeight ?? 0,
                MonthlyTotalWeight = monthlyTotalWeights,
                MonthlyTotalAmount = monthlyTotalAmount
            });
        }

        [ActionDescription("SearchOrderDetail")]
        [HttpPost("[action]")]
        public IActionResult SearchOrderDetail(TEX.ViewModel.Models.OrderDetailVMs.OrderDetailSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<TEX.ViewModel.Models.OrderDetailVMs.OrderDetailListVM>();
                vm.Searcher = searcher;
                //searcher.Limit = 60000;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }
    }
}