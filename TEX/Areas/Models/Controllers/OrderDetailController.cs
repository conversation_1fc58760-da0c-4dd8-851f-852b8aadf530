using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.Model;
using TEX.ViewModel.Models.OrderDetailVMs;


namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    public partial class OrderDetailController : BaseApiController
    {
                                                
        [ActionDescription("Sys.Search")]
        [HttpPost("[action]")]
        public IActionResult SearchOrderDetail(TEX.ViewModel.Models.OrderDetailVMs.OrderDetailSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<TEX.ViewModel.Models.OrderDetailVMs.OrderDetailListVM>();
                vm.Searcher = searcher;
                //searcher.Limit = 60000;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Export")]
        [HttpPost("[action]")]
        public IActionResult OrderDetailExportExcel(TEX.ViewModel.Models.OrderDetailVMs.OrderDetailSearcher searcher)
        {
            var vm = Wtm.CreateVM<TEX.ViewModel.Models.OrderDetailVMs.OrderDetailListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("[action]")]
        public IActionResult OrderDetailExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<TEX.ViewModel.Models.OrderDetailVMs.OrderDetailListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        /// <summary>
        /// 返回所有订单颜色
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetOrderDetails")]
        public ActionResult GetOrderDetails()
        {
            var rv= Ok(DC.Set<OrderDetail>().GetSelectListItems(Wtm, x => x.PurchaseOrder.OrderNo + " - " + x.Color));
            return rv;
        }

    }
}


