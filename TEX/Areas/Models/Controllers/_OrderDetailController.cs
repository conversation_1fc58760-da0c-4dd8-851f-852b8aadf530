using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Mvc;
using WalkingTec.Mvvm.Core.Extensions;
using System.Linq;
using System.Collections.Generic;
using TEX.Model.Models;
using TEX.ViewModel.Models.OrderDetailVMs;
using TEX.Model;
using Microsoft.EntityFrameworkCore;
using BootstrapBlazor.Components;

namespace TEX.Models.Controllers
{
    [AuthorizeJwtWithCookie]
    [ActionDescription("_Model.OrderDetail")]
    [ApiController]
    [Route("/api/Models/OrderDetail")]
    public partial class OrderDetailController : BaseApiController
    {
        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public IActionResult Get(string id)
        {
            var vm = Wtm.CreateVM<OrderDetailVM>(id);
            return Ok(vm);
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("[action]")]
        public async Task<IActionResult> Create(OrderDetailVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoAddAsync();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("[action]")]
        public async Task<IActionResult> Edit(OrderDetailVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                await vm.DoEditAsync(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [HttpPost("BatchEdit")]
        [ActionDescription("Sys.BatchEdit")]
        public ActionResult BatchEdit(OrderDetailBatchVM vm)
        {
            if (!ModelState.IsValid || !vm.DoBatchEdit())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Ids.Count());
            }
        }

        [HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<OrderDetailBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<OrderDetailImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(OrderDetailImportVM vm)
        {

            if (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData())
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm.EntityList.Count);
            }
        }




        [HttpGet("GetPurchaseOrders")]
        public ActionResult GetPurchaseOrders()
        {
            return Ok(DC.Set<PurchaseOrder>().Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved).GetSelectListItems(Wtm, x => x.OrderNo));
        }
        [HttpPost("[action]")]
        public ActionResult Select_GetPurchaseOrderByPurchaseOrderId(List<string> id)
        {
            var rv = DC.Set<PurchaseOrder>().CheckIDs(id).GetSelectListItems(Wtm, x => x.OrderNo);
            return Ok(rv);
        }


        [HttpGet("[action]/{id}")]
        public IActionResult GetOrderDetailSelectListItemsByPurchaseOrderId(string id)
        {
            var rv = DC.Set<OrderDetail>().CheckID(id, x => x.PurchaseOrderId).GetSelectListItems(Wtm, x => x.Color);
            return Ok(rv);
        }

        [HttpGet("[action]/{id}")]
        public ActionResult GetOrderDetailByPurchaseOrderId(string id)
        {
            var rv = DC.Set<OrderDetail>().CheckID(id, x => x.PurchaseOrderId);
            return Ok(rv);
        }
        [HttpPost("[action]")]
        public ActionResult Select_GetOrderDetailByPurchaseOrder(List<string> id)
        {
            var rv = DC.Set<OrderDetail>().CheckIDs(id, x => x.PurchaseOrderId).GetSelectListItems(Wtm, x => x.Color);
            return Ok(rv);
        }

        [HttpGet("[action]/{id}")]
        public ActionResult GetOrderDetailByCustomerId(string id)
        {
            var orderDetails = DC.Set<OrderDetail>()
                         .Where(od => od.PurchaseOrder.CustomerId == Guid.Parse(id))
                         .ToList();

            return Ok(orderDetails);
        }

        [ActionDescription("Sys.Search")]
        [HttpPost("[action]")]
        public IActionResult SearchOrderDetailNoLimit(OrderDetailSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<OrderDetailListVM>();
                vm.Searcher = searcher;
                searcher.Limit = 100000;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }
    }
}