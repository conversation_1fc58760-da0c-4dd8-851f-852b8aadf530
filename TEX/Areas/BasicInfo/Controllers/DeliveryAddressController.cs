using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.BasicInfo.DeliveryAddressVMs;
using TEX.Model.Models;



namespace TEX.Controllers
{
    [Area("BasicInfo")]
    [AuthorizeJwt]
    [ActionDescription("货运地址")]
    [ApiController]
    [Route("api/DeliveryAddress")]
	public partial class DeliveryAddressController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
		public IActionResult Search(DeliveryAddressSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<DeliveryAddressListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [HttpGet("GetDeliveryAddress")]
        public ActionResult GetDeliveryAddress()
        {
            return Ok(DC.Set<DeliveryAddress>().GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetDeliveryAddress/{id}")]
        public ActionResult GetDeliveryAddress(string id)
        {
            return Ok(DC.Set<DeliveryAddress>().Where(x=>x.AffiliationCompanyId==Guid.Parse(id)).GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public DeliveryAddressVM Get(string id)
        {
            var vm = Wtm.CreateVM<DeliveryAddressVM>(id);
            return vm;
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(DeliveryAddressVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(DeliveryAddressVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

		[HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<DeliveryAddressBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(DeliveryAddressSearcher searcher)
        {
            var vm = Wtm.CreateVM<DeliveryAddressListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<DeliveryAddressListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<DeliveryAddressImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(DeliveryAddressImportVM vm)
        {
            if (vm!=null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }


        [HttpGet("GetCompanys")]
        public ActionResult GetCompanys()
        {
            return Ok(DC.Set<Company>().GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [ActionDescription("_Audit")]
        [HttpPost("[action]")]
        public ActionResult UpdateAuditField([FromBody] Guid id)
        {
            // 根据id查询表中的一条记录
            var rv = DC.Set<DeliveryAddress>().FirstOrDefault(o => o.ID == id);

            if (rv != null)
            {
                //审核反审一次性解决
                rv.AuditStatus = rv.AuditStatus == AuditStatusEnum.AuditedApproved ? AuditStatusEnum.NotAudited : AuditStatusEnum.AuditedApproved;
                DC.SaveChangesAsync();
                return Ok();
            }
            else
            {
                return BadRequest("数据不存在,请刷新页面:");
            }
        }
    }
}
