using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Finished.InspectPlanVMs;
using TEX.Model.Finished;
using TEX.Model.Models;


namespace TEX.Controllers
{
    [Area("Finished")]
    [AuthorizeJwt]
    [ActionDescription("检验计划")]
    [ApiController]
    [Route("api/InspectPlan")]
	public partial class InspectPlanController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
		public IActionResult Search(InspectPlanSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<InspectPlanListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [HttpPost("GetTodoInspectPlan")]
        public IActionResult GetTodoInspectPlan(object obj)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<InspectPlanListVM>();
                vm.Searcher = new InspectPlanSearcher()
                {
                    InspectStatus=InspectStatusEnum.Todo
                };
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        /// <summary>
        /// 返回未完成的检验计划列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetTodoInspectPlanDto")]
        public IActionResult GetTodoInspectPlanDto()
        {
            var list=DC.Set<InspectPlan>().Where(x => x.InspectStatus == InspectStatusEnum.Todo).Select(x => new InspectPlanDto { 
                ID=x.ID,
                OrderDetailId=x.OrderDetailId,
                CreateDate = x.CreateDate,
                Color_view = x.OrderDetail.Color,
                PlanNo = x.PlanNo,
                Customer = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                OrderNo = x.OrderDetail.PurchaseOrder.OrderNo,
                ProductName = x.OrderDetail.PurchaseOrder.Product.ProductName,
                ProductSpec = x.OrderDetail.PurchaseOrder.Product.Spec,
                GSM = x.OrderDetail.PurchaseOrder.Product.GSM,
                Width = x.OrderDetail.PurchaseOrder.Product.Width,
                ColorCode = x.OrderDetail.ColorCode,
                PlanBatch = x.PlanBatch,
                InspectionStandard = x.InspectionStandard,
                PlanQty = x.PlanQty,
                QtyUnit = x.QtyUnit,
                InspectStatus = x.InspectStatus,
                PlanFinishDate = x.PlanFinishDate,
                LabelContent = x.LabelContent,
                Remark = x.Remark,
            }).ToList();
            return Ok(list);
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public InspectPlanVM Get(string id)
        {
            var vm = Wtm.CreateVM<InspectPlanVM>(id);
            return vm;
        }

        [HttpGet("GetInspectPlan_ViewById/{id}")]
        public IActionResult GetInspectPlan_ViewById(string id)
        {
            if(Guid.TryParse(id,out Guid gid))
            {
            var vm = DC.Set<InspectPlan>().Where(x => x.ID == gid).Select (x => new InspectPlan_View() {
                ID = x.ID,
                CreateDate = x.CreateDate,
                Color_view = x.OrderDetail.Color,
                PlanNo = x.PlanNo,
                Customer = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                OrderNo = x.OrderDetail.PurchaseOrder.OrderNo,
                ProductName = x.OrderDetail.PurchaseOrder.Product.ProductName,
                ProductSpec = x.OrderDetail.PurchaseOrder.Product.Spec,
                GSM = x.OrderDetail.PurchaseOrder.Product.GSM,
                Width = x.OrderDetail.PurchaseOrder.Product.Width,
                ColorCode = x.OrderDetail.ColorCode,
                PlanBatch = x.PlanBatch,
                InspectionStandard = x.InspectionStandard,
                PlanQty = x.PlanQty,
                QtyUnit = x.QtyUnit,
                InspectStatus = x.InspectStatus,
                PlanFinishDate = x.PlanFinishDate,
                LabelContent = x.LabelContent,
                Remark = x.Remark,
                TenantCode =x.TenantCode,
                IsValid=x.IsValid

            }).FirstOrDefault();
            
            return Ok(vm);
            }
            else
            {
                return NotFound();
            }
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(InspectPlanVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(InspectPlanVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

		[HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<InspectPlanBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(InspectPlanSearcher searcher)
        {
            var vm = Wtm.CreateVM<InspectPlanListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<InspectPlanListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<InspectPlanImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(InspectPlanImportVM vm)
        {
            if (vm!=null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }


        [HttpGet("GetOrderDetails")]
        public ActionResult GetOrderDetails()
        {
            return Ok(DC.Set<OrderDetail>().GetSelectListItems(Wtm, x => x.Color));
        }

    }
}
