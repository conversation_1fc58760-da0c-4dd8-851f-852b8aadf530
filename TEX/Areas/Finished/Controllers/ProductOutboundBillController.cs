using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Finished.ProductOutboundBillVMs;
using TEX.Model.Models;
using BootstrapBlazor.Components;
using BootstrapBlazor.Shared;
using Microsoft.EntityFrameworkCore;
using NPOI.Util;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using TEX.Shared;
using TEX.Model.Finished;
using TEX.DataAccess;


namespace TEX.Controllers;

[Area("Finished")]
[AuthorizeJwtWithCookie]
[ActionDescription("成品出库")]
[ApiController]
[Route("api/ProductOutboundBill")]
public partial class ProductOutboundBillController : BaseApiController
{
    [ActionDescription("Sys.Search")]
    [HttpPost("Search")]
    public IActionResult Search(ProductOutboundBillSearcher searcher)
    {
        if (ModelState.IsValid)
        {
            var vm = Wtm.CreateVM<ProductOutboundBillListVM>();
            vm.Searcher = searcher;
            return Content(vm.GetJson(enumToString: false));
        }
        else
        {
            return BadRequest(ModelState.GetErrorJson());
        }
    }

    [ActionDescription("Sys.Get")]
    [HttpGet("{id}")]
    public ProductOutboundBillVM Get(string id)
    {
        var vm = Wtm.CreateVM<ProductOutboundBillVM>(id);
        return vm;
    }

    [HttpGet("GetWithLotList/{id}")]
    public ProductOutboundBill_View GetWithLotList(string id)
    {
        // 解析传入的 ID 参数为 Guid
        var guidId = Guid.Parse(id);

        // 查询数据库并映射到视图模型
        var bill = DC.Set<ProductOutboundBill>()
            .Include(b => b.Customer)
            .Include(b => b.Receiver)
            .Include(x => x.LotList)
                .ThenInclude(lot => lot.OrderDetail) // 确保包含 OrderDetail
                    .ThenInclude(orderDetail => orderDetail.PurchaseOrder) // 确保包含 PurchaseOrder
                        .ThenInclude(purchaseOrder => purchaseOrder.Product) // 确保包含 Product
            .AsNoTracking()
            .FirstOrDefault(x => x.ID == guidId);

        if (bill == null)
        {
            return null; // 或者抛出自定义异常，例如 throw new NotFoundException("Bill not found");
        }

        // 映射到视图模型
        var vm = new ProductOutboundBill_View
        {
            ID = bill.ID,
            CreateDate = bill.CreateDate,
            BillNo = bill.BillNo,
            Customer_view = bill.Customer?.CompanyName,
            Receiver_view = bill.Receiver?.CompanyName,
            CustomerId = bill.CustomerId,
            ReceiverId = bill.ReceiverId,
            Meters = bill.Meters,
            Weight = bill.Weight,
            Yards = bill.Yards,
            Remark = bill.Remark,
            AuditStatus = bill.AuditStatus,
            Details = bill.LotList.Select(lot => new ProductOutboundLot_View
            {
                ID = lot.ID,
                OutboundBillId = lot.OutboundBillId,
                OrderNo_view = lot.OrderDetail?.PurchaseOrder?.OrderNo,
                Product_view = lot.OrderDetail?.PurchaseOrder?.Product?.ProductName,
                OrderDetailId = lot.OrderDetailId,
                Spec_view = lot.OrderDetail?.PurchaseOrder?.Product?.Spec,
                Color = lot.Color,
                ColorCode = lot.ColorCode,
                LotNo = lot.LotNo,
                Pcs = lot.Pcs,
                Meters = lot.Meters,
                Weight = lot.Weight,
                Yards = lot.Yards,
                Remark = lot.Remark,
            }).ToList() // 转换为列表
        };

        return vm;
    }
    /// <summary>
    /// 获取出库单详细信息,包含缸号和卷号
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>

    [HttpGet("[action]/{id}")]
    public ProductOutboundBill_View GetWithRolls(string id)
    {
        // 使用分离查询方式，避免Include嵌套过深导致的问题
        var guidId = Guid.Parse(id);
        
        // 1. 先查询主表数据
        var bill = DC.Set<ProductOutboundBill>()
            .Include(b => b.Customer)
            .Include(b => b.Receiver)
            .AsNoTracking()
            .FirstOrDefault(x => x.ID == guidId);

        if (bill == null)
        {
            return null;
        }

        // 2. 单独查询LotList数据，包含OrderDetail和Product信息
        var lots = DC.Set<ProductOutboundLot>()
            .Where(l => l.OutboundBillId == guidId)
            .Include(l => l.OrderDetail)
                .ThenInclude(od => od.PurchaseOrder)
                    .ThenInclude(po => po.Product)
            .AsNoTracking()
            .ToList();

        // 3. 单独查询RollList数据
        var lotIds = lots.Select(l => l.ID).ToList();
        var rolls = DC.Set<ProductOutboundRoll>()
            .Where(r => lotIds.Contains(r.LotId))
            .AsNoTracking()
            .ToList();

        // 4. 手动构建视图模型
        var vm = new ProductOutboundBill_View
        {
            ID = bill.ID,
            CreateDate = bill.CreateDate,
            BillNo = bill.BillNo,
            Customer_view = bill.Customer?.CompanyName,
            Receiver_view = bill.Receiver?.CompanyName,
            CustomerId = bill.CustomerId,
            ReceiverId = bill.ReceiverId,
            Meters = bill.Meters,
            Weight = bill.Weight,
            Yards = bill.Yards,
            Remark = bill.Remark,
            AuditStatus = bill.AuditStatus,
            Details = new List<ProductOutboundLot_View>()
        };

        // 5. 处理LotList和RollList的映射
        foreach (var lot in lots)
        {
            var lotView = new ProductOutboundLot_View
            {
                ID = lot.ID,
                OutboundBillId = lot.OutboundBillId,
                OrderNo_view = lot.OrderDetail?.PurchaseOrder?.OrderNo,
                Product_view = lot.OrderDetail?.PurchaseOrder?.Product?.ProductName,
                OrderDetailId = lot.OrderDetailId,
                Spec_view = lot.OrderDetail?.PurchaseOrder?.Product?.Spec,
                Color = lot.Color,
                ColorCode = lot.ColorCode,
                LotNo = lot.LotNo,
                Pcs = lot.Pcs,
                Meters = lot.Meters,
                Weight = lot.Weight,
                Yards = lot.Yards,
                Remark = lot.Remark,
                RollList = new List<ProductOutboundRoll>()
            };

            // 为每个Lot添加对应的Roll数据
            var lotRolls = rolls.Where(r => r.LotId == lot.ID).ToList();
            foreach (var roll in lotRolls)
            {
                lotView.RollList.Add(new ProductOutboundRoll
                {
                    ID = roll.ID,
                    LotId = roll.LotId,
                    RollNo = roll.RollNo,
                    Meters = roll.Meters,
                    Weight = roll.Weight,
                    Yards = roll.Yards,
                    Grade = roll.Grade,
                    Remark = roll.Remark
                });
            }

            vm.Details.Add(lotView);
        }

        // 6. 对Details进行排序
        vm.Details = vm.Details.OrderBy(lot => lot.OrderNo_view)
                              .ThenBy(lot => lot.Color)
                              .ThenBy(lot => lot.LotNo)
                              .ToList();

        return vm;
    }

    [HttpGet("[action]/{billno}")]
    public ProductOutboundBill_View GetWithRollsByBillNo(string billno)
    {
        var bill = DC.Set<ProductOutboundBill>()
            .AsNoTracking()
            .FirstOrDefault(x => x.BillNo == billno);

        if (bill == null)
        {
            return null;
        }

        return GetWithRolls(bill.ID.ToString());
    }



        [ActionDescription("出库单Excel")]
    [HttpPost("[action]")]
    public IActionResult DownloadOutBill( string id)
    {
        var v= GetWithRolls(id);
        var data=ExcelHelper.GenerateStockOutBillFile(v);
        return File(data, "application/vnd.ms-excel", "出库单.xlsx");
    }

    [ActionDescription("Sys.Create")]
    [HttpPost("Add")]
    public IActionResult Add(ProductOutboundBillVM vm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState.GetErrorJson());
        }
        else
        {
            vm.DoAdd();
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Entity);
            }
        }

    }

    [ActionDescription("Sys.Edit")]
    [HttpPut("Edit")]
    public IActionResult Edit(ProductOutboundBillVM vm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState.GetErrorJson());
        }
        else
        {
            vm.DoEdit(false);
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(vm.Entity);
            }
        }
    }

    /// <summary>
    /// 使用EFcore添加出库单(三级联动) - AddWithLotAndRoll API
    /// </summary>
    /// <param name="vm"></param>
    /// <returns></returns>
    [HttpPost("AddWithLotAndRoll")]
    public async Task<IActionResult> AddWithLotAndRoll(ProductOutboundBillVM vm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState.GetErrorJson());
        }

        // {{ AURA-X: Fix - 检测数据库类型，在内存数据库中跳过事务以避免测试项目FakeNestedTransaction异常. Source: Microsoft官方文档 }}
        var isInMemoryDatabase = Wtm.DC.Database.ProviderName?.Contains("InMemory") == true;
        var transaction = isInMemoryDatabase ? null : Wtm.DC.BeginTransaction();

        try
        {
            var bill = vm.Entity;
            var tenantCode = Wtm.LoginUserInfo?.CurrentTenant;
            var usercode = Wtm.LoginUserInfo?.ITCode;

            // 验证业务数据
            if (bill.LotList == null)
            {
                bill.LotList = new();
            }

            // {{ AURA-X: Add - 验证批次和卷号数据的唯一性，包括数据库重复检查 }}
            await ValidateLotListAsync(bill);

            if (!ModelState.IsValid)
            {
                var k = ModelState.Values.First();
                ErrorObj errorObj = new()
                {
                    Message = new(),
                    Form = new()
                };
                foreach (var er in k.Errors)
                {
                    errorObj.Message.Add(er.ErrorMessage);
                }
                return BadRequest(errorObj);
            }

            // {{ AURA-X: Add - 出库前检查库存充足性（测试时可注释） }}
            /*
            var stockCheckResult = await CheckStockAvailability(bill.LotList);
            if (!stockCheckResult.IsSuccess)
            {
                ModelState.AddModelError("库存不足", stockCheckResult.ErrorMessage);
                return BadRequest(ModelState.GetErrorJson());
            }
            */

            // 设置租户信息
            bill.TenantCode = tenantCode;
            bill.CreateBy = usercode;
            bill.CreateTime = DateTime.Now;
            bill.IsValid = true;

            // 处理LotList数据
            if (bill.LotList != null && bill.LotList.Any())
            {
                bill.Pcs = bill.LotList.Sum(x => x.Pcs);
                bill.Meters = bill.LotList.Sum(x => x.Meters);
                bill.Weight = bill.LotList.Sum(x => x.Weight);
                bill.Yards = bill.LotList.Sum(x => x.Yards);

                foreach (var lot in bill.LotList)
                {
                    lot.OutboundBillId = bill.ID;
                    lot.CreateTime = DateTime.Now;
                    lot.TenantCode = tenantCode;
                    lot.CreateBy = usercode;
                    lot.IsValid = true;

                    if (lot.RollList != null && lot.RollList.Any())
                    {
                        lot.Pcs = lot.RollList.Count;
                        lot.Meters = lot.RollList.Sum(x => x.Meters);
                        lot.Weight = lot.RollList.Sum(x => x.Weight);
                        lot.Yards = lot.RollList.Sum(x => x.Yards);

                        foreach (var roll in lot.RollList)
                        {
                            roll.LotId = lot.ID;
                            roll.CreateTime = DateTime.Now;
                            roll.TenantCode = tenantCode;
                            roll.CreateBy = usercode;
                            roll.IsValid = true;
                        }
                    }
                }
            }

            // {{ AURA-X: Fix - 使用EF Core导航属性自动级联添加，避免重复添加问题 }}
            DataContext dc = (DataContext)DC;
            dc.Set<ProductOutboundBill>().Add(bill);

            // {{ AURA-X: Add - 扣减库存：按OrderDetailId分组统计并更新ProductStock表 }}
            var stockList = bill.LotList
                .GroupBy(x => x.OrderDetailId)
                .Select(x => new StockInfo
                {
                    OrderDetailId = x.Key,
                    TotalPcs = x.Sum(y => y.Pcs),
                    TotalMeters = x.Sum(y => y.Meters),
                    TotalWeight = x.Sum(y => y.Weight),
                    TotalYards = x.Sum(y => y.Yards),
                }).ToList();

            var orderDetailIds = stockList.Select(r => r.OrderDetailId).ToList();
            var productStocks = await dc.Set<ProductStock>()
                .Where(s => orderDetailIds.Contains(s.OrderDetailId))
                .ToDictionaryAsync(s => s.OrderDetailId);

            foreach (var stockChange in stockList)
            {
                //var existingStock = productStocks.GetValueOrDefault(stockChange.OrderDetailId);
                //if (existingStock != null)
                //{
                //    // {{ AURA-X: Modify - 出库扣减库存（与入库相反） }}
                //    existingStock.TotalPcs -= stockChange.TotalPcs;
                //    existingStock.TotalWeight -= stockChange.TotalWeight;
                //    existingStock.TotalMeters -= stockChange.TotalMeters;
                //    existingStock.TotalYards -= stockChange.TotalYards;
                //    existingStock.UpdateTime = DateTime.Now;
                //    existingStock.UpdateBy = usercode;
                //}
                if (!productStocks.TryGetValue(stockChange.OrderDetailId, out var existingStock))
                {
                    // 新增库存记录
                    var newStock = new ProductStock
                    {
                        OrderDetailId = stockChange.OrderDetailId,
                        TotalPcs = -stockChange.TotalPcs,
                        TotalMeters = -stockChange.TotalMeters,
                        TotalWeight = -stockChange.TotalWeight,
                        TotalYards = -stockChange.TotalYards,
                        CreateTime = DateTime.Now,
                        CreateBy = usercode,
                        TenantCode = tenantCode
                    };
                    base.DC.Set<ProductStock>().Add(newStock);
                }
                else
                {
                    // 更新现有库存记录
                    // {{ AURA-X: Modify - 出库扣减库存（与入库相反） }}
                    existingStock.TotalPcs -= stockChange.TotalPcs;
                    existingStock.TotalWeight -= stockChange.TotalWeight;
                    existingStock.TotalMeters -= stockChange.TotalMeters;
                    existingStock.TotalYards -= stockChange.TotalYards;
                    existingStock.UpdateTime = DateTime.Now;
                    existingStock.UpdateBy = usercode;
                    // 不需要调用Update，EF Core会自动跟踪变更
                }
            }

            // 统一保存所有更改
            await dc.SaveChangesAsync();

            // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
            if (transaction != null)
            {
                transaction.Commit();
            }

            // {{ AURA-X: Fix - 在同一个DataContext中查询完整的实体数据，确保包含关联数据 }}
            var bill2 = await dc.Set<ProductOutboundBill>()
                .Include(x => x.LotList.Where(l => l.IsValid))
                .ThenInclude(x => x.RollList.Where(r => r.IsValid))
                .FirstOrDefaultAsync(x => x.ID == bill.ID);

            return Ok(bill2 ?? bill);
        }
        catch (Exception ex)
        {
            // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
            if (transaction != null)
            {
                transaction.Rollback();
            }
            ModelState.AddModelError("", ex.Message);
            return BadRequest(ModelState.GetErrorJson());
        }
    }

    /// <summary>
    /// 三级联动修改 - 利用EF Core 8实体跟踪机制实现优雅更新（出库）
    /// </summary>
    [HttpPut("EditWithLotAndRoll")]
    public async Task<IActionResult> EditWithLotAndRoll(ProductOutboundBillVM vm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState.GetErrorJson());
        }

        // {{ AURA-X: Fix - 检测数据库类型，在内存数据库中跳过事务以避免FakeNestedTransaction异常. Source: Microsoft官方文档 }}
        var isInMemoryDatabase = Wtm.DC.Database.ProviderName?.Contains("InMemory") == true;
        var transaction = isInMemoryDatabase ? null : Wtm.DC.BeginTransaction();

        try
        {
            var bill = vm.Entity;
            var tenantCode = Wtm.LoginUserInfo?.CurrentTenant;
            var userCode = Wtm.LoginUserInfo?.ITCode;

            if (bill.LotList is null)
            {
                bill.LotList = [];
            }

            // {{ AURA-X: Add - 验证批次和卷号数据的唯一性 }}
            await ValidateLotListAsync(bill);
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }

            // 1. 更新现有实体的Bill和Lot的统计信息和基本信息
            foreach (var lot in bill.LotList)
            {
                lot.RollList = lot.RollList ?? [];
                // 1.1 更新现有Lot的统计信息
                if (lot.RollList.Any())
                {
                    lot.Pcs = lot.RollList.Count;
                    lot.Meters = lot.RollList.Sum(x => x.Meters);
                    lot.Weight = lot.RollList.Sum(x => x.Weight);
                    lot.Yards = lot.RollList.Sum(x => x.Yards);
                }
            }

            // 1.2 更新现有Bill的统计信息
            if (bill.LotList.Any())
            {
                bill.Pcs = bill.LotList.Sum(x => x.Pcs);
                bill.Meters = bill.LotList.Sum(x => x.Meters);
                bill.Weight = bill.LotList.Sum(x => x.Weight);
                bill.Yards = bill.LotList.Sum(x => x.Yards);
            }

            // 2. 清理ChangeTracker避免实体跟踪冲突
            DataContext dc = (DataContext)DC;
            dc.ChangeTracker.Clear();

            // 3. 查询数据库中现有的完整实体（包含三级关联）
            var existingBill = await DC.Set<ProductOutboundBill>()
                .Include(x => x.LotList)
                .ThenInclude(x => x.RollList)
                .FirstOrDefaultAsync(x => x.ID == bill.ID);

            if (existingBill == null)
            {
                ModelState.AddModelError("", "出库单不存在");
                return BadRequest(ModelState.GetErrorJson());
            }

            // {{ AURA-X: Add - 计算修改前统计
            
            var originalStockList = existingBill.LotList
                .Where(x => x.IsValid)
                .GroupBy(x => x.OrderDetailId)
                .Select(x => new StockInfo
                {
                    OrderDetailId = x.Key,
                    TotalPcs = x.Sum(y => y.Pcs),
                    TotalMeters = x.Sum(y => y.Meters),
                    TotalWeight = x.Sum(y => y.Weight),
                    TotalYards = x.Sum(y => y.Yards),
                }).ToList();

            var newLotList = new List<ProductOutboundLot>();
            var newRollList = new List<ProductOutboundRoll>();

            // 4.1 处理传入的Lot数据
            foreach (var inputLot in bill.LotList)
            {
                var existingLot = existingBill.LotList.FirstOrDefault(x => x.ID == inputLot.ID);
                if (existingLot == null)
                {
                    // 新增Lot
                    inputLot.OutboundBillId = bill.ID;
                    inputLot.CreateTime = DateTime.Now;
                    inputLot.CreateBy = userCode;
                    inputLot.TenantCode = tenantCode;
                    inputLot.IsValid = true;
                    newLotList.Add(inputLot);

                    // 处理新Lot下的Roll
                    if (inputLot.RollList != null)
                    {
                        foreach (var roll in inputLot.RollList)
                        {
                            roll.LotId = inputLot.ID;
                            roll.CreateTime = DateTime.Now;
                            roll.CreateBy = userCode;
                            roll.TenantCode = tenantCode;
                            roll.IsValid = true;
                            newRollList.Add(roll);
                        }
                    }
                }
                else
                {
                    // 更新现有Lot
                    existingLot.Color = inputLot.Color;
                    existingLot.ColorCode = inputLot.ColorCode;
                    existingLot.LotNo = inputLot.LotNo;
                    existingLot.Pcs = inputLot.Pcs;
                    existingLot.Meters = inputLot.Meters;
                    existingLot.Weight = inputLot.Weight;
                    existingLot.Yards = inputLot.Yards;
                    existingLot.Remark = inputLot.Remark;
                    existingLot.UpdateTime = DateTime.Now;
                    existingLot.UpdateBy = userCode;

                    // 处理现有Lot下的Roll
                    if (inputLot.RollList != null)
                    {
                        foreach (var inputRoll in inputLot.RollList)
                        {
                            var existingRoll = existingLot.RollList?.FirstOrDefault(x => x.ID == inputRoll.ID);
                            if (existingRoll == null)
                            {
                                // 新增Roll
                                inputRoll.LotId = existingLot.ID;
                                inputRoll.CreateTime = DateTime.Now;
                                inputRoll.CreateBy = userCode;
                                inputRoll.TenantCode = tenantCode;
                                inputRoll.IsValid = true;
                                newRollList.Add(inputRoll);
                            }
                            else
                            {
                                // 更新现有Roll
                                existingRoll.RollNo = inputRoll.RollNo;
                                existingRoll.Weight = inputRoll.Weight;
                                existingRoll.Meters = inputRoll.Meters;
                                existingRoll.Yards = inputRoll.Yards;
                                existingRoll.Grade = inputRoll.Grade;
                                existingRoll.Remark = inputRoll.Remark;
                                existingRoll.UpdateTime = DateTime.Now;
                                existingRoll.UpdateBy = userCode;
                            }
                        }
                    }

                    // 软删除不在传入数据中的Roll
                    if (existingLot.RollList != null)
                    {
                        var inputRollIds = inputLot.RollList?.Select(x => x.ID).ToList() ?? new List<Guid>();
                        var rollsToDelete = existingLot.RollList.Where(x => !inputRollIds.Contains(x.ID) && x.IsValid).ToList();
                        foreach (var rollToDelete in rollsToDelete)
                        {
                            rollToDelete.IsValid = false;
                            rollToDelete.UpdateTime = DateTime.Now;
                            rollToDelete.UpdateBy = userCode;
                        }
                    }
                }
            }

            // 4.2 软删除不在传入数据中的Lot
            var inputLotIds = bill.LotList.Select(x => x.ID).ToList();
            var lotsToDelete = existingBill.LotList.Where(x => !inputLotIds.Contains(x.ID) && x.IsValid).ToList();
            foreach (var lotToDelete in lotsToDelete)
            {
                lotToDelete.IsValid = false;
                lotToDelete.UpdateTime = DateTime.Now;
                lotToDelete.UpdateBy = userCode;

                // 同时软删除该Lot下的所有Roll
                if (lotToDelete.RollList != null)
                {
                    foreach (var rollToDelete in lotToDelete.RollList.Where(x => x.IsValid))
                    {
                        rollToDelete.IsValid = false;
                        rollToDelete.UpdateTime = DateTime.Now;
                        rollToDelete.UpdateBy = userCode;
                    }
                }
            }

            // 5. 更新Bill主表信息
            existingBill.CreateDate = bill.CreateDate;
            existingBill.BillNo = bill.BillNo;
            existingBill.CustomerId = bill.CustomerId;
            existingBill.ReceiverId = bill.ReceiverId;
            existingBill.Pcs = bill.Pcs;
            existingBill.Meters = bill.Meters;
            existingBill.Weight = bill.Weight;
            existingBill.Yards = bill.Yards;
            existingBill.Remark = bill.Remark;
            existingBill.AuditStatus = bill.AuditStatus;
            existingBill.UpdateTime = DateTime.Now;
            existingBill.UpdateBy = userCode;

            // 6. 批量添加新的Lot和Roll
            if (newLotList.Any())
            {
                dc.Set<ProductOutboundLot>().AddRange(newLotList);
            }
            if (newRollList.Any())
            {
                dc.Set<ProductOutboundRoll>().AddRange(newRollList);
            }

            // {{ AURA-X: Add - 计算修改后的库存统计和库存差异，更新ProductStock表. Confirmed via 寸止 }}
            // 计算修改后的库存统计
            var currentStockList = bill.LotList
                .Where(x => x.IsValid)
                .GroupBy(x => x.OrderDetailId)
                .Select(x => new StockInfo
                {
                    OrderDetailId = x.Key,
                    TotalPcs = x.Sum(y => y.Pcs),
                    TotalMeters = x.Sum(y => y.Meters),
                    TotalWeight = x.Sum(y => y.Weight),
                    TotalYards = x.Sum(y => y.Yards),
                }).ToList();

            // 计算出库差异（出库：原出库 - 新出库 = 需要调整的出库）
            // 原出库100,现出库10,调整数100-10=90,库存应该增加90
            var stockDifferences = CalculateDifferences(originalStockList, currentStockList);

            //// {{ AURA-X: Add - 出库库存差异检查：确保调整后库存不为负数 }}
            //if (stockDifferences.Any())
            //{
            //    var stockCheckResult = await CheckStockAdjustmentAvailability(stockDifferences);
            //    if (!stockCheckResult.IsSuccess)
            //    {
            //        ModelState.AddModelError("库存不足", stockCheckResult.ErrorMessage);
            //        return BadRequest(ModelState.GetErrorJson());
            //    }
            //}

            // 更新ProductStock表
            if (stockDifferences.Any())
            {
                var orderDetailIds = stockDifferences.Select(r => r.OrderDetailId).ToList();
                var productStocks = await DC.Set<ProductStock>()
                    .Where(s => orderDetailIds.Contains(s.OrderDetailId))
                    .ToDictionaryAsync(s => s.OrderDetailId);

                foreach (var stockChange in stockDifferences)
                {
                    var existingStock = productStocks.GetValueOrDefault(stockChange.OrderDetailId);
                    if (existingStock != null)
                    {
                        // {{ AURA-X: Modify - 出库库存调整：原库存加上差异值 }}
                        // 原出库100,现出库10,调整数100-10=90,库存应该增加90
                        existingStock.TotalPcs += stockChange.TotalPcs;
                        existingStock.TotalWeight += stockChange.TotalWeight;
                        existingStock.TotalMeters += stockChange.TotalMeters;
                        existingStock.TotalYards += stockChange.TotalYards;
                        existingStock.UpdateTime = DateTime.Now;
                        existingStock.UpdateBy = userCode;
                        // 不需要调用Update，EF Core会自动跟踪变更
                    }
                }
            }

            // 统一保存所有更改
            await dc.SaveChangesAsync();

            // {{ AURA-X: Fix - 条件性提交事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
            if (transaction != null)
            {
                transaction.Commit();
            }

            // {{ AURA-X: Fix - 查询更新后的完整实体数据，确保包含最新的关联数据 }}
            var updatedBill = await dc.Set<ProductOutboundBill>()
                .Include(x => x.LotList.Where(l => l.IsValid))
                .ThenInclude(x => x.RollList.Where(r => r.IsValid))
                .FirstOrDefaultAsync(x => x.ID == bill.ID);

            return Ok(updatedBill ?? bill);
        }
        catch (Exception ex)
        {
            // {{ AURA-X: Fix - 条件性回滚事务，内存数据库跳过事务操作. Source: Microsoft官方文档 }}
            if (transaction != null)
            {
                transaction.Rollback();
            }
            ModelState.AddModelError("", ex.Message);
            return BadRequest(ModelState.GetErrorJson());
        }
    }

    /// <summary>
    /// 验证LotList中的数据是否符合业务规则（出库）
    /// 1. ProductOutboundLot表中OutboundBillId+LotNo+OrderDetailId组合全局唯一
    /// 2. ProductOutboundRoll表中LotNo+OrderDetailId+RollNo组合全局唯一,LotNo+OrderDetailId需要从Lot分组
    /// </summary>
    /// <param name="bill">出库单</param>
    private async Task ValidateLotListAsync(ProductOutboundBill bill)
    {
        List<ProductOutboundLot> lotList = bill.LotList;
        if (lotList == null || !lotList.Any())
            return;

        // 按LotNo+OrderDetailId分组，验证每个分组中的所有RollNo不重复
        var lotGroups = lotList.GroupBy(x => new { x.LotNo, x.OrderDetailId });
        foreach (var lotGroup in lotGroups)
        {
            // 收集该分组下所有的Roll
            var allRolls = lotGroup.SelectMany(lot => lot.RollList ?? new List<ProductOutboundRoll>()).ToList();

            if (allRolls.Any())
            {
                var rollNoGroup = allRolls.GroupBy(x => x.RollNo);
                foreach (var rollGroup in rollNoGroup)
                {
                    if (rollGroup.Count() > 1)
                    {
                        ModelState.AddModelError("卷号重复", $"缸号: {lotGroup.Key.LotNo} 卷号: {rollGroup.Key} 重复");
                    }
                }
            }
        }

        // 2.2 检查ProductOutboundRoll表中LotNo+OrderDetailId+RollNo组合全局唯一（批量优化）
        // 收集所有需要验证的Roll信息
        var rollsToValidate = new List<(string LotNo, Guid OrderDetailId, int RollNo, Guid RollId)>();
        foreach (var lot in lotList)
        {
            if (lot.RollList == null || !lot.RollList.Any())
                continue;

            foreach (var roll in lot.RollList)
            {
                rollsToValidate.Add((lot.LotNo, lot.OrderDetailId, roll.RollNo, roll.ID));
            }
        }

        if (rollsToValidate.Any())
        {
            // 批量查询数据库中已存在的Roll记录
            var lotNos = rollsToValidate.Select(x => x.LotNo).Distinct().ToList();
            var orderDetailIds = rollsToValidate.Select(x => x.OrderDetailId).Distinct().ToList();
            var rollNos = rollsToValidate.Select(x => x.RollNo).Distinct().ToList();

            var existingRolls = await DC.Set<ProductOutboundRoll>()
                .Where(x => lotNos.Contains(x.Lot.LotNo) &&
                           orderDetailIds.Contains(x.Lot.OrderDetailId) &&
                           rollNos.Contains(x.RollNo) &&
                           x.IsValid)
                .Select(x => new { x.Lot.LotNo, x.Lot.OrderDetailId, x.RollNo, x.ID })
                .ToListAsync();

            // 在内存中进行验证
            foreach (var rollToValidate in rollsToValidate)
            {
                var conflictingRoll = existingRolls.FirstOrDefault(x =>
                    x.LotNo == rollToValidate.LotNo &&
                    x.OrderDetailId == rollToValidate.OrderDetailId &&
                    x.RollNo == rollToValidate.RollNo &&
                    x.ID != rollToValidate.RollId); // 排除当前正在编辑的Roll

                if (conflictingRoll != null)
                {
                    ModelState.AddModelError("卷号重复", $"缸号:{rollToValidate.LotNo}, 卷号:{rollToValidate.RollNo} 在数据库中已存在");
                }
            }
        }
    }

    /// <summary>
    /// 检查库存充足性（出库前检查）
    /// </summary>
    /// <param name="lotList">出库批次列表</param>
    /// <returns>检查结果</returns>
    private async Task<(bool IsSuccess, string ErrorMessage)> CheckStockAvailability(List<ProductOutboundLot> lotList)
    {
        if (lotList == null || !lotList.Any())
            return (true, string.Empty);

        // 按OrderDetailId分组统计出库数量
        var outboundStockList = lotList
            .GroupBy(x => x.OrderDetailId)
            .Select(x => new StockInfo
            {
                OrderDetailId = x.Key,
                TotalPcs = x.Sum(y => y.Pcs),
                TotalMeters = x.Sum(y => y.Meters),
                TotalWeight = x.Sum(y => y.Weight),
                TotalYards = x.Sum(y => y.Yards),
            }).ToList();

        // 查询当前库存
        var orderDetailIds = outboundStockList.Select(x => x.OrderDetailId).ToList();
        var currentStocks = await DC.Set<ProductStock>()
            .Where(s => orderDetailIds.Contains(s.OrderDetailId) && s.IsValid)
            .ToDictionaryAsync(s => s.OrderDetailId);

        // 检查每个OrderDetail的库存是否充足
        foreach (var outboundStock in outboundStockList)
        {
            var currentStock = currentStocks.GetValueOrDefault(outboundStock.OrderDetailId);
            if (currentStock == null)
            {
                return (false, $"订单明细 {outboundStock.OrderDetailId} 无库存记录");
            }

            if (currentStock.TotalPcs < outboundStock.TotalPcs ||
                currentStock.TotalMeters < outboundStock.TotalMeters ||
                currentStock.TotalWeight < outboundStock.TotalWeight ||
                currentStock.TotalYards < outboundStock.TotalYards)
            {
                return (false, $"订单明细 {outboundStock.OrderDetailId} 库存不足。" +
                              $"当前库存：{currentStock.TotalPcs}件/{currentStock.TotalMeters}米/{currentStock.TotalWeight}kg/{currentStock.TotalYards}码，" +
                              $"出库数量：{outboundStock.TotalPcs}件/{outboundStock.TotalMeters}米/{outboundStock.TotalWeight}kg/{outboundStock.TotalYards}码");
            }
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 检查库存调整可用性（编辑时检查）
    /// </summary>
    /// <param name="stockDifferences">库存差异列表</param>
    /// <returns>检查结果</returns>
    private async Task<(bool IsSuccess, string ErrorMessage)> CheckStockAdjustmentAvailability(List<StockInfo> stockDifferences)
    {
        if (stockDifferences == null || !stockDifferences.Any())
            return (true, string.Empty);

        // 查询当前库存
        var orderDetailIds = stockDifferences.Select(x => x.OrderDetailId).ToList();
        var currentStocks = await DC.Set<ProductStock>()
            .Where(s => orderDetailIds.Contains(s.OrderDetailId) && s.IsValid)
            .ToDictionaryAsync(s => s.OrderDetailId);

        // 检查调整后库存是否为负数
        foreach (var stockDiff in stockDifferences)
        {
            var currentStock = currentStocks.GetValueOrDefault(stockDiff.OrderDetailId);
            if (currentStock == null)
            {
                // 如果差异为正数（增加库存），则无需检查
                if (stockDiff.TotalPcs > 0 || stockDiff.TotalMeters > 0 || stockDiff.TotalWeight > 0 || stockDiff.TotalYards > 0)
                    continue;

                return (false, $"订单明细 {stockDiff.OrderDetailId} 无库存记录");
            }

            // 计算调整后的库存（出库：当前库存 - 差异值）
            var adjustedPcs = currentStock.TotalPcs - stockDiff.TotalPcs;
            var adjustedMeters = currentStock.TotalMeters - stockDiff.TotalMeters;
            var adjustedWeight = currentStock.TotalWeight - stockDiff.TotalWeight;
            var adjustedYards = currentStock.TotalYards - stockDiff.TotalYards;

            if (adjustedPcs < 0 || adjustedMeters < 0 || adjustedWeight < 0 || adjustedYards < 0)
            {
                return (false, $"订单明细 {stockDiff.OrderDetailId} 调整后库存不足。" +
                              $"当前库存：{currentStock.TotalPcs}件/{currentStock.TotalMeters}米/{currentStock.TotalWeight}kg/{currentStock.TotalYards}码，" +
                              $"调整差异：{stockDiff.TotalPcs}件/{stockDiff.TotalMeters}米/{stockDiff.TotalWeight}kg/{stockDiff.TotalYards}码");
            }
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 计算出库差异（原出库 - 新出库 = 差异）
    /// </summary>
    /// <param name="originalStockList">原出库列表</param>
    /// <param name="currentStockList">当前出库列表</param>
    /// <returns>出库差异列表</returns>
    private List<StockInfo> CalculateDifferences(List<StockInfo> originalStockList, List<StockInfo> currentStockList)
    {
        var differences = new List<StockInfo>();

        // 获取所有涉及的OrderDetailId
        var allOrderDetailIds = originalStockList.Select(x => x.OrderDetailId)
            .Union(currentStockList.Select(x => x.OrderDetailId))
            .Distinct()
            .ToList();

        foreach (var orderDetailId in allOrderDetailIds)
        {
            var original = originalStockList.FirstOrDefault(x => x.OrderDetailId == orderDetailId);
            var current = currentStockList.FirstOrDefault(x => x.OrderDetailId == orderDetailId);

            var originalPcs = original?.TotalPcs ?? 0;
            var originalMeters = original?.TotalMeters ?? 0;
            var originalWeight = original?.TotalWeight ?? 0;
            var originalYards = original?.TotalYards ?? 0;

            var currentPcs = current?.TotalPcs ?? 0;
            var currentMeters = current?.TotalMeters ?? 0;
            var currentWeight = current?.TotalWeight ?? 0;
            var currentYards = current?.TotalYards ?? 0;

            // 计算差异：原库存 - 新库存
            var diffPcs = originalPcs - currentPcs;
            var diffMeters = originalMeters - currentMeters;
            var diffWeight = originalWeight - currentWeight;
            var diffYards = originalYards - currentYards;

            // 只有存在差异时才添加到结果中
            if (diffPcs != 0 || diffMeters != 0 || diffWeight != 0 || diffYards != 0)
            {
                differences.Add(new StockInfo
                {
                    OrderDetailId = orderDetailId,
                    TotalPcs = diffPcs,
                    TotalMeters = diffMeters,
                    TotalWeight = diffWeight,
                    TotalYards = diffYards
                });
            }
        }

        return differences;
    }

    [HttpPost("BatchDelete")]
    [ActionDescription("Sys.Delete")]
    public IActionResult BatchDelete(string[] ids)
    {
        var vm = Wtm.CreateVM<ProductOutboundBillBatchVM>();
        if (ids != null && ids.Length > 0)
        {
            vm.Ids = ids;
        }
        else
        {
            return Ok();
        }
        if (!ModelState.IsValid || !vm.DoBatchDelete())
        {
            return BadRequest(ModelState.GetErrorJson());
        }
        else
        {
            return Ok(ids.Length);
        }
    }


    [ActionDescription("Sys.Export")]
    [HttpPost("ExportExcel")]
    public IActionResult ExportExcel(ProductOutboundBillSearcher searcher)
    {
        var vm = Wtm.CreateVM<ProductOutboundBillListVM>();
        vm.Searcher = searcher;
        vm.SearcherMode = ListVMSearchModeEnum.Export;
        return vm.GetExportData();
    }

    [ActionDescription("Sys.CheckExport")]
    [HttpPost("ExportExcelByIds")]
    public IActionResult ExportExcelByIds(string[] ids)
    {
        var vm = Wtm.CreateVM<ProductOutboundBillListVM>();
        if (ids != null && ids.Length > 0)
        {
            vm.Ids = new List<string>(ids);
            vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
        }
        return vm.GetExportData();
    }

    [ActionDescription("Sys.DownloadTemplate")]
    [HttpGet("GetExcelTemplate")]
    public IActionResult GetExcelTemplate()
    {
        var vm = Wtm.CreateVM<ProductOutboundBillImportVM>();
        var qs = new Dictionary<string, string>();
        foreach (var item in Request.Query.Keys)
        {
            qs.Add(item, Request.Query[item]);
        }
        vm.SetParms(qs);
        var data = vm.GenerateTemplate(out string fileName);
        return File(data, "application/vnd.ms-excel", fileName);
    }

    [ActionDescription("Sys.Import")]
    [HttpPost("Import")]
    public ActionResult Import(ProductOutboundBillImportVM vm)
    {
        if (vm != null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
        {
            return BadRequest(vm.GetErrorJson());
        }
        else
        {
            return Ok(vm?.EntityList?.Count ?? 0);
        }
    }


    [HttpGet("GetDeliveryAddress")]
    public ActionResult GetDeliveryAddress()
    {
        return Ok(DC.Set<DeliveryAddress>().GetSelectListItems(Wtm, x => x.CompanyName));
    }


    //尝试使用CascadingTree方法实现树状数据层次化失败,在订单中已实现树状数据层次化
    /*[HttpPost("[action]")]
    public List<TreeViewItem<TreeFoo>> GetOrderTreeByCustomerId(string id)
    {
        var treeData = new List<TreeViewItem<TreeFoo>>();
        var tree = DC.Set<ProductInboundBill>().Include(x => x.POrder).ThenInclude(x => x.Product).Where(x => x.POrder.CustomerId == Guid.Parse(id)).Select(x => new TreeFoo()
        {
            Id = x.ID.ToString(),
            Text = x.POrder.OrderNo + x.POrder.Product.ProductName,
            ParentId = null,

        }).ToList();


        var treev = CascadingTree(tree).ToList();
        return treev;
    }

    [HttpGet("[action]/{id}")]
    public List<TreeFoo> GetOrderTreeFooByCustomerId(string id)
    {
        if (!Guid.TryParse(id, out Guid customerId))
        {
            return new List<TreeFoo>();
        }

        var tree = DC.Set<ProductInboundBill>()
            .Include(x => x.POrder)
            .ThenInclude(x => x.Product)
            .Where(x => x.POrder != null && x.POrder.CustomerId == customerId)
            .Select(x => new
            {
                x.POrderId,
                x.POrder.OrderNo,
                x.POrder.Product.ProductName
            })
            .Distinct() // 去重
            .Select(x => new TreeFoo()
            {
                Id = x.POrderId.ToString(),
                Text = x.OrderNo + x.ProductName,
                ParentId = null,
            }).ToList();

        return tree;
    }*/
    /// <summary>
    /// 树状数据层次化方法
    /// </summary>
    /// <param name="items">数据集合</param>
    /// <param name="parent">父级节点</param>
    public static IEnumerable<TreeViewItem<TreeFoo>> CascadingTree(IEnumerable<TreeFoo> items, TreeViewItem<TreeFoo> parent = null) => items.CascadingTree(null,
        (foo, parent) => foo.ParentId == parent?.Value.Id,
        foo => new TreeViewItem<TreeFoo>(foo)
        {
            Text = foo.Text,
            Icon = foo.Icon,
            IsActive = foo.IsActive
        }).ToList();
}
