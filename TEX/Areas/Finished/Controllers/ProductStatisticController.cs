//using EFCore.BulkExtensions; //需要更改连接字符串和Mysql数据库配置文件,麻烦

using BootstrapBlazor.Components;
using Elsa.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.DataAccess;
using TEX.Model.Finished;
using TEX.Model.Models;
using TEX.Model.Statistics;
using TEX.ViewModel.Finished.ProductInboundBillVMs;
using TEX.ViewModel.Finished.ProductStockVMs;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;


namespace TEX.Controllers
{
    [Area("Statistic")]
    [AuthorizeJwtWithCookie]
    [ActionDescription("成品入库统计")]
    [ApiController]
    [Route("api/ProductStatistic")]
    public partial class ProductStatisticController : BaseApiController
    {




        [ActionDescription("获取库存订单列表")]
        [HttpPost("GetStockOrders")]
        public async Task<IActionResult> GetStockOrders(ProductStockSearcher searcher)
        {
            List<ComboSelectListItem> orders = DC.Set<ProductStock>()
                 .Include(x => x.OrderDetail)
                 .ThenInclude(x => x.PurchaseOrder)
                 .Where(x => x.TotalPcs > 0)
                 .Select(x => new ComboSelectListItem
                 {
                     Text = x.OrderDetail.PurchaseOrder.OrderNo,
                     Value = x.OrderDetail.PurchaseOrderId.ToString(),
                 }).ToList();
            await Task.CompletedTask;

            return Ok(orders);
        }










        #region 统计查询API

        /// <summary>
        /// 获取入库统计数据
        /// </summary>
        /// <param name="searcher">搜索条件</param>
        /// <returns>统计结果</returns>
        [ActionDescription("获取入库统计数据")]
        [HttpPost("GetStatistics")]
        public async Task<IActionResult> GetStatistics(ProductInboundBillSearcher searcher)
        {
            try
            {
                var vm = Wtm.CreateVM<ProductInboundBillStatisticsVM>();
                var result = await vm.GetStatisticsAsync(searcher);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"获取统计数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取入库分组统计数据
        /// </summary>
        /// <param name="searcher">搜索条件</param>
        /// <param name="groupType">分组类型</param>
        /// <returns>分组统计结果</returns>
        [ActionDescription("获取入库分组统计数据")]
        [HttpPost("GetGroupedStatistics")]
        public async Task<IActionResult> GetGroupedStatistics(ProductInboundBillSearcher searcher, StatisticsGroupType groupType)
        {
            try
            {
                var vm = Wtm.CreateVM<ProductInboundBillStatisticsVM>();
                var result = await vm.GetGroupedStatisticsAsync(searcher, groupType);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"获取分组统计数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 钻取查询明细数据
        /// </summary>
        /// <param name="drillDownParams">钻取参数</param>
        /// <returns>明细数据</returns>
        [ActionDescription("钻取查询明细数据")]
        [HttpPost("GetDetailsByDrillDown")]
        public async Task<IActionResult> GetDetailsByDrillDown(DrillDownParams drillDownParams)
        {
            try
            {
                // 解析原始搜索条件
                ProductInboundBillSearcher searcher = System.Text.Json.JsonSerializer.Deserialize<ProductInboundBillSearcher>(
                    drillDownParams.OriginalSearchParams ?? "{}");

                // 根据钻取参数添加额外的过滤条件
                var vm = Wtm.CreateVM<ProductInboundBillListVM>();
                vm.Searcher = ApplyDrillDownFilter(searcher, drillDownParams);

                // 设置分页参数
                vm.Searcher.Page = drillDownParams.PageIndex;
                vm.Searcher.Limit = drillDownParams.PageSize;

                var result = vm.GetJson(enumToString: false);
                return Content(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"钻取查询失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用钻取过滤条件
        /// </summary>
        private ProductInboundBillSearcher ApplyDrillDownFilter(ProductInboundBillSearcher searcher, DrillDownParams drillDownParams)
        {
            switch (drillDownParams.GroupType?.ToLower())
            {
                case "customer":
                    if (Guid.TryParse(drillDownParams.GroupKey, out var customerId))
                    {
                        // 需要通过POrder关联查询客户
                        // 这里可能需要调整搜索器以支持客户ID过滤
                    }
                    break;

                case "product":
                    if (Guid.TryParse(drillDownParams.GroupKey, out var productId))
                    {
                        // 需要通过POrder关联查询产品
                    }
                    break;

                case "finishingfactory":
                    if (Guid.TryParse(drillDownParams.GroupKey, out var factoryId))
                    {
                        searcher.FinishingFactoryId = factoryId;
                    }
                    break;

                case "warehouse":
                    searcher.Wearhouse = drillDownParams.GroupKey;
                    break;

                case "date":
                    if (DateTime.TryParse(drillDownParams.GroupKey, out var date))
                    {
                        searcher.CreateDate = new DateRange(date.Date, date.Date.AddDays(1).AddSeconds(-1));
                    }
                    break;

                case "month":
                    if (DateTime.TryParse($"{drillDownParams.GroupKey}-01", out var monthDate))
                    {
                        var startDate = new DateTime(monthDate.Year, monthDate.Month, 1);
                        var endDate = startDate.AddMonths(1).AddSeconds(-1);
                        searcher.CreateDate = new DateRange(startDate, endDate);
                    }
                    break;
            }

            return searcher;
        }

        #endregion
    }
}