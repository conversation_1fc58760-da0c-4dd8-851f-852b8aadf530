using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Finished.InspectedRollVMs;
using TEX.Model.Models;
using FastReport;
using System.IO;
using Elsa.Server.Api.Endpoints.WorkflowDefinitions;
using FastReport.Export.Pdf;
using BootstrapBlazor.Components;
using Microsoft.EntityFrameworkCore;
using TEX.ViewModel.Models.OrderDetailVMs;
using System.Text.Json;
using System.Threading.Tasks;
using TEX.ViewModel.Models;
using TEX.Shared;
using Microsoft.AspNetCore.Http;
using System.Linq.Expressions;
using TEX.ViewModel.Models.PurchaseOrderVMs;
using Elsa.Models;
using Microsoft.Extensions.Logging;


namespace TEX.Controllers
{
    [Area("Finished")]
    [AuthorizeJwtWithCookie]
    [ActionDescription("检验")]
    [ApiController]
    [Route("api/InspectedRoll")]
    public partial class InspectedRollController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
        public IActionResult Search(InspectedRollSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<InspectedRollListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));


            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("SearchLot")]
        [HttpPost("SearchLot")]
        public IActionResult SearchLot(InspectedLotSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<InspectedLotListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [HttpGet("GetPOrderTreeItemByCustomerId/{id}")]
        public ActionResult GetPOrderTreeItemByCustomerId(string id)
        {
            if (!Guid.TryParse(id, out Guid customerId))
            {
                return Ok(new List<PurchaseOrderWithDetails_TreeView>());
            }

            // 获取已有检验记录的OrderDetailId
            var inspectedOrderDetailIds = DC.Set<InspectedRoll>()
                .Where(ir => ir.OrderDetail.PurchaseOrder.CustomerId == customerId)
                .Select(ir => ir.OrderDetailId)
                .Distinct()
                .ToList();

            // 获取相关的订单
            var pOrders = DC.Set<OrderDetail>()
                .Where(od => inspectedOrderDetailIds.Contains(od.ID))
                .Select(od => new PurchaseOrderWithDetails_TreeView
                {
                    ID = od.PurchaseOrder.ID,
                    ParentId = null,
                    Text = od.PurchaseOrder.OrderNo + " - " + od.PurchaseOrder.Product.ProductName,
                    Icon = "order-icon",
                    IsActive = true
                })
                .Distinct()
                .OrderByDescending(x => x.Text)
                .ToList();

            // 获取相关的订单明细
            var pOrderDetails = DC.Set<OrderDetail>()
                .Where(od => inspectedOrderDetailIds.Contains(od.ID))
                .Select(od => new PurchaseOrderWithDetails_TreeView
                {
                    ID = od.ID,
                    ParentId = od.PurchaseOrderId,
                    Text = od.Color,
                    Icon = "detail-icon",
                    IsActive = true
                })
                .ToList();

            // 合并订单和订单明细
            var query = pOrders.Concat(pOrderDetails).ToList();

            return Ok(query);
        }

        [HttpGet("GetInspectCustomer")]
        public IActionResult GetInspectCustomer()
        {
            //查询InspectedRoll表中存在的OrderDetailId,返回相应的客户信息
            var rv = DC.Set<InspectedRoll>()
                .Select(x => new SelectedItem
                {
                    Text = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
                    Value = x.OrderDetail.PurchaseOrder.Customer.ID.ToString()
                }).Distinct();
            return Ok(rv);
        }






        [ActionDescription("Sys.Search")]
        [HttpPost("SearchNoLimit")]
        public IActionResult SearchNoLimit(InspectedRollSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<InspectedRollListVM>();
                searcher.Limit = 1000;
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        /// <summary>
        /// 获取检验按订单颜色缸号分组统计信息
        /// </summary>
        /// <returns></returns>
        //[ActionDescription("Sys.Search")]
        [HttpPost("SearchInspectedLot")]
        public IActionResult InspectedLot()
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<InspectedRollListVM>();
                var data = DC.Set<InspectedRoll>().GroupBy(x => new { x.OrderDetailId, x.InspectedLot })
                    .Select(group => new InspectLot
                    {
                        //OrderDetailId = group.Key.OrderDetailId,
                        LotNO = group.Key.InspectedLot,
                        RollsCount = group.Count(), // 计算每组的卷数
                        TotalMeters = group.Sum(x => x.Meters), // 计算每组的总米数
                        TotalWeight = group.Sum(x => x.Weight) // 计算每组的总重量
                    }).ToList();
                return Ok(data);
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }

        }


        //成品出库从检验缸号记录中查询卷明细
        [HttpGet("GetRollsByOrderDetailIdAndLotNo")]
        public IActionResult GetRollsByOrderDetailIdAndLotNo(string orderDetailId, string lotNo)
        {
            if (!Guid.TryParse(orderDetailId, out Guid orderDetailIdGuid))
            {
                return BadRequest("Invalid orderDetailId format");
            }

            if (string.IsNullOrEmpty(lotNo))
            {
                return BadRequest("LotNo cannot be empty");
            }


            var data = DC.Set<InspectedRoll>()
                .Where(x => x.OrderDetailId == orderDetailIdGuid && x.InspectedLot == lotNo)
                .ToList();

            return Ok(data);
        }



        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public InspectedRollVM Get(string id)
        {
            var vm = Wtm.CreateVM<InspectedRollVM>(id);
            return vm;
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(InspectedRollVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }


        /// <summary>
        /// 直接转VM调用原生Add接口,本接口弃用
        /// </summary>
        /// <param name="roll"></param>
        /// <returns></returns>
        [ActionDescription("Sys.Create" + "1")]
        [HttpPost("AddRoll")]
        public IActionResult AddRoll(InspectedRoll roll)
        {

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                DC.Set<InspectedRoll>().Add(roll);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    DC.SaveChanges();
                    return Ok(roll);

                }
            }
        }

        /// <summary>
        /// 新增或更新检验卷
        /// </summary>
        /// <param name="rolls">检验卷列表</param>
        /// <returns>成功处理的记录数</returns>
        [ActionDescription("AddOrUpdateRollList")]
        [HttpPost("AddOrUpdateRollList")]
        public IActionResult AddOrUpdateRollList(List<InspectedRoll> rolls)
        {
            if (rolls == null || !rolls.Any())
            {
                return BadRequest("检验卷列表不能为空");
            }

            try
            {
                List<Guid> ids = new();
                // 使用事务确保数据一致性
                using (var transaction = DC.BeginTransaction())
                {
                    try
                    {
                        
                        // 获取所有需要更新的ID列表
                        List<Guid> rollIds = rolls.Where(r => r.ID != Guid.Empty).Select(r => r.ID).ToList();
                        
                        // 批量查询现有记录，减少数据库查询次数
                        var existingRolls = DC.Set<InspectedRoll>().AsNoTracking()
                            .Where(r => rollIds.Contains(r.ID))
                            .ToDictionary(r => r.ID);
                        
                        // 分离新增和更新操作
                        var rollsToAdd = new List<InspectedRoll>();
                        var rollsToUpdate = new List<InspectedRoll>();
                        
                        foreach (var roll in rolls)
                        {
                            // 验证必要字段
                            if (roll.OrderDetailId == Guid.Empty)
                            {
                                continue; // 跳过无效记录
                            }
                            
                            if (roll.ID != Guid.Empty && existingRolls.ContainsKey(roll.ID))
                            {
                                rollsToUpdate.Add(roll);
                            }
                            else
                            {
                                rollsToAdd.Add(roll);
                            }
                        }
                        
                        // 批量添加新记录
                        if (rollsToAdd.Any())
                        {
                            DC.Set<InspectedRoll>().AddRange(rollsToAdd);
                            
                            ids.AddRange(rollsToAdd.Select(r => r.ID));
                        }
                        
                        // 批量更新现有记录
                        foreach (var roll in rollsToUpdate)
                        {
                            DC.Set<InspectedRoll>().Update(roll);
                            ids.AddRange(rollsToAdd.Select(r => r.ID));
                        }
                        
                        // 保存所有更改
                        if (ids.Count > 0)
                        {
                            DC.SaveChanges();
                        }
                        
                        // 提交事务
                        transaction.Commit();
                        
                        return Ok(ids);
                    }
                    catch (Exception ex)
                    {
                        // 回滚事务
                        transaction.Rollback();
                        throw; // 重新抛出异常以便外层捕获
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Wtm.LoggerFactory.CreateLogger("Exception").LogError(ex, $"批量处理检验卷失败，共{rolls.Count}条记录");
                return BadRequest(new { Success = false, Message = "批量处理检验卷时发生错误: " + ex.Message });
            }
        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(InspectedRollVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        /// <summary>
        /// WinForm端调用Edit方法ModelState验证错误,找不出原因,只能单独做一个API
        /// 找到原因了,是因为List没有返回OrderDetailId,导致验证失败
        /// 本接口可以弃用
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        [HttpPut("EditRoll")]
        public IActionResult EditRoll(InspectedRollVM vm)
        {
            var original = DC.Set<InspectedRoll>().Find(vm.Entity.ID);
            if (original == null)
            {
                return NotFound();
            }
            else
            {
                original.InspectedLot = vm.Entity.InspectedLot;
                original.RollNo = vm.Entity.RollNo;
                original.Meters = vm.Entity.Meters;
                original.Weight = vm.Entity.Weight;
                original.GW = vm.Entity.GW;
                original.Yards = vm.Entity.Yards;
                original.DefectsRecord = vm.Entity.DefectsRecord;
                original.Score = vm.Entity.Score;
                original.TotalScore = vm.Entity.TotalScore;
                original.Remark = vm.Entity.Remark;
                original.AuditStatus = vm.Entity.AuditStatus;
                original.FreeYards = vm.Entity.FreeYards;


                DC.SaveChanges();
                return Ok(vm.Entity);
            }
        }


        [HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<InspectedRollBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(InspectedRollSearcher searcher)
        {
            var vm = Wtm.CreateVM<InspectedRollListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<InspectedRollListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<InspectedRollImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(InspectedRollImportVM vm)
        {
            if (vm != null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }

        /// <summary>
        /// 检验卷中返回不重复的OrderDetailId,显示:产品+颜色
        /// </summary>
        /// <returns>不重复的OrderDetailId,显示:产品+颜色</returns>
        [HttpGet("GetOrderDetailsOfInspected")]
        public ActionResult GetOrderDetailsOfInspected()
        {
            return Ok(DC.Set<InspectedRoll>().GroupBy(ir => ir.OrderDetailId).
                Select(x => new SelectedItem
                {
                    Text = x.First().OrderDetail.PurchaseOrder.Product.ProductName + " - " + x.First().OrderDetail.Color,
                    Value = x.First().OrderDetailId.ToString()
                })
             );
        }

        /// <summary>
        /// 获取订单明细的缸号
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public ActionResult GetLotNoByOrderDetailId(string id)
        {
            var rv = DC.Set<InspectedRoll>()
                .Where(x => x.OrderDetailId == Guid.Parse(id))
                .Select(x => x.InspectedLot).Distinct();

            return Ok(rv);
        }

        /// <summary>
        /// 获取订单的检验缸号
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public ActionResult GetLotNoByPurchaseOrderId(string id)
        {
            //方法语法: EFCore翻译出来的sql语句一致
            //var r = DC.Set<InspectedRoll>()
            //        .Join(
            //            inner: DC.Set<OrderDetail>(),
            //            outerKeySelector: ir => ir.OrderDetailId,
            //            innerKeySelector: od => (Guid?)od.ID,
            //            resultSelector: (ir, od) => new
            //            {
            //                ir = ir,
            //                od = od
            //            })
            //        .Where(x => x.od.PurchaseOrderId == Guid.Parse(id))
            //        .Select(c => c.ir.InspectedLot)
            //        .Distinct();

            //查询语法
            var rv = (from ir in DC.Set<InspectedRoll>()
                      join od in DC.Set<OrderDetail>() on ir.OrderDetailId equals od.ID
                      where od.PurchaseOrderId == Guid.Parse(id)
                      select ir.InspectedLot).Distinct();



            return Ok(rv);
        }

        /// <summary>
        /// 查询产品的检验缸号
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public ActionResult GetLotNoByProductId(string productId)
        {
            var rv = (from ir in DC.Set<InspectedRoll>()
                      join od in DC.Set<OrderDetail>() on ir.OrderDetailId equals od.ID
                      join po in DC.Set<PurchaseOrder>() on od.PurchaseOrderId equals po.ID
                      //join p in DC.Set<Product>() on po.ProductId equals p.ID
                      where po.ProductId == Guid.Parse(productId)
                      select ir.InspectedLot
           ).Distinct();

            return Ok(rv);
        }


        /// <summary>
        /// 返回打印pdf
        /// </summary>
        /// <returns></returns>
        [HttpGet("Roll")]
        public IActionResult Roll()
        {
            string webRootPath = AppContext.BaseDirectory;


            Report report = new Report();
            report.Load(webRootPath + "\\Files\\ReportsFrx\\Labels.frx");
            //report.RegisterData(businessObjects, "Categories");
            report.Prepare();

            PDFExport export = new PDFExport();
            MemoryStream stream = new MemoryStream();
            report.Export(export, stream);
            var t = stream.GetBuffer();
            stream.Close();
            report.Dispose();

            //不需要增加这些Headers
            //Response.Headers.Add("Cache-Control", "max-age=31536000, must-revalidate");
            //Response.Headers.Add("accept-ranges", "bytes");
            //Response.Headers.Add("Access-Control-Expose-Headers", "Content-Disposition");

            return File(t, "application/pdf");



            //HTMLExport export = new ();
            //MemoryStream stream = new MemoryStream();
            //report.Export(export, stream);
            //stream.Close();
            //report.Dispose();
            //return File(stream.ToArray(), "text/html");
        }


        [HttpGet("[action]")]
        public ActionResult GetInspectedPurchaseOrdersSelectedItem()
        {
            //var rv = DC.Set<InspectedRoll>()
            //     .Include(x => x.OrderDetail)
            //     .ThenInclude(x => x.PurchaseOrder)
            //     .Where(x => x.OrderDetail.PurchaseOrder.AuditStatus == AuditStatusEnum.AuditedApproved)
            //     .Select(
            //    x => new SelectedItem
            //    {
            //        Text = x.OrderDetail.PurchaseOrder.OrderNo,
            //        Value = x.OrderDetailId.ToString(),
            //    })
            //     .Distinct()
            //     .ToList();
            var rv = DC.Set<InspectedRoll>()
                .Select(i => i.OrderDetailId)
                .Distinct()
                .Join(
                    DC.Set<OrderDetail>(),
                    i => i,
                    o => o.ID,
                    (i, o) => new { i, o }
                )
                .Join(
                    DC.Set<PurchaseOrder>().Where(p => p.AuditStatus == AuditStatusEnum.AuditedApproved),
                    io => io.o.PurchaseOrderId,
                    p => p.ID,
                    (io, p) => new SelectedItem
                    {
                        Text = p.OrderNo,
                        Value = p.ID.ToString()
                    }
                )
                .Distinct()
                .ToList();

            return Ok(rv);
        }

        [HttpGet("[action]")]
        public ActionResult GetInspectedCustomerSelectedItem()
        {
            //var rv = DC.Set<InspectedRoll>()
            //     .Include(x => x.OrderDetail)
            //     .ThenInclude(x => x.PurchaseOrder)
            //     .Where(x => x.OrderDetail.PurchaseOrder.AuditStatus == AuditStatusEnum.AuditedApproved)
            //     .Select(
            //    x => new SelectedItem
            //    {
            //        Text = x.OrderDetail.PurchaseOrder.Customer.CompanyName,
            //        Value = x.OrderDetail.PurchaseOrder.CustomerId.ToString(),
            //    })
            //     .Distinct()
            //     .ToList();
            var rv = DC.Set<InspectedRoll>()
                .Select(i => i.OrderDetailId)
                .Distinct()
                .Join(
                    DC.Set<OrderDetail>(),
                    i => i,
                    o => o.ID,
                    (i, o) => o
                )
                .Join(
                    DC.Set<PurchaseOrder>().Where(p => p.AuditStatus == AuditStatusEnum.AuditedApproved),
                    o => o.PurchaseOrderId,
                    p => p.ID,
                    (o, p) => new { o, p }
                )
                .Join(
                    DC.Set<Company>(),
                    op => op.p.CustomerId,
                    c => c.ID,
                    (op, c) => new SelectedItem
                    {
                        Text = c.CompanyName,
                        Value = op.p.CustomerId.ToString()
                    }
                )
                .Distinct()
                .ToList();

            return Ok(rv);
        }

        [HttpGet("[action]")]
        public ActionResult GetInspectedProductSelectedItem()
        {
            var rv = DC.Set<InspectedRoll>()
                .Select(i => i.OrderDetailId)
                .Distinct()
                .Join(
                    DC.Set<OrderDetail>(),
                    i => i,
                    o => o.ID,
                    (i, o) => o
                )
                .Join(
                    DC.Set<PurchaseOrder>().Where(p => p.AuditStatus == AuditStatusEnum.AuditedApproved),
                    o => o.PurchaseOrderId,
                    p => p.ID,
                    (o, p) => new { o, p }
                )
                .Join(
                    DC.Set<Product>(),
                    op => op.p.ProductId,
                    c => c.ID,
                    (op, c) => new SelectedItem
                    {
                        Text = c.ProductName,
                        Value = op.p.ProductId.ToString()
                    }
                )
                .Distinct()
                .ToList();

            return Ok(rv);
        }

        /// <summary>
        /// 导出检验汇总报表
        /// </summary>
        /// <param name="Lots"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        public IActionResult ExportExcelReportByOrderDetailIdAndLotNo([FromBody] object Lots)
        {
            List<string> lotList;
            Expression<Func<InspectedRoll, bool>> exp;
            // 解析参数

            string s = Lots.ToString();
            try
            {

                ;
                var id = Guid.Parse(s);
                exp = x => x.OrderDetailId == id;
            }
            catch (Exception)
            {
                lotList = JsonSerializer.Deserialize<List<string>>(s);
                exp = x => lotList.Contains(x.OrderDetailId.ToString() + "," + x.InspectedLot);

            }

            try
            {
                // 获取检验数据
                var inspectedRolls = DC.Set<InspectedRoll>()
                    .Where(exp)
                    .Select(x => new InspectedRoll_View
                    {
                        OrderNo = x.OrderDetail.PurchaseOrder.OrderNo,
                        ProductName = x.OrderDetail.PurchaseOrder.Product.ProductName,
                        Spec = x.OrderDetail.PurchaseOrder.Product.Spec,
                        InspectedLot = x.InspectedLot,
                        RollNo = x.RollNo,
                        Color_view = x.OrderDetail.Color,
                        Yards = x.Yards,
                        TotalScore = x.TotalScore,
                        Score = x.Score,
                        Grade = x.Grade,
                        DefectsRecord = x.DefectsRecord
                    })
                    //.OrderBy(x => x.CreateTime)//Select中需要有这个字段才能排序
                    .OrderBy(x => x.InspectedLot)
                    .ThenBy(x => x.RollNo)
                    .ToList();

                if (!inspectedRolls.Any())
                {
                    return BadRequest("未找到相关检验数据");
                }

                // 获取模板文件路径
                string templatePath = Path.Combine(AppContext.BaseDirectory, "Files", "检验报告.xlsx");
                if (!System.IO.File.Exists(templatePath))
                {
                    return BadRequest("报表模板文件不存在");
                }

                // 生成Excel文件
                byte[] fileContent = ExcelHelper.GenerateInspectReportNPOI(templatePath, inspectedRolls);

                // 设置文件名
                string fileName = $"检验报告_{DateTime.Now:yyyyMMddHHmmss}.xlsx";

                //文件名需要进行UrlEncode编码才能添加到响应头中
                string encodedFileName = Uri.EscapeDataString(fileName);
                Response.Headers.Append("Content-Disposition", $"attachment; filename={encodedFileName}");

                return File(fileContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            catch (Exception ex)
            {
                return BadRequest($"生成报表失败: {ex.Message}");
            }


        }


        /// <summary>
        /// 获取检验汇总数据
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetInspectDashboardData")]
        public async Task<ActionResult<InspectDashboardData>> GetInspectDashboardData()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1); // 添加明天的日期
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var startOfYear = new DateTime(today.Year, 1, 1);

            // 单次查询获取年、月、周、日的汇总数据
            var periodTotals = await DC.Set<InspectedRoll>()
                .GroupBy(_ => 1)
                .Select(g => new
                {
                    // 年度统计
                    YearMeters = g.Where(x => x.CreateTime >= startOfYear).Sum(x => x.Meters),
                    YearYards = g.Where(x => x.CreateTime >= startOfYear).Sum(x => x.Yards),
                    YearWeight = g.Where(x => x.CreateTime >= startOfYear).Sum(x => x.Weight),

                    // 月度统计
                    MonthMeters = g.Where(x => x.CreateTime >= startOfMonth).Sum(x => x.Meters),
                    MonthYards = g.Where(x => x.CreateTime >= startOfMonth).Sum(x => x.Yards),
                    MonthWeight = g.Where(x => x.CreateTime >= startOfMonth).Sum(x => x.Weight),

                    // 周统计
                    WeekMeters = g.Where(x => x.CreateTime >= startOfWeek).Sum(x => x.Meters),
                    WeekYards = g.Where(x => x.CreateTime >= startOfWeek).Sum(x => x.Yards),
                    WeekWeight = g.Where(x => x.CreateTime >= startOfWeek).Sum(x => x.Weight),

                    // 日统计
                    TodayMeters = g.Where(x => x.CreateTime >= today && x.CreateTime < tomorrow).Sum(x => x.Meters),
                    TodayYards = g.Where(x => x.CreateTime >= today && x.CreateTime < tomorrow).Sum(x => x.Yards),
                    TodayWeight = g.Where(x => x.CreateTime >= today && x.CreateTime < tomorrow).Sum(x => x.Weight)
                })
                .FirstOrDefaultAsync();

            // 单次查询获取月度趋势数据
            var monthlyTrends = await DC.Set<InspectedRoll>()
                .Where(x => x.CreateTime.HasValue)
                .GroupBy(x => new { x.CreateTime.Value.Year, x.CreateTime.Value.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalYards = g.Sum(x => x.Yards),
                    TotalMeters = g.Sum(x => x.Meters)
                })
                .OrderBy(x => x.Year)
                .ThenBy(x => x.Month)
                .ToListAsync();

            var monthlyWeight = monthlyTrends.Select(x => new InspectMonthlyTotalWeight
            {
                Year = x.Year,
                Month = x.Month,
                MonthlyTotalWeight = x.TotalWeight
            }).ToList();

            var monthlyYards = monthlyTrends.Select(x => new InspectMonthlyTotalYards
            {
                Year = x.Year,
                Month = x.Month,
                MonthlyTotalYards = x.TotalYards
            }).ToList();

            var monthlyMeters = monthlyTrends.Select(x => new InspectMonthlyTotalMeters
            {
                Year = x.Year,
                Month = x.Month,
                MonthlyTotalMeters = x.TotalMeters
            }).ToList();
            if (periodTotals == null)
            {
                return NotFound("No data found for the specified period.");
            }
            return Ok(new InspectDashboardData
            {
                InspectYearTotalMeters = periodTotals.YearMeters,
                InspectYearTotalYards = periodTotals.YearYards,
                InspectYearTotalWeight = periodTotals.YearWeight,
                InspectMonthTotalMeters = periodTotals.MonthMeters,
                InspectMonthTotalYards = periodTotals.MonthYards,
                InspectMonthTotalWeight = periodTotals.MonthWeight,
                InspectWeekTotalMeters = periodTotals.WeekMeters,
                InspectWeekTotalYards = periodTotals.WeekYards,
                InspectWeekTotalWeight = periodTotals.WeekWeight,
                InspectTodayTotalMeters = periodTotals.TodayMeters,
                InspectTodayTotalYards = periodTotals.TodayYards,
                InspectTodayTotalWeight = periodTotals.TodayWeight,
                InspectMonthlyTotalWeight = monthlyWeight,
                InspectMonthlyTotalYards = monthlyYards,
                InspectMonthlyTotalMeters = monthlyMeters
            });
        }
    }
}