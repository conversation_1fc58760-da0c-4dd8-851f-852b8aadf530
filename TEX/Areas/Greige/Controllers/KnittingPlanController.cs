using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Greige.KnittingPlanVMs;
using TEX.Model.Models;
using TEX.Model;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using TEX.ViewModel.Greige.KnittingProcessVMs;
using System.Reflection;

namespace TEX.Controllers
{
    public class EnumDisplayNameConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType.IsEnum;
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            return Enum.Parse(objectType, reader.Value.ToString());
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteValue(((Enum)value).GetDisplayName());
        }
    }

    [Area("Greige")]
    [AuthorizeJwtWithCookie]
    [ActionDescription("织造计划")]
    [ApiController]
    [Route("api/KnittingPlan")]
    public partial class KnittingPlanController : BaseApiController
    {
        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            StringEscapeHandling = StringEscapeHandling.Default,
            Formatting = Formatting.None,
            NullValueHandling = NullValueHandling.Include,
            DateFormatString = "yyyy-MM-dd HH:mm:ss"
        };

        private static readonly JsonSerializerSettings EnumSettings = new JsonSerializerSettings
        {
            StringEscapeHandling = StringEscapeHandling.Default,
            Formatting = Formatting.None,
            NullValueHandling = NullValueHandling.Include,
            Converters = new List<JsonConverter> { new EnumDisplayNameConverter() }
        };

        private string ConvertMachineInfo(string machineInfoJson)
        {
            if (string.IsNullOrEmpty(machineInfoJson)) return "{}";
            var machineInfo = JsonConvert.DeserializeObject<MachineInfo>(machineInfoJson);
            return JsonConvert.SerializeObject(machineInfo, EnumSettings);
        }

        private string ConvertYarnInfo(string yarnInfoJson)
        {
            if (string.IsNullOrEmpty(yarnInfoJson)) return "[]";
            var yarnInfoList = JsonConvert.DeserializeObject<List<YarnInfo>>(yarnInfoJson);
            return JsonConvert.SerializeObject(yarnInfoList, EnumSettings);
        }

        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
        public IActionResult Search(KnittingPlanSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<KnittingPlanListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public KnittingPlanVM Get(string id)
        {
            var vm = Wtm.CreateVM<KnittingPlanVM>(id);
            return vm;
        }

        [HttpGet("GetKnitPlanDTO/{id}")]
        public IActionResult GetKnitPlanDTO(string id)
        {
            var dto = DC.Set<KnittingPlan>()
                .Include(x => x.KnittingProcess)
                .Include(x => x.PurchaseOrder)
                .Include(x => x.KnittingFactory)
                .CheckID(id)
                .Select(x => new KnittingPlanDTO
                {
                    BillNo = x.BillNo,
                    OrderNo = x.PurchaseOrder.OrderNo,
                    ProductName = x.ProductName,
                    KnittingFactory = x.KnittingFactory.CompanyName,
                    PlanDate = x.PlanDate,
                    DeliveryDate = x.DeliveryDate,
                    PieceCount = x.PieceCount,
                    TotalWeight = (double)x.TotalWeight,
                    WeightPerPiece = (double)x.WeightPerPiece,
                    Requirements = x.Requirements ?? "",
                    BatchNo = x.BatchNo ?? "",
                    CreateBy = x.CreateBy,
                    Status = x.Status.GetDisplayName(),
                    Remark = x.Remark ?? "",
                    ProcessCode = x.KnittingProcess.ProcessCode,
                    FinishedWeight = x.KnittingProcess.FinishedWeight,
                    FinishedWidth = x.KnittingProcess.FinishedWidth,
                    FinishedPileHeight = x.KnittingProcess.FinishedPileHeight,
                    GreigeWeight = x.KnittingProcess.Weight,
                    GreigeWidth = x.KnittingProcess.Width,
                    GreigePileHeight = x.KnittingProcess.PileLength,
                    MachineInfoJson = x.KnittingProcess.MachineInfoJson,
                    YarnInfoJson = x.KnittingProcess.YarnInfoJson
                }).FirstOrDefault();

            if (dto == null)
                return NotFound();

            // 处理嵌套的 JSON 字符串中的枚举
            dto.MachineInfoJson = ConvertMachineInfo(dto.MachineInfoJson);
            dto.YarnInfoJson = ConvertYarnInfo(dto.YarnInfoJson);

            return Content(JsonConvert.SerializeObject(dto, JsonSettings), "application/json");
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(KnittingPlanVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(KnittingPlanVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

        [HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<KnittingPlanBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(KnittingPlanSearcher searcher)
        {
            var vm = Wtm.CreateVM<KnittingPlanListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<KnittingPlanListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<KnittingPlanImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(KnittingPlanImportVM vm)
        {
            if (vm != null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }


        [HttpGet("GetPurchaseOrders")]
        public ActionResult GetPurchaseOrders()
        {
            return Ok(DC.Set<PurchaseOrder>().GetSelectListItems(Wtm, x => x.OrderNo));
        }

        [HttpGet("GetCompanys")]
        public ActionResult GetCompanys()
        {
            return Ok(DC.Set<Company>().GetSelectListItems(Wtm, x => x.CompanyName));
        }

        [HttpGet("GetKnittingProcesss")]
        public ActionResult GetKnittingProcesss()
        {
            return Ok(DC.Set<KnittingProcess>().GetSelectListItems(Wtm, x => x.ProcessCode));
        }
    }
}
