using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Mvc;
using TEX.ViewModel.Greige.KnittingProcessVMs;
using TEX.Model.Models;
using Microsoft.EntityFrameworkCore;


namespace TEX.Controllers
{
    [Area("Greige")]
    [AuthorizeJwtWithCookie]
    [ActionDescription("坯布管理")]
    [ApiController]
    [Route("api/KnittingProcess")]
	public partial class KnittingProcessController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
		public IActionResult Search(KnittingProcessSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<KnittingProcessListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public KnittingProcessVM Get(string id)
        {
            var vm = Wtm.CreateVM<KnittingProcessVM>(id);
            return vm;
        }


        [HttpGet("[action]/{id}")]
        public IActionResult GetView(string id)
        {
            if (!Guid.TryParse(id, out var guid))
            {
                return BadRequest("ID格式无效");
            }

            var view =  DC.Set<KnittingProcess>()
                .Where(x => x.ID == guid)
                .Select(x => new KnittingProcess_View()
            {
                ID = x.ID,
                ProcessCode = x.ProcessCode,
                Category_view = x.Category.Description,
                MachineInfoJson = x.MachineInfoJson,
                Weight = x.Weight,
                Width = x.Width,
                PileLength = x.PileLength,
                FinishedWeight = x.FinishedWeight,
                FinishedWidth = x.FinishedWidth,
                FinishedPileHeight = x.FinishedPileHeight,
                YarnInfoJson = x.YarnInfoJson,
                Remark = x.Remark,
                CreateTime = x.CreateTime
            }).FirstOrDefault();
            if (view == null)
            {
                return NotFound("未找到对应工艺数据");
            }
            return Ok(view);
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(KnittingProcessVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(KnittingProcessVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

		[HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<KnittingProcessBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(KnittingProcessSearcher searcher)
        {
            var vm = Wtm.CreateVM<KnittingProcessListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<KnittingProcessListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<KnittingProcessImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(KnittingProcessImportVM vm)
        {
            if (vm!=null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }

        [HttpPost("[action]")]
        public ActionResult Select_GetKnittingProcess()
        {
            var rv = DC.Set<KnittingProcess>().GetSelectListItems(Wtm, x => x.ProcessCode);
            return Ok(rv);
        }
    }
}
