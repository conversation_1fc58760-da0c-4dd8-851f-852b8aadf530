using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.Model.Models;
using TEX.ViewModel.Greige.PatternDetailVMs;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using WalkingTec.Mvvm.Core.Support.FileHandlers;
using WalkingTec.Mvvm.Mvc;

namespace TEX.Controllers
{
    [Area("Greige")]
    [AuthorizeJwt]
    [ActionDescription("提花稿")]
    [ApiController]
    [Route("api/PatternDetail")]
	public partial class PatternDetailController : BaseApiController
    {
        [ActionDescription("Sys.Search")]
        [HttpPost("Search")]
		public IActionResult Search(PatternDetailSearcher searcher)
        {
            if (ModelState.IsValid)
            {
                var vm = Wtm.CreateVM<PatternDetailListVM>();
                vm.Searcher = searcher;
                return Content(vm.GetJson(enumToString: false));
            }
            else
            {
                return BadRequest(ModelState.GetErrorJson());
            }
        }

        [ActionDescription("Sys.Get")]
        [HttpGet("{id}")]
        public PatternDetailVM Get(string id)
        {
            var vm = Wtm.CreateVM<PatternDetailVM>(id);
            return vm;
        }

        /// <summary>
        /// 获取提花稿详细信息
        /// </summary>
        /// <param name="id">提花稿ID</param>
        /// <returns>提花稿详细信息</returns>
        [HttpGet("{GetPatternDetail_View}/{id}")]
        public async Task<ActionResult<PatternDetail_View>> GetPatternDetail_View(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("ID不能为空");
            }

            if (!Guid.TryParse(id, out Guid patternDetailId))
            {
                return BadRequest("无效的ID格式");
            }

            try
            {
                var patternDetail = await Wtm.DC.Set<PatternDetail>()
                    .Include(x => x.Pattern)
                    .Where(x => x.ID == patternDetailId)
                    .Select(x => new PatternDetail_View
                    {
                        ID = x.ID,
                        CreateDate = x.CreateDate,
                        PatternName_view = x.Pattern.PatternName,
                        Customer_view = x.Pattern.Customer,
                        PatternCode = x.PatternCode,
                        PatternVersion = x.PatternVersion,
                        FabricCategory = x.FabricCategory,
                        PatternRepeatWidth = x.PatternRepeatWidth,
                        PatternRepeatHeight = x.PatternRepeatHeight,
                        RequiredFullWidth = x.RequiredFullWidth,
                        RequiredGsm = x.RequiredGsm,
                        RequiredCuttableWidth = x.RequiredCuttableWidth,
                        MachineInch = x.MachineInch,
                        MachineTotalNeedles = x.MachineTotalNeedles,
                        MachineSpec = x.MachineSpec,
                        JacquardFeed = x.JacquardFeed,
                        PatternWeftPoint = x.PatternWeftPoint,
                        PatternWarpPoint = x.PatternWarpPoint,
                        GreigeRepeatWidth = x.GreigeRepeatWidth,
                        GreigeRepeatHeight = x.GreigeRepeatHeight,
                        GreigeWidth = x.GreigeWidth,
                        GreigeGsm = x.GreigeGsm,
                        FabricRepeatWidth = x.FabricRepeatWidth,
                        FabricRepeatHeight = x.FabricRepeatHeight,
                        FabricFullWidth = x.FabricFullWidth,
                        FabricGsm = x.FabricGsm,
                        VersionModified = x.VersionModified,
                        Remark = x.Remark,
                    })
                    .FirstOrDefaultAsync();

                if (patternDetail == null)
                {
                    return NotFound($"未找到ID为{id}的提花稿");
                }

                return Ok(patternDetail);
            }
            catch (Exception ex)
            {
                // 记录异常日志
                Wtm.LoggerFactory.CreateLogger("Exception").LogError(ex, $"获取提花稿详情失败: {ex.Message}");
                return StatusCode(500, "获取提花稿详情时发生错误");
            }
        }

        [ActionDescription("Sys.Create")]
        [HttpPost("Add")]
        public IActionResult Add(PatternDetailVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoAdd();
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }

        }

        [ActionDescription("Sys.Edit")]
        [HttpPut("Edit")]
        public IActionResult Edit(PatternDetailVM vm)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                vm.DoEdit(false);
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState.GetErrorJson());
                }
                else
                {
                    return Ok(vm.Entity);
                }
            }
        }

		[HttpPost("BatchDelete")]
        [ActionDescription("Sys.Delete")]
        public IActionResult BatchDelete(string[] ids)
        {
            var vm = Wtm.CreateVM<PatternDetailBatchVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = ids;
            }
            else
            {
                return Ok();
            }
            if (!ModelState.IsValid || !vm.DoBatchDelete())
            {
                return BadRequest(ModelState.GetErrorJson());
            }
            else
            {
                return Ok(ids.Count());
            }
        }


        [ActionDescription("Sys.Export")]
        [HttpPost("ExportExcel")]
        public IActionResult ExportExcel(PatternDetailSearcher searcher)
        {
            var vm = Wtm.CreateVM<PatternDetailListVM>();
            vm.Searcher = searcher;
            vm.SearcherMode = ListVMSearchModeEnum.Export;
            return vm.GetExportData();
        }

        [ActionDescription("Sys.CheckExport")]
        [HttpPost("ExportExcelByIds")]
        public IActionResult ExportExcelByIds(string[] ids)
        {
            var vm = Wtm.CreateVM<PatternDetailListVM>();
            if (ids != null && ids.Count() > 0)
            {
                vm.Ids = new List<string>(ids);
                vm.SearcherMode = ListVMSearchModeEnum.CheckExport;
            }
            return vm.GetExportData();
        }

        [ActionDescription("Sys.DownloadTemplate")]
        [HttpGet("GetExcelTemplate")]
        public IActionResult GetExcelTemplate()
        {
            var vm = Wtm.CreateVM<PatternDetailImportVM>();
            var qs = new Dictionary<string, string>();
            foreach (var item in Request.Query.Keys)
            {
                qs.Add(item, Request.Query[item]);
            }
            vm.SetParms(qs);
            var data = vm.GenerateTemplate(out string fileName);
            return File(data, "application/vnd.ms-excel", fileName);
        }

        [ActionDescription("Sys.Import")]
        [HttpPost("Import")]
        public ActionResult Import(PatternDetailImportVM vm)
        {
            if (vm!=null && (vm.ErrorListVM.EntityList.Count > 0 || !vm.BatchSaveData()))
            {
                return BadRequest(vm.GetErrorJson());
            }
            else
            {
                return Ok(vm?.EntityList?.Count ?? 0);
            }
        }


        [HttpGet("GetPatterns")]
        public ActionResult GetPatterns()
        {
            return Ok(DC.Set<Pattern>().GetSelectListItems(Wtm, x => x.PatternName));
        }

    }
}
