<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<CopyRefAssembliesToPublishDirectory>true</CopyRefAssembliesToPublishDirectory>
		<AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
		<Nullable>disable</Nullable>
		<NoWarn>$(NoWarn);NU1902</NoWarn>
		<NoWarn>$(NoWarn);NU1903</NoWarn>
	</PropertyGroup>

	<!-- {{ AURA-X: Add - 排除wwwroot目录内容以避免与TEX.Shared项目的静态资源冲突. Source: ASP.NET Core官方文档 }} -->
	<ItemGroup>
		<Content Remove="wwwroot\**" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Files\检验报告.xlsx" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="Files\发货码单模板.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Files\检验报告.xlsx">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Files\ReportsFrx\Labels.frx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>



	<ItemGroup>
		
		<PackageReference Include="Longbow.Logging" Version="8.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.11" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.11" />
		<!--<PackageReference Include="System.Net.Http.Json" Version="8.0.1" />-->
		<PackageReference Include="WalkingTec.Mvvm.Mvc" Version="8.1.12" />
		<ProjectReference Include="..\TEX.Shared\TEX.Shared.csproj" />
		<ProjectReference Include="..\TEX.DataAccess\TEX.DataAccess.csproj" />
	</ItemGroup>



	<ItemGroup>
	  <Reference Include="FastReport">
	    <HintPath>Files\FastReport.dll</HintPath>
	  </Reference>
	</ItemGroup>
</Project>

