@page "/_host"
@namespace TEX
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = null;
}
@inject WalkingTec.Mvvm.Core.WTMContext wtm
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>六星科技面料管理系统</title>
    <base href="~/" />
    <link href="_content/TEX.Shared/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css" rel="stylesheet">
    <link rel="stylesheet" href="_content/TEX.Shared/css/site.css">
    <link rel="stylesheet" href="_content/TEX.Shared/css/loading.css">
    <link rel="stylesheet" href="_content/TEX.Shared/font/iconfont.css">
    <link rel="stylesheet" href="TEX.styles.css">
</head>
<body>
    <component type="typeof(TEX.Shared.App)" render-mode="Server" />
    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"></script>
    <script src="_content/BootstrapBlazor.SummerNote/js/jquery-3.5.1.min.js"></script>
    <script src="_content/TEX.Shared/js/common.js"></script>
    <script src="_framework/blazor.server.js"></script>
    <script src="_content/TEX.Shared/js/webSocketPrintClient.js"></script>
    <script src="_content/TEX.Shared/js/clipboard.js"></script>
</body>
</html>
