# TEX项目出入库系统优化实施方案

## 文档说明

本文档是《出入库系统长期架构设计指导文档》的配套实施方案，提供具体的代码实现、部署步骤和测试验证方法。

## 1. 第一阶段：基础优化实施（立即执行）

### 1.1 创建优化索引

```sql
-- 执行脚本：01_create_optimized_indexes.sql

-- 1. 当年查询优化索引
CREATE INDEX idx_inbound_lot_current_year 
ON ProductInboundLots(CreateDate, OrderDetailId, IsValid) 
WHERE YEAR(CreateDate) = YEAR(NOW());

CREATE INDEX idx_outbound_lot_current_year 
ON ProductOutboundLots(CreateDate, OrderDetailId, IsValid) 
WHERE YEAR(CreateDate) = YEAR(NOW());

CREATE INDEX idx_inbound_roll_current_year 
ON ProductInboundRolls(LotId, CreateDate, IsValid) 
WHERE YEAR(CreateDate) = YEAR(NOW());

CREATE INDEX idx_outbound_roll_current_year 
ON ProductOutboundRolls(LotId, CreateDate, IsValid) 
WHERE YEAR(CreateDate) = YEAR(NOW());

-- 2. 基础关联查询索引
CREATE INDEX idx_orderdetail_purchase_product 
ON OrderDetails(PurchaseOrderId, ID);

CREATE INDEX idx_purchase_customer_product 
ON PurchaseOrders(CustomerId, ProductId, ID);

CREATE INDEX idx_stock_orderdetail 
ON ProductStocks(OrderDetailId, IsValid);

-- 3. 统计查询复合索引
CREATE INDEX idx_inbound_lot_orderdetail_date 
ON ProductInboundLots(OrderDetailId, CreateDate, IsValid);

CREATE INDEX idx_outbound_lot_orderdetail_date 
ON ProductOutboundLots(OrderDetailId, CreateDate, IsValid);

-- 验证索引创建
SHOW INDEX FROM ProductInboundLots;
SHOW INDEX FROM ProductOutboundLots;
SHOW INDEX FROM ProductInboundRolls;
SHOW INDEX FROM ProductOutboundRolls;
```

### 1.2 优化当年查询ViewModel

```csharp
// 文件：TEX.ViewModel/Finished/OptimizedInventoryStatisticsVM.cs

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;
using TEX.Model.Models;

namespace TEX.ViewModel.Finished
{
    /// <summary>
    /// 优化的库存统计查询ViewModel
    /// </summary>
    public class OptimizedInventoryStatisticsVM : BaseVM
    {
        /// <summary>
        /// 当年数据统计查询（95%的查询场景）
        /// </summary>
        public async Task<List<CurrentYearStatistics>> GetCurrentYearStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? productId = null,
            Guid? customerId = null)
        {
            var currentYear = DateTime.Now.Year;
            startDate ??= new DateTime(currentYear, 1, 1);
            endDate ??= DateTime.Now;
            
            // 确保查询范围在当年内，利用分区优势
            if (startDate.Value.Year != currentYear || endDate.Value.Year != currentYear)
            {
                startDate = new DateTime(currentYear, 1, 1);
                endDate = new DateTime(currentYear, 12, 31);
            }
            
            var productFilter = productId.HasValue ? $"AND po.ProductId = '{productId}'" : "";
            var customerFilter = customerId.HasValue ? $"AND po.CustomerId = '{customerId}'" : "";
            
            // 使用原生SQL查询，利用优化索引
            var sql = $@"
                SELECT 
                    od.ID as OrderDetailId,
                    po.OrderNo,
                    po.CustomerOrderNo,
                    c.CompanyName as CustomerName,
                    p.ProductName,
                    p.ProductCode,
                    od.Color,
                    od.ColorCode,
                    
                    COUNT(DISTINCT il.ID) as InboundLots,
                    COUNT(ir.ID) as InboundRolls,
                    COALESCE(SUM(ir.Weight), 0) as InboundWeight,
                    COALESCE(SUM(ir.Meters), 0) as InboundMeters,
                    
                    COUNT(DISTINCT ol.ID) as OutboundLots,
                    COUNT(orr.ID) as OutboundRolls,
                    COALESCE(SUM(orr.Weight), 0) as OutboundWeight,
                    COALESCE(SUM(orr.Meters), 0) as OutboundMeters,
                    
                    ps.TotalWeight as CurrentStockWeight,
                    ps.TotalMeters as CurrentStockMeters
                    
                FROM OrderDetails od
                INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
                INNER JOIN Companies c ON po.CustomerId = c.ID
                INNER JOIN Products p ON po.ProductId = p.ID
                LEFT JOIN ProductInboundLots il ON il.OrderDetailId = od.ID 
                    AND il.IsValid = 1 
                    AND il.CreateDate BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd}'
                LEFT JOIN ProductInboundRolls ir ON ir.LotId = il.ID AND ir.IsValid = 1
                LEFT JOIN ProductOutboundLots ol ON ol.OrderDetailId = od.ID 
                    AND ol.IsValid = 1 
                    AND ol.CreateDate BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd}'
                LEFT JOIN ProductOutboundRolls orr ON orr.LotId = ol.ID AND orr.IsValid = 1
                LEFT JOIN ProductStocks ps ON ps.OrderDetailId = od.ID AND ps.IsValid = 1
                WHERE od.IsValid = 1 AND po.IsValid = 1
                    {productFilter}
                    {customerFilter}
                GROUP BY od.ID, po.OrderNo, po.CustomerOrderNo, c.CompanyName, p.ProductName, 
                         p.ProductCode, od.Color, od.ColorCode, ps.TotalWeight, ps.TotalMeters
                ORDER BY po.OrderNo, od.Color";
            
            return await DC.Database.SqlQuery<CurrentYearStatistics>(sql).ToListAsync();
        }
        
        /// <summary>
        /// 按PurchaseOrder分类查询数据统计
        /// </summary>
        public async Task<List<PurchaseOrderStatistics>> GetPurchaseOrderStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? customerId = null)
        {
            var currentYear = DateTime.Now.Year;
            startDate ??= new DateTime(currentYear, 1, 1);
            endDate ??= DateTime.Now;
            
            var customerFilter = customerId.HasValue ? $"AND po.CustomerId = '{customerId}'" : "";
            
            var sql = $@"
                SELECT 
                    po.ID as PurchaseOrderId,
                    po.OrderNo,
                    po.CustomerOrderNo,
                    c.CompanyName as CustomerName,
                    p.ProductName,
                    COUNT(DISTINCT od.ID) as OrderDetailCount,
                    COUNT(DISTINCT il.ID) as TotalInboundLots,
                    COUNT(ir.ID) as TotalInboundRolls,
                    COALESCE(SUM(ir.Weight), 0) as TotalInboundWeight,
                    COALESCE(SUM(ir.Meters), 0) as TotalInboundMeters,
                    COUNT(DISTINCT ol.ID) as TotalOutboundLots,
                    COUNT(orr.ID) as TotalOutboundRolls,
                    COALESCE(SUM(orr.Weight), 0) as TotalOutboundWeight,
                    COALESCE(SUM(orr.Meters), 0) as TotalOutboundMeters
                FROM PurchaseOrders po
                INNER JOIN Companies c ON po.CustomerId = c.ID
                INNER JOIN Products p ON po.ProductId = p.ID
                INNER JOIN OrderDetails od ON od.PurchaseOrderId = po.ID
                LEFT JOIN ProductInboundLots il ON il.OrderDetailId = od.ID 
                    AND il.IsValid = 1 
                    AND il.CreateDate BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd}'
                LEFT JOIN ProductInboundRolls ir ON ir.LotId = il.ID AND ir.IsValid = 1
                LEFT JOIN ProductOutboundLots ol ON ol.OrderDetailId = od.ID 
                    AND ol.IsValid = 1 
                    AND ol.CreateDate BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd}'
                LEFT JOIN ProductOutboundRolls orr ON orr.LotId = ol.ID AND orr.IsValid = 1
                WHERE po.IsValid = 1 AND od.IsValid = 1
                    {customerFilter}
                GROUP BY po.ID, po.OrderNo, po.CustomerOrderNo, c.CompanyName, p.ProductName
                ORDER BY po.OrderNo";
            
            return await DC.Database.SqlQuery<PurchaseOrderStatistics>(sql).ToListAsync();
        }
        
        /// <summary>
        /// 单品查询：根据日期或时间段查询指定Product的Lot明细和统计
        /// </summary>
        public async Task<ProductDetailStatistics> GetProductDetailStatisticsAsync(
            Guid productId,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var currentYear = DateTime.Now.Year;
            startDate ??= new DateTime(currentYear, 1, 1);
            endDate ??= DateTime.Now;
            
            var result = new ProductDetailStatistics();
            
            // 获取产品基本信息
            result.Product = await DC.Set<Product>()
                .Where(p => p.ID == productId)
                .FirstOrDefaultAsync();
            
            var dateFilter = $"AND ib.CreateDate BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd}'";
            
            // 获取Lot明细
            var lotDetailsSql = $@"
                SELECT 
                    il.ID as LotId,
                    il.LotNo,
                    od.Color,
                    od.ColorCode,
                    ib.BillNo as InboundBillNo,
                    ib.CreateDate as InboundDate,
                    COUNT(ir.ID) as RollCount,
                    SUM(ir.Weight) as TotalWeight,
                    SUM(ir.Meters) as TotalMeters,
                    SUM(ir.Yards) as TotalYards,
                    il.Location,
                    il.InboundStatus
                FROM ProductInboundLots il
                INNER JOIN ProductInboundBills ib ON il.InboundBillId = ib.ID
                INNER JOIN OrderDetails od ON il.OrderDetailId = od.ID
                INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
                LEFT JOIN ProductInboundRolls ir ON ir.LotId = il.ID AND ir.IsValid = 1
                WHERE po.ProductId = '{productId}' 
                    AND il.IsValid = 1 
                    AND ib.IsValid = 1
                    {dateFilter}
                GROUP BY il.ID, il.LotNo, od.Color, od.ColorCode, ib.BillNo, ib.CreateDate, il.Location, il.InboundStatus
                ORDER BY ib.CreateDate DESC, il.LotNo";
            
            result.LotDetails = await DC.Database.SqlQuery<LotDetailInfo>(lotDetailsSql).ToListAsync();
            
            // 获取汇总统计
            var summarySql = $@"
                SELECT 
                    COUNT(DISTINCT il.ID) as TotalLots,
                    COUNT(ir.ID) as TotalRolls,
                    COALESCE(SUM(ir.Weight), 0) as TotalWeight,
                    COALESCE(SUM(ir.Meters), 0) as TotalMeters,
                    COALESCE(SUM(ir.Yards), 0) as TotalYards
                FROM ProductInboundLots il
                INNER JOIN ProductInboundBills ib ON il.InboundBillId = ib.ID
                INNER JOIN OrderDetails od ON il.OrderDetailId = od.ID
                INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
                LEFT JOIN ProductInboundRolls ir ON ir.LotId = il.ID AND ir.IsValid = 1
                WHERE po.ProductId = '{productId}' 
                    AND il.IsValid = 1 
                    AND ib.IsValid = 1
                    {dateFilter}";
            
            result.Summary = await DC.Database.SqlQuery<ProductSummaryInfo>(summarySql).FirstOrDefaultAsync();
            
            return result;
        }
    }
}
```

### 1.3 数据传输对象定义

```csharp
// 文件：TEX.ViewModel/Finished/InventoryStatisticsDTOs.cs

using System;
using System.Collections.Generic;
using TEX.Model.Models;

namespace TEX.ViewModel.Finished
{
    /// <summary>
    /// 当年统计查询结果
    /// </summary>
    public class CurrentYearStatistics
    {
        public Guid OrderDetailId { get; set; }
        public string OrderNo { get; set; }
        public string CustomerOrderNo { get; set; }
        public string CustomerName { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string Color { get; set; }
        public string ColorCode { get; set; }
        
        public int InboundLots { get; set; }
        public int InboundRolls { get; set; }
        public decimal InboundWeight { get; set; }
        public decimal InboundMeters { get; set; }
        
        public int OutboundLots { get; set; }
        public int OutboundRolls { get; set; }
        public decimal OutboundWeight { get; set; }
        public decimal OutboundMeters { get; set; }
        
        public decimal CurrentStockWeight { get; set; }
        public decimal CurrentStockMeters { get; set; }
    }
    
    /// <summary>
    /// 采购订单统计结果
    /// </summary>
    public class PurchaseOrderStatistics
    {
        public Guid PurchaseOrderId { get; set; }
        public string OrderNo { get; set; }
        public string CustomerOrderNo { get; set; }
        public string CustomerName { get; set; }
        public string ProductName { get; set; }
        public int OrderDetailCount { get; set; }
        
        public int TotalInboundLots { get; set; }
        public int TotalInboundRolls { get; set; }
        public decimal TotalInboundWeight { get; set; }
        public decimal TotalInboundMeters { get; set; }
        
        public int TotalOutboundLots { get; set; }
        public int TotalOutboundRolls { get; set; }
        public decimal TotalOutboundWeight { get; set; }
        public decimal TotalOutboundMeters { get; set; }
    }
    
    /// <summary>
    /// 产品详细统计结果
    /// </summary>
    public class ProductDetailStatistics
    {
        public Product Product { get; set; }
        public List<LotDetailInfo> LotDetails { get; set; } = new();
        public ProductSummaryInfo Summary { get; set; }
    }
    
    /// <summary>
    /// Lot详细信息
    /// </summary>
    public class LotDetailInfo
    {
        public Guid LotId { get; set; }
        public string LotNo { get; set; }
        public string Color { get; set; }
        public string ColorCode { get; set; }
        public string InboundBillNo { get; set; }
        public DateTime InboundDate { get; set; }
        public int RollCount { get; set; }
        public decimal TotalWeight { get; set; }
        public decimal TotalMeters { get; set; }
        public decimal TotalYards { get; set; }
        public string Location { get; set; }
        public InboundStatusEnum InboundStatus { get; set; }
    }
    
    /// <summary>
    /// 产品汇总信息
    /// </summary>
    public class ProductSummaryInfo
    {
        public int TotalLots { get; set; }
        public int TotalRolls { get; set; }
        public decimal TotalWeight { get; set; }
        public decimal TotalMeters { get; set; }
        public decimal TotalYards { get; set; }
    }

    /// <summary>
    /// 混合方案：Lot与Bill信息合并结果
    /// </summary>
    public class LotWithBillInfo
    {
        public Guid LotId { get; set; }
        public string LotNo { get; set; }
        public string Color { get; set; }
        public string Location { get; set; }
        public InboundStatusEnum InboundStatus { get; set; }

        // Bill信息
        public Guid BillId { get; set; }
        public string BillNo { get; set; }
        public DateTime BillDate { get; set; }
        public string Warehouse { get; set; }
        public AuditStatusEnum BillAuditStatus { get; set; }
        public string BillAuditedBy { get; set; }

        // 统计信息
        public int RollCount { get; set; }
        public decimal TotalWeight { get; set; }
        public decimal TotalMeters { get; set; }
        public decimal TotalYards { get; set; }

        // 关联信息
        public string OrderNo { get; set; }
        public string CustomerOrderNo { get; set; }
        public string CustomerName { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string OrderColor { get; set; }
        public string ColorCode { get; set; }
    }

    /// <summary>
    /// 库存移动汇总信息
    /// </summary>
    public class InventoryMovementSummary
    {
        public Guid OrderDetailId { get; set; }
        public string OrderNo { get; set; }
        public string CustomerOrderNo { get; set; }
        public string CustomerName { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string Color { get; set; }
        public string ColorCode { get; set; }

        // 入库统计
        public int InboundLots { get; set; }
        public int InboundRolls { get; set; }
        public decimal InboundWeight { get; set; }
        public decimal InboundMeters { get; set; }
        public decimal InboundYards { get; set; }

        // 出库统计
        public int OutboundLots { get; set; }
        public int OutboundRolls { get; set; }
        public decimal OutboundWeight { get; set; }
        public decimal OutboundMeters { get; set; }
        public decimal OutboundYards { get; set; }

        // 库存统计
        public int StockPcs { get; set; }
        public decimal StockWeight { get; set; }
        public decimal StockMeters { get; set; }
        public decimal StockYards { get; set; }
        public string Wearhouse { get; set; }
        public string Location { get; set; }
    }

    /// <summary>
    /// 年度库存汇总信息
    /// </summary>
    public class YearlyInventorySummary
    {
        public int SummaryYear { get; set; }
        public Guid OrderDetailId { get; set; }
        public Guid ProductId { get; set; }
        public Guid CustomerId { get; set; }

        public int YearInboundLots { get; set; }
        public int YearInboundRolls { get; set; }
        public decimal YearInboundWeight { get; set; }
        public decimal YearInboundMeters { get; set; }
        public decimal YearInboundYards { get; set; }

        public int YearOutboundLots { get; set; }
        public int YearOutboundRolls { get; set; }
        public decimal YearOutboundWeight { get; set; }
        public decimal YearOutboundMeters { get; set; }
        public decimal YearOutboundYards { get; set; }

        public decimal YearEndStockWeight { get; set; }
        public decimal YearEndStockMeters { get; set; }
        public decimal YearEndStockYards { get; set; }
    }

    /// <summary>
    /// 库存查询请求参数
    /// </summary>
    public class InventoryQueryRequest
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? CustomerId { get; set; }
        public InventoryQueryType QueryType { get; set; }
    }

    /// <summary>
    /// 库存查询类型枚举
    /// </summary>
    public enum InventoryQueryType
    {
        LotDetail,      // Lot明细查询
        Summary,        // 汇总统计查询
        YearlySummary   // 年度总结查询
    }
}
```

## 2. 第一阶段验证测试

### 2.1 性能测试脚本

```sql
-- 执行脚本：02_performance_test.sql

-- 测试1：当年查询性能测试
SET @start_time = NOW(6);
SELECT COUNT(*), SUM(Weight), SUM(Meters)
FROM ProductInboundRolls r
INNER JOIN ProductInboundLots l ON r.LotId = l.ID
INNER JOIN OrderDetails od ON l.OrderDetailId = od.ID
WHERE YEAR(r.CreateDate) = YEAR(NOW())
AND r.IsValid = 1 AND l.IsValid = 1;
SET @end_time = NOW(6);
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as ExecutionTimeMs;

-- 测试2：按订单统计查询性能测试
SET @start_time = NOW(6);
SELECT od.ID, COUNT(r.ID), SUM(r.Weight), SUM(r.Meters)
FROM OrderDetails od
LEFT JOIN ProductInboundLots l ON l.OrderDetailId = od.ID AND l.IsValid = 1
LEFT JOIN ProductInboundRolls r ON r.LotId = l.ID AND r.IsValid = 1
WHERE YEAR(COALESCE(r.CreateDate, NOW())) = YEAR(NOW())
GROUP BY od.ID
LIMIT 100;
SET @end_time = NOW(6);
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as ExecutionTimeMs;

-- 测试3：产品明细查询性能测试
SET @start_time = NOW(6);
SELECT l.LotNo, COUNT(r.ID), SUM(r.Weight)
FROM ProductInboundLots l
INNER JOIN ProductInboundRolls r ON r.LotId = l.ID
INNER JOIN OrderDetails od ON l.OrderDetailId = od.ID
INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
WHERE po.ProductId = (SELECT ID FROM Products LIMIT 1)
AND YEAR(l.CreateDate) = YEAR(NOW())
AND l.IsValid = 1 AND r.IsValid = 1
GROUP BY l.ID, l.LotNo;
SET @end_time = NOW(6);
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as ExecutionTimeMs;
```

### 2.2 功能测试用例

```csharp
// 文件：TEX.Test/InventoryStatisticsVMTest.cs

using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading.Tasks;
using TEX.ViewModel.Finished;

[TestClass]
public class OptimizedInventoryStatisticsVMTest : BaseTest
{
    [TestMethod]
    public async Task GetCurrentYearStatistics_ShouldReturnValidData()
    {
        // Arrange
        var vm = new OptimizedInventoryStatisticsVM();
        vm.DC = DC;
        var startDate = new DateTime(DateTime.Now.Year, 1, 1);
        var endDate = DateTime.Now;
        
        // Act
        var result = await vm.GetCurrentYearStatisticsAsync(startDate, endDate);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Count >= 0);
        
        // 验证数据完整性
        foreach (var item in result)
        {
            Assert.IsTrue(item.InboundWeight >= 0);
            Assert.IsTrue(item.InboundMeters >= 0);
            Assert.IsTrue(item.OutboundWeight >= 0);
            Assert.IsTrue(item.OutboundMeters >= 0);
            Assert.IsNotNull(item.OrderNo);
            Assert.IsNotNull(item.ProductName);
        }
    }
    
    [TestMethod]
    public async Task GetPurchaseOrderStatistics_ShouldReturnValidData()
    {
        // Arrange
        var vm = new OptimizedInventoryStatisticsVM();
        vm.DC = DC;
        
        // Act
        var result = await vm.GetPurchaseOrderStatisticsAsync();
        
        // Assert
        Assert.IsNotNull(result);
        
        // 验证汇总数据准确性
        foreach (var item in result)
        {
            Assert.IsTrue(item.TotalInboundRolls >= item.TotalInboundLots);
            Assert.IsTrue(item.TotalOutboundRolls >= item.TotalOutboundLots);
            Assert.IsTrue(item.OrderDetailCount > 0);
        }
    }
    
    [TestMethod]
    public async Task GetProductDetailStatistics_ShouldReturnCompleteData()
    {
        // Arrange
        var vm = new OptimizedInventoryStatisticsVM();
        vm.DC = DC;
        var productId = GetTestProductId(); // 获取测试用的产品ID
        
        // Act
        var result = await vm.GetProductDetailStatisticsAsync(productId);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.IsNotNull(result.Product);
        Assert.IsNotNull(result.LotDetails);
        Assert.IsNotNull(result.Summary);
        
        // 验证明细与汇总的一致性
        var detailTotalRolls = result.LotDetails.Sum(x => x.RollCount);
        Assert.AreEqual(detailTotalRolls, result.Summary.TotalRolls);
        
        var detailTotalWeight = result.LotDetails.Sum(x => x.TotalWeight);
        Assert.AreEqual(detailTotalWeight, result.Summary.TotalWeight);
    }
}
```

## 3. 混合优化方案实施

### 3.1 创建查询优化视图

```sql
-- 执行脚本：03_create_optimization_views.sql

-- 1. 入库Lot与Bill信息合并视图
CREATE VIEW v_InboundLotWithBillInfo AS
SELECT
    l.ID as LotId,
    l.LotNo,
    l.Color,
    l.Location,
    l.InboundStatus,
    l.OrderDetailId,
    l.CreateDate as LotCreateDate,

    -- Bill信息（避免关联查询）
    b.ID as BillId,
    b.BillNo,
    b.CreateDate as BillDate,
    b.POrderId,
    b.FinishingFactoryId,
    b.Warehouse,
    b.AuditStatus as BillAuditStatus,
    b.AuditedBy as BillAuditedBy,
    b.AuditedComment as BillAuditedComment,

    -- 统计信息（实时计算）
    (SELECT COUNT(*) FROM ProductInboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as RollCount,
    (SELECT SUM(r.Weight) FROM ProductInboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as TotalWeight,
    (SELECT SUM(r.Meters) FROM ProductInboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as TotalMeters,
    (SELECT SUM(r.Yards) FROM ProductInboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as TotalYards,

    l.IsValid,
    l.TenantCode,
    l.Remark
FROM ProductInboundLots l
INNER JOIN ProductInboundBills b ON l.InboundBillId = b.ID
WHERE l.IsValid = 1 AND b.IsValid = 1;

-- 2. 出库Lot与Bill信息合并视图
CREATE VIEW v_OutboundLotWithBillInfo AS
SELECT
    l.ID as LotId,
    l.LotNo,
    l.Color,
    l.OrderDetailId,
    l.CreateDate as LotCreateDate,

    -- Bill信息
    b.ID as BillId,
    b.BillNo,
    b.CreateDate as BillDate,
    b.CustomerId,
    b.ReceiverId,
    b.Warehouse,
    b.AuditStatus as BillAuditStatus,
    b.AuditedBy as BillAuditedBy,

    -- 统计信息
    (SELECT COUNT(*) FROM ProductOutboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as RollCount,
    (SELECT SUM(r.Weight) FROM ProductOutboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as TotalWeight,
    (SELECT SUM(r.Meters) FROM ProductOutboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as TotalMeters,
    (SELECT SUM(r.Yards) FROM ProductOutboundRolls r WHERE r.LotId = l.ID AND r.IsValid = 1) as TotalYards,

    l.IsValid,
    l.TenantCode,
    l.Remark
FROM ProductOutboundLots l
INNER JOIN ProductOutboundBills b ON l.OutboundBillId = b.ID
WHERE l.IsValid = 1 AND b.IsValid = 1;

-- 3. 综合库存统计视图
CREATE VIEW v_InventoryMovementSummary AS
SELECT
    od.ID as OrderDetailId,
    po.OrderNo,
    po.CustomerOrderNo,
    c.CompanyName as CustomerName,
    p.ProductName,
    p.ProductCode,
    od.Color,
    od.ColorCode,

    -- 入库统计
    COALESCE(inbound.TotalLots, 0) as InboundLots,
    COALESCE(inbound.TotalRolls, 0) as InboundRolls,
    COALESCE(inbound.TotalWeight, 0) as InboundWeight,
    COALESCE(inbound.TotalMeters, 0) as InboundMeters,
    COALESCE(inbound.TotalYards, 0) as InboundYards,

    -- 出库统计
    COALESCE(outbound.TotalLots, 0) as OutboundLots,
    COALESCE(outbound.TotalRolls, 0) as OutboundRolls,
    COALESCE(outbound.TotalWeight, 0) as OutboundWeight,
    COALESCE(outbound.TotalMeters, 0) as OutboundMeters,
    COALESCE(outbound.TotalYards, 0) as OutboundYards,

    -- 库存统计
    ps.TotalPcs as StockPcs,
    ps.TotalWeight as StockWeight,
    ps.TotalMeters as StockMeters,
    ps.TotalYards as StockYards,
    ps.Wearhouse,
    ps.Location

FROM OrderDetails od
INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
INNER JOIN Companies c ON po.CustomerId = c.ID
INNER JOIN Products p ON po.ProductId = p.ID
LEFT JOIN ProductStocks ps ON ps.OrderDetailId = od.ID AND ps.IsValid = 1
LEFT JOIN (
    SELECT
        OrderDetailId,
        COUNT(DISTINCT LotId) as TotalLots,
        SUM(RollCount) as TotalRolls,
        SUM(TotalWeight) as TotalWeight,
        SUM(TotalMeters) as TotalMeters,
        SUM(TotalYards) as TotalYards
    FROM v_InboundLotWithBillInfo
    GROUP BY OrderDetailId
) inbound ON inbound.OrderDetailId = od.ID
LEFT JOIN (
    SELECT
        OrderDetailId,
        COUNT(DISTINCT LotId) as TotalLots,
        SUM(RollCount) as TotalRolls,
        SUM(TotalWeight) as TotalWeight,
        SUM(TotalMeters) as TotalMeters,
        SUM(TotalYards) as TotalYards
    FROM v_OutboundLotWithBillInfo
    GROUP BY OrderDetailId
) outbound ON outbound.OrderDetailId = od.ID
WHERE od.IsValid = 1 AND po.IsValid = 1;
```

### 3.2 基于视图的优化查询ViewModel

```csharp
// 文件：TEX.ViewModel/Finished/HybridInventoryStatisticsVM.cs

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;
using TEX.Model.Models;

namespace TEX.ViewModel.Finished
{
    /// <summary>
    /// 混合优化方案的库存统计查询ViewModel
    /// 保留三级结构的完整性，通过视图获得两级结构的查询性能
    /// </summary>
    public class HybridInventoryStatisticsVM : BaseVM
    {
        /// <summary>
        /// 使用视图进行快速Lot查询，获得接近两级结构的性能优势
        /// </summary>
        public async Task<List<LotWithBillInfo>> GetOptimizedLotStatisticsAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? productId = null,
            Guid? customerId = null)
        {
            var currentYear = DateTime.Now.Year;
            startDate ??= new DateTime(currentYear, 1, 1);
            endDate ??= DateTime.Now;

            var productFilter = productId.HasValue ? $"AND po.ProductId = '{productId}'" : "";
            var customerFilter = customerId.HasValue ? $"AND po.CustomerId = '{customerId}'" : "";

            // 直接查询视图，获得接近两级结构的查询性能
            var sql = $@"
                SELECT
                    v.LotId,
                    v.LotNo,
                    v.Color,
                    v.Location,
                    v.InboundStatus,
                    v.BillId,
                    v.BillNo,
                    v.BillDate,
                    v.Warehouse,
                    v.BillAuditStatus,
                    v.BillAuditedBy,
                    v.RollCount,
                    v.TotalWeight,
                    v.TotalMeters,
                    v.TotalYards,
                    po.OrderNo,
                    po.CustomerOrderNo,
                    c.CompanyName as CustomerName,
                    p.ProductName,
                    p.ProductCode,
                    od.Color as OrderColor,
                    od.ColorCode
                FROM v_InboundLotWithBillInfo v
                INNER JOIN OrderDetails od ON v.OrderDetailId = od.ID
                INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
                INNER JOIN Companies c ON po.CustomerId = c.ID
                INNER JOIN Products p ON po.ProductId = p.ID
                WHERE v.BillDate BETWEEN '{startDate:yyyy-MM-dd}' AND '{endDate:yyyy-MM-dd}'
                    {productFilter}
                    {customerFilter}
                ORDER BY v.BillDate DESC, v.LotNo";

            return await DC.Database.SqlQuery<LotWithBillInfo>(sql).ToListAsync();
        }

        /// <summary>
        /// 使用综合统计视图进行快速汇总查询
        /// </summary>
        public async Task<List<InventoryMovementSummary>> GetInventoryMovementSummaryAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            Guid? productId = null)
        {
            var currentYear = DateTime.Now.Year;
            startDate ??= new DateTime(currentYear, 1, 1);
            endDate ??= DateTime.Now;

            var productFilter = productId.HasValue ? $"AND ProductId = '{productId}'" : "";

            // 使用综合统计视图，一次查询获得完整的出入库统计
            var sql = $@"
                SELECT *
                FROM v_InventoryMovementSummary
                WHERE 1=1
                    {productFilter}
                ORDER BY OrderNo, Color";

            return await DC.Database.SqlQuery<InventoryMovementSummary>(sql).ToListAsync();
        }

        /// <summary>
        /// 智能查询路由：根据查询需求自动选择最优方案
        /// </summary>
        public async Task<object> GetSmartInventoryReportAsync(InventoryQueryRequest request)
        {
            // 根据查询类型选择最优的查询策略
            switch (request.QueryType)
            {
                case InventoryQueryType.LotDetail:
                    // 需要Lot明细时，使用视图查询
                    return await GetOptimizedLotStatisticsAsync(
                        request.StartDate, request.EndDate,
                        request.ProductId, request.CustomerId);

                case InventoryQueryType.Summary:
                    // 需要汇总统计时，使用综合统计视图
                    return await GetInventoryMovementSummaryAsync(
                        request.StartDate, request.EndDate, request.ProductId);

                case InventoryQueryType.YearlySummary:
                    // 年度总结查询，使用预聚合表（第二阶段实施）
                    return await GetYearlySummaryAsync(request.StartDate.Year, request.EndDate.Year);

                default:
                    // 默认使用Lot明细查询
                    return await GetOptimizedLotStatisticsAsync(
                        request.StartDate, request.EndDate,
                        request.ProductId, request.CustomerId);
            }
        }

        /// <summary>
        /// 年度汇总查询（为第二阶段预留）
        /// </summary>
        private async Task<List<YearlyInventorySummary>> GetYearlySummaryAsync(int startYear, int endYear)
        {
            // 第二阶段实施时，这里将使用预聚合的年度汇总表
            // 目前使用视图进行年度汇总查询
            var sql = $@"
                SELECT
                    YEAR(v.BillDate) as SummaryYear,
                    v.OrderDetailId,
                    po.ProductId,
                    po.CustomerId,
                    COUNT(DISTINCT v.LotId) as YearInboundLots,
                    SUM(v.RollCount) as YearInboundRolls,
                    SUM(v.TotalWeight) as YearInboundWeight,
                    SUM(v.TotalMeters) as YearInboundMeters,
                    SUM(v.TotalYards) as YearInboundYards
                FROM v_InboundLotWithBillInfo v
                INNER JOIN OrderDetails od ON v.OrderDetailId = od.ID
                INNER JOIN PurchaseOrders po ON od.PurchaseOrderId = po.ID
                WHERE YEAR(v.BillDate) BETWEEN {startYear} AND {endYear}
                GROUP BY YEAR(v.BillDate), v.OrderDetailId, po.ProductId, po.CustomerId
                ORDER BY SummaryYear, po.ProductId";

            return await DC.Database.SqlQuery<YearlyInventorySummary>(sql).ToListAsync();
        }
    }
}
```

## 4. 第一阶段部署检查清单

### 4.1 部署前检查

- [ ] 数据库备份已完成
- [ ] 索引创建脚本已验证
- [ ] 视图创建脚本已验证
- [ ] 性能测试脚本已准备
- [ ] 回滚方案已制定

### 3.2 部署步骤

1. **创建索引**（预计耗时：10-30分钟）
   ```bash
   mysql -u username -p database_name < 01_create_optimized_indexes.sql
   ```

2. **验证索引**
   ```sql
   SHOW INDEX FROM ProductInboundLots;
   EXPLAIN SELECT * FROM ProductInboundLots WHERE CreateDate >= '2024-01-01';
   ```

3. **部署代码**
   - 部署OptimizedInventoryStatisticsVM.cs
   - 部署InventoryStatisticsDTOs.cs
   - 更新相关Controller引用

4. **执行性能测试**
   ```bash
   mysql -u username -p database_name < 02_performance_test.sql
   ```

### 3.3 部署后验证

- [ ] 所有索引创建成功
- [ ] 查询执行计划使用了新索引
- [ ] 性能测试结果符合预期（提升80%以上）
- [ ] 功能测试全部通过
- [ ] 生产环境监控正常

### 3.4 回滚方案

如果出现问题，执行以下回滚步骤：

```sql
-- 删除新创建的索引
DROP INDEX idx_inbound_lot_current_year ON ProductInboundLots;
DROP INDEX idx_outbound_lot_current_year ON ProductOutboundLots;
DROP INDEX idx_inbound_roll_current_year ON ProductInboundRolls;
DROP INDEX idx_outbound_roll_current_year ON ProductOutboundRolls;
DROP INDEX idx_orderdetail_purchase_product ON OrderDetails;
DROP INDEX idx_purchase_customer_product ON PurchaseOrders;
DROP INDEX idx_stock_orderdetail ON ProductStocks;
DROP INDEX idx_inbound_lot_orderdetail_date ON ProductInboundLots;
DROP INDEX idx_outbound_lot_orderdetail_date ON ProductOutboundLots;
```

---

**文档版本：** v1.0  
**创建日期：** 2024-07-24  
**适用范围：** TEX项目出入库系统第一阶段优化  
**维护责任：** 开发团队
