# TEX项目 ProductLot 实现指南

## 概述

本文档详细说明了TEX项目中ProductInboundLot（入库批次）和ProductOutboundLot（出库批次）模块的Add/Edit/BatchDelete功能实现。这些功能实现了完整的两级联动（Lot-Roll）业务逻辑，包括库存管理和Bill统计更新。

## 项目架构

### 实体关系
```
Bill (入库单/出库单)
├── Lot (批次)
    ├── Roll (卷)
    ├── Roll (卷)
    └── ...
```

### 核心实体
- **ProductInboundBill/ProductOutboundBill**: 入库单/出库单主表
- **ProductInboundLot/ProductOutboundLot**: 入库/出库批次表
- **ProductInboundRoll/ProductOutboundRoll**: 入库/出库卷表
- **ProductStock**: 库存统计表

## 功能实现详情

### 1. ProductInboundLotVM 入库批次管理

#### 1.1 DoAdd方法 - 新增入库批次
**文件位置**: `TEX.ViewModel/Finished/ProductInboundLotVMs/ProductInboundLotVM.cs`

**核心功能**:
- **两级联动**: Roll数据自动汇总到Lot（Pcs、Weight、Meters、Yards）
- **库存增加**: 根据入库数据增加ProductStock库存
- **Bill统计更新**: 更新入库单的统计字段
- **事务处理**: 确保数据一致性

**实现逻辑**:
```csharp
// 1. Roll汇总到Lot
Entity.Pcs = Entity.RollList?.Count ?? 0;
Entity.Meters = Entity.RollList?.Sum(x => x.Meters) ?? 0;
Entity.Weight = Entity.RollList?.Sum(x => x.Weight) ?? 0;
Entity.Yards = Entity.RollList?.Sum(x => x.Yards) ?? 0;

// 2. 库存增加
existingStock.TotalPcs += Entity.Pcs;
existingStock.TotalWeight += Entity.Weight;
existingStock.TotalMeters += Entity.Meters;
existingStock.TotalYards += Entity.Yards;

// 3. Bill统计更新
bill.Pcs = bill.LotList.Sum(x => x.Pcs);
bill.Meters = bill.LotList.Sum(x => x.Meters);
bill.Weight = bill.LotList.Sum(x => x.Weight);
bill.Yards = bill.LotList.Sum(x => x.Yards);
```

#### 1.2 DoEdit方法 - 修改入库批次
**核心功能**:
- **两级联动修改**: 支持Roll的增删改，自动重新计算Lot统计
- **库存差异更新**: 计算修改前后的差异，更新库存
- **Bill统计重新计算**: 基于最新的Lot数据重新计算Bill统计
- **实体跟踪处理**: 兼容内存数据库测试环境

**实现逻辑**:
```csharp
// 1. 获取原始数据
var originalLot = DC.Set<ProductInboundLot>()
    .Include(x => x.RollList)
    .AsNoTracking()
    .FirstOrDefault(x => x.ID == Entity.ID);

// 2. 计算库存差异
var stockDifference = new StockInfo
{
    TotalPcs = Entity.Pcs - (originalLot?.Pcs ?? 0),
    TotalWeight = Entity.Weight - (originalLot?.Weight ?? 0),
    TotalMeters = Entity.Meters - (originalLot?.Meters ?? 0),
    TotalYards = Entity.Yards - (originalLot?.Yards ?? 0)
};

// 3. 更新库存
existingStock.TotalPcs += stockDifference.TotalPcs;
existingStock.TotalWeight += stockDifference.TotalWeight;
existingStock.TotalMeters += stockDifference.TotalMeters;
existingStock.TotalYards += stockDifference.TotalYards;
```

### 2. ProductOutboundLotVM 出库批次管理

#### 2.1 DoAdd方法 - 新增出库批次
**文件位置**: `TEX.ViewModel/Finished/ProductOutboundLotVMs/ProductOutboundLotVM.cs`

**核心功能**:
- **两级联动**: Roll数据自动汇总到Lot
- **库存扣减**: 根据出库数据减少ProductStock库存
- **Bill统计更新**: 更新出库单的统计字段
- **负库存支持**: 允许库存为负数（出库大于现有库存）

**实现逻辑**:
```csharp
// 库存扣减（与入库相反）
existingStock.TotalPcs -= Entity.Pcs;
existingStock.TotalWeight -= Entity.Weight;
existingStock.TotalMeters -= Entity.Meters;
existingStock.TotalYards -= Entity.Yards;
```

#### 2.2 DoEdit方法 - 修改出库批次
**核心功能**:
- **两级联动修改**: 支持Roll的增删改
- **库存差异更新**: 出库增加时进一步扣减库存，出库减少时恢复库存
- **Bill统计重新计算**: 基于最新的Lot数据重新计算Bill统计

### 3. BatchDelete批量删除功能

#### 3.1 ProductInboundLotBatchVM.DoBatchDelete
**文件位置**: `TEX.ViewModel/Finished/ProductInboundLotVMs/ProductInboundLotBatchVM.cs`

**核心功能**:
- **库存减少**: 入库删除时减少库存（取消入库）
- **关联删除**: 软删除Lot及其关联的Roll
- **Bill统计更新**: 重新计算受影响Bill的统计字段
- **批量操作**: 使用ExecuteUpdateAsync进行高效批量更新

**实现逻辑**:
```csharp
// 1. 库存减少
existingStock.TotalPcs -= stockReduce.TotalPcs;
existingStock.TotalWeight -= stockReduce.TotalWeight;
existingStock.TotalMeters -= stockReduce.TotalMeters;
existingStock.TotalYards -= stockReduce.TotalYards;

// 2. 批量软删除Roll
DC.Set<ProductInboundRoll>()
   .Where(b => rollIds.Contains(b.ID))
   .ExecuteUpdateAsync(setters => setters
       .SetProperty(r => r.IsValid, false)
       .SetProperty(r => r.UpdateTime, DateTime.Now)
       .SetProperty(r => r.UpdateBy, userCode));

// 3. 批量软删除Lot
DC.Set<ProductInboundLot>()
   .Where(l => lotIds.Contains(l.ID))
   .ExecuteUpdateAsync(setters => setters
       .SetProperty(l => l.IsValid, false)
       .SetProperty(l => l.UpdateTime, DateTime.Now)
       .SetProperty(l => l.UpdateBy, userCode));
```

#### 3.2 ProductOutboundLotBatchVM.DoBatchDelete
**文件位置**: `TEX.ViewModel/Finished/ProductOutboundLotVMs/ProductOutboundLotBatchVM.cs`

**核心功能**:
- **库存恢复**: 出库删除时增加库存（取消出库）
- **关联删除**: 软删除Lot及其关联的Roll
- **Bill统计更新**: 重新计算受影响Bill的统计字段

**实现逻辑**:
```csharp
// 库存恢复（与入库删除相反）
existingStock.TotalPcs += stockRestore.TotalPcs;
existingStock.TotalWeight += stockRestore.TotalWeight;
existingStock.TotalMeters += stockRestore.TotalMeters;
existingStock.TotalYards += stockRestore.TotalYards;
```

## 库存管理逻辑

### 入库业务
- **新增入库**: 库存 += 入库数量
- **修改入库**: 库存 += (新数量 - 原数量)
- **删除入库**: 库存 -= 原数量

### 出库业务
- **新增出库**: 库存 -= 出库数量
- **修改出库**: 库存 -= (新数量 - 原数量)
- **删除出库**: 库存 += 原数量

## 事务处理

### 内存数据库兼容性
```csharp
// 检测数据库类型，内存数据库跳过事务
var isInMemoryDatabase = base.DC.Database.ProviderName?.Contains("InMemory") == true;
var transaction = isInMemoryDatabase ? null : base.DC.BeginTransaction();

try
{
    // 业务逻辑
    if (transaction != null)
    {
        transaction.Commit();
    }
}
catch (Exception)
{
    if (transaction != null)
    {
        transaction.Rollback();
    }
    throw;
}
```

### 错误处理
- **事务回滚**: 异常时自动回滚所有操作
- **数据一致性**: 确保库存、统计字段的一致性
- **审计追踪**: 记录操作人和操作时间

## 测试覆盖

### 单元测试
- **DoAdd测试**: 验证两级联动、库存更新、Bill统计
- **DoEdit测试**: 验证修改逻辑、库存差异、数据一致性
- **BatchDelete测试**: 验证批量删除、库存恢复、关联删除
- **边界情况测试**: 空数据、null数据、负库存等场景

### 测试文件位置
- `TEX.Test/ProductInboundLotApiTest.cs`
- `TEX.Test/ProductOutboundLotApiTest.cs`

## 性能优化

### 批量操作
- 使用`ExecuteUpdateAsync`进行批量更新
- 使用`AddRange`进行批量插入
- 避免N+1查询问题

### 数据库查询优化
- 合理使用`Include`预加载关联数据
- 使用`AsNoTracking`避免不必要的实体跟踪
- 使用`Dictionary`优化数据查找

## 注意事项

### 1. 内存数据库限制
- `ExecuteUpdateAsync`在内存数据库中不被支持
- 测试环境需要特殊处理事务操作
- 实际生产环境使用SQL Server数据库，功能完全正常

### 2. 数据一致性
- 所有操作都在事务中执行
- 库存更新和统计字段更新保持同步
- 软删除保持数据完整性和审计追踪

### 3. 扩展性
- 代码结构支持后续功能扩展
- 遵循TEX项目的编码规范和架构模式
- 支持多租户和多数据库环境

## 总结

ProductInboundLot和ProductOutboundLot模块实现了完整的两级联动业务逻辑，包括：

1. **数据联动**: Lot-Roll两级数据的自动汇总和同步
2. **库存管理**: 精确的库存增减和差异计算
3. **统计更新**: Bill统计字段的实时更新
4. **批量操作**: 高效的批量删除和更新
5. **事务保证**: 完整的事务处理和错误恢复
6. **测试覆盖**: 全面的单元测试和边界情况测试

这些实现为TEX项目的纺织面料管理提供了稳定、高效的技术支撑。

## 附录

### A. 关键代码片段

#### A.1 StockInfo类定义
```csharp
public class StockInfo
{
    public Guid OrderDetailId { get; set; }
    public int TotalPcs { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalMeters { get; set; }
    public decimal TotalYards { get; set; }
}
```

#### A.2 两级联动汇总逻辑
```csharp
// Roll数据汇总到Lot
private void CalculateLotSummary(ProductInboundLot lot)
{
    if (lot.RollList != null && lot.RollList.Any())
    {
        lot.Pcs = lot.RollList.Count;
        lot.Meters = lot.RollList.Sum(x => x.Meters);
        lot.Weight = lot.RollList.Sum(x => x.Weight);
        lot.Yards = lot.RollList.Sum(x => x.Yards);
    }
    else
    {
        lot.Pcs = 0;
        lot.Meters = 0;
        lot.Weight = 0;
        lot.Yards = 0;
    }
}
```

#### A.3 库存更新通用方法
```csharp
private void UpdateStock(Guid orderDetailId, StockInfo stockChange, bool isInbound)
{
    var existingStock = DC.Set<ProductStock>()
        .FirstOrDefault(s => s.OrderDetailId == orderDetailId);

    if (existingStock != null)
    {
        // 入库增加库存，出库减少库存
        var multiplier = isInbound ? 1 : -1;
        existingStock.TotalPcs += stockChange.TotalPcs * multiplier;
        existingStock.TotalWeight += stockChange.TotalWeight * multiplier;
        existingStock.TotalMeters += stockChange.TotalMeters * multiplier;
        existingStock.TotalYards += stockChange.TotalYards * multiplier;
        existingStock.UpdateTime = DateTime.Now;
        existingStock.UpdateBy = LoginUserInfo?.ITCode;
    }
    else
    {
        // 创建新库存记录
        var newStock = new ProductStock
        {
            OrderDetailId = orderDetailId,
            TotalPcs = stockChange.TotalPcs * (isInbound ? 1 : -1),
            TotalWeight = stockChange.TotalWeight * (isInbound ? 1 : -1),
            TotalMeters = stockChange.TotalMeters * (isInbound ? 1 : -1),
            TotalYards = stockChange.TotalYards * (isInbound ? 1 : -1),
            CreateTime = DateTime.Now,
            CreateBy = LoginUserInfo?.ITCode,
            TenantCode = LoginUserInfo?.CurrentTenant
        };
        DC.Set<ProductStock>().Add(newStock);
    }
}
```

### B. 数据流程图

#### B.1 入库流程
```
用户输入Lot数据 + Roll列表
    ↓
Roll数据汇总到Lot (Pcs, Weight, Meters, Yards)
    ↓
保存Lot和Roll到数据库
    ↓
更新ProductStock (增加库存)
    ↓
更新ProductInboundBill统计字段
    ↓
提交事务
```

#### B.2 出库流程
```
用户输入Lot数据 + Roll列表
    ↓
Roll数据汇总到Lot (Pcs, Weight, Meters, Yards)
    ↓
保存Lot和Roll到数据库
    ↓
更新ProductStock (减少库存)
    ↓
更新ProductOutboundBill统计字段
    ↓
提交事务
```

#### B.3 批量删除流程
```
获取要删除的Lot列表 (包含Roll)
    ↓
按OrderDetailId分组计算库存变化
    ↓
更新ProductStock (入库删除减库存，出库删除加库存)
    ↓
批量软删除Roll (ExecuteUpdateAsync)
    ↓
批量软删除Lot (ExecuteUpdateAsync)
    ↓
重新计算受影响Bill的统计字段
    ↓
提交事务
```

### C. 错误处理和调试

#### C.1 常见错误
1. **DbUpdateConcurrencyException**: 实体跟踪冲突
   - 解决方案: 使用AsNoTracking()或ChangeTracker.Clear()

2. **ExecuteUpdateAsync不支持**: 内存数据库限制
   - 解决方案: 检测数据库类型，使用传统更新方式

3. **库存负数**: 出库大于现有库存
   - 解决方案: 系统允许负库存，业务层面控制

#### C.2 调试技巧
```csharp
// 启用EF Core日志
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    optionsBuilder.LogTo(Console.WriteLine, LogLevel.Information);
}

// 检查实体状态
var entry = DC.Entry(entity);
Console.WriteLine($"Entity State: {entry.State}");

// 检查变更跟踪
var changes = DC.ChangeTracker.Entries()
    .Where(e => e.State != EntityState.Unchanged)
    .ToList();
```

### D. 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2025-07-23 | 初始版本，完成基本功能实现 |
| 1.1 | 2025-07-23 | 添加BatchDelete功能 |
| 1.2 | 2025-07-23 | 完善测试覆盖和文档 |

### E. 相关文档

- [ProductInboundBill实现指南](./ProductInboundBill_Implementation_Guide.md)
- [ProductOutboundBill实现总结](./ProductOutboundBill_Implementation_Summary.md)
- [TEX项目架构文档](./README.md)

---

**文档维护**: TEX开发团队
**最后更新**: 2025年7月23日
**版本**: 1.2
