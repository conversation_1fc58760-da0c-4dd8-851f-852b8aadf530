# ProductInboundBill 入库单三级联动实现指南

## 文档概述

本文档详细分析了TEX项目中ProductInboundBill入库单的AddWithLotAndRoll和EditWithLotAndRoll方法的实现逻辑，为后续出库操作的开发提供技术指导和最佳实践参考。

## 目录

- [1. 系统架构概述](#1-系统架构概述)
- [2. 数据模型设计](#2-数据模型设计)
- [3. 数据验证机制](#3-数据验证机制)
- [4. AddWithLotAndRoll实现分析](#4-addwithlotandroll实现分析)
- [5. EditWithLotAndRoll实现分析](#5-editwithlotandroll实现分析)
- [6. 库存管理机制](#6-库存管理机制)
- [7. 事务处理策略](#7-事务处理策略)
- [8. 测试验证方法](#8-测试验证方法)
- [9. 出库操作设计指导](#9-出库操作设计指导)
- [10. 最佳实践总结](#10-最佳实践总结)

## 1. 系统架构概述

### 1.1 三级实体关系

TEX项目采用三级联动的数据结构来管理入库单：

```
ProductInboundBill (入库单)
├── ProductInboundLot (批次)
    ├── ProductInboundRoll (卷)
```

### 1.2 核心组件

- **Controller层**: `ProductInboundBillController` - 处理API请求和业务逻辑
- **Model层**: 实体模型定义和数据验证
- **DataAccess层**: EF Core数据访问和事务管理
- **Test层**: 单元测试和集成测试

### 1.3 技术栈

- **.NET 8**: 主要开发框架
- **WTM Framework**: 基于WalkingTec.Mvvm的快速开发框架
- **Entity Framework Core 8**: ORM数据访问
- **MySQL**: 主数据库
- **内存数据库**: 测试环境

## 2. 数据模型设计

### 2.1 核心实体结构

#### ProductInboundBill (入库单)
```csharp
public class ProductInboundBill : BasePoco, IPersistPoco, ITenant
{
    public string BillNo { get; set; }              // 单据编号
    public DateTime CreateDate { get; set; }        // 创建日期
    public Guid POrderId { get; set; }              // 采购订单ID
    public Guid FinishingFactoryId { get; set; }    // 后整厂ID
    public string Warehouse { get; set; }           // 仓库
    public int Pcs { get; set; }                    // 总件数
    public decimal Weight { get; set; }             // 总重量
    public decimal Meters { get; set; }             // 总米数
    public decimal Yards { get; set; }              // 总码数
    public AuditStatusEnum AuditStatus { get; set; } // 审核状态
    public List<ProductInboundLot> LotList { get; set; } // 批次列表
}
```

#### ProductInboundLot (批次)
```csharp
public class ProductInboundLot : BasePoco, IPersistPoco, ITenant
{
    public Guid InboundBillId { get; set; }         // 入库单ID
    public Guid OrderDetailId { get; set; }        // 订单明细ID
    public string LotNo { get; set; }              // 批次号
    public string Color { get; set; }              // 颜色
    public string ColorCode { get; set; }          // 颜色代码
    public int Pcs { get; set; }                   // 件数
    public decimal Weight { get; set; }            // 重量
    public decimal Meters { get; set; }            // 米数
    public decimal Yards { get; set; }             // 码数
    public string Location { get; set; }           // 库位
    public InboundStatusEnum InboundStatus { get; set; } // 入库状态
    public List<ProductInboundRoll> RollList { get; set; } // 卷列表
}
```

#### ProductInboundRoll (卷)
```csharp
public class ProductInboundRoll : BasePoco, IPersistPoco, ITenant
{
    public Guid LotId { get; set; }                // 批次ID
    public string LotNo { get; set; }              // 批次号
    public int RollNo { get; set; }                // 卷号
    public decimal Weight { get; set; }            // 重量
    public decimal Meters { get; set; }            // 米数
    public decimal Yards { get; set; }             // 码数
    public string Grade { get; set; }              // 等级
    public InboundStatusEnum InboundStatus { get; set; } // 入库状态
}
```

### 2.2 库存管理实体

#### ProductStock (库存表)
```csharp
public class ProductStock : BasePoco, IPersistPoco, ITenant
{
    public Guid OrderDetailId { get; set; }        // 订单明细ID
    public int TotalPcs { get; set; }              // 总件数
    public decimal TotalWeight { get; set; }       // 总重量
    public decimal TotalMeters { get; set; }       // 总米数
    public decimal TotalYards { get; set; }        // 总码数
    public string Wearhouse { get; set; }          // 仓库
    public string Location { get; set; }           // 库位
}
```

#### StockInfo (库存信息辅助类)
```csharp
public class StockInfo
{
    public Guid OrderDetailId { get; set; }
    public int TotalPcs { get; set; }
    public decimal TotalMeters { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalYards { get; set; }
}
```

## 3. 数据验证机制

### 3.1 验证规则概述

TEX项目的ProductInboundBill入库单采用严格的数据验证机制，确保数据完整性和业务规则的正确执行。验证分为两个层次：

1. **内部重复性验证**：检查传入数据内部的重复性
2. **数据库唯一性验证**：检查数据库中的全局唯一性约束

### 3.2 核心验证规则

#### 3.2.1 ProductInboundLot表验证规则
- **InboundBillId + LotNo + OrderDetailId** 组合全局唯一 (无必要,已取消)
- 同一入库单内不能有重复的LotNo+OrderDetailId组合

#### 3.2.2 ProductInboundRoll表验证规则
- **LotNo + OrderDetailId + RollNo** 组合全局唯一
- LotNo+OrderDetailId需要从Lot分组中获取，确保Roll归属正确

### 3.3 ValidateLotListAsync方法实现

#### 3.3.1 方法签名
```csharp
/// <summary>
/// 验证LotList中的数据是否符合业务规则
/// 1. ProductInboundLot表中InboundBillId+LotNo+OrderDetailId组合全局唯一
/// 2. ProductInboundRoll表中LotNo+OrderDetailId+RollNo组合全局唯一,LotNo+OrderDetailId需要从Lot分组
/// 3. 允许向已存在的Lot中添加新的Roll（只要Roll不重复）
/// </summary>
/// <param name="bill">入库单</param>
private async Task ValidateLotListAsync(ProductInboundBill bill)
```

#### 3.3.2 内部重复性检查（性能优化）
```csharp
// 1. 检查传入数据内部的重复性
// 每个Lot的LotNo和OrderDetailId组合必须唯一 (无必要,已取消)
var lotNoOrderDetailIdGroup = lotList.GroupBy(x => new { x.LotNo, x.OrderDetailId });
var duplicateGroups = lotNoOrderDetailIdGroup.Where(g => g.Count() > 1).ToList();

if (duplicateGroups.Any())
{
    // 批量查询OrderDetail信息，避免N+1查询问题
    var duplicateOrderDetailIds = duplicateGroups.Select(g => g.Key.OrderDetailId).Distinct().ToList();
    var duplicateOrderDetails = await DC.Set<OrderDetail>()
        .Where(x => duplicateOrderDetailIds.Contains(x.ID))
        .Select(x => new { x.ID, x.Color })
        .ToListAsync();
    var duplicateOrderDetailDict = duplicateOrderDetails.ToDictionary(x => x.ID, x => x.Color);

    foreach (var group in duplicateGroups)
    {
        var color = duplicateOrderDetailDict.GetValueOrDefault(group.Key.OrderDetailId, "未知");
        ModelState.AddModelError("缸号重复", $"颜色:{color}, 缸号:{group.Key.LotNo}");
    }
}

// 检查每个Lot内部的Roll的RollNo重复(只需要验证RollNo)
foreach (var lot in lotList)
{
    if (lot.RollList != null && lot.RollList.Any())
    {
        var rollNoGroup = lot.RollList.GroupBy(x => x.RollNo);
        foreach (var group in rollNoGroup)
        {
            if (group.Count() > 1)
            {
                ModelState.AddModelError("卷号重复", $"缸号: {lot.LotNo} 卷号: {group.Key} 重复");
            }
        }
    }
}

//更新版本:可能存在相同缸号的记录,所以按缸号分组查询
// 提取所有相关的LotNo和OrderDetailId
                var lotNos = rollsToValidate.Select(x => x.LotNo).Distinct().ToList();
                var orderDetailIds = rollsToValidate.Select(x => x.OrderDetailId).Distinct().ToList();

                // 批量查询数据库中所有相关的Roll记录（避免复杂的关联查询）
                var existingRolls = await (from roll in dc.ProductInboundRolls
                                          join lot in dc.ProductInboundLots on roll.LotId equals lot.ID
                                          where lotNos.Contains(lot.LotNo) &&
                                                orderDetailIds.Contains(lot.OrderDetailId) &&
                                                roll.IsValid == true &&
                                                roll.TenantCode == tenantCode
                                          select new {
                                              roll.ID,
                                              lot.LotNo,
                                              lot.OrderDetailId,
                                              roll.RollNo
                                          }).ToListAsync();

                // 在内存中进行验证
                foreach (var rollToValidate in rollsToValidate)
                {
                    var conflictingRoll = existingRolls.FirstOrDefault(x =>
                        x.LotNo == rollToValidate.LotNo &&
                        x.OrderDetailId == rollToValidate.OrderDetailId &&
                        x.RollNo == rollToValidate.RollNo &&
                        x.ID != rollToValidate.RollId); // 排除当前正在编辑的Roll

                    if (conflictingRoll != null)
                    {
                        ModelState.AddModelError("卷号重复", $"缸号 {rollToValidate.LotNo} 中卷号 {rollToValidate.RollNo} 已存在，请检查数据");
                    }
                }
```

#### 3.3.3 数据库唯一性检查（批量优化）

**Lot唯一性验证**：(无必要已取消)
   - ProductInboundLot表不需要验证,应该允许同一个入库单中出现同一个LotNo+OrderDetailId组合,方便分开标记同一缸中可能需要分开的情况

```csharp
// 2.1 检查ProductInboundLot表中InboundBillId+LotNo+OrderDetailId组合全局唯一（批量优化）
if (lotList.Any())
{
    // 收集所有需要验证的Lot信息
    var lotsToValidate = lotList.Select(x => new {
        x.ID,
        x.LotNo,
        x.OrderDetailId
    }).ToList();

    // 批量查询数据库中当前入库单的所有Lot记录
    var existingLots = await dc.ProductInboundLots
        .Where(x => x.InboundBillId == bill.ID &&
                   x.IsValid == true &&
                   x.TenantCode == tenantCode)
        .Select(x => new {
            x.ID,
            x.LotNo,
            x.OrderDetailId
        })
        .ToListAsync();

    // 批量查询OrderDetail信息用于错误提示
    var orderDetailIds = lotsToValidate.Select(x => x.OrderDetailId).Distinct().ToList();
    var orderDetails = await DC.Set<OrderDetail>()
        .Where(x => orderDetailIds.Contains(x.ID))
        .Select(x => new { x.ID, x.Color })
        .ToListAsync();
    var orderDetailDict = orderDetails.ToDictionary(x => x.ID, x => x.Color);

    // 在内存中进行验证
    foreach (var lotToValidate in lotsToValidate)
    {
        var conflictingLot = existingLots.FirstOrDefault(x =>
            x.LotNo == lotToValidate.LotNo &&
            x.OrderDetailId == lotToValidate.OrderDetailId &&
            x.ID != lotToValidate.ID); // 排除当前正在编辑的Lot

        if (conflictingLot != null)
        {
            var color = orderDetailDict.GetValueOrDefault(lotToValidate.OrderDetailId, "未知");
            ModelState.AddModelError("缸号重复", $"颜色:{color}, 缸号:{lotToValidate.LotNo} 在当前入库单中已存在");
        }
    }
}
```

**Roll唯一性验证**：
```csharp
// 2.2 检查ProductInboundRoll表中LotNo+OrderDetailId+RollNo组合全局唯一（批量优化）
// 收集所有需要验证的Roll信息
var rollsToValidate = new List<(string LotNo, Guid OrderDetailId, int RollNo, Guid RollId)>();
foreach (var lot in lotList)
{
    if (lot.RollList == null || !lot.RollList.Any())
        continue;

    foreach (var roll in lot.RollList)
    {
        rollsToValidate.Add((lot.LotNo, lot.OrderDetailId, roll.RollNo, roll.ID));
    }
}

if (rollsToValidate.Any())
{
    // 提取所有相关的LotNo和OrderDetailId
    var lotNos = rollsToValidate.Select(x => x.LotNo).Distinct().ToList();
    var orderDetailIds = rollsToValidate.Select(x => x.OrderDetailId).Distinct().ToList();

    // 批量查询数据库中所有相关的Roll记录（避免复杂的关联查询）
    var existingRolls = await (from roll in dc.ProductInboundRolls
                              join lot in dc.ProductInboundLots on roll.LotId equals lot.ID
                              where lotNos.Contains(lot.LotNo) &&
                                    orderDetailIds.Contains(lot.OrderDetailId) &&
                                    roll.IsValid == true &&
                                    roll.TenantCode == tenantCode
                              select new {
                                  roll.ID,
                                  lot.LotNo,
                                  lot.OrderDetailId,
                                  roll.RollNo
                              }).ToListAsync();

    // 在内存中进行验证
    foreach (var rollToValidate in rollsToValidate)
    {
        var conflictingRoll = existingRolls.FirstOrDefault(x =>
            x.LotNo == rollToValidate.LotNo &&
            x.OrderDetailId == rollToValidate.OrderDetailId &&
            x.RollNo == rollToValidate.RollNo &&
            x.ID != rollToValidate.RollId); // 排除当前正在编辑的Roll

        if (conflictingRoll != null)
        {
            ModelState.AddModelError("卷号重复", $"缸号 {rollToValidate.LotNo} 中卷号 {rollToValidate.RollNo} 已存在，请检查数据");
        }
    }
}
```

### 3.4 性能优化策略

#### 3.4.1 查询次数优化
**优化前**：
- 每个Lot单独查询数据库 → N次查询
- 每个Roll单独查询数据库 → N*M次查询（N个Lot，每个Lot有M个Roll）
- 每个重复错误单独查询OrderDetail → K次查询

**优化后**：
- 最多3次数据库查询：OrderDetail批量查询 + Lot批量查询 + Roll批量查询
- 性能提升：约95%的查询次数减少

#### 3.4.2 内存使用优化
- 使用匿名类型和Select投影，只查询必要的字段
- 使用Dictionary进行快速查找
- 及时释放不需要的集合引用

#### 3.4.3 SQL查询优化
- 避免复杂的Any()子查询，使用简单的Contains()查询
- 使用显式JOIN替代导航属性查询
- 分离查询条件，避免EF Core转换失败

### 3.5 验证集成

#### 3.5.1 AddWithLotAndRoll集成
```csharp
[HttpPost("AddWithLotAndRoll")]
public async Task<IActionResult> AddWithLotAndRoll(ProductInboundBillVM vm)
{
    if (!ModelState.IsValid)
    {
        return BadRequest(ModelState.GetErrorJson());
    }

    // 数据验证
    await ValidateLotListAsync(bill);
    if (!ModelState.IsValid)
    {
        var k = ModelState.Values.First();
        ErrorObj errorObj=new() { 
            Message=new(),
            Form=new()
        };
        var i = 0;
        foreach (var er in k.Errors)
        {
            errorObj.Message.Add( er.ErrorMessage);
        }
        return BadRequest(errorObj);
    }

    // 后续业务逻辑...
}
```

#### 3.5.2 EditWithLotAndRoll集成
```csharp
[HttpPut("EditWithLotAndRoll")]
public async Task<IActionResult> EditWithLotAndRoll(ProductInboundBillVM vm)
{
    if (!ModelState.IsValid)
    {
        return BadRequest(ModelState.GetErrorJson());
    }

    // 数据验证
    await ValidateLotListAsync(bill);
    if (!ModelState.IsValid)
    {
        var k = ModelState.Values.First();
        ErrorObj errorObj=new() { 
            Message=new(),
            Form=new()
        };
        var i = 0;
        foreach (var er in k.Errors)
        {
            errorObj.Message.Add( er.ErrorMessage);
        }
        return BadRequest(errorObj);
    }

    // 后续业务逻辑...
}
```
#### 3.5.3 验证错误通知

```
//错误包装到ErrorObj的Message中
if (!ModelState.IsValid)
{
    var k = ModelState.Values.First();
    ErrorObj errorObj=new() { 
        Message=new(),
        Form=new()
    };
    //var i = 0;//作为Form的key,不能重复
    foreach (var er in k.Errors)
    {
        errorObj.Message.Add( er.ErrorMessage);
        //errorObj.Form.Add(i.ToString(), er.ErrorMessage);//和Message重复
        //i++;
    }
    //var e = ModelState.GetErrorJson();//只能显示第一个错误
    return BadRequest(errorObj);
}

```

```
//Blazor页面PostsForm的错误通知示例:
await PostsForm(vform, "/api/ProductInboundBill/AddWithLotAndRoll", (s) => "Sys.OprationSuccess", async (err) => {
            // 使用PostsForm的错误处理回调来显示自定义通知
            if (err.Message != null && err.Message.Count > 0)
            {
                foreach (var key in err.Message)
                {
                    await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], key);
                }
            }
        });
```

### 3.6 测试验证

#### 3.6.1 验证测试用例
- **AddWithLotAndRollTest_LotNoOrderDetailIdDuplicateValidation**：测试LotNo+OrderDetailId组合全局唯一性验证
- **AddWithLotAndRollTest_RollNoDuplicateValidation**：测试LotNo+OrderDetailId+RollNo组合全局唯一性验证
- **EditWithLotAndRollTest_LotNoOrderDetailIdDuplicateValidation**：测试编辑场景下的Lot重复验证
- **EditWithLotAndRollTest_RollNoDuplicateValidation**：测试编辑场景下的Roll重复验证
- **AddWithLotAndRollTest_InternalDataDuplicateValidation**：测试传入数据内部重复性验证

#### 3.6.2 错误信息验证
```csharp
// 正确的错误信息获取方式
var badRequestResult = rv as BadRequestObjectResult;
var error = badRequestResult.Value as ErrorObj;
var errorContent = error.Form.Keys.First() + error.GetFirstError();
Assert.IsTrue(errorContent.Contains("重复"),
    $"错误信息应该包含重复提示，实际内容：{errorContent}");
```

## 4. AddWithLotAndRoll实现分析

### 4.1 方法签名和基本结构

```csharp
[HttpPost("AddWithLotAndRoll")]
public async Task<IActionResult> AddWithLotAndRoll(ProductInboundBillVM vm)
```

### 4.2 核心实现流程

#### 4.2.1 数据验证和初始化
```csharp
// 1. 模型验证
if (!ModelState.IsValid)
{
    return BadRequest(ModelState.GetErrorJson());
}

// 2. 事务初始化（内存数据库兼容）
var isInMemoryDatabase = Wtm.DC.Database.ProviderName?.Contains("InMemory") == true;
var transaction = isInMemoryDatabase ? null : Wtm.DC.BeginTransaction();

// 3. 基本信息设置
var bill = vm.Entity;
var tenantCode = Wtm.LoginUserInfo?.CurrentTenant;
var usercode = Wtm.LoginUserInfo?.ITCode;
bill.TenantCode = tenantCode;
bill.CreateBy = usercode;
bill.CreateTime = DateTime.Now;
```

#### 4.2.2 三级实体关联和统计计算
```csharp
// 1. Bill级别统计计算
if (bill.LotList.Any())
{
    bill.Pcs = bill.LotList.Sum(x => x.Pcs);
    bill.Meters = bill.LotList.Sum(x => x.Meters);
    bill.Weight = bill.LotList.Sum(x => x.Weight);
    bill.Yards = bill.LotList.Sum(x => x.Yards);
}

// 2. Lot级别处理
foreach (var lot in bill.LotList)
{
    // 验证OrderDetailId
    if (lot.OrderDetailId == Guid.Empty)
    {
        return BadRequest($"批次 {lot.LotNo} 的订单明细ID不能为空");
    }
    
    // 设置关联关系和审计信息
    lot.InboundBillId = bill.ID;
    lot.CreateTime = DateTime.Now;
    lot.TenantCode = tenantCode;
    lot.CreateBy = usercode;
    
    // Lot级别统计计算
    if (lot.RollList != null && lot.RollList.Any())
    {
        lot.Pcs = lot.RollList.Count;
        lot.Meters = lot.RollList.Sum(x => x.Meters);
        lot.Weight = lot.RollList.Sum(x => x.Weight);
        lot.Yards = lot.RollList.Sum(x => x.Yards);
    }
    
    // 3. Roll级别处理
    foreach (var roll in lot.RollList)
    {
        roll.LotId = lot.ID;
        roll.LotNo = lot.LotNo;
        roll.CreateTime = DateTime.Now;
        roll.TenantCode = tenantCode;
        roll.CreateBy = usercode;
    }
}
```

#### 4.2.3 库存创建和更新
```csharp
// 1. 按OrderDetailId分组统计库存
var stockList = bill.LotList
    .GroupBy(x => x.OrderDetailId)
    .Select(x => new StockInfo
    {
        OrderDetailId = x.Key,
        TotalPcs = x.Sum(y => y.Pcs),
        TotalMeters = x.Sum(y => y.Meters),
        TotalWeight = x.Sum(y => y.Weight),
        TotalYards = x.Sum(y => y.Yards),
    }).ToList();

// 2. 更新ProductStock表
var orderDetailIds = stockList.Select(r => r.OrderDetailId).ToList();
var productStocks = await DC.Set<ProductStock>()
    .Where(s => orderDetailIds.Contains(s.OrderDetailId))
    .ToDictionaryAsync(s => s.OrderDetailId);

foreach (var stockInfo in stockList)
{
    if (!productStocks.TryGetValue(stockInfo.OrderDetailId, out var existingStock))
    {
        // 创建新库存记录
        var newStock = new ProductStock
        {
            OrderDetailId = stockInfo.OrderDetailId,
            TotalPcs = stockInfo.TotalPcs,
            TotalMeters = stockInfo.TotalMeters,
            TotalWeight = stockInfo.TotalWeight,
            TotalYards = stockInfo.TotalYards,
            CreateTime = DateTime.Now,
            CreateBy = usercode,
            TenantCode = tenantCode
        };
        DC.Set<ProductStock>().Add(newStock);
    }
    else
    {
        // 累加到现有库存
        existingStock.TotalPcs += stockInfo.TotalPcs;
        existingStock.TotalWeight += stockInfo.TotalWeight;
        existingStock.TotalMeters += stockInfo.TotalMeters;
        existingStock.TotalYards += stockInfo.TotalYards;
        existingStock.UpdateTime = DateTime.Now;
        existingStock.UpdateBy = usercode;
    }
}
```

#### 4.2.4 数据保存和事务提交
```csharp
// 1. 添加主实体
DC.Set<ProductInboundBill>().Add(bill);

// 2. 统一保存
await DC.SaveChangesAsync();

// 3. 事务提交（条件性）
if (transaction != null)
{
    transaction.Commit();
}

return Ok(bill);
```

### 4.3 关键设计特点

1. **内存数据库兼容**: 检测数据库类型，跳过事务处理避免FakeNestedTransaction异常
2. **三级统计计算**: 自底向上计算Roll→Lot→Bill的统计数据
3. **库存聚合管理**: 按OrderDetailId分组管理库存，支持多Lot聚合
4. **审计信息完整**: 所有层级都设置完整的审计字段
5. **数据验证严格**: 关键字段如OrderDetailId进行严格验证

## 5. EditWithLotAndRoll实现分析

### 5.1 方法签名和基本结构

```csharp
[HttpPut("EditWithLotAndRoll")]
public async Task<IActionResult> EditWithLotAndRoll(ProductInboundBillVM vm)
```

### 5.2 核心实现流程

#### 5.2.1 数据预处理和原始库存计算
```csharp
// 1. 统计信息预计算
foreach (var lot in bill.LotList)
{
    lot.RollList = lot.RollList ?? [];
    if (lot.RollList.Any())
    {
        lot.Pcs = lot.RollList.Count;
        lot.Meters = lot.RollList.Sum(x => x.Meters);
        lot.Weight = lot.RollList.Sum(x => x.Weight);
        lot.Yards = lot.RollList.Sum(x => x.Yards);
    }
}

// 2. Bill级别统计
if (bill.LotList.Any())
{
    bill.Pcs = bill.LotList.Sum(x => x.Pcs);
    bill.Meters = bill.LotList.Sum(x => x.Meters);
    bill.Weight = bill.LotList.Sum(x => x.Weight);
    bill.Yards = bill.LotList.Sum(x => x.Yards);
}

// 3. 查询现有数据（包含三级关联）
var existingBill = await DC.Set<ProductInboundBill>()
    .Include(x => x.LotList)
    .ThenInclude(x => x.RollList)
    .FirstOrDefaultAsync(x => x.ID == bill.ID);

// 4. 计算原始库存统计
var originalStockList = existingBill.LotList
    .Where(x => x.IsValid)
    .GroupBy(x => x.OrderDetailId)
    .Select(x => new StockInfo
    {
        OrderDetailId = x.Key,
        TotalPcs = x.Sum(y => y.Pcs),
        TotalMeters = x.Sum(y => y.Meters),
        TotalWeight = x.Sum(y => y.Weight),
        TotalYards = x.Sum(y => y.Yards),
    }).ToList();
```

#### 4.2.2 三级实体差异处理

EditWithLotAndRoll采用EF Core实体跟踪机制，通过对比现有数据和传入数据来处理增删改操作：

```csharp
// 1. 处理传入的Lot数据
foreach (var inputLot in bill.LotList)
{
    var existingLot = existingBill.LotList.FirstOrDefault(x => x.ID == inputLot.ID);
    if (existingLot == null)
    {
        // 新增Lot
        inputLot.InboundBillId = bill.ID;
        inputLot.CreateTime = DateTime.Now;
        inputLot.CreateBy = userCode;
        inputLot.TenantCode = tenantCode;
        inputLot.IsValid = true;
        newLotList.Add(inputLot);

        // 处理新Lot下的Roll
        if (inputLot.RollList != null)
        {
            foreach (var roll in inputLot.RollList)
            {
                roll.LotId = inputLot.ID;
                roll.LotNo = inputLot.LotNo;
                roll.CreateTime = DateTime.Now;
                roll.CreateBy = userCode;
                roll.TenantCode = tenantCode;
                roll.IsValid = true;
                newRollList.Add(roll);
            }
        }
    }
    else
    {
        // 修改现有Lot
        existingLot.Color = inputLot.Color;
        existingLot.ColorCode = inputLot.ColorCode;
        existingLot.LotNo = inputLot.LotNo;
        existingLot.Location = inputLot.Location;
        existingLot.InboundStatus = inputLot.InboundStatus;
        existingLot.Remark = inputLot.Remark;
        existingLot.UpdateTime = DateTime.Now;
        existingLot.UpdateBy = userCode;
        updateLotList.Add(existingLot);

        // 处理Roll的增删改
        ProcessRollChanges(existingLot, inputLot, userCode);
    }
}

// 2. 找出需要删除的Lot（软删除）
deleteLotList = existingBill.LotList.Where(x => bill.LotList.All(y => y.ID != x.ID)).ToList();
foreach (var lot in deleteLotList)
{
    lot.IsValid = false;
    lot.UpdateTime = DateTime.Now;
    lot.UpdateBy = userCode;

    // 同时软删除该Lot下的所有Roll
    foreach (var roll in lot.RollList)
    {
        roll.IsValid = false;
        roll.UpdateTime = DateTime.Now;
        roll.UpdateBy = userCode;
        deleteRollList.Add(roll);
    }
}
```

#### 4.2.3 EF Core批量操作

```csharp
// 使用EF Core原生批量操作，避免实体跟踪冲突
if (newLotList.Any())
{
    dc.Set<ProductInboundLot>().AddRange(newLotList);
}

if (newRollList.Any())
{
    dc.Set<ProductInboundRoll>().AddRange(newRollList);
}

if (updateLotList.Any())
{
    dc.Set<ProductInboundLot>().UpdateRange(updateLotList);
}

if (updateRollList.Any())
{
    dc.Set<ProductInboundRoll>().UpdateRange(updateRollList);
}

// 更新Bill主表
existingBill.Warehouse = bill.Warehouse;
existingBill.Remark = bill.Remark;
existingBill.Pcs = bill.Pcs;
existingBill.Weight = bill.Weight;
existingBill.Meters = bill.Meters;
existingBill.Yards = bill.Yards;
existingBill.UpdateTime = DateTime.Now;
existingBill.UpdateBy = userCode;
dc.Set<ProductInboundBill>().Update(existingBill);
```

#### 4.2.4 库存差异计算和更新

```csharp
// 1. 计算修改后的库存统计
var currentStockList = bill.LotList
    .Where(x => x.IsValid)
    .GroupBy(x => x.OrderDetailId)
    .Select(x => new StockInfo
    {
        OrderDetailId = x.Key,
        TotalPcs = x.Sum(y => y.Pcs),
        TotalMeters = x.Sum(y => y.Meters),
        TotalWeight = x.Sum(y => y.Weight),
        TotalYards = x.Sum(y => y.Yards),
    }).ToList();

// 2. 计算库存差异
var stockDifferences = CalculateDifferences(originalStockList, currentStockList);

// 3. 更新ProductStock表
if (stockDifferences.Any())
{
    var orderDetailIds = stockDifferences.Select(r => r.OrderDetailId).ToList();
    var productStocks = await DC.Set<ProductStock>()
        .Where(s => orderDetailIds.Contains(s.OrderDetailId))
        .ToDictionaryAsync(s => s.OrderDetailId);

    foreach (var stockChange in stockDifferences)
    {
        // 跳过没有变化的记录
        if (stockChange.TotalPcs == 0 && stockChange.TotalMeters == 0 &&
            stockChange.TotalWeight == 0 && stockChange.TotalYards == 0)
        {
            continue;
        }

        if (!productStocks.TryGetValue(stockChange.OrderDetailId, out var existingStock))
        {
            // 新增库存记录
            var newStock = new ProductStock
            {
                OrderDetailId = stockChange.OrderDetailId,
                TotalPcs = stockChange.TotalPcs,
                TotalMeters = stockChange.TotalMeters,
                TotalWeight = stockChange.TotalWeight,
                TotalYards = stockChange.TotalYards,
                CreateTime = DateTime.Now,
                CreateBy = userCode,
                TenantCode = tenantCode
            };
            DC.Set<ProductStock>().Add(newStock);
        }
        else
        {
            // 更新现有库存记录
            existingStock.TotalPcs += stockChange.TotalPcs;
            existingStock.TotalWeight += stockChange.TotalWeight;
            existingStock.TotalMeters += stockChange.TotalMeters;
            existingStock.TotalYards += stockChange.TotalYards;
            existingStock.UpdateTime = DateTime.Now;
            existingStock.UpdateBy = userCode;
        }
    }
}
```

### 4.3 关键设计特点

1. **EF Core实体跟踪**: 利用EF Core 8的实体跟踪特性，通过对比现有数据实现优雅更新
2. **库存差异计算**: 通过CalculateDifferences方法精确计算库存变化
3. **批量操作优化**: 使用AddRange、UpdateRange等批量操作提高性能
4. **软删除机制**: 通过IsValid字段实现软删除，保持数据完整性
5. **三级联动处理**: 同时处理Bill、Lot、Roll三个层级的数据变更

## 6. 库存管理机制

### 5.1 库存差异计算算法

CalculateDifferences方法是库存管理的核心算法：

```csharp
private List<StockInfo> CalculateDifferences(List<StockInfo> originalLotList, List<StockInfo> currentLotList)
{
    var differences = new List<StockInfo>();
    var originalDict = originalLotList.ToDictionary(x => x.OrderDetailId);
    var currentDict = currentLotList.ToDictionary(x => x.OrderDetailId);

    // 1. 处理现有OrderDetail的变更
    foreach (var current in currentLotList)
    {
        if (originalDict.TryGetValue(current.OrderDetailId, out StockInfo original))
        {
            // 计算差值（current - original）
            differences.Add(new StockInfo
            {
                OrderDetailId = current.OrderDetailId,
                TotalPcs = current.TotalPcs - original.TotalPcs,
                TotalMeters = current.TotalMeters - original.TotalMeters,
                TotalWeight = current.TotalWeight - original.TotalWeight,
                TotalYards = current.TotalYards - original.TotalYards
            });
        }
        else
        {
            // 新增OrderDetail，直接使用当前值
            differences.Add(new StockInfo
            {
                OrderDetailId = current.OrderDetailId,
                TotalPcs = current.TotalPcs,
                TotalMeters = current.TotalMeters,
                TotalWeight = current.TotalWeight,
                TotalYards = current.TotalYards
            });
        }
    }

    // 2. 处理删除的OrderDetail
    foreach (var original in originalLotList)
    {
        if (!currentDict.ContainsKey(original.OrderDetailId))
        {
            // 返回负值表示库存减少
            differences.Add(new StockInfo
            {
                OrderDetailId = original.OrderDetailId,
                TotalPcs = -original.TotalPcs,
                TotalMeters = -original.TotalMeters,
                TotalWeight = -original.TotalWeight,
                TotalYards = -original.TotalYards
            });
        }
    }

    return differences;
}
```

### 5.2 库存聚合策略

系统支持多个Lot指向同一个OrderDetail的库存聚合：

```csharp
// 按OrderDetailId分组聚合库存
var stockList = bill.LotList
    .Where(x => x.IsValid)  // 只统计有效记录
    .GroupBy(x => x.OrderDetailId)
    .Select(x => new StockInfo
    {
        OrderDetailId = x.Key,
        TotalPcs = x.Sum(y => y.Pcs),      // 件数累加
        TotalMeters = x.Sum(y => y.Meters), // 米数累加
        TotalWeight = x.Sum(y => y.Weight), // 重量累加
        TotalYards = x.Sum(y => y.Yards),   // 码数累加
    }).ToList();
```

### 5.3 库存更新模式

1. **新增模式**: 创建新的ProductStock记录
2. **累加模式**: 在现有库存基础上累加差异值
3. **减少模式**: 通过负值差异减少库存
4. **删除模式**: 将库存减少到0（通过负的原始值）

## 7. 事务处理策略

### 6.1 数据库类型检测

```csharp
// 检测数据库类型，内存数据库跳过事务处理
var isInMemoryDatabase = Wtm.DC.Database.ProviderName?.Contains("InMemory") == true;
var transaction = isInMemoryDatabase ? null : Wtm.DC.BeginTransaction();
```

### 6.2 事务生命周期管理

```csharp
try
{
    // 业务逻辑处理
    // ...

    // 统一保存所有更改
    await dc.SaveChangesAsync();

    // 条件性提交事务
    if (transaction != null)
    {
        transaction.Commit();
    }
    return Ok(result);
}
catch (Exception ex)
{
    // 条件性回滚事务
    if (transaction != null)
    {
        transaction.Rollback();
    }
    ModelState.AddModelError("", ex.Message);
    return BadRequest(ModelState.GetErrorJson());
}
```

### 6.3 事务设计原则

1. **原子性**: 所有三级实体的操作在同一事务中
2. **一致性**: 库存更新与实体变更保持一致
3. **隔离性**: 通过事务隔离避免并发问题
4. **持久性**: 事务提交后数据持久化存储

## 8. 测试验证方法

### 7.1 测试架构设计

测试采用分层验证策略：

```csharp
[TestMethod]
public async Task AddWithLotAndRollTest_StockUpdate()
{
    // 1. 数据准备
    ProductInboundBillVM vm = _controller.Wtm.CreateVM<ProductInboundBillVM>();
    ProductInboundBill v = new ProductInboundBill();

    // 2. 执行操作
    var result = await _controller.AddWithLotAndRoll(vm);
    Assert.IsInstanceOfType(result, typeof(OkObjectResult));

    // 3. 验证结果
    using (var context = new DataContext(_seed, DBTypeEnum.Memory))
    {
        var stock = context.Set<ProductStock>()
            .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

        Assert.IsNotNull(stock, "应该创建库存记录");
        Assert.AreEqual(expectedPcs, stock.TotalPcs, "库存件数验证");
        Assert.AreEqual(expectedWeight, stock.TotalWeight, "库存重量验证");
        // ... 其他验证
    }
}
```

### 7.2 测试场景覆盖

#### 7.2.1 基本功能测试
- 三级实体创建和关联验证
- 统计字段计算准确性
- 审计信息完整性

#### 7.2.2 库存更新测试
- 库存记录创建
- 库存累加计算
- 库存差异处理
- 库存聚合验证

#### 7.2.3 边界情况测试
- 空Lot列表处理
- 无Roll的Lot处理
- 事务回滚验证
- 数据验证失败处理

#### 7.2.4 性能测试
- 大量数据处理
- 批量操作效率
- 内存使用优化

### 7.3 测试数据管理

```csharp
// 测试辅助方法
private Guid AddPurchaseOrder()
{
    var po = new PurchaseOrder
    {
        OrderNo = $"PO_{DateTime.Now:yyyyMMddHHmmss}",
        CustomerId = AddCompany(),
        OrderDate = DateTime.Now,
        TenantCode = "test"
    };
    DC.Set<PurchaseOrder>().Add(po);
    DC.SaveChanges();
    return po.ID;
}

private Guid AddOrderDetail()
{
    var od = new OrderDetail
    {
        POrderId = AddPurchaseOrder(),
        ProductId = AddProduct(),
        Quantity = 100,
        TenantCode = "test"
    };
    DC.Set<OrderDetail>().Add(od);
    DC.SaveChanges();
    return od.ID;
}
```

## 9. 出库操作设计指导

### 8.1 出库单数据模型设计

基于入库单的成功经验，出库单应采用类似的三级结构：

```csharp
// 出库单主表
public class ProductOutboundBill : BasePoco, IPersistPoco, ITenant
{
    public string BillNo { get; set; }              // 出库单号
    public DateTime OutboundDate { get; set; }      // 出库日期
    public Guid CustomerId { get; set; }            // 客户ID
    public string Warehouse { get; set; }           // 出库仓库
    public int Pcs { get; set; }                    // 总件数
    public decimal Weight { get; set; }             // 总重量
    public decimal Meters { get; set; }             // 总米数
    public decimal Yards { get; set; }              // 总码数
    public OutboundStatusEnum OutboundStatus { get; set; } // 出库状态
    public List<ProductOutboundLot> LotList { get; set; } // 出库批次列表
}

// 出库批次
public class ProductOutboundLot : BasePoco, IPersistPoco, ITenant
{
    public Guid OutboundBillId { get; set; }        // 出库单ID
    public Guid InboundLotId { get; set; }          // 对应入库批次ID
    public Guid OrderDetailId { get; set; }        // 订单明细ID
    public string LotNo { get; set; }              // 批次号
    public int Pcs { get; set; }                   // 出库件数
    public decimal Weight { get; set; }            // 出库重量
    public decimal Meters { get; set; }            // 出库米数
    public decimal Yards { get; set; }             // 出库码数
    public List<ProductOutboundRoll> RollList { get; set; } // 出库卷列表
}

// 出库卷
public class ProductOutboundRoll : BasePoco, IPersistPoco, ITenant
{
    public Guid LotId { get; set; }                // 出库批次ID
    public Guid InboundRollId { get; set; }        // 对应入库卷ID
    public string LotNo { get; set; }              // 批次号
    public int RollNo { get; set; }                // 卷号
    public decimal Weight { get; set; }            // 出库重量
    public decimal Meters { get; set; }            // 出库米数
    public decimal Yards { get; set; }             // 出库码数
    public string Grade { get; set; }              // 等级
}
```

### 8.2 出库库存管理策略

#### 8.2.1 库存扣减机制

```csharp
// 出库时库存扣减逻辑
private async Task<bool> DeductStock(List<StockInfo> outboundStockList)
{
    var orderDetailIds = outboundStockList.Select(x => x.OrderDetailId).ToList();
    var productStocks = await DC.Set<ProductStock>()
        .Where(s => orderDetailIds.Contains(s.OrderDetailId))
        .ToDictionaryAsync(s => s.OrderDetailId);

    foreach (var outboundStock in outboundStockList)
    {
        if (!productStocks.TryGetValue(outboundStock.OrderDetailId, out var existingStock))
        {
            throw new InvalidOperationException($"订单明细 {outboundStock.OrderDetailId} 没有库存记录");
        }

        // 检查库存是否充足
        if (existingStock.TotalPcs < outboundStock.TotalPcs ||
            existingStock.TotalWeight < outboundStock.TotalWeight ||
            existingStock.TotalMeters < outboundStock.TotalMeters ||
            existingStock.TotalYards < outboundStock.TotalYards)
        {
            throw new InvalidOperationException($"订单明细 {outboundStock.OrderDetailId} 库存不足");
        }

        // 扣减库存
        existingStock.TotalPcs -= outboundStock.TotalPcs;
        existingStock.TotalWeight -= outboundStock.TotalWeight;
        existingStock.TotalMeters -= outboundStock.TotalMeters;
        existingStock.TotalYards -= outboundStock.TotalYards;
        existingStock.UpdateTime = DateTime.Now;
        existingStock.UpdateBy = userCode;
    }

    return true;
}
```

#### 8.2.2 库存回滚机制

```csharp
// 出库撤销时库存回滚逻辑
private async Task<bool> RestoreStock(List<StockInfo> outboundStockList)
{
    var orderDetailIds = outboundStockList.Select(x => x.OrderDetailId).ToList();
    var productStocks = await DC.Set<ProductStock>()
        .Where(s => orderDetailIds.Contains(s.OrderDetailId))
        .ToDictionaryAsync(s => s.OrderDetailId);

    foreach (var outboundStock in outboundStockList)
    {
        if (productStocks.TryGetValue(outboundStock.OrderDetailId, out var existingStock))
        {
            // 恢复库存
            existingStock.TotalPcs += outboundStock.TotalPcs;
            existingStock.TotalWeight += outboundStock.TotalWeight;
            existingStock.TotalMeters += outboundStock.TotalMeters;
            existingStock.TotalYards += outboundStock.TotalYards;
            existingStock.UpdateTime = DateTime.Now;
            existingStock.UpdateBy = userCode;
        }
        else
        {
            // 创建新的库存记录
            var newStock = new ProductStock
            {
                OrderDetailId = outboundStock.OrderDetailId,
                TotalPcs = outboundStock.TotalPcs,
                TotalMeters = outboundStock.TotalMeters,
                TotalWeight = outboundStock.TotalWeight,
                TotalYards = outboundStock.TotalYards,
                CreateTime = DateTime.Now,
                CreateBy = userCode,
                TenantCode = tenantCode
            };
            DC.Set<ProductStock>().Add(newStock);
        }
    }

    return true;
}
```

### 8.3 出库操作API设计

#### 8.3.1 AddWithLotAndRoll (出库新增)

```csharp
[HttpPost("AddWithLotAndRoll")]
public async Task<IActionResult> AddWithLotAndRoll(ProductOutboundBillVM vm)
{
    // 1. 数据验证
    // 2. 库存充足性检查
    // 3. 库存扣减
    // 4. 三级实体创建
    // 5. 事务提交
}
```

#### 8.3.2 EditWithLotAndRoll (出库修改)

```csharp
[HttpPut("EditWithLotAndRoll")]
public async Task<IActionResult> EditWithLotAndRoll(ProductOutboundBillVM vm)
{
    // 1. 查询原始出库数据
    // 2. 计算库存差异
    // 3. 库存调整（可能增加或减少）
    // 4. 三级实体更新
    // 5. 事务提交
}
```

#### 8.3.3 CancelOutbound (出库撤销)

```csharp
[HttpPost("CancelOutbound/{id}")]
public async Task<IActionResult> CancelOutbound(Guid id)
{
    // 1. 查询出库数据
    // 2. 库存回滚
    // 3. 状态更新为已撤销
    // 4. 事务提交
}
```

## 10. 最佳实践总结

### 10.1 架构设计原则

1. **三级联动一致性**: 保持Bill-Lot-Roll三级结构的一致性
2. **库存管理集中化**: 通过ProductStock表集中管理库存
3. **事务原子性**: 所有相关操作在同一事务中完成
4. **软删除策略**: 使用IsValid字段实现软删除，保持数据完整性
5. **审计信息完整**: 所有层级都包含完整的审计字段
6. **数据验证严格**: 实施严格的数据验证机制，确保数据完整性和业务规则正确执行

### 10.2 性能优化策略

1. **批量操作**: 使用AddRange、UpdateRange等批量操作
2. **查询优化**: 使用Include预加载关联数据
3. **索引设计**: 在关键字段上建立适当索引
4. **分页处理**: 大数据量查询使用分页
5. **缓存策略**: 对频繁查询的数据进行缓存
6. **验证优化**: 使用批量查询和内存验证，避免N+1查询问题，实现95%的查询次数减少

### 10.3 测试策略

1. **单元测试**: 覆盖所有核心业务逻辑
2. **集成测试**: 验证三级实体的完整流程
3. **性能测试**: 验证大数据量处理能力
4. **边界测试**: 测试各种边界情况和异常场景
5. **回归测试**: 确保新功能不影响现有功能
6. **验证测试**: 全面测试数据验证逻辑，包括内部重复性和数据库唯一性验证

### 10.4 错误处理策略

1. **数据验证**: 在多个层级进行数据验证
2. **异常捕获**: 完整的异常捕获和处理机制
3. **事务回滚**: 异常时自动回滚事务
4. **错误日志**: 详细的错误日志记录
5. **用户友好**: 向用户返回友好的错误信息
6. **验证错误处理**: 通过ErrorObj正确获取和处理WTM框架的验证错误信息

### 10.5 安全考虑

1. **权限控制**: 基于角色的访问控制
2. **数据隔离**: 多租户数据隔离
3. **输入验证**: 严格的输入数据验证
4. **SQL注入防护**: 使用参数化查询
5. **业务规则验证**: 通过ValidateLotListAsync确保业务规则的严格执行，防止数据不一致

### 10.6 数据验证最佳实践

1. **分层验证**: 在Controller层进行业务规则验证，在Model层进行基础数据验证
2. **批量优化**: 使用批量查询和内存验证，避免性能问题
3. **错误信息友好**: 提供具体的错误信息，帮助用户快速定位问题
4. **测试覆盖**: 为所有验证场景编写完整的测试用例
5. **性能监控**: 监控验证逻辑的性能，及时发现和解决性能瓶颈
5. **审计跟踪**: 完整的操作审计跟踪

---

## 结语

本文档详细分析了TEX项目中ProductInboundBill入库单的AddWithLotAndRoll和EditWithLotAndRoll方法的实现逻辑，提供了完整的技术指导和最佳实践。这些经验和模式可以直接应用于出库操作的开发，确保系统的一致性和可维护性。

通过遵循这些设计原则和实现模式，可以构建出高质量、高性能、易维护的库存管理系统。

**文档版本**: 1.0
**创建日期**: 2025-07-17
**最后更新**: 2025-07-17
**作者**: TEX开发团队
```
