# BootstrapBlazor 组件库使用指南

本文档记录了TEX项目中使用BootstrapBlazor组件库的常用方法和最佳实践。

## 目录

- [Tab 组件](#tab-组件)
- [Table 组件](#table-组件)
- [Form 组件](#form-组件)
  - [ValidateForm 使用](#validateform-使用)
  - [Enter键处理](#enter键处理)
- [Dialog 组件](#dialog-组件)
- [Toast 通知](#toast-通知)

---

## Tab 组件

### 基本概念

BootstrapBlazor的Tab组件支持多标签页功能，常用于后台管理系统的多页面切换。

### 关闭当前Tab页面

在Tab页面中，可以通过级联参数获取Tab组件实例，然后调用关闭方法。
通过TabItem这个级联参数可以设置当前Tab页的属性,如:Tab页标题:TabItem.Text

#### 实现步骤

1. **声明级联参数**
```csharp
[CascadingParameter]
public Tab TabSet { get; set; }

[CascadingParameter]
[NotNull]
public TabItem? TabItem { get; set; } //当前Tab,
```

2. **调用关闭方法**
```csharp
public async Task OnClose()
{
    if (TabSet != null)
    {
        // 关闭当前Tab页面
        await TabSet.CloseCurrentTab();
    }
    else
    {
        // 备用方案：导航到其他页面
        Navigation.NavigateTo("/SomeOtherPage");
    }
}
```

#### 完整示例

```razor
@page "/Example/Create"
@inherits BasePage

[CascadingParameter]
public Tab TabSet { get; set; }

<div class="container-fluid">
    <!-- 页面内容 -->
    <h3>创建示例</h3>
    
    <!-- 表单内容 -->
    <EditForm Model="Model" OnValidSubmit="Submit">
        <!-- 表单字段 -->
    </EditForm>
</div>

<div class="modal-footer table-modal-footer">
    <Button Color="Color.Secondary" Icon="fa fa-close" Text="关闭" OnClick="OnClose" />
    <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="提交" />
</div>

@code {
    [CascadingParameter]
    public Tab TabSet { get; set; }
    
    private ExampleVM Model = new ExampleVM();
    
    public async Task OnClose()
    {
        if (TabSet != null)
        {
            await TabSet.CloseCurrentTab();
        }
        else
        {
            Navigation.NavigateTo("/Example");
        }
    }
    
    private async Task Submit()
    {
        // 提交逻辑
        var success = await SubmitData();
        
        if (success)
        {
            await WtmBlazor.Toast.Success("操作成功", "数据已保存");
            
            // 提交成功后关闭Tab
            if (TabSet != null)
            {
                await TabSet.CloseCurrentTab();
            }
            else
            {
                Navigation.NavigateTo("/Example");
            }
        }
    }
}
```

#### 注意事项

1. **级联参数检查**: 始终检查`TabSet`是否为null，因为页面可能在非Tab环境中使用
2. **备用方案**: 当`TabSet`为null时，提供备用的导航方案
3. **异步调用**: `CloseCurrentTab()`是异步方法，需要使用`await`

#### 适用场景

- 创建/编辑页面的关闭按钮
- 表单提交成功后的页面关闭
- 任何需要关闭当前Tab的场景

---

## Table 组件

### 数据绑定注意事项

BootstrapBlazor的Table组件对数据类型有特定要求：

```csharp
// ✅ 正确：使用IEnumerable<T>
public IEnumerable<ProductInboundLot> Items { get; set; } = new List<ProductInboundLot>();

// ❌ 错误：直接使用List<T>可能导致绑定问题
public List<ProductInboundLot> Items { get; set; } = new List<ProductInboundLot>();
```

### 基本用法

```razor
<Table TItem="ProductInboundLot" 
       Items="@Items"
       IsPagination="true"
       PageItemsSource="@PageItemsSource"
       IsStriped="true"
       IsBordered="true"
       ShowSkeleton="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.LotNo" Text="批次号" />
        <TableColumn @bind-Field="@context.Quantity" Text="数量" />
        <!-- 更多列定义 -->
    </TableColumns>
</Table>
```

---

## Form 组件

### ValidateForm 使用

```razor
<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <BootstrapInput @bind-Value="@Model.Name" DisplayText="名称" />
        </div>
        <!-- 更多表单字段 -->
    </div>
</ValidateForm>

@code {
    private ValidateForm vform { get; set; }
    private ExampleVM Model = new ExampleVM();

    private async Task Submit(EditContext context)
    {
        // 表单验证通过后的处理逻辑
    }
}
```

### Enter键处理

在BootstrapBlazor的ValidateForm中，默认情况下按下Enter键会触发表单提交。如果需要自定义Enter键行为，有以下两种方法：

#### 方法1：键盘事件拦截（推荐）

通过在ValidateForm上添加`@onkeydown`事件处理器来拦截Enter键，并阻止默认的提交行为：

```razor
<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit" @onkeydown="@OnKeyDown">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
        <BootstrapInput @bind-Value="@Model.Entity.CreateTime" Format="yyyy-MM-dd" />
        <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
        <BootstrapInput @bind-Value="@Model.Entity.InspectedLot" />
        <!-- 更多表单字段 -->
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    private ValidateForm vform { get; set; }
    private ExampleVM Model = new ExampleVM();

    // 键盘事件处理
    private async Task OnKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            // 阻止默认的Submit行为
            e.PreventDefault();

            // 执行自定义Enter键逻辑
            await OnCustomEnterAction();
        }
    }

    // 自定义Enter键处理逻辑
    private async Task OnCustomEnterAction()
    {
        // 在这里添加您希望在Enter键按下时执行的逻辑
        // 例如：验证特定字段、移动焦点、执行搜索等

        await WtmBlazor.Toast.Info("提示", "检测到Enter键按下，执行自定义操作");

        // 如果需要，您仍然可以手动触发提交
        // await Submit(new EditContext(Model));
    }

    private async Task Submit(EditContext context)
    {
        // 表单验证通过后的处理逻辑
    }
}
```

#### 方法2：移除表单提交绑定

完全移除`OnValidSubmit`绑定和Submit按钮类型，改为使用自定义的OnClick事件：

```razor
<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
        <BootstrapInput @bind-Value="@Model.Entity.CreateTime" Format="yyyy-MM-dd" />
        <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
        <BootstrapInput @bind-Value="@Model.Entity.InspectedLot" />
        <!-- 更多表单字段 -->
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClick="@OnCustomSubmit" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    private ValidateForm vform { get; set; }
    private ExampleVM Model = new ExampleVM();

    // 自定义提交方法
    private async Task OnCustomSubmit()
    {
        // 手动验证表单
        if (vform.Validate())
        {
            // 验证通过，执行提交逻辑
            await SubmitData();
        }
        else
        {
            // 验证失败，显示错误信息
            await WtmBlazor.Toast.Error("验证失败", "请检查表单输入");
        }
    }

    private async Task SubmitData()
    {
        // 实际的数据提交逻辑
        await WtmBlazor.Toast.Success("操作成功", "数据已保存");
    }
}
```

#### 方法对比

| 特性 | 方法1：键盘事件拦截 | 方法2：移除表单提交绑定 |
|------|-------------------|----------------------|
| **复杂度** | 简单，保持现有结构 | 中等，需要重构提交逻辑 |
| **兼容性** | 完全兼容现有代码 | 需要修改现有提交逻辑 |
| **灵活性** | 高，可选择性处理Enter键 | 高，完全自定义提交流程 |
| **维护性** | 好，变更最小 | 一般，需要维护额外的验证逻辑 |
| **推荐场景** | 大部分情况，特别是现有项目 | 新项目或需要完全自定义提交流程 |

#### 注意事项

1. **事件冒泡**: 使用方法1时，确保`e.PreventDefault()`正确阻止了默认行为
2. **表单验证**: 使用方法2时，需要手动调用`vform.Validate()`进行表单验证
3. **用户体验**: 确保自定义Enter键行为符合用户预期
4. **键盘导航**: 考虑Tab键等其他键盘导航的影响

#### 适用场景

- **搜索表单**: Enter键触发搜索而不是提交
- **多步骤表单**: Enter键进入下一步而不是提交
- **实时验证**: Enter键触发特定字段的验证
- **自动保存**: Enter键触发自动保存功能

---

## Dialog 组件

### 打开对话框

```csharp
// 打开创建对话框
await WtmBlazor.OpenDialog<Create>(
    title: "创建项目",
    parameters: new Dictionary<string, object>
    {
        ["OnCloseDialog"] = new Func<DialogResult, Task>(async (result) =>
        {
            if (result == DialogResult.Yes)
            {
                // 对话框确认关闭后的处理
                await RefreshData();
            }
        })
    }
);
```

### 关闭对话框

```csharp
// 在对话框组件中
public void OnClose()
{
    CloseDialog(); // 取消关闭
}

public void OnSubmitSuccess()
{
    CloseDialog(DialogResult.Yes); // 确认关闭
}
```

---

## Toast 通知

### 基本用法

```csharp
// 成功通知
await WtmBlazor.Toast.Success("操作成功", "数据已保存");

// 错误通知
await WtmBlazor.Toast.Error("操作失败", "请检查输入数据");

// 信息通知
await WtmBlazor.Toast.Information("提示", "请注意相关事项");

// 警告通知
await WtmBlazor.Toast.Warning("警告", "此操作不可撤销");
```

---

## 最佳实践

### 1. 组件生命周期

```csharp
protected override async Task OnInitializedAsync()
{
    // 组件初始化逻辑
    await LoadData();
    await base.OnInitializedAsync();
}

protected override async Task OnAfterRenderAsync(bool firstRender)
{
    if (firstRender)
    {
        // 首次渲染后的逻辑
    }
    await base.OnAfterRenderAsync(firstRender);
}
```

### 2. 错误处理

```csharp
try
{
    // 业务逻辑
    await SomeOperation();
}
catch (Exception ex)
{
    await WtmBlazor.Toast.Error("操作失败", ex.Message);
    Console.WriteLine($"错误详情: {ex}");
}
```

### 3. 异步操作

```csharp
// 使用IsAsync属性处理异步按钮操作
<Button Color="Color.Primary" 
        Text="提交" 
        OnClick="SubmitAsync" 
        IsAsync="true" />

@code {
    private async Task SubmitAsync()
    {
        // 异步提交逻辑
        await Task.Delay(1000); // 模拟异步操作
    }
}
```

---

## 常见问题

### Q: Tab页面无法关闭？
**A**: 确保使用了`[CascadingParameter] Tab TabSet`并调用`await TabSet.CloseCurrentTab()`。

### Q: Table数据不显示？
**A**: 检查数据类型是否为`IEnumerable<T>`，而不是`List<T>`。

### Q: 表单验证不生效？
**A**: 确保Model类的属性有正确的验证特性，如`[Required]`、`[StringLength]`等。

### Q: Enter键仍然触发表单提交？
**A**: 检查是否正确添加了`@onkeydown="@OnKeyDown"`事件处理器，并在处理方法中调用了`e.PreventDefault()`。

### Q: 自定义Enter键处理后表单无法提交？
**A**: 如果使用方法2（移除OnValidSubmit），确保在自定义提交方法中调用了`vform.Validate()`进行表单验证。

### Q: 表格明细点击后不显示对应行的数据？
**A**: 检查`OnDetailsClick`方法中的逻辑，确保在判断`SelectedLot.ID == item.ID`之前没有提前设置`SelectedLot = item`。正确的做法是先判断是否为同一行，再决定是切换显示状态还是加载新数据。

### Q: Table组件Enter键焦点导航不工作？
**A**: 确保以下几点：
1. 添加了`@using Microsoft.JSInterop;`引用
2. 注入了`IJSRuntime`服务
3. 为每个输入框设置了唯一的`id`属性
4. 正确实现了`OnEnterAsync`事件处理
5. 添加了JavaScript的`focusElement`函数

### Q: 焦点导航时找不到目标元素？
**A**: 检查元素ID的命名规范是否正确，确保JavaScript函数中的多种查找策略都已实现。如果仍有问题，可以在浏览器开发者工具中检查DOM结构和元素ID。

### Q: 添加新行后焦点设置失败？
**A**: 在调用`OnAddAsync()`后必须添加`StateHasChanged()`和适当的延迟。正确的顺序是：
```csharp
await OnAddAsync();           // 添加新行
StateHasChanged();           // 强制重新渲染
await Task.Delay(100);       // 等待DOM更新
await FocusElement(elementId); // 设置焦点
```

### Q: 为什么必须调用StateHasChanged()？
**A**: Blazor的渲染机制是异步的，当`OnAddAsync()`将新行添加到数据集合后，对应的DOM元素可能还没有实际渲染到页面上。`StateHasChanged()`强制Blazor立即重新渲染组件，确保新行的DOM元素已经存在，这样JavaScript才能找到目标元素并成功设置焦点。

---

## 高级功能实现

### 表格行明细显示优化

#### 问题描述
在使用BootstrapBlazor Table组件时，点击某一行的明细按钮后可以正确显示明细表格，但再点击另外一行的明细按钮时，明细表格会隐藏，而不是显示新点击行的数据。

#### 解决方案

**核心问题**：在 `OnDetailsClick` 方法中，逻辑判断有误导致每次点击都会切换显示状态。

**修改前的错误逻辑**：
```csharp
private void OnDetailsClick(ProductInboundLot item)
{
    SelectedLot = item;  // 在开始就设置了选中项
    RollDetailList = item.RollList ?? new List<ProductInboundRoll>();

    if (SelectedLot is not null && SelectedLot.ID == item.ID)  // 这个条件永远为真
    {
        // 每次都会执行切换逻辑
        parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
        childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";
    }
    // else 分支永远不会执行
}
```

**修改后的正确逻辑**：
```csharp
private void OnDetailsClick(ProductInboundLot item)
{
    // 检查是否点击的是同一行
    if (SelectedLot is not null && SelectedLot.ID == item.ID)
    {
        // 点击同一行时，切换显示状态
        parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
        childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";
    }
    else
    {
        // 点击不同行时，显示新行的数据
        SelectedLot = item;
        RollDetailList = item.RollList ?? new List<ProductInboundRoll>();

        // 确保明细表格显示
        parentTableClass = "parent-table-compressed";
        childTableClass = "child-table-visible";
    }
    StateHasChanged();
}
```

**关键改进点**：
1. **先判断再赋值**：将 `SelectedLot = item` 移到 `else` 分支中
2. **明确的行为逻辑**：
   - 点击同一行：切换显示/隐藏
   - 点击不同行：显示新行的明细数据
3. **数据加载时机**：只在点击不同行时才重新加载数据

#### 适用场景
- 主从表格结构的明细显示
- 需要在同一页面显示多级数据的场景
- 任何需要动态切换显示内容的表格组件

---

### Table组件Enter键焦点导航,实现批量数据快速录入

#### 问题描述
在BootstrapBlazor Table组件的编辑模式下，用户希望在单元格填写完数据后按Enter键，焦点能够自动切换到当前单元格的下一行对应的单元格，提升数据录入效率。

#### 解决方案

##### 方案一：基于元素ID的导航方案

**技术架构**：
- 使用JavaScript Interop控制DOM焦点
- 为每个输入框分配唯一ID
- 通过行索引和列索引计算下一个焦点位置
- 利用BootstrapInput的OnEnterAsync事件

**实现步骤**：

**1. 添加必要的依赖**
```csharp
@using Microsoft.JSInterop;

[Inject]
private IJSRuntime JSRuntime { get; set; }
```

**2. 为输入框分配唯一ID和事件处理**
```razor
<TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center">
    <EditTemplate Context="v">
        <BootstrapInput @bind-Value="@v.Pcs" FormatString="0" IsSelectAllTextOnFocus
                      id="@($"lot-pcs-{DetailList.ToList().IndexOf(v)}")"
                      OnEnterAsync="@((val) => OnLotEnterAsync(v, "pcs"))"/>
    </EditTemplate>
</TableColumn>
```

**3. 实现Enter键事件处理逻辑**
```csharp
// Lot表格Enter键处理
private async Task OnLotEnterAsync(ProductInboundLot currentLot, string columnName)
{
    var lotList = DetailList.ToList();
    var currentIndex = lotList.IndexOf(currentLot);
    var nextIndex = currentIndex + 1;

    // 如果是最后一行，自动添加新行
    if (nextIndex >= lotList.Count)
    {
        await OnAddAsync(); // 添加新行
        StateHasChanged(); // 强制重新渲染，确保新行DOM元素已创建
        await Task.Delay(100); // 等待DOM更新完成
        nextIndex = lotList.Count; // 新行的索引
    }

    // 计算下一个焦点的元素ID
    var nextElementId = $"lot-{columnName}-{nextIndex}";
    await FocusElement(nextElementId);
}

// 通过JavaScript设置焦点
private async Task FocusElement(string elementId)
{
    try
    {
        await JSRuntime.InvokeVoidAsync("focusElement", elementId);
    }
    catch (Exception ex)
    {
        // 忽略焦点设置失败的错误，不影响主要功能
        System.Console.WriteLine($"Focus failed for element {elementId}: {ex.Message}");
    }
}
```

**4. 添加JavaScript焦点控制函数**
```html
<script>
    // 焦点导航JavaScript函数
    window.focusElement = function (elementId) {
        try {
            // 尝试通过ID查找元素
            let element = document.getElementById(elementId);

            if (!element) {
                // 如果通过ID找不到，尝试通过属性选择器查找
                element = document.querySelector(`[id="${elementId}"]`);
            }

            if (!element) {
                // 如果还是找不到，尝试查找input元素
                element = document.querySelector(`input[id="${elementId}"]`);
            }

            if (element) {
                // 设置焦点并选中文本
                element.focus();
                if (element.select) {
                    element.select();
                }
                return true;
            } else {
                console.warn(`Element with id '${elementId}' not found`);
                return false;
            }
        } catch (error) {
            console.error(`Error focusing element '${elementId}':`, error);
            return false;
        }
    };
</script>
```

**ID命名规范**：
- Lot表格：`lot-{columnName}-{rowIndex}`
- Roll表格：`roll-{columnName}-{rowIndex}`

**支持的列**：
- Lot表格：Pcs、Weight、Meters、Yards
- Roll表格：RollNo、Meters、Yards、Weight

##### 方案二：基于DOM结构的导航方案（推荐）

**技术架构**：
- 使用JavaScript Interop控制DOM焦点
- 基于DOM结构进行导航，无需为每个输入框分配唯一ID
- 通用的Enter键处理方法，支持多个表格
- 利用BootstrapInput的OnEnterAsync事件

**实现步骤**：

**1. 添加必要的依赖**
```csharp
@using Microsoft.JSInterop;

[Inject]
private IJSRuntime JSRuntime { get; set; }
```

**2. 为输入框添加Enter键事件处理**
```razor
<TableColumn @bind-Field="@context.LotNo" Align="Alignment.Center">
    <EditTemplate Context="v">
        <BootstrapInput @bind-Value="@v.LotNo" FormatString="0" IsSelectAllTextOnFocus
                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
    </EditTemplate>
</TableColumn>
<TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center">
    <EditTemplate Context="v">
        <BootstrapInput @bind-Value="@v.Pcs" FormatString="0" IsSelectAllTextOnFocus
                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
    </EditTemplate>
</TableColumn>
<!-- 其他列类似配置 -->
```

**3. 实现通用Enter键处理逻辑**
```csharp
// 通用Enter键处理方法 - 提取重复代码
private async Task HandleEnterKeyAsync(string tableType, Func<Task> addMethod)
{
    try
    {
        var flagVariable = $"window.blazorAddNew{tableType}Row";

        // 先尝试使用JavaScript导航到下一行
        await JSRuntime.InvokeVoidAsync("eval", $@"
            (function() {{
                const activeElement = document.activeElement;
                if (activeElement && activeElement.tagName === 'INPUT') {{
                    const result = window.navigateToNextRow(activeElement);
                    if (!result) {{
                        // 如果导航失败，触发添加新行
                        {flagVariable} = true;
                    }}
                }}
            }})();
        ");

        // 检查是否需要添加新行
        var needNewRow = await JSRuntime.InvokeAsync<bool>("eval", $"{flagVariable} || false");
        if (needNewRow)
        {
            await JSRuntime.InvokeVoidAsync("eval", $"{flagVariable} = false;");
            await addMethod(); // 调用传入的添加方法
            StateHasChanged(); // 强制重新渲染
            await Task.Delay(30); // 等待DOM更新完成

            // 再次尝试导航
            await JSRuntime.InvokeVoidAsync("eval", @"
                (function() {
                    const activeElement = document.activeElement;
                    if (activeElement && activeElement.tagName === 'INPUT') {
                        window.navigateToNextRow(activeElement);
                    }
                })();
            ");
        }
    }
    catch (Exception ex)
    {
        // 如果JavaScript导航失败，回退到添加新行
        System.Console.WriteLine($"Enter navigation failed for {tableType}: {ex.Message}");
        await addMethod();
        StateHasChanged();
    }
}

// Lot表格Enter键处理 - 调用通用方法
private async Task OnLotEnterAsync()
{
    await HandleEnterKeyAsync("Lot", OnAddAsync);
}

// Roll表格Enter键处理 - 调用通用方法
private async Task OnRollEnterAsync()
{
    await HandleEnterKeyAsync("Roll", OnAddRollAsync);
}
```

**4. 添加JavaScript DOM导航函数**
```html
<script>
    // Enter键导航到下一行 - 简化版本，不依赖ID
    window.navigateToNextRow = function (currentInput) {
        try {
            const td = currentInput.closest('td');
            const tr = td.parentNode;
            const nextRow = tr.nextElementSibling;

            if (nextRow) {
                // 获取当前单元格在行中的索引
                const cells = [...tr.children];
                const currentIndex = cells.indexOf(td);
                const nextRowCells = [...nextRow.children];

                // 在下一行的相同列位置查找输入框
                if (nextRowCells[currentIndex]) {
                    const nextInput = nextRowCells[currentIndex].querySelector('input');
                    if (nextInput && !nextInput.disabled && !nextInput.readOnly) {
                        nextInput.focus();
                        if (nextInput.select) {
                            nextInput.select();
                        }
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            console.error('Error navigating to next row:', error);
            return false;
        }
    };
</script>
```

**支持的表格类型**：
- Lot表格：LotNo、Pcs、Weight、Meters、Yards、Location、Remark
- Roll表格：RollNo、Weight、Meters、Yards、Remark

#### 方案对比

| 特性 | 方案一：基于ID导航 | 方案二：基于DOM结构导航（推荐） |
|------|-------------------|----------------------|
| **实现复杂度** | 中等，需要维护元素ID | 简单，无需维护ID |
| **代码维护性** | 一般，ID命名需要规范 | 好，通用方法复用性高 |
| **性能** | 好，直接通过ID查找 | 好，DOM遍历开销小 |
| **灵活性** | 一般，依赖ID命名规范 | 高，基于DOM结构自适应 |
| **扩展性** | 需要为每个表格定制 | 通用方法，易于扩展 |
| **容错性** | 一般，ID不存在时失败 | 好，多层错误处理 |

#### 性能深度分析

##### 判断是否添加新行的性能对比

**方案一：基于C#集合操作判断**
```csharp
var lotList = DetailList.ToList();           // O(n) - 创建新List
var currentIndex = lotList.IndexOf(currentLot); // O(n) - 线性搜索
var nextIndex = currentIndex + 1;
if (nextIndex >= lotList.Count)              // O(1) - 直接比较
```

**方案二：基于JavaScript DOM导航判断**
```javascript
// JavaScript中判断是否需要添加新行
const result = window.navigateToNextRow(activeElement);
if (!result) {
    // 如果导航失败（没有下一行），触发添加新行
    window.blazorAddNewLotRow = true;
}
```

##### 性能对比表

| 方面 | 方案一（C#集合操作） | 方案二（JavaScript DOM导航） |
|------|---------------------|---------------------------|
| **时间复杂度** | O(n) + O(n) = O(2n) | O(1) - 只检查DOM结构 |
| **内存开销** | 创建新List副本 | 无额外内存分配 |
| **网络开销** | 无 | JavaScript Interop调用 |
| **数据量敏感性** | 随行数线性增长 | 与行数无关 |
| **小数据量（<20行）** | 差异不明显 | 略快 |
| **中等数据量（20-100行）** | 性能下降明显 | 性能稳定 |
| **大数据量（>100行）** | 性能问题严重 | 性能优秀 |

##### 实际性能测试场景

```csharp
// 假设不同行数的性能开销：

// 10行数据
DetailList.ToList()           // 遍历10个元素
lotList.IndexOf(currentLot)   // 平均搜索5个元素
// 总操作：约15次元素访问

// 100行数据
DetailList.ToList()           // 遍历100个元素
lotList.IndexOf(currentLot)   // 平均搜索50个元素
// 总操作：约150次元素访问

// 1000行数据
DetailList.ToList()           // 遍历1000个元素
lotList.IndexOf(currentLot)   // 平均搜索500个元素
// 总操作：约1500次元素访问

// 而JavaScript DOM导航始终只需要1次DOM操作
```

##### 性能优化建议

如果必须使用方案一，可以考虑以下优化：

```csharp
// 优化版本：避免重复的ToList()调用
private int GetCurrentLotIndex(ProductInboundLot currentLot)
{
    // 如果DetailList本身就是List<T>，直接使用IndexOf
    if (DetailList is List<ProductInboundLot> list)
    {
        return list.IndexOf(currentLot);
    }

    // 否则手动查找，避免创建副本
    int index = 0;
    foreach (var lot in DetailList)
    {
        if (lot == currentLot) return index;
        index++;
    }
    return -1;
}

// 使用优化版本
var currentIndex = GetCurrentLotIndex(currentLot);
if (currentIndex >= 0 && currentIndex == DetailList.Count() - 1)
{
    // 是最后一行，需要添加新行
}
```

**结论**：方案二不仅在代码简洁性上更优，在性能上也明显优于方案一，特别是在数据量较大的情况下。

**共同功能特性**：
- ✅ Enter键自动跳转到下一行同列
- ✅ 最后一行自动添加新行
- ✅ 支持多个表格（Lot表格和Roll表格）
- ✅ 智能焦点定位和文本选中
- ✅ 错误处理，不影响主要功能

**方案二的技术优势**：
- **简化实现**：不需要为每个输入框分配唯一ID
- **DOM结构导航**：通过父子关系查找目标元素，更加灵活
- **通用性强**：一套代码可以处理多个表格的Enter键导航
- **容错性好**：多层错误处理，确保功能稳定性
- **维护成本低**：无需维护复杂的ID命名规范

#### 推荐使用方案

**强烈推荐使用方案二（基于DOM结构导航）**，原因如下：

##### 代码质量优势
1. **实现更简洁**：无需为每个输入框维护唯一ID
2. **代码复用性更高**：通用的`HandleEnterKeyAsync`方法
3. **维护成本更低**：不依赖ID命名规范
4. **扩展性更好**：新增表格时无需修改导航逻辑

##### 性能优势
5. **时间复杂度优势**：O(1) vs O(2n)，在大数据量时性能差异显著
6. **内存效率**：无需创建List副本，减少GC压力
7. **响应速度**：DOM导航比集合搜索更快，用户体验更好

##### 实践验证
8. **实际项目验证**：已在TEX项目的InboundEditTemplate.razor中成功应用
9. **稳定性验证**：经过实际业务场景测试，功能稳定可靠

##### 适用场景建议
- **小型表格（<20行）**：两种方案差异不大，推荐方案二（代码更简洁）
- **中型表格（20-100行）**：强烈推荐方案二（性能优势明显）
- **大型表格（>100行）**：必须使用方案二（方案一性能不可接受）

#### 适用场景
- 大量数据录入的表格
- 需要快速连续输入的场景
- 提升用户体验的数据编辑界面
- 任何需要键盘导航的表格组件

#### 注意事项

##### 通用注意事项（两个方案都适用）

1. **StateHasChanged()的重要性**：在添加新行后必须调用`StateHasChanged()`强制重新渲染
   - **原因**：Blazor的渲染是异步的，`OnAddAsync()`添加新行到数据集合后，DOM元素可能还没有实际创建
   - **后果**：如果不调用`StateHasChanged()`，JavaScript尝试设置焦点时找不到目标元素，导致焦点设置失败
   - **解决方案**：在`OnAddAsync()`后立即调用`StateHasChanged()`，然后等待DOM更新完成

2. **命名空间冲突**：注意Console的命名空间冲突，使用`System.Console`

3. **错误处理**：焦点设置失败不应影响主要业务逻辑

##### 方案一特定注意事项

1. **ID命名规范**：必须严格遵循ID命名规范，确保唯一性
2. **索引计算**：使用`DetailList.ToList().IndexOf(v)`计算索引时要注意性能
3. **DOM更新时序**：
   ```csharp
   await OnAddAsync();           // 添加新行到数据集合
   StateHasChanged();           // 强制重新渲染UI
   await Task.Delay(100);       // 等待DOM更新完成
   await FocusElement(elementId); // 设置焦点
   ```

##### 方案二特定注意事项

1. **DOM结构依赖**：依赖于标准的Table DOM结构（tr > td > input），如果表格结构发生变化需要相应调整JavaScript代码

2. **DOM更新时序**：正确的执行顺序应该是：
   ```csharp
   await addMethod();           // 添加新行到数据集合
   StateHasChanged();          // 强制重新渲染UI
   await Task.Delay(30);       // 等待DOM更新完成（时间可以更短）
   // JavaScript自动尝试导航到新行
   ```

3. **通用性考虑**：`HandleEnterKeyAsync`方法设计为通用方法，可以轻松扩展到其他表格

4. **JavaScript容错**：JavaScript导航失败时会自动回退到添加新行，确保功能的可用性

---

## 参考资源

- [BootstrapBlazor 官方文档](https://www.blazor.zone/)
- [BootstrapBlazor GitHub](https://github.com/dotnetcore/BootstrapBlazor)
- [TEX项目组件使用示例](../ProductInboundBill_TabClose_Final_Solution.md)
