# BootstrapBlazor Tab组件使用指南

## 概述

BootstrapBlazor的Tab组件是多标签页管理的核心组件，广泛用于后台管理系统中实现多页面切换功能。本文档详细介绍Tab组件的使用方法和最佳实践。

## 基本概念

### Tab组件层次结构
```
Layout (包含Tab组件)
├── Tab 1 (页面组件)
├── Tab 2 (页面组件)  
└── Tab 3 (页面组件) ← 当前活动Tab
```

### 级联参数机制
BootstrapBlazor使用Blazor的级联参数（CascadingParameter）机制，让子组件能够访问父级的Tab组件实例。

## 关闭当前Tab页面

### 方法一：使用级联参数（推荐）

这是**最标准、最可靠**的方法：

```csharp
[CascadingParameter]
public Tab TabSet { get; set; }

public async Task OnClose()
{
    if (TabSet != null)
    {
        await TabSet.CloseCurrentTab();
    }
    else
    {
        // 备用方案：当不在Tab环境中时
        Navigation.NavigateTo("/SomeListPage");
    }
}
```

### 完整实现示例

```razor
@page "/Finished/ProductInboundBill/Create"
@inherits BasePage

<div class="container-fluid">
    <h3>创建入库单</h3>
    
    <EditForm Model="@Model" OnValidSubmit="@Submit">
        <div class="row g-3">
            <div class="col-12 col-sm-6">
                <BootstrapInput @bind-Value="@Model.BillNo" DisplayText="单据号" />
            </div>
            <!-- 更多表单字段 -->
        </div>
    </EditForm>
</div>

<div class="modal-footer table-modal-footer">
    <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
</div>

@code {
    [CascadingParameter]
    public Tab TabSet { get; set; }
    
    private ProductInboundBillVM Model = new ProductInboundBillVM();
    private bool isClosing = false; // 防重复调用标志
    
    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    public async Task OnClose()
    {
        if (isClosing) return;
        
        try
        {
            isClosing = true;
            
            if (OnCloseDialog != null)
            {
                // Dialog模式：使用传统的CloseDialog方法
                CloseDialog();
            }
            else if (TabSet != null)
            {
                // Tab模式：使用BootstrapBlazor的Tab关闭方法
                await TabSet.CloseCurrentTab();
            }
            else
            {
                // 备用方案：导航到列表页面
                Navigation.NavigateTo("/Finished/ProductInboundBill");
            }
        }
        finally
        {
            await Task.Delay(500); // 防止快速重复点击
            isClosing = false;
        }
    }
    
    /// <summary>
    /// 表单提交处理
    /// </summary>
    private async Task Submit(EditContext context)
    {
        try
        {
            // 执行提交逻辑
            var success = await SubmitData();
            
            if (success)
            {
                await WtmBlazor.Toast.Success(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.OprationSuccess"]);
                
                // 提交成功后关闭页面
                if (OnCloseDialog != null)
                {
                    CloseDialog(DialogResult.Yes);
                }
                else if (TabSet != null)
                {
                    await TabSet.CloseCurrentTab();
                }
                else
                {
                    Navigation.NavigateTo("/Finished/ProductInboundBill");
                }
            }
        }
        catch (Exception ex)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], ex.Message);
        }
    }
    
    private async Task<bool> SubmitData()
    {
        // 实际的数据提交逻辑
        // 返回true表示成功，false表示失败
        return true;
    }
}
```

## 双模式支持模式

### 设计理念

为了让页面组件既能在Dialog中使用，也能在Tab中使用，我们采用双模式支持：

```csharp
public async Task HandlePageClose()
{
    if (OnCloseDialog != null)
    {
        // Dialog模式：页面在对话框中打开
        CloseDialog();
    }
    else if (TabSet != null)
    {
        // Tab模式：页面在Tab中打开
        await TabSet.CloseCurrentTab();
    }
    else
    {
        // 独立页面模式：直接导航
        Navigation.NavigateTo("/ListPage");
    }
}
```

### 模式检测逻辑

| 参数状态 | 模式 | 关闭方法 |
|---------|------|----------|
| `OnCloseDialog != null` | Dialog模式 | `CloseDialog()` |
| `TabSet != null` | Tab模式 | `await TabSet.CloseCurrentTab()` |
| 两者都为null | 独立页面模式 | `Navigation.NavigateTo()` |

## 最佳实践

### 1. 防重复调用

```csharp
private bool isClosing = false;

public async Task OnClose()
{
    if (isClosing) return; // 防止重复调用
    
    try
    {
        isClosing = true;
        // 关闭逻辑...
    }
    finally
    {
        await Task.Delay(500);
        isClosing = false;
    }
}
```

### 2. 异常处理

```csharp
public async Task OnClose()
{
    try
    {
        if (TabSet != null)
        {
            await TabSet.CloseCurrentTab();
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"关闭Tab失败: {ex.Message}");
        // 降级为导航方式
        Navigation.NavigateTo("/ListPage");
    }
}
```

### 3. 用户确认

```csharp
public async Task OnClose()
{
    // 如果有未保存的更改，询问用户
    if (HasUnsavedChanges())
    {
        var confirmed = await WtmBlazor.Confirm("确认关闭", "有未保存的更改，确定要关闭吗？");
        if (!confirmed) return;
    }
    
    if (TabSet != null)
    {
        await TabSet.CloseCurrentTab();
    }
}
```

## 常见问题与解决方案

### Q1: TabSet为null怎么办？

**原因**: 页面不在Tab环境中运行，或者级联参数配置错误。

**解决方案**:
```csharp
if (TabSet != null)
{
    await TabSet.CloseCurrentTab();
}
else
{
    // 备用方案
    Navigation.NavigateTo("/ListPage");
}
```

### Q2: 关闭Tab后页面仍然存在？

**原因**: 可能是Tab组件版本问题或者调用方法不正确。

**解决方案**:
1. 确保使用最新版本的BootstrapBlazor
2. 检查`CloseCurrentTab()`方法是否正确调用
3. 查看浏览器控制台是否有错误信息

### Q3: 如何关闭指定的Tab？

```csharp
// 关闭指定URL的Tab
await TabSet.CloseTab("/Finished/ProductInboundBill/Edit/123");

// 关闭指定索引的Tab
await TabSet.CloseTab(2);
```

### Q4: 如何在关闭前执行清理操作？

```csharp
public async Task OnClose()
{
    // 执行清理操作
    await CleanupResources();
    
    // 保存草稿
    await SaveDraft();
    
    // 关闭Tab
    if (TabSet != null)
    {
        await TabSet.CloseCurrentTab();
    }
}
```

## Tab组件其他常用方法

### 添加新Tab

```csharp
await TabSet.AddTab(new Dictionary<string, object>
{
    ["Text"] = "新标签页",
    ["Url"] = "/NewPage",
    ["Icon"] = "fa fa-plus"
});
```

### 激活指定Tab

```csharp
await TabSet.ActiveTab("/SomePage");
```

### 获取当前活动Tab信息

```csharp
var activeTab = TabSet.ActiveTabItem;
if (activeTab != null)
{
    Console.WriteLine($"当前Tab: {activeTab.Text}");
}
```

## 总结

使用`[CascadingParameter] Tab TabSet`和`await TabSet.CloseCurrentTab()`是关闭BootstrapBlazor Tab页面的标准方法。结合双模式支持和适当的错误处理，可以创建既灵活又可靠的页面组件。

### 关键要点

1. ✅ **使用级联参数获取Tab实例**
2. ✅ **调用CloseCurrentTab()方法关闭**
3. ✅ **提供备用方案处理非Tab环境**
4. ✅ **添加防重复调用机制**
5. ✅ **适当的异常处理**

这种方法已在TEX项目的ProductInboundBill模块中成功应用，可以作为其他页面组件的参考模板。
