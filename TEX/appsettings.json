{
  "Logging": {
    "Console": {
      "IncludeScopes": true,
      "LogLevel": {
        "Default": "Information",
        "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
        "BootstrapBlazor.Localization": "Warning",
        "Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware": "Warning",
        "Microsoft.AspNetCore.Routing.EndpointMiddleware": "Warning",
        "System.Net.Http.HttpClient.ApiClient.LogicalHandler": "Warning",
        "Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor": "Warning",
        "System.Net.Http.HttpClient.ApiClient.ClientHandler": "Warning",
        "Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker": "Warning",
        "Microsoft.AspNetCore.Mvc": "Warning"
      }
    },
    "Debug": {
      "IncludeScopes": true,
      "LogLevel": {
        "Default": "Information",
        "Microsoft.AspNetCore": "Warning",
        "BootstrapBlazor.Localization": "Warning",
        "System.Net.Http.HttpClient.github": "Warning",
        "System.Net.Http.HttpClient.ApiClient.LogicalHandler": "Warning"
      }
    },
    "WTM": {
      "LogLevel": {
        "Default": "Debug"
      }
    }
  },
  "Connections": [
    {
      "Key": "default",
      //"Value": "Server=(localdb)\\mssqllocaldb;Database=TEX_db3;Trusted_Connection=True;",
      "DbContext": "DataContext",
      //"DBType": "SqlServer", //DataBase, you can choose mysql,sqlserver,pgsql,sqlite,oracle
      "Value": "Server=192.168.2.10;User ID=root;Password=**************;Database=tex_db6",
      "DBType": "mysql"
    }
  ],
  "CookiePre": "TEX", //cookie prefix
  "IsQuickDebug": true, //is debug mode
  "EnableTenant": true, //是否启动多租户
  "DetailedErrors": true,
  "CorsOptions": {
    "EnableAll": true
  },
  "ErrorHandler": "/_Framework/Error",
  "Languages": "zh,en",
  "BlazorMode": "server", // server or wasm
  "UIOptions": {
    "DataTable": {
      "RPP": 20, //default records per page for all datagrid
      "ShowPrint": true,
      "ShowFilter": true
    },
    "ComboBox": {
      "DefaultEnableSearch": true
    },
    "DateTime": {
      "DefaultReadonly": true
    },
    "SearchPanel": {
      "DefaultExpand": false
    }
  },
  "PageMode": "Tab", //display mode，Single or Tab
  "TabMode": "Simple", //Tab mode，Default or Simple
  "IsFilePublic": true, //Can download or view attachment file without login
  "FileUploadOptions": {
    "UploadLimit": 2097152000,
    "SaveFileMode": "Database", //上传文件的保存方式，可选Database,local,oss
    "Settings": {
      "local": [
        {
          "GroupName": "default",
          "GroupLocation": ""
        }
      ],
      "oss": [
        {
          "GroupName": "default",
          "GroupLocation": "wtmimg",
          "ServerUrl": "",
          "Key": "",
          "Secret": ""
        }
      ]
    }
  },
  "JwtOptions": {
    "Issuer": "http://localhost",
    "Audience": "http://localhost",
    "Expires": 36000,
    "SecurityKey": "superSecretKey@345",
    "RefreshTokenExpires": 86400,
    "LoginPath": "/_Framework/Redirect401"
  },
  "CookieOptions": {
    "Issuer": "http://localhost",
    "Audience": "http://localhost",
    "Domain": "",
    "Expires": 36000,
    "SlidingExpiration": true,
    "SecurityKey": "superSecretKey@345",
    "RefreshTokenExpires": 86400,
    "LoginPath": "/Login/Login"
  },
  "Domains": {
    "server": { //Blazor Server模式下页面调用接口的内网地址
      "Address": "http://localhost:27110"
    },
    "serverpub": { //Blazor Server模式下页面调用接口的外网地址，可为空，为空表示api和页面部署在同一地址下
      //"Address": "http://localhost:27110"
    },
    "mainhost": {
      // "Address": "https://localhost:5001"
    },
    "github": {
      "Address": "https://api.github.com"
    }
  },
  "AppSettings": {
    "key1": "value1",
    "key2": "value2"
  }
}
