
/// <summary>
///@date : 2023-12-6
///@desc : 半成品流转单明细 - W_I_P_Detail
/// </summary>
namespace TEX.Model.Models;

//[Table("WIPDetails)]
[Display(Name = "_WIPDetail")]
public class WIPDetail : BasePoco, IPersistPoco, ITenant,  IRemark
{
    public WIP WIP { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    public Guid WIPId { get; set; }

    public OrderDetail OrderDetail { get; set; }
    //[Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OrderDetail")]
    [Comment("订单明细")]
    public Guid OrderDetailId { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Color")]
    [Comment("颜色")]
    public string Color { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_LotNo")]
    [Comment("缸号")]
    public string LotNo { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Pcs")]
    [Comment("匹数")]
    public int Pcs { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Display(Name = "_Remark")]
    [Comment("备注")]
    public string Remark { get; set; }


    [Display(Name = "_Isvalid")]
    [Comment("是否有效")]
    public bool IsValid { get; set; } = true;
    public string TenantCode { get; set; }
}