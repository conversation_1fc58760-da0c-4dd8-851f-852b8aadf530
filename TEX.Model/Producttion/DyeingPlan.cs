
/// <summary>
///@date : 2023-12-4
///@desc : 染整计划单 - DYEING_PLAN
/// </summary>
namespace TEX.Model.Models;

//[Table("DyeingPlans)]
[Display(Name = "_DyeingPlan")]
public class DyeingPlan : BasePoco, IPersistPoco, ITenant, IAudited, IRemark
{

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_CreateDate")]
    [Comment("日期")]
    public DateTime CreateDate { get; set; }= DateTime.Now;


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_BillNo")]
    [Comment("计划单号")]
    public String BillNo { get; set; }= DateTime.Now.ToString("yMMddHHmmssf");


    [Display(Name = "_Version")]
    [Comment("版本")]
    public int Version { get; set; } = 1;

    [JsonIgnore]
    [Display(Name = "_FinishingFactory")]
    [Comment("染整厂")]
    public Company FinishingFactory { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_FinishingFactory")]
    [Comment("染整厂")]
    public Guid FinishingFactoryId { get; set; }

    
    public PurchaseOrder POrder { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_POrderId")]
    [Comment("订单号")]
    public Guid POrderId { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_PlanBatch")]
    [Comment("计划批次")]
    public string PlanBatch { get; set; }


    [Display(Name = "_FinishingProcess")]
    [Comment("染整工艺")]
    public string FinishingProcess { get; set; }


    //[Display(Name = "_Fabric")]
    //[Comment("品名")]
    //public Product Fabric { get; set; }
    //[Display(Name = "_FabricId")]
    //[Required(ErrorMessage = "Validate.{0}required")]
    //[Comment("品名")]
    //public Guid FabricId { get; set; }


    [Display(Name = "_Width")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Range(100, 350, ErrorMessage = "Validate.{0}range{1}{2}")]
    [Comment("有效门幅")]
    public int Width { get; set; }


    [Display(Name = "_GSM")]
    [Range(1, 2000, ErrorMessage = "Validate.{0}range{1}{2}")]
    [Comment("克重")]
    public int GSM { get; set; }


    [Display(Name = "_Light")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("主光源")]
    public LightEnum Light { get; set; } = LightEnum.D65;


    [Display(Name = "_Light2")]
    [Comment("副光源")]
    public LightEnum? Light2 { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("匹数")]
    public int Pcs { get; set; }


    [Display(Name = "_Qty")]
    [Precision(18, 1)]
    [Comment("数量")]
    public decimal TotalQty { get; set; }

    [Display(Name = "_TotalMeters")]
    [Precision(18, 1)]
    public decimal TotalMeters { get; set; }

    [Display(Name = "_TotalWeight")]
    [Precision(18, 1)]
    public decimal TotalWeight { get; set; }


    [Display(Name = "_GreigeBatch")]
    [Comment("坯布批次")]
    public string GreigeBatch { get; set; }

    [JsonIgnore]
    public Company GreigeVender { get; set; }
    [Display(Name = "_GreigeVender")]
    [Comment("坯布厂")]
    public Guid? GreigeVenderId { get; set; }

    public List<PlanDetail> DetailList { get; set; }


    [Display(Name = "_DyingDemand")]
    [Comment("染整要求")]
    public string DyeingDemand { get; set; }


    [Display(Name = "_PackDemand")]
    [Comment("包装要求")]
    public string PackDemand { get; set; }


    [Display(Name = "_AdditionalDemend")]
    [Comment("其他要求")]
    public string AdditionalDemend { get; set; }


    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;


    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }


    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Isvalid")]
    [Comment("是否有效")]
    public bool IsValid { get; set; } = true;


    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    public string TenantCode { get; set; }


    [Display(Name = "_Admin.Remark")]
    [Comment("备注")]
    public string Remark { get; set; }

}