
/// <summary>
///@date : 2023-12-6
///@desc : 半成品 - W_I_P
/// </summary>
namespace TEX.Model.Models;

//[Table("WIPs)]
[Display(Name = "_WIP")]
public class WIP : BasePoco, IPersistPoco, ITenant, IAudited, IRemark
{
    [Display(Name = "_BillNo")]
    [Comment("单号")]
    public string BillNo { get; set; }=DateTime.Now.ToString("yMMddHHmmssf");


    [Display(Name = "_CreateDate")]
    [Comment("日期")]
    public DateTime CreateDate { get; set; } = DateTime.Now;


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_POrderId")]
    [Comment("订单号")]
    public Guid POrderId { get; set; }
    public PurchaseOrder POrder { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Shipper")]
    [Comment("发货方")]
    public Guid ShipperId { get; set; }
    public Company Shipper { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Receiver")]
    [Comment("收货方")]
    public Guid ReceiverId { get; set; }
    public Company Receiver { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("匹数")]
    public int Pcs { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Procedure")]
    [Comment("工序")]
    public ProcedureEnum Procedure { get; set; }


    [Display(Name = "_FinishingProcess")]
    [Comment("后整工艺")]
    public string FinishingProcess { get; set; }

    public List<WIPDetail> DetailList { get; set; }

    [Display(Name = "_Remark")]
    [Comment("备注")]
    public string Remark { get; set; }

    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;


    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }


    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }

    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    public string TenantCode { get; set; }


    [Display(Name = "_Isvalid")]
    [Comment("是否有效")]
    public bool IsValid { get; set; } = true;

}