using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TEX.Model.Producttion;

public partial class DyeingPlanDTO
{

    public DateTime CreateDate { get; set; }

    public string BillNo { get; set; }
    public int Version { get; set; }

    public string FinishingFactory { get; set; }

    public string OrderNO { get; set; }

    public string ProductName { get; set; }
    public string PlanBatch { get; set; }

    //public string FinishingProcess { get; set; }


    public int Width { get; set; }

    public int GSM { get; set; }

    public string Light { get; set; }

    public string Light2 { get; set; }

    public string GreigeBatch { get; set; }

    public string GreigeVender { get; set; }

    public int Pcs { get; set; }

    public decimal TotalQty { get; set; }

    public decimal TotalMeters { get; set; }

    public decimal TotalWeight { get; set; }

    public string DyeingDemand { get; set; }

    public string PackDemand { get; set; }

    public List<PlanDetailDTO> PlanDetails { get; set; }

    //public string AdditionalDemend { get; set; }

    //public string AuditStatus { get; set; }

    public string AuditedBy { get; set; }
    public string CreateBy { get; set; }

    //public string AuditedComment { get; set; }

    public string Remark { get; set; }
}

public partial class PlanDetailDTO
{

    public string Color { get; set; }

    public string ColorCode { get; set; }

    public int Pcs { get; set; }
    public decimal Meters { get; set; }
    public decimal Qty { get; set; }
    public decimal Weight { get; set; }
    public string QtyUnit { get; set; }

    public DateTime? DeliveryDate { get; set; }

    //public decimal FinishingPrice { get; set; }

    public string GreigeBatch { get; set; }

    public string Remark { get; set; }


}
