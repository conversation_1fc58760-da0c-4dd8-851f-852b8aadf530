
/// <summary>
///@date : 2023-12-4
///@desc : 计划明细 - PLAN_Detail
/// </summary>
namespace TEX.Model.Models;

//[Table("PlanDetails)]
[Display(Name = "_PlanDetail")]
public class PlanDetail : BasePoco, IPersistPoco, ITenant,  IRemark
{
    [JsonIgnore]
    public DyeingPlan DyeingPlan { get; set; }
    [Display(Name = "_DyeingPlan")]
    [Comment("染色计划")]
    public Guid DyeingPlanId { get; set; }

    [JsonIgnore]
    public OrderDetail OrderDetail { get; set; }
    [Display(Name = "_Color")]
    [Comment("染色计划")]
    public Guid OrderDetailId { get; set; }

    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Color")]
    [Comment("颜色")]
    public string Color { get; set; }

    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]

    [Display(Name = "_ColorCode")]
    [Comment("色号")]
    public string ColorCode { get; set; }



    [Display(Name = "_Pcs")]
    [Comment("匹数")]
    public int Pcs { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Precision(18, 1)]
    [Display(Name = "_Qty")]
    [Comment("数量")]
    public decimal Qty { get; set; }

    [Display(Name = "_Meters")]
    [Precision(18, 1)]
    public decimal Meters { get; set; }

    [Display(Name = "_Weight")]
    [Precision(18, 1)]
    public decimal Weight { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_QtyUnit")]
    public AccountingUnitEnum QtyUnit { get; set; } = AccountingUnitEnum.M;



    [Display(Name = "_DeliveryDate")]
    [Comment("交期")]
    public DateTime? DeliveryDate { get; set; }


    [Precision(18, 2)]
    [Display(Name = "_FinishingPrice")]
    [Comment("单价")]
    public decimal FinishingPrice { get; set; }


    //public Company GreigeVender { get; set; }
    //[Display(Name = "_GreigeVender")]
    //[Comment("坯布厂")]
    //public Guid? GreigeVenderId { get; set; }


    [Display(Name = "_GreigeBatch")]
    [Comment("坯布批次")]
    public string GreigeBatch { get; set; }

    [Display(Name = "_DyeingProcess")]
    [Comment("工艺")]
    public string DyeingProcess { get; set; }

    public List<LotAllocate> LotAllocateList { get; set; }

    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }

}