
/// <summary>
///@date : 2023-12-6
///@desc : 配缸记录 - Lot_Allocate
/// </summary>
namespace TEX.Model.Models;

//[Table("LotAllocates)]
[Display(Name = "_LotAllocate")]
public class LotAllocate : BasePoco, IPersistPoco, ITenant, IRemark
{

    [Display(Name = "_PlanDetailId")]
    public Guid PlanDetailId { get; set; }
    public PlanDetail PlanDetail { get; set; }


    [Display(Name = "_Date")]
    [Comment("日期")]
    public DateTime CreateDate { get; set; }=DateTime.Now;


    [Display(Name = "_ProductName")]
    [Comment("品种")]
    public string ProductName { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_Color")]
    [Comment("颜色")]
    public string Color { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_ColorCode")]
    [Comment("色号")]
    public string ColorCode { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_LotNo")]
    [Comment("缸号")]
    public string LotNo { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("匹数")]
    public int Pcs { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }



    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }

}