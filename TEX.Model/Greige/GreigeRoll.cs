
namespace TEX.Model.Models;
///<summary>
///@date : 2025-7-25
///@desc : 坯布 - GreigeRoll
/// </summary>

//[Table("GreigeRolls)]
[Display(Name = "_GreigeRoll")]
public class GreigeRoll : BasePoco,  ITenant
{
    
    //[Required(ErrorMessage = "Validate.{0}required")]
    //[Display(Name = "_ID")]  
    //public new int  ID { get; set; }
    
    //Roll可能拆分,所以有ParentId
    public Guid? ParentId { get; set; }
    public virtual GreigeRoll Parent { get; set; }

    //单品查询用
    public Guid ProductId { get; set; }
    public virtual Product Product { get; set; }

    public Guid KnittingPlanId { get; set; }
    public virtual KnittingPlan KnittingPlan { get; set; }

    // 出入库单关联
    [Display(Name = "入库单")]
    [Comment("入库单")]
    public Guid? InboundBillId { get; set; }
    public virtual GreigeInboundBill InboundBill { get; set; }

    [Display(Name = "出库单")]
    [Comment("出库单")]
    public Guid? OutboundBillId { get; set; }
    public virtual GreigeOutboundBill OutboundBill { get; set; }

    // Roll操作类型
    [Display(Name = "操作类型")]
    [Comment("操作类型")]
    public RollOperationEnum OperationType { get; set; } = RollOperationEnum.Original;

    [Display(Name = "操作备注")]
    [StringLength(200, ErrorMessage = "Validate.{0}StringMax{1}")]
    [Comment("操作备注")]
    public string OperationRemark { get; set; }

    
    [StringLength(64, ErrorMessage = "Validate.{0}StringMax{1}")]
     
    [Display(Name = "_BatchNo")]
    [Comment("批号")]    
    public string BatchNo { get; set; }
    
    [StringLength(64, ErrorMessage = "Validate.{0}StringMax{1}")]
     
    [Display(Name = "_MachineId")]
    [Comment("机台号")]    
    public string MachineId { get; set; }
    
     
    [Display(Name = "_RollNo")]
    [Comment("卷号")]    
    public int RollNo { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_InboundWeight")]
    [Comment("重量")]    
    public decimal InboundWeight { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_InboundMeters")]
    [Comment("米数")]    
    public decimal InboundMeters { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_InboundYards")]
    [Comment("码数")]    
    public decimal InboundYards { get; set; }
    
    [StringLength(64, ErrorMessage = "Validate.{0}StringMax{1}")]
     
    [Display(Name = "_InboundBillNo")]
    [Comment("入库单号")]    
    public string InboundBillNo { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_QcWeight")]
    [Comment("检验重量")]    
    public decimal QcWeight { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_QcMeters")]
    [Comment("检验米数")]    
    public decimal QcMeters { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_QcYards")]
    [Comment("检验码数")]    
    public decimal QcYards { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_OutboundWeight")]
    [Comment("出库重量")]    
    public decimal OutboundWeight { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_OutboundMeters")]
    [Comment("出库米数")]    
    public decimal OutboundMeters { get; set; }
    
    
    [Precision(18,1)] 
    [Display(Name = "_OutboundYards")]
    [Comment("出库码数")]    
    public decimal OutboundYards { get; set; }
    
    [StringLength(64, ErrorMessage = "Validate.{0}StringMax{1}")]
     
    [Display(Name = "_OutboundBillNo")]
    [Comment("出库单号")]    
    public string OutboundBillNo { get; set; }
    
    
     
    [Display(Name = "_Grade")]
    [Comment("等级")]    
    public string Grade { get; set; }
    
    
     
    [Display(Name = "_Worker")]
    [Comment("挡车工")]    
    public string Worker { get; set; }
    
    
     
    [Display(Name = "_Inspector")]
    [Comment("检验人")]    
    public string Inspector { get; set; }
    
    
     
    [Display(Name = "_Status")]
    [Comment("状态")]    
    public string Status { get; set; }

    public string TenantCode { get; set; }
}

/// <summary>
/// Roll操作类型枚举
/// </summary>
public enum RollOperationEnum
{
    [Display(Name = "原始")]
    Original = 0,    // 原始Roll

    [Display(Name = "拆分")]
    Split = 1,       // 拆分产生

    [Display(Name = "合并")]
    Merged = 2       // 合并产生
}
