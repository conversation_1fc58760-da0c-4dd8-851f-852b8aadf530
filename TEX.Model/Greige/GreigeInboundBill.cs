namespace TEX.Model.Models;

/// <summary>
/// 坯布入库单
/// </summary>
[Table("GreigeInboundBills")]
[Display(Name = "坯布入库单")]
public class GreigeInboundBill : BasePoco, IPersistPoco, ITenant
{
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_BillNo")]
    [Comment("入库单号")]
    [StringLength(64, ErrorMessage = "Validate.{0}StringMax{1}")]
    public string BillNo { get; set; } = DateTime.Now.ToString("GI-yyMMddHHmmssf");

    [Display(Name = "入库日期")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("入库日期")]
    public DateTime InboundDate { get; set; } = DateTime.Now;

    [Display(Name = "_KnittingFactory")]
    [Comment("织造厂")]
    public Guid? KnittingFactoryId { get; set; }
    public virtual Company KnittingFactory { get; set; }

    [Display(Name = "_Warehouse")]
    [StringLength(64, ErrorMessage = "Validate.{0}StringMax{1}")]
    [Comment("仓库")]
    public string Warehouse { get; set; }
    

    // 统计字段
    [Display(Name = "总卷数")]
    [Comment("总卷数")]
    public int TotalRolls { get; set; }

    [Display(Name = "总重量")]
    [Precision(18, 1)]
    [Comment("总重量")]
    public decimal TotalWeight { get; set; }

    [Display(Name = "总米数")]
    [Precision(18, 1)]
    [Comment("总米数")]
    public decimal TotalMeters { get; set; }

    [Display(Name = "总码数")]
    [Precision(18, 1)]
    [Comment("总码数")]
    public decimal TotalYards { get; set; }

    // 导航属性
    public virtual List<GreigeRoll> RollList { get; set; }

    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;

    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }

    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }

    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;

    [Display(Name = "_TenantCode")]
    [StringLength(50, ErrorMessage = "Validate.{0}StringMax{1}")]
    public string TenantCode { get; set; }

    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
}
