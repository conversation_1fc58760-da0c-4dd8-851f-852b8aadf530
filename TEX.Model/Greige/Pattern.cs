using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


/// <summary>
///@date : 2025-5-27
///@desc : 订单花型 - Pattern
/// </summary>
namespace TEX.Model.Models;


public class Pattern : BasePoco, IPersistPoco, ITenant
{

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_CreateDate")]
    public DateTime CreateDate { get; set; }=DateTime.Now;


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_PictureName")]
    public string PatternName { get; set; }

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_CodeNo")]
    public int CodeNo { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Customer")]
    public string Customer { get; set; }



    [Display(Name = "_Description")]
    public string Description { get; set; }



    [Display(Name = "_Requirements")]
    public string Requirements { get; set; }



    [Display(Name = "_Remark")]
    public string Remark { get; set; }

    public List<PatternDetail> DetailList { get; set; }
    public List<PatternPic> Images { get; set; }


    [Display(Name = "_Tenantcode")]
    public string TenantCode { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Isvalid")]
    public bool IsValid { get; set; }

}

public class PatternPic : TopBasePoco, ISubFile
{
    public Guid PatternId { get; set; }
    public Pattern Pattern { get; set; }
    public Guid FileId { get; set; }
    public FileAttachment File { get; set; }
    public int Order { get; set; }
}