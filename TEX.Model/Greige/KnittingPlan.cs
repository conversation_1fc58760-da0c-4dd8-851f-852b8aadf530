namespace TEX.Model.Models;

/// <summary>
/// 织造计划
/// </summary>
[Table("KnittingPlans")]
[Display(Name = "织造计划")]
public class KnittingPlan : BasePoco, IPersistPoco, ITenant, IRemark
{
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_BillNo")]
    [Comment("计划单号")]
    public string BillNo { get; set; } = DateTime.Now.ToString("yyMMddHHmmssf");

    [Display(Name = "日期")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("日期")]
    public DateTime PlanDate { get; set; }= DateTime.Now;

    [Display(Name = "_PurchaseOrder")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("订单")]
    public Guid? PurchaseOrderId { get; set; }
    public virtual PurchaseOrder PurchaseOrder { get; set; }

    [Display(Name = "_KnittingFactory")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("织造厂")]
    public Guid? KnittingFactoryId { get; set; }
    public virtual Company KnittingFactory { get; set; }

    [Display(Name = "品种")]
    [StringLength(100, ErrorMessage = "Validate.{0}StringMax{1}")]
    [Comment("品种")]
    public string ProductName { get; set; }

    [Display(Name = "_KnittingProcess")]
    [Comment("织造工艺")]
    public Guid? KnittingProcessId { get; set; }
    public virtual KnittingProcess KnittingProcess { get; set; }

    [Display(Name = "匹数")]
    [Comment("匹数")]
    public int? PieceCount { get; set; }

    [Display(Name = "重量")]
    [Precision(18, 2)]
    [Comment("总重量(KG)")]
    public decimal? TotalWeight { get; set; }

    [Display(Name = "单匹重")]
    [Precision(18, 2)]
    [Comment("单匹重(KG)")]
    public decimal? WeightPerPiece { get; set; }

    [Display(Name = "批次")]
    [StringLength(50, ErrorMessage = "Validate.{0}StringMax{1}")]
    [Comment("批次")]
    public string BatchNo { get; set; } = string.Empty;

    [Display(Name = "交期")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("交期")]
    public DateTime? DeliveryDate { get; set; }

    [Display(Name = "要求")]
    [Comment("特殊要求")]
    public string Requirements { get; set; } = string.Empty;

    [Display(Name = "完成状态")]
    public PlanStatusEnum? Status { get; set; } = PlanStatusEnum.Confirmed;

    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;

    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }

    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;

    [Display(Name = "_TenantCode")]
    [StringLength(50, ErrorMessage = "Validate.{0}StringMax{1}")]
    public string TenantCode { get; set; }

    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; } = string.Empty;
}

public enum PlanStatusEnum
{
    [Display(Name = "未开始")]
    Confirmed = 1,

    [Display(Name = "进行中")]
    InProgress = 2,

    [Display(Name = "已完成")]
    Completed = 3,

    [Display(Name = "已取消")]
    Cancelled = 4
}