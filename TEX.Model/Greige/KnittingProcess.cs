using System.Text.Json;

namespace TEX.Model.Models;

/// <summary>
/// 织造工艺
/// </summary>
[Table("KnittingProcesses")]
[Display(Name = "织造工艺")]
public class KnittingProcess : BasePoco, IPersistPoco, ITenant, IRemark
{

    [Display(Name = "工艺编码")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("工艺编码")]
    public string ProcessCode { get; set; }

    [Display(Name = "_Category")]
    [Comment("品种类别")]
    public DictItem Category { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Category")]
    public int CategoryId { get; set; }

    [Display(Name = "机型信息")]
    [Comment("机型信息(JSON)")]
    public string MachineInfoJson { get; set; }

    [NotMapped]
    public MachineInfo MachineInfo
    {
        get => string.IsNullOrEmpty(MachineInfoJson)
            ? new MachineInfo()
            : JsonSerializer.Deserialize<MachineInfo>(MachineInfoJson);
        set => MachineInfoJson = JsonSerializer.Serialize(value);
    }

    [Display(Name = "下机克重")]
    [Comment("下机克重")]
    public int? Weight { get; set; }

    [Display(Name = "下机门幅")]
    [Comment("下机门幅")]
    public int? Width { get; set; }

    [Display(Name = "下机毛高")]
    [Comment("下机毛高")]
    public int? PileLength { get; set; }

    [Display(Name = "成品克重")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("成品克重")]
    public int? FinishedWeight { get; set; }

    [Display(Name = "成品门幅")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("成品门幅")]
    public int? FinishedWidth { get; set; }

    [Display(Name = "成品毛高")]
    [Comment("成品毛高")]
    public int? FinishedPileHeight { get; set; }


    [Display(Name = "纱支信息")]
    [Comment("纱支信息(JSON)")]
    public string YarnInfoJson { get; set; }

    [NotMapped]
    public List<YarnInfo> YarnInfoList
    {
        get => string.IsNullOrEmpty(YarnInfoJson) 
            ? [] 
            : JsonSerializer.Deserialize<List<YarnInfo>>(YarnInfoJson);
        set => YarnInfoJson = JsonSerializer.Serialize(value);
    }

    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;

    [Display(Name = "_TenantCode")]
    [StringLength(50, ErrorMessage = "Validate.{0}StringMax{1}")]
    public string TenantCode { get; set; }

    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
}

/// <summary>
/// 机台信息
/// </summary>
public class MachineInfo
{
    [Display(Name = "机型")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public MachineTypeEnum MachineType { get; set; } = MachineTypeEnum.NormalMachine;

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "机台尺寸")]
    public int MachineDiameter { get; set; } = 30;

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "针距")]
    public int Gauge { get; set; } = 16;

    [Display(Name = "针排数")]
    public int NeedleRows { get; set; } = 2;

    [Display(Name = "排针方式")]
    public string NeedleArrangement { get; set; } 

    [Display(Name = "总针数")]
    public int TotalNeedles { get; set; }

    [Display(Name = "总路数")]
    public int TotalCourses { get; set; }

    [Display(Name = "提花工位")]
    public int JacquadBit { get; set; }

    //[JsonIgnore]
    //[Display(Name = "机型信息")]
    //public string ShowText
    //{
    //    get => $"{MachineType}-{MachineDiameter}寸 {Gauge}针";
    //    set => _showText = value;
    //}
    //private string _showText;
}

/// <summary>
/// 纱支信息
/// </summary>
public class YarnInfo
{
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "纱支种类")]
    public YarnType YarnType { get; set; }= YarnType.FDY;

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "纱支规格")]
    public YarnSpecEnum YarnSpec { get; set; } = YarnSpecEnum.T100D48F;

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "根数")]
    public int? YarnCount { get; set; } = 1;

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "用途")]
    public YarnUsageEnum YarnUsage { get; set; } = YarnUsageEnum.BottomYarn;

    [Display(Name = "纱长")]
    public string YarnLength { get; set; } = string.Empty;

    [Display(Name = "比例")]
    [Precision(18, 2)]
    public decimal ConsumptionRatio { get; set; }

    [Display(Name = "批号")]
    public string YarnBatchNo { get; set; } = string.Empty;

    [Display(Name = "_Remark")]
    public string Remark { get; set; } = string.Empty;
}