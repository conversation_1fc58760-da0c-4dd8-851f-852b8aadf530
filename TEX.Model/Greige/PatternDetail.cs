using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

/// <summary>
///@date : 2025-5-27
///@desc : 提花稿 - Patterns
/// </summary>
namespace TEX.Model.Models;

//[Table("Patternss)]
[Display(Name = "_Patterns")]
public class PatternDetail : BasePoco, IPersistPoco, ITenant
{
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_CreateDate")]
    public DateTime CreateDate { get; set; } = DateTime.Now;

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Pattern")]
    public Guid PatternId { get; set; }
    public Pattern Pattern { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_PatternCode")]
    public int PatternCode { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_PatternVersion")]
    public int PatternVersion { get; set; }



    [Display(Name = "_FabricCategory")]
    public string FabricCategory { get; set; }



    [Display(Name = "_PatternRepeatWidth")]
    public double? PatternRepeatWidth { get; set; }



    [Display(Name = "_PatternRepeatHeight")]
    public double? PatternRepeatHeight { get; set; }


    
    [Display(Name = "_RequiredFullWidth")]
    public int? RequiredFullWidth { get; set; }


    
    [Display(Name = "_RequiredGsm")]
    public int? RequiredGsm { get; set; }



    [Display(Name = "_RequiredCuttableWidth")]
    public int? RequiredCuttableWidth { get; set; }



    [Display(Name = "_MachineInch")]
    public int? MachineInch { get; set; }



    [Display(Name = "_MachineTotalNeedles")]
    public int? MachineTotalNeedles { get; set; }



    [Display(Name = "_MachineSpec")]
    public string MachineSpec { get; set; }



    [Display(Name = "_JacquardFeed")]
    public int? JacquardFeed { get; set; }



    [Display(Name = "_PatternWeftPoint")]
    public int? PatternWeftPoint { get; set; }



    [Display(Name = "_PatternWarpPoint")]
    public int? PatternWarpPoint { get; set; }



    [Display(Name = "_GreigeRepeatWidth")]
    public int? GreigeRepeatWidth { get; set; }



    [Display(Name = "_GreigeRepeatHeight")]
    public int? GreigeRepeatHeight { get; set; }



    [Display(Name = "_GreigeWidth")]
    public int? GreigeWidth { get; set; }



    [Display(Name = "_GreigeGsm")]
    public int? GreigeGsm { get; set; }



    [Display(Name = "_FabricRepeatWidth")]
    public int? FabricRepeatWidth { get; set; }



    [Display(Name = "_FabricRepeatHeight")]
    public int? FabricRepeatHeight { get; set; }



    [Display(Name = "_FabricFullWidth")]
    public int? FabricFullWidth { get; set; }



    [Display(Name = "_FabricGsm")]
    public int? FabricGsm { get; set; }



    [Display(Name = "_VersionModified")]
    public string  VersionModified { get; set; }

    public List<PatternDetailPic> Images { get; set; }

    [Display(Name = "_Remark")]
    public string Remark { get; set; }

    public string TenantCode { get; set; }
    public bool IsValid { get; set; }

}


public class PatternDetailPic : TopBasePoco, ISubFile
{
    public Guid PatternDetailId { get; set; }
    public PatternDetail PatternDetail { get; set; }
    public Guid FileId { get; set; }
    public FileAttachment File { get; set; }
    public int Order { get; set; }
}