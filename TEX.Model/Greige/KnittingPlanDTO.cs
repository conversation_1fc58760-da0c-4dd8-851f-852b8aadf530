using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JsonIgnoreAttribute = System.Text.Json.Serialization.JsonIgnoreAttribute;


namespace TEX.Model
{
    public partial class KnittingPlanDTO
    {
        public string BillNo { get; set; }
        public string OrderNo { get; set; }
        public string ProductName { get; set; }
        public string KnittingFactory { get; set; }
        public DateTime PlanDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public long? PieceCount { get; set; }
        public double? TotalWeight { get; set; }
        public double? WeightPerPiece { get; set; }
        public string Requirements { get; set; }
        public string BatchNo { get; set; }
        public string CreateBy { get; set; }
        public string Status { get; set; } 
        public string Remark { get; set; }
        public string ProcessCode { get; set; }
        public int? FinishedWeight { get; set; }
        public int? FinishedWidth { get; set; }
        public int? FinishedPileHeight { get; set; }

        public int? GreigeWeight { get; set; }
        public int? GreigeWidth { get; set; }
        public int? GreigePileHeight { get; set; }

        public string MachineInfoJson { get; set; }
        public string YarnInfoJson { get; set; }

        //public string MachineType { get; set; } = "普通大圆机";
        //public int MachineDiameter { get; set; }
        //public int Gauge { get; set; }
        //public int? TotalCourses { get; set; }
        //public int? NeedleRows { get; set; }
        //public string NeedleArrangement { get; set; }
        //public int? TotalNeedles { get; set; }
        //public int? JacquadBit { get; set; }
    }
    public partial class YarnInfoDTO
    {
        public string YarnType { get; set; } = string.Empty;
        public string YarnSpec { get; set; } = string.Empty;
        public string YarnUsage { get; set; } = string.Empty;
        public int YarnCount { get; set; } = 1;
        public string YarnLength { get; set; } = string.Empty;
        public decimal ConsumptionRatio { get; set; } = 0;
        public string YarnBatchNo { get; set; } = string.Empty;
        public string Remark { get; set; } = string.Empty;
    }

    public partial class MachineInfoDTO
    {
        public string MachineType { get; set; } = "普通大圆机";
        public int MachineDiameter { get; set; }
        public int Gauge { get; set; }
        public int TotalCourses { get; set; }
        public int NeedleRows { get; set; }
        public string NeedleArrangement { get; set; } = string.Empty;
        public int TotalNeedles { get; set; }
        public int JacquadBit { get; set; }
    }
}
