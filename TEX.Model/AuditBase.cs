namespace TEX.Model.Models;

//框架多级继承将失去软删除功能,提取成接口
public interface IAudited
{
    [Display(Name = "_Model._AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; }


    [Display(Name = "_Model._AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }


    [Display(Name = "_Model._AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }
}

public interface IRemark
{

    [Display(Name = "_Remark")]
    [Comment("备注")]
    public string Remark { get; set; }

}

//框架多级继承将失去软删除功能,提取成接口
public class AuditBase:PersistTenantBase
{
    [Display(Name = "_Model._AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;


    [Display(Name = "_Model._AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }


    [Display(Name = "_Model._AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }
}

public class PersistTenantBase : BasePoco, IPersistPoco, ITenant
{

    [Display(Name = "_Remark")]
    [Comment("备注")]
    public string Remark { get; set; }

    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; }

    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
}

