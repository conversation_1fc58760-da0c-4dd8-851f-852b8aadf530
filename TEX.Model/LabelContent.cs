using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace TEX.Model.Models;

public  class LabelContent
{
    [DisplayName("订单号")]
    public string 订单号 { get; set; }
    [DisplayName("款号")]
    public string 款号 { get; set; }
    [DisplayName("品名")]
    public string 品名 { get; set; }
    [DisplayName("规格")]
    public string 规格 { get; set; }
    [DisplayName("颜色")]
    public string 颜色 { get; set; }
    [DisplayName("色号")]
    public string 色号 { get; set; }
    [DisplayName("缸号")]
    public string 缸号 { get; set; }
    [DisplayName("卷号")]
    public string 卷号 { get; set; }
    [DisplayName("米数")]
    public Decimal  米数 { get; set; }
    [DisplayName("码数")]
    public Decimal 码数 { get; set; }
    [DisplayName("净重")]
    public Decimal 净重 { get; set; }
    [DisplayName("毛重")]
    public Decimal 毛重 { get; set; }
    [DisplayName("备注")]
    public string 备注 { get; set; }
    [DisplayName("日期")]
    public string 日期 { get; set; }
    [DisplayName("克重")]
    public int 克重 { get; set; }
    [DisplayName("有效门幅")]
    public int 有效门幅 { get; set; }
    [DisplayName("计划数")]
    public string 计划数 { get; set; }
    [DisplayName("备用1")]
    public string Plus1 { get; set; }
    [DisplayName("备用2")]
    public string Plus2 { get; set; }
    [DisplayName("备用3")]
    public string Plus3 { get; set; }

    // 字典存储属性是否显示的状态
    //private Dictionary<string, bool> propertyVisibility = new Dictionary<string, bool>();

    //public bool IsVisible(string propertyName)
    //{
    //    // 默认情况下，所有属性都是不可见的
    //    if (!propertyVisibility.ContainsKey(propertyName))
    //    {
    //        return false;
    //    }
    //    return propertyVisibility[propertyName];
    //}

    //public void SetVisibility(string propertyName, bool isVisible)
    //{
    //    propertyVisibility[propertyName] = isVisible;
    //}

    // 用于数据库的属性，存储 JSON 字符串
    //public string PropertyVisibilityJson
    //{
    //    get { return JsonConvert.SerializeObject(propertyVisibility); }
    //    set { propertyVisibility = JsonConvert.DeserializeObject<Dictionary<string, bool>>(value); }
    //}
}
