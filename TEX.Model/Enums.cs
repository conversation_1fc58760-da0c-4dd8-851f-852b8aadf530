namespace TEX.Model.Models;
using Microsoft.Extensions.Localization;
using System.Globalization;
using System.Threading;
using System.Resources;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

public enum InboundTypeEnum
{
    [Display(Name = "采购入库")]
    Puchase,
    [Display(Name = "退货入库")]
    Return,
    [Display(Name = "换货入库")]
    Exchange,
    [Display(Name = "盘点报溢")]
    StockTaking,
    [Display(Name = "期初库存")]
    Init
}
public enum InboundStatusEnum
{
    [Display(Name = "入库")]
    Inbound,
    [Display(Name = "全部出库")]
    Outbound,
    [Display(Name = "部分出库")]
    PartOutbound
}
public enum DeliveryTypeEnum
{
    [Display(Name = "快递")]
    Express,
    [Display(Name = "物流")]
    Logistics,
    [Display(Name = "专车直送")]
    On_Demand_Courier,
    [Display(Name = "海运")]
    Sea,
    [Display(Name = "空运")]
    Air,
}
public enum FabricCategoryEnum
{
    [Display(Name = "_Enum._FabricCategoryEnum._Taffeta")]
    Taffeta,
    [Display(Name = "_Enum._FabricCategoryEnum._NylonTaffeta")]
    NylonTaffeta,
    [Display(Name = "_Enum._FabricCategoryEnum._Pongee")]
    Pongee,
    [Display(Name = "_Enum._FabricCategoryEnum._TwoWayStretch")]
    TwoWayStretch,
    [Display(Name = "_Enum._FabricCategoryEnum._OneWayStretch")]
    OneWayStretch,
    [Display(Name = "_Enum._FabricCategoryEnum._Oxford")]
    Oxford,
    [Display(Name = "_Enum._FabricCategoryEnum._Taslan")]
    Taslan,
    [Display(Name = "_Enum._FabricCategoryEnum._Fakecotton")]
    Fakecotton,
    [Display(Name = "_Enum._FabricCategoryEnum._MicroFiber")]
    MicroFiber,
    [Display(Name = "_Enum._FabricCategoryEnum._ImitationSilk")]
    ImitationSilk,
    [Display(Name = "_Enum._FabricCategoryEnum._NPTaffeta")]
    NPTaffeta,
    [Display(Name = "_Enum._FabricCategoryEnum._Faille")]
    Faille,
    [Display(Name = "_Enum._FabricCategoryEnum._MicroSuede")]
    MicroSuede,
    [Display(Name = "_Enum._FabricCategoryEnum._Satin")]
    Satin,
    [Display(Name = "_Enum._FabricCategoryEnum._Gabardine")]
    Gabardine,
    [Display(Name = "_Enum._FabricCategoryEnum._CalvaryTwill")]
    CalvaryTwill,
    [Display(Name = "_Enum._FabricCategoryEnum._Others")]
    Others
}
public enum OrderTypeEnum
{
    [Display(Name = "_Enum._OrderTypeEnum._Greige")]
    Greige,
    [Display(Name = "_Enum._OrderTypeEnum._Knitting")]
    Knitting,
    [Display(Name = "_Enum._OrderTypeEnum._Fabric")]
    Fabric
}
public enum LightEnum
{
    [Display(Name = "_Enum._LightEnum._D65")]
    D65 = 1,
    [Display(Name = "_Enum._LightEnum._LED")]
    LED = 2,
    [Display(Name = "_Enum._LightEnum._TL84")]
    TL84 = 4,
    [Display(Name = "_Enum._LightEnum._TL83")]
    TL83 = 8,
    [Display(Name = "_Enum._LightEnum._U3000")]
    U3000 = 16,
    [Display(Name = "_Enum._LightEnum._A")]
    A = 32,
    [Display(Name = "_Enum._LightEnum._CWF")]
    CWF = 64,
    [Display(Name = "_Enum._LightEnum._D65_LED")]
    D65_LED = 128,
    [Display(Name = "_Enum._LightEnum._DayLight")]
    DayLight = 256
}
public enum AccountingUnitEnum
{
    [Display(Name = "_Enum._AccountingUnitEnum._M")]
    M,
    [Display(Name = "_Enum._AccountingUnitEnum._KG")]
    KG,
    [Display(Name = "_Enum._AccountingUnitEnum._Y")]
    Y
}
public enum CurrencyEnum
{
    [Display(Name = "_Enum._CurrencyEnum._CNY")]
    CNY,
    [Display(Name = "_Enum._CurrencyEnum._USD")]
    USD,
    [Display(Name = "_Enum._CurrencyEnum._GBP")]
    GBP,
    [Display(Name = "_Enum._CurrencyEnum._HKD")]
    HKD
}
public enum AuditStatusEnum
{
    [Display(Name = "_Enum._AuditStatusEnum._NotAudited")]
    NotAudited,
    [Display(Name = "_Enum._AuditStatusEnum._AuditedApproved")]
    AuditedApproved,
    [Display(Name = "_Enum._AuditStatusEnum._AuditedFailed")]
    AuditedFailed,
    [Display(Name = "_Enum._AuditStatusEnum._Cancelled")]
    Cancelled,
    [Display(Name = "_Enum._AuditStatusEnum._Completed")]
    Completed,
    [Display(Name = "Deleted")]
    Deleted
}
public enum RelationshipEnum
{
    [Display(Name = "_Enum._RelationshipEnum._Customer")]
    Customer,
    [Display(Name = "_Enum._RelationshipEnum._Vender")]
    Vender
}
public enum CompanyTypeEnum
{
    [Display(Name = "_Enum._CompanyTypeEnum._TradingCompany")]
    TradingCompany,
    [Display(Name = "_Enum._CompanyTypeEnum._GarmentFactory")]
    GarmentFactory,
    [Display(Name = "_Enum._CompanyTypeEnum._DyeingFactory")]
    DyeingFactory,
    [Display(Name = "_Enum._CompanyTypeEnum._FinishingFactory")]
    FinishingFactory,
    [Display(Name = "_Enum._CompanyTypeEnum._WovenFactory")]
    WovenFactory,
    [Display(Name = "_KnittingFactory")]
    KnittingFactory,
    [Display(Name = "_Enum._CompanyTypeEnum._GreigeVender")]
    GreigeVender,
    [Display(Name = "_Enum._CompanyTypeEnum._FabricVender")]
    FabricVender
}

public enum ProcedureEnum
{
    [Display(Name = "染色检验")]
    Dyeing = 1,
    [Display(Name = "印花")]
    Printing = 2,
    [Display(Name = "涂层")]
    Coating = 4,
    [Display(Name = "轧光")]
    Cire = 8,
    [Display(Name = "烫金")]
    Foil = 16,//Bronzing
    [Display(Name = "水洗")]
    Washing = 32,
    [Display(Name = "贴合")]
    Laminate = 64,
    [Display(Name = "贴膜")]
    StickingFilm = 128,
    [Display(Name = "预缩")]
    Shrinking = 256,
    [Display(Name = "热定型")]
    HeatSetting = 512,
    [Display(Name = "复合")]
    Compound = 1024,
    [Display(Name = "磨毛")]
    Sanding = 2048,
    [Display(Name = "PVC压延")]
    PvcCalender = 4096,
    [Display(Name = "起绒")]
    FleeceFinish = 8192,
    [Display(Name = "柔软")]
    Mellow = 16384,
    [Display(Name = "针织织造")]
    Knitting = 32768,
    [Display(Name = "坯布")]
    Greige = 65536,
    [Display(Name = "成品检验")]
    Inspection = 131072,
}
public enum FabricDefectEnum
{
    横档,
    破洞,
    拼匹,
    纬弧,
    纬斜,
    脏污,
    折痕,
    色花,
    断经,
    断纬,
    擦伤,
    色点,
    色纱,
    异纤,
    竖条,
    掉边,
    其他
}

public enum CompletedStatusEnum
{
    [Display(Name = "_StandBy")]
    StandBy,
    [Display(Name = "_InProcess")]
    InProcess,
    [Display(Name = "_Completed")]
    Completed,
    [Display(Name = "_Cancelled")]
    Cancelled
}

/// <summary>
/// 纱支用途
/// </summary>
public enum MachineTypeEnum
{
    [Display(Name = "普通大圆机")]
    NormalMachine = 1,

    [Display(Name = "提花机")]
    JacquardMachine = 2,

    [Display(Name = "开幅机")]
    OpeningMachine = 3,

    [Display(Name = "其他")]
    Other = 4
}

/// <summary>
/// 纱支用途
/// </summary>
public enum YarnUsageEnum
{
    [Display(Name = "面丝")]
    SurfaceYarn = 1,

    [Display(Name = "连接丝")]
    ConnectionYarn = 2,

    [Display(Name = "底丝")]
    BottomYarn = 3,

    [Display(Name = "其他")]
    Other = 4
}

/// <summary>
/// 纱支品种
/// </summary>
public enum YarnType
{
    [Display(Name = "FDY")]
    FDY = 1,

    [Display(Name = "DTY")]
    DTY = 2,

    [Display(Name = "DTY(S+Z)")]
    SZ = 3,
    [Display(Name = "扁平丝")]
    FlatFilament = 6,

    [Display(Name = "三角丝")]
    TriangleYarn = 7,

    [Display(Name = "异形丝")]
    SpecialYarn = 8,

    [Display(Name = "圆孔丝")]
    CircularHoleBrightYarn = 8,

    [Display(Name = "其他")]
    Others = 9,

    [Display(Name = "PA6")]
    PA6 = 20,

    [Display(Name = "PA66")]
    PA66 = 21,

    [Display(Name = "POY")]
    POY = 19,

}

/// <summary>
/// 纱支规格
/// </summary>
public enum YarnSpecEnum
{
    T100D36F  = 1,
    T100D48F  = 2,
    T100D72F  = 3,
    T100D144F = 4,
    T100D288F = 5,
    T150D36F  = 11,
    T150D48F  = 12,
    T150D72F  = 13,
    T150D144F = 14,
    T150D288F = 15,
    T300D36F  = 21,
    T300D48F  = 22,
    T300D72F  = 23,
    T300D144F = 24,
    T300D288F = 25,
    T300D576F = 26,
    T200D36F  = 31,
    T200D48F  = 32,
    T200D72F  = 33,
    T200D144F = 34,
    T200D288F = 35,
    
    
}
public class RefDicNameAttribute : Attribute
{
    public string Name { get; set; }
}

// 扩展方法用于获取枚举的Display名称
public static class EnumExtensions
{
    private static IStringLocalizer _localizer;

    public static void Configure(IStringLocalizer localizer)
    {
        _localizer = localizer;
    }

    public static string GetDisplayName(this Enum enumValue)
    {
        var enumType = enumValue.GetType();
        var enumValueString = enumValue.ToString();
        var memberInfo = enumType.GetField(enumValueString);

        if (memberInfo == null)
            return enumValueString;

        // 1. 尝试获取多语言配置
        var resourceKey = $"_Enum.{enumType.Name}.{enumValueString}";
        if (_localizer != null)
        {
            var localizedValue = _localizer[resourceKey];
            if (!localizedValue.ResourceNotFound && !string.IsNullOrEmpty(localizedValue.Value))
            {
                return localizedValue.Value;
            }
        }

        // 2. 尝试获取Display特性
        var displayAttribute = memberInfo.GetCustomAttributes(typeof(DisplayAttribute), false)
            .FirstOrDefault() as DisplayAttribute;
        if (displayAttribute != null && !string.IsNullOrEmpty(displayAttribute.Name))
        {
            return displayAttribute.Name;
        }

        // 3. 返回枚举值的原始字符串
        return enumValueString;
    }
}