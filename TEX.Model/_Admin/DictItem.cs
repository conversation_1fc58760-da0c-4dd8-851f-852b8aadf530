
using System.ComponentModel;

namespace TEX.Model.Models;

[Display(Name = "_DictField")]
public class DictItem:TopBasePoco,ITenant
{
    [Key]
    [Required(ErrorMessage = "Validate.{0}required")]
    public new int ID { get; set; }

    [Display(Name = "_DictType")]
    public int DictTypeId { get; set; }
    public DictType DictType { get; set; }

    [Display(Name= "_DictItemName")]
    public string ItemName { get; set; }

    [Display(Name = "_Description")]
    public string Description { get; set; }

    [Display(Name = "_DictOrder")]
    public int DictOrder { get; set; } = 100;

    [Display(Name = "_Remark")]
    public string Remark { get; set; }
    public string TenantCode { get; set; }
}
