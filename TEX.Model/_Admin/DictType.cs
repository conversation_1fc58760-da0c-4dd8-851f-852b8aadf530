namespace TEX.Model.Models;

[Display(Name = "_DictType")]
public class DictType : TopBasePoco,ITenant
{
    [Key]
    [Required(ErrorMessage = "Validate.{0}required")]
    public new int ID { get; set; }

    [Display(Name = "_DictName")]
    public string Name { get; set; }

    [Display(Name = "_Description")]
    public string Description { get; set; }

    [Display(Name = "_DictOrder")]
    public int DictOrder { get; set; }

    [InverseProperty ("DictType")]
    public List<DictItem> DictItems { get; set; }
    public string TenantCode { get; set; }
}
