using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TEX.Model.Finished;

public class InspectPlan : BasePoco, IPersistPoco, ITenant, IRemark
{
    [Display(Name = "_CreateDate")]
    public DateTime CreateDate { get; set; }=DateTime.Now;
    public OrderDetail OrderDetail { get; set; }
    [Display(Name = "_OrderDetail")]
    public Guid OrderDetailId { get; set; }

    [Display(Name = "_PlanNo")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string PlanNo { get; set; }=DateTime.Now.ToString("yyMMddHHmmssf");

    [Display(Name = "_Batch")]
    public string PlanBatch { get; set; }

    [Display(Name = "_InspectionStandard")]
    public InspectionStandardEnum InspectionStandard { get; set; }=InspectionStandardEnum.FourPoint;
    [Display(Name = "_PlanQty")]
    public int PlanQty { get; set; }
    [Display(Name = "_Unit")]
    public AccountingUnitEnum QtyUnit { get; set; } = AccountingUnitEnum.M;
    [Display(Name = "_InspectStatus")]
    public InspectStatusEnum InspectStatus { get; set; }=InspectStatusEnum.Todo;

    [Display(Name = "_PlanFinishedDate")]
    public DateTime? PlanFinishDate { get; set; }
    [Display(Name = "_Remark")]
    public string Remark { get; set; }
    public string LabelContent { get; set; }
    public string TenantCode { get; set; }
    public bool IsValid { get; set; }
}


public enum InspectionStandardEnum
{
    [Display(Name = "美标四分制")]
    FourPoint,
    [Display(Name = "国标十分制")]
    TenPoint,
    [Display(Name = "其他")]
    Other
}

public enum InspectStatusEnum
{
    
    [Display(Name = "未检验")]
    Todo,
    [Display(Name = "已完成")]
    Finished
}

public class InspectPlanDto
{
    public Guid ID { get; set; }
    [Display(Name = "_CreateDate")]
    public DateTime CreateDate { get; set; }
    public Guid OrderDetailId { get; set; }
    [Display(Name = "_PlanNo")]
    public string PlanNo { get; set; }
    [Display(Name = "_Customer")]
    public String Customer { get; set; }
    [Display(Name = "_OrderNo")]
    public string OrderNo { get; set; }
    [Display(Name = "_ProductName")]
    public string ProductName { get; set; }
    [Display(Name = "_ProductSpec")]
    public string ProductSpec { get; set; }
    [Display(Name = "_Color")]
    public String Color_view { get; set; }
    [Display(Name = "_ColorCode")]
    public string ColorCode { get; set; }
    [Display(Name = "_GSM")]
    public int GSM { get; set; }
    [Display(Name = "_Width")]
    public int Width { get; set; }
    [Display(Name = "_Batch")]
    public string PlanBatch { get; set; }
    [Display(Name = "_InspectionStandard")]
    public InspectionStandardEnum InspectionStandard { get; set; }
    [Display(Name = "_PlanQty")]
    public int PlanQty { get; set; }
    [Display(Name = "_Unit")]
    public AccountingUnitEnum QtyUnit { get; set; }
    [Display(Name = "_InspectStatus")]
    public InspectStatusEnum InspectStatus { get; set; }
    [Display(Name = "_PlanFinishedDate")]
    public DateTime? PlanFinishDate { get; set; }
    [Display(Name = "_Remark")]
    public string Remark { get; set; }
    public string LabelContent { get; set; }
}