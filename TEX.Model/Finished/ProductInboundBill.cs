
using Elsa;

/// <summary>
///@date : 2023-12-3
///@desc : 成品入库 - Product_INBOUND_Bill
/// </summary>
namespace TEX.Model.Models;

//[Table("ProductInboundBills)]
[Display(Name = "_ProductInboundBill")]
public class ProductInboundBill : BasePoco, IPersistPoco, ITenant, IAudited, IRemark
{
    [Display(Name = "_CreateDate")]
    [Comment("日期")]
    public DateTime CreateDate { get; set; }= DateTime.Now;


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_InboundBillNo")]
    [Comment("入库单号")]
    public string BillNo { get; set; } = DateTime.Now.ToString("yyMMddHHmmssf");


    public PurchaseOrder POrder { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_PurchaseOrderId")]
    [Comment("订单号")]
    public Guid POrderId { get; set; }


    public Company FinishingFactory { get; set; }
    [Display(Name = "_FinishingFactory")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("染整厂")]
    public Guid FinishingFactoryId { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_Warehouse")]
    [Comment("仓库")]
    public string Warehouse { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("件数")]
    public int Pcs { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal Yards { get; set; }


    public List<ProductInboundLot> LotList { get; set; }

    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }
    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }
    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }

}