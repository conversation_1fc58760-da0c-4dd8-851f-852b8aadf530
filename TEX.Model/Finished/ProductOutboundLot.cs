
/// <summary>
///@date : 2023-12-8
///@desc : 成品出库缸号 - Product_Outbound_LOT
/// </summary>
namespace TEX.Model.Models;

//[Table("ProductOutboundLots)]
[Display(Name = "_ProductOutboundLot")]
public class ProductOutboundLot : BasePoco, IPersistPoco, ITenant, IAudited, IRemark
{

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OutboundBillId")]
    [Comment("出库单号")]
    public Guid OutboundBillId { get; set; }
    [JsonIgnore]
    public ProductOutboundBill OutboundBill { get; set; }

    [JsonIgnore]
    public OrderDetail OrderDetail { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OrderDetail")]
    [Comment("订单明细")]
    public Guid OrderDetailId { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Color")]
    [Comment("颜色")]
    public string Color { get; set; }

    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_ColorCode")]
    [Comment("色号")]
    public string ColorCode { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_LotNo")]
    [Comment("缸号")]
    public string LotNo { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("件数")]
    public int Pcs { get; set; }

    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal Yards { get; set; }

    [Display(Name = "_Remark")]
    [Comment("备注")]
    public string Remark { get; set; }

    public List<ProductOutboundRoll> RollList { get; set; }


    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; }


    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }


    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }


    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    public bool IsValid { get; set; } = true;


    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    public string TenantCode { get; set; }

}