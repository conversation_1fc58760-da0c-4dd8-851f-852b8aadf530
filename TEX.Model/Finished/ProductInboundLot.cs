
/// <summary>
///@date : 2023-12-3
///@desc : 成品入库缸号 - Product_Inbound_LOT
/// </summary>
namespace TEX.Model.Models;

//[Table("ProductInboundLots)]
[Display(Name = "_ProductInboundLot")]
public class ProductInboundLot : BasePoco, IPersistPoco, ITenant, IRemark
{

    [Display(Name = "_InboundBill")]
    [Comment("入库单号")]
    public ProductInboundBill InboundBill { get; set; }

    [Display(Name = "_InboundBill")]
    [Comment("入库单号")]
    public Guid InboundBillId { get; set; }


    public OrderDetail OrderDetail { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OrderDetail")]
    [Comment("订单明细")]
    public Guid OrderDetailId { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    //[Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Color")]
    [Comment("颜色")]
    public string Color { get; set; }

    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_ColorCode")]
    [Comment("色号")]
    public string ColorCode { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_LotNo")]
    [Comment("缸号")]
    public string LotNo { get; set; }


    [Display(Name = "_RollsCount")]
    [Comment("件数")]
    public int Pcs { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal Yards { get; set; }

    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]

    [Display(Name = "_Location")]
    [Comment("库位")]
    public string Location { get; set; }


    public List<ProductInboundRoll> RollList { get; set; }

    [Display(Name = "_Status")]
    [Comment("入库状态")]
    public InboundStatusEnum InboundStatus { get; set; } = InboundStatusEnum.Inbound;

    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }

}