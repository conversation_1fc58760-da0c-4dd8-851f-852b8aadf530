using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TEX.Model.Finished;

public class ProductStock: BasePoco, IPersistPoco, ITenant
{
    public OrderDetail OrderDetail { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OrderDetail")]
    [Comment("订单明细")]
    public Guid OrderDetailId { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("件数")]
    public int TotalPcs { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal TotalWeight { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal TotalMeters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal TotalYards { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_Wearhouse")]
    [Comment("仓库")]
    public string Wearhouse { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_Location")]
    [Comment("库位")]
    public string Location { get; set; }
    public string TenantCode { get; set; }
    public bool IsValid { get; set; } = true;
}

public class StockInfo
{
    public Guid OrderDetailId { get; set; }
    public int TotalPcs { get; set; }
    public Decimal TotalMeters { get; set; }
    public Decimal TotalWeight { get; set; }
    public Decimal TotalYards { get; set; }
}