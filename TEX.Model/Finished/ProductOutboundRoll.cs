
/// <summary>
///@date : 2023-12-3
///@desc : 成品入库卷号 - Product_Outbound_ROLL
/// </summary>
namespace TEX.Model.Models;

//[Table("ProductOutboundRolls)]
[Display(Name = "_ProductOutboundRoll")]
public class ProductOutboundRoll : BasePoco, IPersistPoco, ITenant, IRemark
{
    
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_Lot")]
    [Comment("缸号")]
    public Guid LotId { get; set; }
    [JsonIgnore]
    public ProductOutboundLot Lot { get; set; }

    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_RollNo")]
    [Comment("卷号")]
    public int RollNo { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal Yards { get; set; }



    [Display(Name = "_Grade")]
    [Comment("等级")]
    public string Grade { get; set; }

    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }

    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    

}
