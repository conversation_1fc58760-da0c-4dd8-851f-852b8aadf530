using Newtonsoft.Json;

/// <summary>
///@date : 2024-4-4
///@desc : 检验 - Inspected_ROLL
/// </summary>
namespace TEX.Model.Models;


[Display(Name = "_InspectedRoll")]
public class InspectedRoll : BasePoco, IPersistPoco, ITenant, IAudited, IRemark
{

    public OrderDetail OrderDetail { get; set; }
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_OrderDetail")]
    [Comment("订单明细")]
    public Guid OrderDetailId { get; set; }

    //[Display(Name = "_LotNO")]
    //[Comment("缸号")]
    public Guid? InboundLotId { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_LotNo")]
    [Comment("缸号")]
    public string InspectedLot { get; set; }


    [Required(ErrorMessage = "Validate.{0}required")]
    [Display(Name = "_RollNo")]
    [Comment("卷号")]
    public int RollNo { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }

    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("毛重")]
    public decimal GW { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal Yards { get; set; }

    [Precision(18, 1)]
    [Display(Name = "_FreeYards")]
    [Comment("送码")]
    public decimal FreeYards { get; set; }


    [Display(Name = "_Score")]
    [Comment("分数")]
    public Double Score { get; set; }


    [Display(Name = "_TotalScore")]
    [Comment("总分")]
    public int TotalScore { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_Grade")]
    [Comment("评级")]
    public string Grade { get; set; }


    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Display(Name = "_ProcessName")]
    [Comment("工序")]
    public string ProcessName { get; set; }


    [JsonProperty(PropertyName = "DefectsRecord")]
    public string DefectsRecord { get; set; }


    //会报错:The entity type 'Defect' requires a primary key to be defined. 
    //[System.Text.Json.Serialization.JsonIgnore]
    //public List<Defect> DefectList
    //{
    //    get
    //    {
    //        return DefectsRecord != null ? JsonConvert.DeserializeObject<List<Defect>>(DefectsRecord) : new List<Defect>();
    //    }
    //    set
    //    {
    //        DefectsRecord = JsonConvert.SerializeObject(value);
    //    }
    //}

    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }
    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }
    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_MachineNo")]
    public string MachineNo { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
}

public class DefectReport
{
    public string DefectName { get; set; }
    public string DefectScore { get; set; }

}