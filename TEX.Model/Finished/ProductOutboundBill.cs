
/// <summary>
///@date : 2023-12-8
///@desc : 成品出库 - Product_OutBOUND_Bill
/// </summary>
namespace TEX.Model.Models;

//[Table("ProductOutboundBills)]
[Display(Name = "_ProductOutboundBill")]
public class ProductOutboundBill : BasePoco, IPersistPoco, ITenant, IAudited, IRemark
{

    [Display(Name = "_CreateDate")]
    [Required]
    [Comment("日期")]
    public DateTime CreateDate { get; set; }=DateTime.Now;


    [Display(Name = "_BillNo")]
    [Required]
    [Comment("单号")]
    public String BillNo { get; set; } = DateTime.Now.ToString("yMMddHHmmssf");


    [Display(Name = "_Customer")]
    [Required]
    [Comment("客户")]
    public Guid CustomerId { get; set; }
    public Company Customer { get; set; }


    [Display(Name = "_Receiver")]
    [Required]
    [Comment("收货方")]
    public Guid ReceiverId { get; set; }
    public DeliveryAddress Receiver { get; set; }


    //
    //[Display(Name = "_CustomerOrderNo")]
    //[Comment("客户款号")]
    //public String CustomerOrderNo { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("匹数")]
    public int Pcs { get; set; }

    [Precision(18, 1)]
    [Display(Name = "_Weight")]
    [Comment("重量")]
    public decimal Weight { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Meters")]
    [Comment("米数")]
    public decimal Meters { get; set; }


    [Precision(18, 1)]
    [Display(Name = "_Yards")]
    [Comment("码数")]
    public decimal Yards { get; set; }

    public List<ProductOutboundLot> LotList { get; set; }



    //[Precision(18, 2)]
    //[Display(Name = "_Qty")]
    //[Comment("数量")]
    //public decimal Qty { get; set; }


    //[Display(Name = "_QtyUnit")]
    //[Comment("数量单位")]
    //public AccountingUnitEnum QtyUnit { get; set; } = AccountingUnitEnum.M;


    //[Precision(18, 2)]
    //[Display(Name = "_Weight")]
    //[Comment("重量")]
    //public decimal Weight { get; set; }


    //[Display(Name = "_WeightUnit")]
    //[Comment("重量单位")]
    //public AccountingUnitEnum WeightUnit { get; set; } = AccountingUnitEnum.KG;


    [Display(Name = "_Remark")]
    [Comment("备注")]
    public string Remark { get; set; }


    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; }


    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }


    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }


    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    public bool IsValid { get; set; } = true;


    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    public string TenantCode { get; set; }

}