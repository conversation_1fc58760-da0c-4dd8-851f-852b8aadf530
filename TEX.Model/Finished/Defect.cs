using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TEX.Model.Models
{
    public class Defect 
    {
        [Display(Name = "_Defects")]
        [Comment("疵点")]
        public FabricDefectEnum DefectName { get; set; }

        [Display(Name = "_Score")]
        [Comment("扣分")]
        public int Score { get; set; }

        [Display(Name = "_DefectLocation")]
        [Comment("位置")]
        public int? Location { get; set; }

    }
}
