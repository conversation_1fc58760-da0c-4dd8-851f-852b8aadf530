
namespace TEX.Model.Models;

/// <summary>
/// 订单
/// </summary>
[Table("PurchaseOrders")]

[Display(Name = "_Model.PurchaseOrder")]
public class PurchaseOrder : BasePoco, IPersistPoco,ITenant,IAudited,IRemark
{
    [Display(Name = "_CreateDate")]
    [Comment("下单日期")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public DateTime CreateDate { get; set; }= DateTime.Now;
    [Display(Name = "_DeliveryDate")]
    [Comment("交期")]
    public DateTime? DeliveryDate { get; set; }
    [Display(Name = "_Customer")]
    [Comment("客户")]
    public Company Customer { get; set; }
    [Display(Name = "_Customer")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("客户")]
    public Guid? CustomerId { get; set; }
    [Display(Name = "_OrderNo")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("订单号")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string OrderNo { get; set; }
    [Display(Name = "_CustomerOrderNo")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("客户款号")]
    public string CustomerOrderNo { get; set; }
    
    public Contact Merchandiser { get; set; }
    [Display(Name = "_Merchandiser")]
    [Comment("业务员")]
    public Guid? MerchandiserId { get; set; }
    [Display(Name = "_OrderType")]
    [Comment("订单类型")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public OrderTypeEnum? OrderType { get; set; } = OrderTypeEnum.Fabric;
    [Display(Name = "_Product")]
    [Comment("产品名称")]
    public Product Product { get; set; }
    [Display(Name = "_Product")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("产品名称")]
    public Guid ProductId { get; set; }
    [Display(Name = "_DyeingProductName")]
    public string DyeingProductName { get; set; }
    [Display(Name = "_Light")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("主光源")]
    public LightEnum? Light { get; set; } = LightEnum.D65;
    [Display(Name = "_Light2")]
    [Comment("副光源")]
    public LightEnum? Light2 { get; set; }
    [Display(Name = "_AccountUnit")]
    [Comment("计量单位")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public AccountingUnitEnum? AccountUnit { get; set; }= AccountingUnitEnum.M;
    [Display(Name = "_PriceUnit")]
    [Comment("计价单位")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public CurrencyEnum? PriceUnit { get; set; }= CurrencyEnum.CNY;
    [Display(Name = "_TotalMeters")]
    [Precision(18, 1)]
    [Comment("总米数")]
    public decimal? TotalMeters { get; set; }
    [Display(Name = "_TotalYards")]
    [Precision(18, 1)]
    [Comment("总码数")]
    public decimal? TotalYards { get; set; }
    [Display(Name = "_TotalWeight")]
    [Precision(18, 1)]
    [Comment("总重量")]
    public decimal? TotalWeight { get; set; }
    [Display(Name = "_TotalAmount")]
    [Precision(18, 2)]
    [Comment("总金额")]
    public decimal? TotalAmount { get; set; }

    [Display(Name = "_CompletedStatus")]
    public CompletedStatusEnum? CompletedStatus { get; set; }= CompletedStatusEnum.InProcess;
    [Display(Name = "_PurchaseOrder")]
    [InverseProperty("PurchaseOrder")]
    public List<OrderDetail> OrderDetailList { get; set; }
    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
    [Display(Name = "_AuditedBy")]
    [Comment("审核人")]
    public string AuditedBy { get; set; }
    [Display(Name = "_AuditedComment")]
    [Comment("审核意见")]
    public string AuditedComment { get; set; }
    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
}
