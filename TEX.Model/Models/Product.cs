

namespace TEX.Model.Models;
/// <summary>
/// 产品
/// </summary>
[Table("Products")]

[Display(Name = "_Model.Product")]
public class Product : TreePoco<Product>,IBasePoco,IPersistPoco,ITenant
{
    [Display(Name = "_ProductCode")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("产品编码")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string ProductCode { get; set; }
    [Display(Name = "_ProductName")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("产品名称")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string ProductName { get; set; }
    [Display(Name = "_Category")]
    [Comment("产品分类")]
    public DictItem Category { get; set; }
    [Display(Name = "_Category")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public int CategoryId { get; set; }
    [Display(Name = "_Contents")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("成份")]
    public string Contents { get; set; }
    [Display(Name = "_Spec")]
    [StringLength(128, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("规格")]
    public string Spec { get; set; }
    [Display(Name = "_GSM")]
    [Comment("平方克重")]
    public int GSM { get; set; }
    [Display(Name = "_Width")]
    [Comment("有效门幅")]
    [Range(0,300,ErrorMessage="Validate.{0}range{1}{2}")]
    public int Width { get; set; }
    [Display(Name = "_Width")]
    [Comment("全幅")]
    [Range(0, 300, ErrorMessage = "Validate.{0}range{1}{2}")]
    public int FullWidth { get; set; }
    [Display(Name = "_DyeingProductName")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("染色品名")]
    public string DyeingProductName { get; set; }
    [Display(Name = "_PileLength")]
    [Comment("毛长")]
    public int? PileLength { get; set; }
    [Display(Name = "_DyeingProcess")]
    [Comment("染色工艺")]
    public string DyeingProcess { get; set; }
    [Display(Name = "_KnittingProcess")]
    [Comment("织造工艺")]
    public string KnittingProcess { get; set; }
    [Display(Name = "_FinishingProcess")]
    [Comment("后整工艺")]
    public string FinishingProcess { get; set; }
    [Display(Name = "_Photo")]
    public List<ProductPhoto> Photo { get; set; }
    [Display(Name = "_Product")]
    [InverseProperty("Product")]
    public List<PurchaseOrder> PurchaseOrder_Product { get; set; }
    [Display(Name = "_CreateTime")]
    [Comment("创建时间")]
    public DateTime? CreateTime { get; set; }
    [Display(Name = "_UpdateTime")]
    [Comment("修改时间")]
    public DateTime? UpdateTime { get; set; }
    [Display(Name = "_CreateBy")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("创建人")]
    public string CreateBy { get; set; }
    [Display(Name = "_UpdateBy")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("修改人")]
    public string UpdateBy { get; set; }
    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
}
public class ProductPhoto : TopBasePoco, ISubFile
{
    public Guid ProductId { get; set; }
    public Product Product { get; set; }
    public Guid FileId { get; set; }
    public FileAttachment File { get; set; }
    public int Order { get; set; }
}
