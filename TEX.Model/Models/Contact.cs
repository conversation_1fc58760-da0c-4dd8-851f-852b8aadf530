
/// <summary>
///@date : 2023-10-8
///@desc : 联系人 - Contact
/// </summary>
namespace TEX.Model.Models;
public class Contact : BasePoco, IPersistPoco, ITenant, IRemark
{

    [Display(Name = "_ContactName")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("联系人")]
    public string ContactName { get; set; }


    [Display(Name = "_AffiliationCompany")]
    [Comment("所属公司")]
    public Company AffiliationCompany { get; set; }
    [Display(Name = "_AffiliationCompany")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("所属公司")]
    public Guid? AffiliationCompanyId { get; set; }


    [Display(Name = "_PositionTitle")]
    [Comment("职位")]
    public string PositionTitle { get; set; }


    [Display(Name = "_MobilePhone")]
    [Comment("手机")]
    public string MobilePhone { get; set; }


    [Display(Name = "_Address")]
    [Comment("地址")]
    public string Address { get; set; }


    [Display(Name = "_Phone")]
    [Comment("电话")]
    public string Phone { get; set; }


    [Display(Name = "_Email")]
    [Comment("邮箱")]
    public string Email { get; set; }


    [Display(Name = "_WeChat")]
    [Comment("微信")]
    public string WeChat { get; set; }


    [Display(Name = "_QQ")]
    [Comment("QQ")]
    public string QQ { get; set; }


    [Display(Name = "_Fax")]
    [Comment("传真")]
    public string Fax { get; set; }

    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
}