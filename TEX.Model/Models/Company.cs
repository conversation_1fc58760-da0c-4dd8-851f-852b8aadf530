namespace TEX.Model.Models;

/// <summary>
/// 往来单位
/// </summary>
	[Table("Companys")]

[Display(Name = "_Model.Company")]
public class Company : BasePoco,IPersistPoco,ITenant
{
    [Display(Name = "_CompanyCode")]
    [StringLength(32, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("公司代码")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string CompanyCode { get; set; }
    [Display(Name = "_CompanyName")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("公司名称")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string CompanyName { get; set; }
    [Display(Name = "_CompanyFullName")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("公司全称")]
    public string CompanyFullName { get; set; }
    [Display(Name = "_CompanyType")]
    [Comment("公司类型")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public CompanyTypeEnum CompanyType { get; set; }
    [Display(Name = "_Relationship")]
    [Comment("往来关系")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public RelationshipEnum Relationship { get; set; }
    [Display(Name = "_ContactPhone")]
    [Comment("电话")]
    [RegularExpression("^[-0-9\\s]{8,30}$", ErrorMessage = "Validate.{0}formaterror")]
    public string ContactPhone { get; set; }
    [Display(Name = "_Adress")]
    [Comment("地址")]
    public string Adress { get; set; }
    [Display(Name = "_TaxNO")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("税号")]
    public string TaxNO { get; set; }
    [Display(Name = "_InvoiceInfo")]
    [Comment("开票资料")]
    public string InvoiceInfo { get; set; }
    [Display(Name = "_Customer")]
    [InverseProperty("Customer")]
    public List<PurchaseOrder> PurchaseOrder_Customer { get; set; }
    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
}
