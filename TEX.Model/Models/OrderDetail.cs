
namespace TEX.Model.Models;

/// <summary>
/// OrderDetail
/// </summary>
[Table("OrderDetails")]

[Display(Name = "_Model.OrderDetail")]
public class OrderDetail : BasePoco, IPersistPoco, ITenant, IRemark
{
    [Display(Name = "_PurchaseOrder")]
    [Comment("订单号")]
    public PurchaseOrder PurchaseOrder { get; set; }
    [Display(Name = "_PurchaseOrder")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("订单号")]
    public Guid? PurchaseOrderId { get; set; }
    [Display(Name = "_Color")]
    [StringLength(32, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("颜色")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public string Color { get; set; }
    [Display(Name = "_EngColor")]
    [StringLength(32, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("颜色(英)")]
    public string EngColor { get; set; }
    [Display(Name = "_ColorCode")]
    [StringLength(32, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("色号")]
    public string ColorCode { get; set; }
    [Display(Name = "_Meters")]
    [Precision(18, 1)]
    [Comment("米数")]
    public decimal? Meters { get; set; }
    [Display(Name = "_KG")]
    [Precision(18, 1)]
    [Comment("重量")]
    public decimal? KG { get; set; }
    [Display(Name = "_Yards")]
    [Precision(18, 1)]
    [Comment("码数")]
    public decimal? Yards { get; set; }
    
    [Display(Name = "_Price")]
    [Precision(18, 2)]
    [Comment("单价")]
    public decimal? Price { get; set; }
    [Display(Name = "_Amount")]
    [Comment("金额")]
    [Precision(18, 2)]
    public decimal? Amount { get; set; }
    [Display(Name = "_IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
}
