
/// <summary>
///@date : 2023-10-11
///@desc : 货运地址 - ShippingAddress
/// </summary>
namespace TEX.Model.Models;

[Table("DeliveryAddresses")]
[Display(Name = "_DeliveryAddress")]
public class DeliveryAddress : BasePoco, IPersistPoco, ITenant, IRemark
{

    [Display(Name = "_CompanyName")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("公司名称")]
    public string CompanyName { get; set; }

    [Display(Name = "_CompanyFullName")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string FullName { get; set; }

    [Display(Name = "_ContactName")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [StringLength(64, ErrorMessage = "Validate.{0}stringmax{1}")]
    [Comment("联系人")]
    public string ContactName { get; set; }


    [Display(Name = "_Phone")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("电话")]
    public string Phone { get; set; }


    [Display(Name = "_Address")]
    [Required(ErrorMessage = "Validate.{0}required")]
    [Comment("地址")]
    public string Address { get; set; }


    [Display(Name = "_AffiliationCompany")]
    [Comment("关联公司")]
    public Company AffiliationCompany { get; set; }
    [Display(Name = "_AffiliationCompany")]
    [Comment("关联公司")]
    public Guid? AffiliationCompanyId { get; set; }

    public int SortNo { get; set; }


    [Display(Name = "_Model._IsValid")]
    [Comment("是否有效")]
    [Required(ErrorMessage = "Validate.{0}required")]
    public bool IsValid { get; set; } = true;
    [Display(Name = "_TenantCode")]
    [Comment("租户号")]
    [StringLength(50, ErrorMessage = "Validate.{0}stringmax{1}")]
    public string TenantCode { get; set; }
    [Display(Name = "_Admin.Remark")]
    public string Remark { get; set; }
    [Display(Name = "_AuditStatus")]
    [Comment("审核状态")]
    public AuditStatusEnum AuditStatus { get; set; } = AuditStatusEnum.NotAudited;
}