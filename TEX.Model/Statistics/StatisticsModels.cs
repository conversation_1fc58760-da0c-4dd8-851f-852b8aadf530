using System.ComponentModel.DataAnnotations;

namespace TEX.Model.Statistics;


/// <summary>
/// 统计结果基础模型
/// </summary>
public class ProductStatisticsResult
{
    
    [Display(Name = "_Customer")]
    public String Customer_view { get; set; }
    [Display(Name = "_OrderNo")]
    public String OrderNo_view { get; set; }
    [Display(Name = "_ProductName")]
    public String ProductName_view { get; set; }

    [Display(Name = "_Color")]
    public String Color_view { get; set; }


    [Display(Name = "_Pcs")]
    [Comment("总件数")]
    public int TotalPcs { get; set; }

    [Display(Name = "_Weight")]
    [Comment("总重量")]
    public decimal TotalWeight { get; set; }

    [Display(Name = "_Meters")]
    [Comment("总米数")]
    public decimal TotalMeters { get; set; }

    [Display(Name = "_Yards")]
    [Comment("总码数")]
    public decimal TotalYards { get; set; }



}


/// <summary>
/// 统计结果基础模型
/// </summary>
public class StatisticsResult
{
    [Display(Name = "_Pcs")]
    [Comment("总件数")]
    public int TotalPcs { get; set; }

    [Display(Name = "_Weight")]
    [Comment("总重量")]
    public decimal TotalWeight { get; set; }

    [Display(Name = "_Meters")]
    [Comment("总米数")]
    public decimal TotalMeters { get; set; }

    [Display(Name = "_Yards")]
    [Comment("总码数")]
    public decimal TotalYards { get; set; }

    [Display(Name = "单据数量")]
    [Comment("单据数量")]
    public int TotalBills { get; set; }

    [Display(Name = "缸数")]
    [Comment("缸数")]
    public int TotalLots { get; set; }
}

/// <summary>
/// 分组统计结果模型
/// </summary>
public class GroupedStatisticsResult : StatisticsResult
{
    [Display(Name = "分组键")]
    [Comment("分组键")]
    public string GroupKey { get; set; }

    [Display(Name = "分组名称")]
    [Comment("分组显示名称")]
    public string GroupName { get; set; }

    [Display(Name = "分组日期")]
    [Comment("分组日期（时间维度）")]
    public DateTime? GroupDate { get; set; }

    [Display(Name = "分组类型")]
    [Comment("分组类型")]
    public string GroupType { get; set; }
}

/// <summary>
/// 钻取查询参数模型
/// </summary>
public class DrillDownParams
{
    /// <summary>
    /// 分组键
    /// </summary>
    public string GroupKey { get; set; }

    /// <summary>
    /// 分组类型（Customer/Product/Date/OrderDetail等）
    /// </summary>
    public string GroupType { get; set; }

    /// <summary>
    /// 原始搜索条件（JSON格式）
    /// </summary>
    public string OriginalSearchParams { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 统计查询类型枚举
/// </summary>
public enum StatisticsGroupType
{
    /// <summary>
    /// 按客户分组
    /// </summary>
    Customer,

    /// <summary>
    /// 按产品分组
    /// </summary>
    Product,

    /// <summary>
    /// 按订单明细分组
    /// </summary>
    OrderDetail,

    /// <summary>
    /// 按日期分组
    /// </summary>
    Date,

    /// <summary>
    /// 按月份分组
    /// </summary>
    Month,

    /// <summary>
    /// 按仓库分组
    /// </summary>
    Warehouse,

    /// <summary>
    /// 按染整厂分组
    /// </summary>
    FinishingFactory
}

/// <summary>
/// 入库统计结果模型
/// </summary>
public class InboundStatisticsResult : StatisticsResult
{
    [Display(Name = "入库日期范围")]
    public string DateRange { get; set; }

    [Display(Name = "主要客户")]
    public string TopCustomers { get; set; }

    [Display(Name = "主要产品")]
    public string TopProducts { get; set; }
}

/// <summary>
/// 出库统计结果模型
/// </summary>
public class OutboundStatisticsResult : StatisticsResult
{
    [Display(Name = "出库日期范围")]
    public string DateRange { get; set; }

    [Display(Name = "主要客户")]
    public string TopCustomers { get; set; }

    [Display(Name = "主要收货方")]
    public string TopReceivers { get; set; }
}

/// <summary>
/// 库存统计结果模型
/// </summary>
public class StockStatisticsResult : StatisticsResult
{
    [Display(Name = "库存订单数")]
    public int TotalOrders { get; set; }

    [Display(Name = "零库存订单数")]
    public int ZeroStockOrders { get; set; }

    [Display(Name = "主要仓库")]
    public string TopWarehouses { get; set; }

    [Display(Name = "统计时间")]
    public DateTime StatisticsTime { get; set; } = DateTime.Now;
}
