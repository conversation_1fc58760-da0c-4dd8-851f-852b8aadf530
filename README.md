# TEX 纺织企业管理系统

## 项目概述

TEX是一个基于WTM（WalkingTec.Mvvm）开源框架构建的纺织企业管理系统，采用.NET 8和Blazor Server技术栈开发。系统专为纺织行业设计，涵盖从订单管理、坯布管理、生产计划、染色工艺到成品检验的完整业务流程。

### 主要功能

- **订单管理** - 采购订单、订单明细、客户管理
- **坯布管理** - 提花花型、织造工艺、织造计划
- **生产管理** - 染色计划、半成品流转、批次分配
- **成品管理** - 成品库存、检验计划、质量控制
- **基础数据** - 产品信息、公司信息、字典管理
- **系统管理** - 用户权限、多租户、审计日志

## 技术栈

### 后端技术
- **.NET 8** - 主要开发框架
- **WTM Framework** - 基于WalkingTec.Mvvm的快速开发框架
- **Entity Framework Core** - ORM数据访问
- **MySQL** - 主数据库
- **ASP.NET Core MVC** - Web API和控制器

### 前端技术
- **Blazor Server** - 服务端渲染的交互式Web UI
- **BootstrapBlazor** - UI组件库
- **Bootstrap** - CSS框架
- **FontAwesome** - 图标库

### 开发工具
- **Visual Studio 2022** - 推荐IDE
- **Swagger** - API文档和测试
- **xUnit** - 单元测试框架

## 项目架构

项目采用经典的分层架构模式：

```
TEX/
├── TEX/                    # 主Web应用程序（MVC + API）
├── TEX.Shared/             # Blazor共享组件和页面
├── TEX.Model/              # 数据模型和实体
├── TEX.DataAccess/         # 数据访问层
├── TEX.ViewModel/          # 视图模型和业务逻辑
├── TEX.Test/               # 单元测试
└── TEX.Client/             # 客户端项目（预留）
```

### 核心模块

#### 1. 坯布管理 (Greige)
- **Pattern** - 提花花型管理
- **KnittingProcess** - 织造工艺
- **KnittingPlan** - 织造计划

#### 2. 生产管理 (Production)
- **DyeingPlan** - 染色计划
- **WIP** - 半成品流转单
- **LotAllocate** - 批次分配

#### 3. 成品管理 (Finished)
- **ProductStock** - 成品库存
- **InspectPlan** - 检验计划

#### 4. 基础数据 (Models)
- **Product** - 产品信息
- **Company** - 公司信息
- **PurchaseOrder** - 采购订单
- **OrderDetail** - 订单明细

## 环境要求

### 开发环境
- **.NET 8 SDK** 或更高版本
- **Visual Studio 2022** 或 **VS Code**
- **MySQL 8.0** 或更高版本
- **Node.js** (可选，用于前端工具)

### 运行环境
- **Windows Server 2019+** 或 **Linux**
- **.NET 8 Runtime**
- **MySQL 8.0+**
- **IIS** 或 **Nginx** (生产环境)

## 安装和配置

### 1. 克隆项目
```bash
git clone [repository-url]
cd TEX
```

### 2. 数据库配置

编辑 `appsettings.json` 文件中的数据库连接字符串：

```json
{
  "Connections": [
    {
      "Key": "default",
      "DbContext": "DataContext",
      "Value": "Server=localhost;User ID=root;Password=your_password;Database=tex_db",
      "DBType": "mysql"
    }
  ]
}
```

### 3. 安装依赖包
```bash
dotnet restore
```

### 4. 数据库迁移
```bash
dotnet ef database update --project TEX.DataAccess --startup-project TEX
```

### 5. 运行项目
```bash
dotnet run --project TEX
```

## 配置说明

### 主要配置项

- **BlazorMode**: 设置为 "server" 使用Blazor Server模式
- **EnableTenant**: 启用多租户功能
- **Languages**: 支持的语言 "zh,en"
- **UIOptions**: UI组件的默认配置
- **FileUploadOptions**: 文件上传配置

### 多租户配置

系统支持多租户架构，通过 `TenantCode` 字段实现数据隔离。

## 运行项目

### 开发模式
```bash
dotnet run --project TEX
```
访问: http://localhost:5000

### 生产模式
```bash
dotnet publish -c Release
```

## API文档

项目集成了Swagger，启动后访问：
- 开发环境: http://localhost:5000/swagger
- 生产环境: 根据配置的域名访问

## 测试

运行单元测试：
```bash
dotnet test
```

## 部署说明

### IIS部署
1. 发布项目到目标文件夹
2. 在IIS中创建应用程序
3. 配置应用程序池为 .NET Core
4. 设置环境变量和连接字符串

### Docker部署
```dockerfile
# 示例Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY . .
EXPOSE 80
ENTRYPOINT ["dotnet", "TEX.dll"]
```

## 开发指南

### 代码规范
- 遵循C#编码规范
- 使用有意义的命名
- 添加必要的注释和文档

### 数据库规范
- 所有实体继承自 `BasePoco`
- 实现 `ITenant` 接口支持多租户
- 使用 `Comment` 特性添加字段说明

### 前端开发
- 使用BootstrapBlazor组件
- 遵循Blazor组件开发规范
- 实现响应式设计

## 业务流程

### 典型业务流程

1. **订单录入** → 创建采购订单和订单明细
2. **织造计划** → 根据订单创建织造计划，分配织造工艺
3. **坯布生产** → 执行织造计划，记录生产进度
4. **染色计划** → 创建染色计划，安排染色工序
5. **半成品流转** → 记录半成品在各工序间的流转
6. **成品检验** → 创建检验计划，执行质量检验
7. **成品入库** → 检验合格的产品入库管理

### 数据流转

```
订单管理 → 织造计划 → 坯布生产 → 染色计划 → 半成品流转 → 成品检验 → 库存管理
```

## 功能模块详解

### 坯布管理模块

#### 提花花型 (Pattern)
- 花型编码管理
- 花型设计要求
- 客户花型档案
- 花型明细配置

#### 织造工艺 (KnittingProcess)
- 工艺编码和分类
- 机型信息配置
- 工艺参数设置
- 成品规格定义

#### 织造计划 (KnittingPlan)
- 计划单号生成
- 订单关联
- 织造厂分配
- 生产进度跟踪

### 生产管理模块

#### 染色计划 (DyeingPlan)
- 染色批次规划
- 颜色配方管理
- 工艺流程安排
- 质量标准设定

#### 半成品流转 (WIP)
- 流转单管理
- 工序间转移
- 重量米数记录
- 质量状态跟踪

#### 批次分配 (LotAllocate)
- 缸号分配
- 批次合并
- 数量分配
- 追溯管理

### 成品管理模块

#### 成品库存 (ProductStock)
- 库存数量统计
- 仓库位置管理
- 批次追溯
- 库存预警

#### 检验计划 (InspectPlan)
- 检验标准设定
- 检验项目配置
- 检验结果记录
- 不合格品处理

## 系统特性

### 多租户支持
- 数据完全隔离
- 独立的用户权限
- 个性化配置
- 灵活的租户管理

### 权限管理
- 基于角色的权限控制
- 细粒度的功能权限
- 数据权限控制
- 审计日志记录

### 国际化支持
- 中英文双语界面
- 可扩展的语言包
- 本地化数据格式
- 时区自动转换

### 报表功能
- 生产进度报表
- 库存统计报表
- 质量分析报表
- 自定义报表模板

## 常见问题

### Q: 如何重置管理员密码？
A: 可以通过数据库直接修改FrameworkUsers表中的密码字段，或使用WTM框架提供的密码重置功能。

### Q: 如何配置多数据库？
A: 在appsettings.json的Connections节点中添加多个数据库连接配置，并在代码中指定使用的连接。

### Q: 如何自定义业务流程？
A: 可以通过修改相应的ViewModel和Controller来调整业务逻辑，或使用WTM的工作流功能。

### Q: 如何进行数据备份？
A: 建议使用MySQL的mysqldump工具进行定期备份，并配置自动备份脚本。

## 更新日志

### v1.0.0 (当前版本)
- 完成基础业务模块开发
- 实现多租户架构
- 集成Blazor Server UI
- 支持MySQL数据库

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 技术支持

- **WTM框架文档**: https://wtmdoc.walkingtec.cn/
- **Blazor文档**: https://docs.microsoft.com/zh-cn/aspnet/core/blazor/
- **BootstrapBlazor**: https://www.blazor.zone/

## 许可证

本项目基于WTM开源框架开发，遵循相应的开源许可证。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 这是一个企业级纺织管理系统，请在生产环境中妥善配置安全设置和数据备份策略。
