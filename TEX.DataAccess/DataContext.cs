using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Attributes;
using TEX.Model;
using System.Collections.Generic;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TEX.Model.Models;
using TEX.Model.Finished;


namespace TEX.DataAccess
{
    public partial class DataContext : FrameworkContext
    {

        //成品检验
        public DbSet<InspectPlan> InspectPlans { get; set; }

        //坯布相关
        public DbSet<KnittingProcess> KnittingProcesses { get; set; }
        public DbSet<KnittingPlan> KnittingPlans { get; set; }
        public DbSet<PatternDetail> PatternDetails { get; set; }
        public DbSet<Pattern> Patterns { get; set; }
        public DbSet<GreigeRoll> GreigeRolls { get; set; }
        public DbSet<GreigeInboundBill> GreigeInboundBills { get; set; }
        public DbSet<GreigeOutboundBill> GreigeOutboundBills { get; set; }

        //生产管理
        public DbSet<LotAllocate> LotAllocates { get; set; }
        public DbSet<DyeingPlan> DyeingPlans { get; set; }
        public DbSet<PlanDetail> PlanDetails { get; set; }
        public DbSet<WIP> WIPs { get; set; }
        public DbSet<WIPDetail> WIPDetails { get; set; }

        //成品库存
        public DbSet<ProductStock> ProductStocks { get; set; }

        //成品出库
        public DbSet<InspectedRoll> InspectedRolls { get; set; }
        public DbSet<ProductOutboundRoll> ProductOutboundRolls { get; set; }
        public DbSet<ProductOutboundLot> ProductOutboundLots { get; set; }
        public DbSet<ProductOutboundBill> ProductOutboundBills { get; set; }

        //成品入库
        public DbSet<ProductInboundRoll> ProductInboundRolls { get; set; }
        public DbSet<ProductInboundLot> ProductInboundLots { get; set; }
        public DbSet<ProductInboundBill> ProductInboundBills { get; set; }

        //订单及基础信息
        public DbSet<DeliveryAddress> DeliveryAddresses { get; set; }
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<Company> Companys { get; set; }
        public DbSet<OrderDetail> OrderDetails { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<DictType> DictTypes { get; set; }
        public DbSet<DictItem> DictItems { get; set; }

        //框架自带
        public DbSet<FrameworkUser> FrameworkUsers { get; set; }
        public DbSet<FrameworkUserRole> FrameworkUserRoles { get; set; }
        public DbSet<FrameworkUserGroup> FrameworkUserGroups { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //modelBuilder.Entity<DyeingPlan>()
            //    .HasOne(p => p.GreigeVender)
            //    .WithMany()
            //    .HasForeignKey(p => p.GreigeVenderId)
            //    .OnDelete(DeleteBehavior.NoAction);
            //modelBuilder.Entity<DyeingPlan>()
            //    .HasOne(p => p.POrder)
            //    .WithMany()
            //    .HasForeignKey(p => p.POrderId)
            //    .OnDelete(DeleteBehavior.NoAction);
            //modelBuilder.Entity<DyeingPlan>()
            //    .HasOne(dp => dp.FinishingFactory)
            //    .WithMany()
            //    .HasForeignKey(dp => dp.FinishingFactoryId)
            //    .OnDelete(DeleteBehavior.Restrict);

            //modelBuilder.Entity<DyeingPlan>()
            //    .HasOne(dp => dp.GreigeVender)
            //    .WithMany()
            //    .HasForeignKey(dp => dp.GreigeVenderId)
            //    .OnDelete(DeleteBehavior.Restrict);

            //modelBuilder.Entity<ProductInboundLot>()
            //    .HasOne(dp => dp.InboundBill)
            //    .WithMany()
            //    .HasForeignKey(dp => dp.InboundBillId)
            //    .OnDelete(DeleteBehavior.Restrict);

            //modelBuilder.Entity<ProductInboundLot>()
            //    .HasOne(dp => dp.POrder)
            //    .WithMany()
            //    .HasForeignKey(dp => dp.POrderId)
            //    .OnDelete(DeleteBehavior.Restrict);

            //modelBuilder.Entity<WIP>()
            //    .HasOne(x => x.Shipper)
            //    .WithMany()
            //    .HasForeignKey(x => x.ShipperId)
            //    .OnDelete(DeleteBehavior.NoAction);
            //modelBuilder.Entity<WIP>()
            //    .HasOne(x => x.Receiver)
            //    .WithMany()
            //    .HasForeignKey(x => x.ReceiverId)
            //    .OnDelete(DeleteBehavior.Restrict);
            //modelBuilder.Entity<ProductOutboundBill>()
            //    .HasOne(x => x.Receiver)
            //    .WithMany()
            //    .HasForeignKey(x => x.ReceiverId)
            //    .OnDelete(DeleteBehavior.Restrict);
            //modelBuilder.Entity<ProductOutboundLot>()
            //    .HasOne(x => x.POrder)
            //    .WithMany()
            //    .HasForeignKey(x => x.POrderId)
            //    .OnDelete(DeleteBehavior.Restrict);
            // 设置为不级联删除

            //OnModelCreating方法重写必须写以下默认语句,否则造成框架软删除删不掉
            base.OnModelCreating(modelBuilder);
        }
        public DataContext(CS cs)
             : base(cs)
        {
        }

        public DataContext(string cs, DBTypeEnum dbtype) : base(cs, dbtype)
        {

        }

        public DataContext(string cs, DBTypeEnum dbtype, string version = null) : base(cs, dbtype, version)
        {

        }
        public DataContext(DbContextOptions<DataContext> options) : base(options) { }

        public override async Task<bool> DataInit(object allModules, bool IsSpa)
        {
            var state = await base.DataInit(allModules, IsSpa);
            bool emptydb = false;
            try
            {
                emptydb = Set<FrameworkUser>().Count() == 0 && Set<FrameworkUserRole>().Count() == 0;
            }
            catch { }
            if (state == true || emptydb == true)
            {
                //when state is true, means it's the first time EF create database, do data init here
                //当state是true的时候，表示这是第一次创建数据库，可以在这里进行数据初始化
                var user = new FrameworkUser
                {
                    ITCode = "admin",
                    Password = Utils.GetMD5String("000000"),
                    IsValid = true,
                    Name = "Admin",

                };

                var userrole = new FrameworkUserRole
                {
                    UserCode = user.ITCode,
                    RoleCode = "001"
                };
                var adminmenus = Set<FrameworkMenu>().Where(x => x.Url != null && x.Url.StartsWith("/api") == false).ToList();
                foreach (var item in adminmenus)
                {
                    item.Url = "/_admin" + item.Url;
                }
                Set<FrameworkUser>().Add(user);
                Set<FrameworkUserRole>().Add(userrole);
                await SaveChangesAsync();

                try
                {
                    Dictionary<string, List<object>> data = new Dictionary<string, List<object>>();
                    new Task(() =>
                    {
                    }).Start();
                }
                catch { }
            }
            return state;
        }

        private void SetTestData(Type modelType, Dictionary<string, List<object>> data, int count = 100)
        {
            int exist = 0;
            if (data.ContainsKey(modelType.FullName))
            {
                exist = data[modelType.FullName].Count;
                if (exist > 0)
                    return;
            }
            using (var dc = this.CreateNew())
            {
                Random r = new Random();
                data[modelType.FullName] = new List<object>();
                int retry = 0;
                List<string> ids = new List<string>();
                for (int i = 0; i < count - exist; i++)
                {
                    var modelprops = modelType.GetRandomValuesForTestData();
                    var newobj = modelType.GetConstructor(Type.EmptyTypes).Invoke(null);
                    var idvalue = modelprops.Where(x => x.Key == "ID").Select(x => x.Value).SingleOrDefault();
                    if (idvalue != null)
                    {
                        if (ids.Contains(idvalue.ToLower()) == false)
                        {
                            ids.Add(idvalue.ToLower());
                        }
                        else
                        {
                            retry++;
                            i--;
                            if (retry > count)
                            {
                                break;
                            }
                            continue;
                        }
                    }
                    foreach (var pro in modelprops)
                    {
                        if (pro.Value == "$fk$")
                        {
                            var fkpro = modelType.GetSingleProperty(pro.Key[0..^2]);
                            var fktype = fkpro?.PropertyType;
                            if (fktype != modelType && typeof(TopBasePoco).IsAssignableFrom(fktype) == true)
                            {
                                try
                                {
                                    SetTestData(fktype, data, count);
                                    newobj.SetPropertyValue(pro.Key, (data[fktype.FullName][r.Next(0, data[fktype.FullName].Count)] as TopBasePoco).GetID());

                                }
                                catch { }
                            }
                        }
                        else
                        {
                            var v = pro.Value;
                            if (v.StartsWith("\""))
                            {
                                v = v[1..];
                            }
                            if (v.EndsWith("\""))
                            {
                                v = v[..^1];
                            }
                            newobj.SetPropertyValue(pro.Key, v);
                        }
                    }
                    if (modelType == typeof(FileAttachment))
                    {
                        newobj.SetPropertyValue("Path", "./wwwroot/logo.png");
                        newobj.SetPropertyValue("SaveMode", "local");
                        newobj.SetPropertyValue("Length", 16728);
                    }
                    if (typeof(IBasePoco).IsAssignableFrom(modelType))
                    {
                        newobj.SetPropertyValue("CreateTime", DateTime.Now);
                        newobj.SetPropertyValue("CreateBy", "admin");
                    }
                    if (typeof(IPersistPoco).IsAssignableFrom(modelType))
                    {
                        newobj.SetPropertyValue("IsValid", true);
                    }
                    try
                    {
                        (dc as DbContext).Add(newobj);
                        data[modelType.FullName].Add(newobj);
                    }
                    catch
                    {
                        retry++;
                        i--;
                        if (retry > count)
                        {
                            break;
                        }
                    }
                }
                try
                {
                    dc.SaveChanges();
                }
                catch { }
            }
        }
    }

    /// <summary>
    /// DesignTimeFactory for EF Migration, use your full connection string,
    /// EF will find this class and use the connection defined here to run Add-Migration and Update-Database
    /// </summary>
    public class DataContextFactory : IDesignTimeDbContextFactory<DataContext>
    {
        public DataContext CreateDbContext(string[] args)
        {
            //"Value": "Server=************;User ID=root;Password=**************;Database=tex_db3",
            //return new DataContext("Server=************;User ID=root;Password=**************;Database=tex_db5", DBTypeEnum.MySql);
            return new DataContext("Server=localhost;User ID=sean;Password=*****************;Database=tex_db5", DBTypeEnum.MySql);
            //return new DataContext("Server=(localdb)\\mssqllocaldb;Database=TEX_db3;Trusted_Connection=True;", DBTypeEnum.SqlServer);
        }
    }

}