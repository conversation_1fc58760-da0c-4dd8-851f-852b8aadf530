window.localStorageFuncs = {
    set: function (key, value) {
        localStorage.setItem(key, value);
    },
    get: function (key, start) {
        if (start === undefined) {
            start = 0;
        }
        var t = localStorage.getItem(key);
        if (t == null) {
            return null;
        }
        var rv;
        if (t.length - start > 20000) {
            rv = t.substring(start, start+20000);
        }
        else {
            rv = t.substring(start);
        }
        return rv;
    },
    remove: function (key) {
        localStorage.removeItem(key);
    }
};

window.urlFuncs = {
    getCurrentUrl: function () {
        return window.location.href;
    },
    redirect: function (url) {
        window.location.href = url;
    },
    refresh: function () {
        window.location.reload();
    },
    download: function (url, data, method = "POST") {
        var xhr = new XMLHttpRequest();
        xhr.open(method, url, true);    // 也可以使用POST方式，根据接口
        xhr.responseType = "blob";  // 返回类型blob
        xhr.setRequestHeader('content-type', 'application/json');
        xhr.setRequestHeader('Authorization', 'Bearer ' + localStorageFuncs.get("wtmtoken"));
        xhr.onload = function () {
            if (this.status === 200) {
                var fname = this.getResponseHeader("content-disposition");
                var filename = "";
                if (/filename\*=UTF-8''(.*?)($|;|\s)/.test(fname)) {
                    filename = RegExp.$1;
                }
                else if (/filename=(.*?)($|;|\s)/.test(fname))
                {
                    filename = RegExp.$1;
                }
                filename = decodeURI(filename);
                var blob = this.response;
                var reader = new FileReader();
                reader.readAsDataURL(blob);  
                reader.onload = function (e) {
                    var a = document.createElement('a');
                    a.download = filename;
                    a.href = e.target.result;
                    document.body.append(a);
                    a.click();
                    a.remove();
                }
            }
        };
        // 发送ajax请求
        xhr.send(data)
    }
}

window.commonFuncs = {
    loadFinish: function () {
        var wasm = document.getElementById('loading')
        if (wasm) {
            wasm.classList.add("is-done")
            var handler = window.setTimeout(function () {
                window.clearTimeout(handler)
                wasm.remove()
                document.body.classList.remove('overflow-hidden')
            }, 600);
        }
    }
}

window.downloadFile = function (byteArray, fileName, contentType ="application/vnd.ms-excel") {
    // 创建Blob对象
    var blob = new Blob([byteArray], { type: contentType });

    // 创建下载链接
    var link = document.createElement("a");
    link.href = window.URL.createObjectURL(blob);
    link.download = fileName;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 移除链接
    document.body.removeChild(link);
    //link.remove();
};
