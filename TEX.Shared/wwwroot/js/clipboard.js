/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制到剪贴板的文本
 */
window.CopyTextToClipboard = function(text) {
    // 创建一个临时的textarea元素
    const textarea = document.createElement('textarea');
    
    // 确保换行符被正确处理
    // 将C#的Environment.NewLine替换为浏览器可识别的换行符
    if (text) {
        // 统一处理所有可能的换行符格式（\r\n, \r, \n）
        text = text.replace(/\r\n|\r|\n/g, '\n');
    }
    
    textarea.value = text;
    
    // 设置样式使其不可见
    textarea.style.position = 'fixed';
    textarea.style.left = '-999999px';
    textarea.style.top = '-999999px';
    
    // 添加到DOM
    document.body.appendChild(textarea);
    
    // 选择文本
    textarea.select();
    textarea.setSelectionRange(0, 99999); // 兼容移动设备
    
    // 尝试复制文本
    let success = false;
    try {
        success = document.execCommand('copy');
    } catch (err) {
        console.error('复制到剪贴板失败:', err);
    }
    
    // 移除临时元素
    document.body.removeChild(textarea);
    
    // 返回复制结果
    return success;
};