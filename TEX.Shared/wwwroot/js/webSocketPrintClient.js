/**
 * WebSocket打印客户端管理类
 * 用于处理与打印服务器的WebSocket通信
 * 包含自动重连、心跳检测、错误处理等功能
 */
class WebSocketPrintClient {
    /**
     * @param {Object} options - 配置选项
     * @param {string} [options.url='ws://localhost:55555/ws'] - WebSocket服务器地址
     * @param {number} [options.heartbeatInterval=5000] - 心跳间隔(毫秒)
     * @param {number} [options.reconnectInterval=5000] - 重连间隔(毫秒)
     * @param {number} [options.maxReconnectAttempts=5] - 最大重连次数
     * @param {Function} [options.onConnected] - 连接成功回调
     * @param {Function} [options.onDisconnected] - 断开连接回调
     * @param {Function} [options.onError] - 错误处理回调
     * @param {Function} [options.onMessage] - 消息接收回调
     */
    constructor(options = {}) {
        // 配置参数
        this.url = options.url || 'ws://localhost:55555/ws';
        this.heartbeatInterval = options.heartbeatInterval || 5000;
        this.reconnectInterval = options.reconnectInterval || 5000;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;

        // 内部状态
        this.ws = null;
        this.heartbeatTimer = null;
        this.reconnectTimer = null;
        this.reconnectAttempts = 0;
        this.isConnected = false;
        this.shouldReconnect = true;

        // 回调函数
        this.onConnected = options.onConnected || (() => { });
        this.onDisconnected = options.onDisconnected || (() => { });
        this.onError = options.onError || (() => { });
        this.onMessage = options.onMessage || (() => { });
    }

    /**
     * 连接WebSocket服务器
     * @returns {Promise<void>}
     * @throws {Error} 连接失败时抛出错误
     */
    async connect() {
        try {
            // 预渲染检查
            if (this.isPrerendering) {
                console.log('当前处于预渲染阶段，暂不连接WebSocket');
                return;
            }

            // 连接状态检查
            if (this.ws?.readyState === WebSocket.OPEN || this.ws?.readyState === WebSocket.CONNECTING) {
                console.log('WebSocket已连接或正在连接中');
                return;
            }

            this.ws = new WebSocket(this.url);
            this._setupWebSocketHandlers();
        } catch (error) {
            console.error('连接WebSocket服务器失败:', error);
            this.onError(error);
            throw error;
        }
    }

    /**
     * 设置WebSocket事件处理器
     * @private
     */
    _setupWebSocketHandlers() {
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            this.onConnected();
        };

        this.ws.onclose = () => {
            console.log('WebSocket连接已关闭');
            this.isConnected = false;
            this.stopHeartbeat();
            this.onDisconnected();
            this.shouldReconnect && this.tryReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.onError(error);
        };

        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                console.log('收到消息:', message);
                this.onMessage(event.data);
            } catch (error) {
                console.error('消息处理错误:', error);
                this.onError(error);
            }
        };
    }

    /**
     * 发送消息到服务器
     * @param {string|object} message - 要发送的消息
     * @throws {Error} 未连接或发送失败时抛出错误
     */
    async send(message) {
        if (!this.isConnected) {
            throw new Error('WebSocket未连接');
        }

        try {
            await this.ws.send(typeof message === 'string' ? message : JSON.stringify(message));
        } catch (error) {
            console.error('发送消息失败:', error);
            throw error;
        }
    }

    /**
     * 开始心跳检测
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            this.isConnected && this.send({ messageType: 'Ping' });
        }, this.heartbeatInterval);
    }

    /**
     * 停止心跳检测
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * 尝试重新连接
     */
    tryReconnect() {
        if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数或已禁止重连');
            return;
        }

        this.reconnectTimer = setTimeout(async () => {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            try {
                await this.connect();
            } catch (error) {
                console.error('重连失败:', error);
            }
        }, this.reconnectInterval);
    }

    /**
     * 停止重连
     */
    stopReconnect() {
        this.shouldReconnect = false;
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    /**
     * 断开连接
     */
    async disconnect() {
        this.stopReconnect();
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

// 全局状态管理
const state = {
    printClient: null,
    dotNetRef: null,
    isDisposed: false
};

/**
 * 清理资源
 */
async function cleanupResources() {
    try {
        state.isDisposed = true;
        if (state.printClient) {
            await state.printClient.disconnect();
            state.printClient = null;
        }
        if (state.dotNetRef) {
            state.dotNetRef.dispose();
            state.dotNetRef = null;
        }
    } catch (error) {
        console.error('清理资源失败:', error);
    }
}

/**
 * 调用Blazor方法的安全包装器
 * @param {string} methodName - 要调用的方法名
 * @param {any} args - 方法参数
 */
async function safeInvokeDotNetMethod(methodName, ...args) {
    try {
        if (!state.isDisposed && state.dotNetRef) {
            await state.dotNetRef.invokeMethodAsync(methodName, ...args);
        }
    } catch (error) {
        console.error(`调用${methodName}失败:`, error);
        if (error.message?.includes('DotNetObjectReference instance was already disposed')) {
            await cleanupResources();
        }
    }
}

// 导出给Blazor使用的方法
window.initWebSocketPrintClient = (dotNetReference) => {
    try {
        state.isDisposed = false;
        state.dotNetRef = dotNetReference;
        
        state.printClient = new WebSocketPrintClient({
            url: 'ws://localhost:55555/ws',
            onConnected: () => safeInvokeDotNetMethod('OnConnected'),
            onDisconnected: () => safeInvokeDotNetMethod('OnDisconnected'),
            onError: (error) => {
                const message = typeof error === 'string' ? error : error.message || '未知错误';
                safeInvokeDotNetMethod('OnError', message);
            },
            onMessage: (data) => safeInvokeDotNetMethod('OnMessage', data)
        });
    } catch (error) {
        console.error('初始化WebSocket客户端失败:', error);
        throw error;
    }
};

window.connectWebSocket = async () => {
    try {
        if (!state.printClient || state.isDisposed) {
            throw new Error(state.isDisposed ? 'WebSocket客户端已被释放' : 'WebSocket客户端未初始化');
        }
        await state.printClient.connect();
    } catch (error) {
        console.error('连接WebSocket失败:', error);
        await safeInvokeDotNetMethod('OnError', error.message);
        throw error;
    }
};

window.disconnectWebSocket = async () => {
    try {
        await cleanupResources();
    } catch (error) {
        console.error('断开WebSocket连接失败:', error);
        throw error;
    }
};

window.sendMessage = async (message) => {
    try {
        if (!state.printClient || state.isDisposed) {
            throw new Error(state.isDisposed ? 'WebSocket客户端已被释放' : 'WebSocket客户端未初始化');
        }
        await state.printClient.send(message);
    } catch (error) {
        console.error('发送消息失败:', error);
        await safeInvokeDotNetMethod('OnError', error.message);
        throw error;
    }
}; 