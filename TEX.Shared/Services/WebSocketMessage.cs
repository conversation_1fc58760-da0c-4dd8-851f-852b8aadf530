using System;
using System.Collections.Generic;

namespace TEX.Shared.Services;

/// <summary>
/// WebSocket消息类型枚举
/// </summary>
public enum MessageType
{
    /// <summary>
    /// 心跳请求
    /// </summary>
    Ping,

    /// <summary>
    /// 心跳响应
    /// </summary>
    Pong,

    /// <summary>
    /// 获取打印机列表请求
    /// </summary>
    GetPrinters,

    /// <summary>
    /// 打印机列表响应
    /// </summary>
    PrinterList,

    /// <summary>
    /// 打印请求
    /// </summary>
    PrintRequest,

    /// <summary>
    /// 打印响应
    /// </summary>
    PrintResponse,

    /// <summary>
    /// 打印任务状态
    /// </summary>
    PrintTaskStatus,

    /// <summary>
    /// 获取模板列表请求
    /// </summary>
    GetTemplates,

    /// <summary>
    /// 模板列表响应
    /// </summary>
    TemplateList,

    /// <summary>
    /// 上传模板请求
    /// </summary>
    TemplateUpload,

    /// <summary>
    /// 删除模板请求
    /// </summary>
    TemplateDelete,

    /// <summary>
    /// 错误消息
    /// </summary>
    Error
}

/// <summary>
/// WebSocket消息基类
/// </summary>
public class WebSocketMessageBase
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public MessageType MessageType { get; set; }
}

/// <summary>
/// 打印请求消息
/// </summary>
public class PrintRequestMessage : WebSocketMessageBase
{
    public PrintRequestMessage()
    {
        MessageType = MessageType.PrintRequest;
    }

    /// <summary>
    /// 打印机名称
    /// </summary>
    public string PrinterName { get; set; } = string.Empty;

    /// <summary>
    /// 模板名称
    /// </summary>
    public string PrintFileName { get; set; } = string.Empty;

    /// <summary>
    /// 打印数据(JSON格式)
    /// </summary>
    public string PrintData { get; set; } = string.Empty;
    public string DataTypeName { get; set; } = string.Empty;

    /// <summary>
    /// 打印份数
    /// </summary>
    public int Copies { get; set; } = 1;

    /// <summary>
    /// 是否预览
    /// </summary>
    public bool IsPreview { get; set; } = false;
}

/// <summary>
/// 打印状态消息
/// </summary>
public class PrintStatusMessage : WebSocketMessageBase
{
    public PrintStatusMessage()
    {
        MessageType = MessageType.PrintResponse;
    }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 已打印份数
    /// </summary>
    public int PrintedCopies { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 打印任务ID
    /// </summary>
    public string JobId { get; set; } = string.Empty;
}

/// <summary>
/// 打印机列表消息
/// </summary>
public class PrinterListMessage : WebSocketMessageBase
{
    public PrinterListMessage()
    {
        MessageType = MessageType.PrinterList;
    }

    /// <summary>
    /// 打印机列表
    /// </summary>
    public string[] Printers { get; set; } = Array.Empty<string>();
}

/// <summary>
/// 打印任务状态消息
/// </summary>
public class PrintTaskStatusMessage : WebSocketMessageBase
{
    public PrintTaskStatusMessage()
    {
        MessageType = MessageType.PrintTaskStatus;
    }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 模板列表消息
/// </summary>
public class TemplateListMessage : WebSocketMessageBase
{
    public TemplateListMessage()
    {
        MessageType = MessageType.TemplateList;
    }

    /// <summary>
    /// 模板列表
    /// </summary>
    public Template[] Templates { get; set; } = Array.Empty<Template>();
}

/// <summary>
/// 模板信息
/// </summary>
public class Template
{
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public string LastModified { get; set; } = string.Empty;
}

/// <summary>
/// 模板上传消息
/// </summary>
public class TemplateUploadMessage : WebSocketMessageBase
{
    public TemplateUploadMessage()
    {
        MessageType = MessageType.TemplateUpload;
    }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// 模板内容(Base64编码)
    /// </summary>
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// 模板删除消息
/// </summary>
public class TemplateDeleteMessage : WebSocketMessageBase
{
    public TemplateDeleteMessage()
    {
        MessageType = MessageType.TemplateDelete;
    }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;
}

/// <summary>
/// 错误消息
/// </summary>
public class ErrorMessage : WebSocketMessageBase
{
    public ErrorMessage()
    {
        MessageType = MessageType.Error;
    }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    public string Details { get; set; } = string.Empty;
}

/// <summary>
/// 心跳响应消息
/// </summary>
public class PongMessage : WebSocketMessageBase
{
    public PongMessage()
    {
        MessageType = MessageType.Pong;
    }

    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public string Timestamp { get; set; } = string.Empty;

    /// <summary>
    /// 连接状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 延迟（毫秒）
    /// </summary>
    public double Latency { get; set; }

    /// <summary>
    /// 已接收消息数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 已发送消息数
    /// </summary>
    public long MessagesSent { get; set; }
}