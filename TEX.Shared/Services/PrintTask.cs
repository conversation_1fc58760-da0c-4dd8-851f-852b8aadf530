using System;

namespace TEX.Shared.Services
{
    /// <summary>
    /// 打印任务类
    /// </summary>
    public class PrintTask
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 打印请求信息
        /// </summary>
        public PrintRequestMessage PrintRequest { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 任务状态
        /// </summary>
        public PrintTaskStatus Status { get; set; } = PrintTaskStatus.Waiting;
    }

    /// <summary>
    /// 打印任务状态
    /// </summary>
    public enum PrintTaskStatus
    {
        Waiting,    // 等待打印
        Printing,   // 打印中
        Completed,  // 已完成
        Failed      // 失败
    }
} 