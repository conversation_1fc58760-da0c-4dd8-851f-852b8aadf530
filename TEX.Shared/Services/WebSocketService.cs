using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;


namespace TEX.Shared.Services;

/// <summary>
/// WebSocket客户端服务
/// </summary>
public class WebSocketService : IAsyncDisposable
{
    private readonly ILogger<WebSocketService> _logger;
    private ClientWebSocket? _webSocket;
    private CancellationTokenSource? _cancellationTokenSource;
    private bool _isConnected;
    private readonly Timer _heartbeatTimer;
    private readonly Timer _reconnectTimer;
    private string? _lastConnectedUrl;
    private DateTime _lastHeartbeatResponse = DateTime.Now;
    private const int HeartbeatInterval = 5000; // 5秒发送一次心跳
    private const int HeartbeatTimeout = 15000; // 15秒没有响应则认为断开
    private const int ReconnectInterval = 5000; // 5秒尝试重连一次
    private const int MaxReconnectAttempts = 5; // 最大重连次数
    private int _currentReconnectAttempts = 0;
    private bool _allowAutoReconnect = true; // 是否允许自动重连

    // 事件定义
    public event Action? OnConnected;
    public event Action? OnDisconnected;
    public event Action<string>? OnError;
    public event Action<PrintStatusMessage>? OnPrintStatus;
    public event Action<PrinterListMessage>? OnPrinterList;
    public event Action<TemplateListMessage>? OnTemplateList;

    public WebSocketService(ILogger<WebSocketService> logger)
    {
        _logger = logger;
        // 创建心跳定时器
        _heartbeatTimer = new Timer(SendHeartbeat, null, Timeout.Infinite, HeartbeatInterval);
        // 创建重连定时器
        _reconnectTimer = new Timer(TryReconnect, null, Timeout.Infinite, ReconnectInterval);
    }

    /// <summary>
    /// 连接到WebSocket服务器
    /// </summary>
    public async Task ConnectAsync(string url = "ws://localhost:55555/ws")
    {
        // 先检查并更新状态
        if (_webSocket?.State == WebSocketState.Open)
        {
            await DisconnectAsync();
        }

        _allowAutoReconnect = true; // 手动连接时启用自动重连
        _lastConnectedUrl = url;
        await ConnectInternalAsync(url);
    }

    private async Task ConnectInternalAsync(string url)
    {
        // 确保先清理旧的连接
        if (_webSocket != null)
        {
            _webSocket.Dispose();
            _webSocket = null;
        }

        try
        {
            _logger.LogInformation("开始连接到 WebSocket 服务器: {Url}", url);
            _webSocket = new ClientWebSocket();
            _cancellationTokenSource = new CancellationTokenSource();
            await _webSocket.ConnectAsync(new Uri(url), _cancellationTokenSource.Token);

            _isConnected = true;
            _currentReconnectAttempts = 0;
            _lastHeartbeatResponse = DateTime.Now;
            OnConnected?.Invoke();

            // 启动心跳
            _heartbeatTimer.Change(0, HeartbeatInterval);

            _ = ReceiveMessagesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "WebSocket连接失败");
            _isConnected = false;
            await HandleConnectionFailure();
            throw;
        }
    }

    /// <summary>
    /// 获取打印机列表
    /// </summary>
    public async Task GetPrintersAsync()
    {
        var message = new WebSocketMessageBase { MessageType = MessageType.GetPrinters };
        await SendMessageAsync(message);
    }

    /// <summary>
    /// 获取模板列表
    /// </summary>
    public async Task GetTemplatesAsync()
    {
        var message = new WebSocketMessageBase { MessageType = MessageType.GetTemplates };
        await SendMessageAsync(message);
    }

    /// <summary>
    /// 发送打印请求
    /// </summary>
    public async Task SendPrintRequestAsync(PrintRequestMessage request)
    {
        await SendMessageAsync(request);
    }

    /// <summary>
    /// 上传模板
    /// </summary>
    public async Task UploadTemplateAsync(string templateName, byte[] content)
    {
        var message = new TemplateUploadMessage
        {
            TemplateName = templateName,
            Content = Convert.ToBase64String(content)
        };
        await SendMessageAsync(message);
    }

    /// <summary>
    /// 删除模板
    /// </summary>
    public async Task DeleteTemplateAsync(string templateName)
    {
        var message = new TemplateDeleteMessage
        {
            TemplateName = templateName
        };
        await SendMessageAsync(message);
    }

    private async void SendHeartbeat(object? state)
    {
        if (!_isConnected || _webSocket?.State != WebSocketState.Open)
        {
            return;
        }

        try
        {
            // 检查上次心跳响应时间
            var timeSinceLastResponse = (DateTime.Now - _lastHeartbeatResponse).TotalMilliseconds;
            if (timeSinceLastResponse > HeartbeatTimeout)
            {
                _logger.LogWarning("心跳超时 ({Timeout}ms)，准备重连", timeSinceLastResponse);
                await HandleConnectionFailure();
                return;
            }

            var heartbeat = new WebSocketMessageBase { MessageType = MessageType.Ping };
            await SendMessageAsync(heartbeat);
            _logger.LogTrace("已发送心跳");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送心跳包时发生错误");
            await HandleConnectionFailure();
        }
    }

    private async void TryReconnect(object? state)
    {
        if (_isConnected || !_allowAutoReconnect || _currentReconnectAttempts >= MaxReconnectAttempts || string.IsNullOrEmpty(_lastConnectedUrl))
        {
            _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);
            return;
        }

        _currentReconnectAttempts++;
        _logger.LogInformation("尝试重连 (第 {Attempts} 次)", _currentReconnectAttempts);

        try
        {
            await ConnectInternalAsync(_lastConnectedUrl);
            _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重连失败");
            if (_currentReconnectAttempts >= MaxReconnectAttempts)
            {
                _logger.LogError("达到最大重连次数，停止重连");
                _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);
                OnError?.Invoke("达到最大重连次数，请手动重新连接");
            }
        }
    }

    private async Task HandleConnectionFailure()
    {
        _logger.LogWarning("处理连接失败，当前重连次数: {Attempts}", _currentReconnectAttempts);

        await DisconnectAsync();

        // 只有在允许自动重连的情况下才启动重连定时器
        if (_allowAutoReconnect && _currentReconnectAttempts < MaxReconnectAttempts)
        {
            _reconnectTimer.Change(0, ReconnectInterval);
        }
        else if (!_allowAutoReconnect)
        {
            _logger.LogError("禁用了自动重连，停止重连");
            _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }
        else
        {
            _logger.LogError("达到最大重连次数 {MaxAttempts}，停止重连", MaxReconnectAttempts);
            _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }
    }

    private async Task ReceiveMessagesAsync()
    {
        var buffer = new byte[4096];
        var messageBuffer = new List<byte>();
        try
        {
            while (_webSocket?.State == WebSocketState.Open)
            {
                var result = await _webSocket.ReceiveAsync(
                    new ArraySegment<byte>(buffer), _cancellationTokenSource?.Token ?? CancellationToken.None);

                if (result.MessageType == WebSocketMessageType.Close)
                {
                    await HandleConnectionFailure();
                    break;
                }

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    messageBuffer.AddRange(new ArraySegment<byte>(buffer, 0, result.Count));
                    if (result.EndOfMessage)
                    {
                        var message = Encoding.UTF8.GetString(messageBuffer.ToArray());
                        messageBuffer.Clear();

                        await ProcessMessage(message);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "接收WebSocket消息时发生错误");
            await HandleConnectionFailure();
        }
    }

    private Task ProcessMessage(string message)
    {
        try
        {
            var settings = new JsonSerializerSettings
            {
                MaxDepth = 64,
                StringEscapeHandling = StringEscapeHandling.Default,
                TypeNameHandling = TypeNameHandling.None,
                Formatting = Formatting.None,
                DateFormatString = "o"  // 使用 ISO 8601 格式
            };

            var baseMessage = JsonConvert.DeserializeObject<WebSocketMessageBase>(message, settings);
            if (baseMessage == null)
            {
                _logger.LogError("无法解析消息: {Message}", message);
                OnError?.Invoke("无效的消息格式");
                return Task.CompletedTask;
            }

            switch (baseMessage.MessageType)
            {
                case MessageType.Pong:
                    var pong = JsonConvert.DeserializeObject<PongMessage>(message, settings);
                    if (pong != null)
                    {
                        _lastHeartbeatResponse = DateTime.Now;
                    }
                    break;

                case MessageType.PrinterList:
                    var printerList = JsonConvert.DeserializeObject<PrinterListMessage>(message, settings);
                    if (printerList != null)
                    {
                        OnPrinterList?.Invoke(printerList);
                    }
                    break;

                case MessageType.PrintResponse:
                    var printStatus = JsonConvert.DeserializeObject<PrintStatusMessage>(message, settings);
                    if (printStatus != null)
                    {
                        OnPrintStatus?.Invoke(printStatus);
                    }
                    break;

                case MessageType.TemplateList:
                    var templateList = JsonConvert.DeserializeObject<TemplateListMessage>(message, settings);
                    if (templateList != null)
                    {
                        OnTemplateList?.Invoke(templateList);
                    }
                    break;

                case MessageType.Error:
                    var error = JsonConvert.DeserializeObject<ErrorMessage>(message, settings);
                    if (error != null)
                    {
                        OnError?.Invoke(error.Message);
                    }
                    break;

                default:
                    _logger.LogWarning("未知的消息类型: {MessageType}", baseMessage.MessageType);
                    break;
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "消息反序列化失败: {Message}", message);
            OnError?.Invoke($"消息格式错误: {ex.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息时发生错误: {Message}", message);
            OnError?.Invoke($"消息处理错误: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    private async Task SendMessageAsync<T>(T message) where T : WebSocketMessageBase
    {
        if (_webSocket?.State != WebSocketState.Open)
        {
            throw new InvalidOperationException("WebSocket未连接");
        }

        try
        {
            var settings = new JsonSerializerSettings
            {
                MaxDepth = 64,
                StringEscapeHandling = StringEscapeHandling.Default,
                TypeNameHandling = TypeNameHandling.None,
                Formatting = Formatting.None,
                DateFormatString = "o"  // 使用 ISO 8601 格式
            };

            var json = JsonConvert.SerializeObject(message, settings);
            var buffer = Encoding.UTF8.GetBytes(json);

            var maxChunkSize = 4096; // WebSocket推荐的最大消息块大小
            var totalBytes = buffer.Length;
            var sentBytes = 0;

            while (sentBytes < totalBytes)
            {
                var remainingBytes = totalBytes - sentBytes;
                var chunkSize = Math.Min(maxChunkSize, remainingBytes);
                var isLastChunk = (sentBytes + chunkSize) >= totalBytes;

                await _webSocket.SendAsync(
                    new ArraySegment<byte>(buffer, sentBytes, chunkSize),
                    WebSocketMessageType.Text,
                    isLastChunk,
                    _cancellationTokenSource?.Token ?? CancellationToken.None);

                sentBytes += chunkSize;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送WebSocket消息时发生错误");
            await HandleConnectionFailure();
            throw;
        }
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    public async Task DisconnectAsync()
    {
        try
        {
            // 先停止定时器
            _heartbeatTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // 禁用自动重连
            _allowAutoReconnect = false;

            if (_webSocket != null)
            {
                // 如果连接还是打开状态，尝试正常关闭
                if (_webSocket.State == WebSocketState.Open)
                {
                    try
                    {
                        await _webSocket.CloseAsync(
                            WebSocketCloseStatus.NormalClosure,
                            "Client requested close",
                            CancellationToken.None);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "关闭WebSocket连接时发生错误");
                    }
                }

                // 取消所有正在进行的操作
                if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                }

                // 等待一小段时间，确保所有操作都被取消
                await Task.Delay(100);

                // 清理资源
                try
                {
                    if (_webSocket is not null)
                    {
                        _webSocket.Dispose();
                        _webSocket = null;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理WebSocket资源时发生错误");
                }
            }

            // 清理和重新创建CancellationTokenSource
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = new CancellationTokenSource();
            }

            // 最后更新状态
            _isConnected = false;
            OnDisconnected?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开连接时发生错误");
            throw;
        }
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            if (_isConnected)
            {
                await DisconnectAsync();
            }

            if (_cancellationTokenSource != null)
            {
                await Task.Run(() => {
                    if (!_cancellationTokenSource.IsCancellationRequested)
                    {
                        _cancellationTokenSource.Cancel();
                    }
                    _cancellationTokenSource.Dispose();
                });
            }

            if (_webSocket != null)
            {
                await Task.Run(() => _webSocket.Dispose());
            }

            if (_heartbeatTimer != null)
            {
                await _heartbeatTimer.DisposeAsync();
            }

            if (_reconnectTimer != null)
            {
                await _reconnectTimer.DisposeAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放资源时发生错误");
        }
    }

    /// <summary>
    /// 获取连接状态
    /// </summary>
    public bool IsConnected => _isConnected;
}

