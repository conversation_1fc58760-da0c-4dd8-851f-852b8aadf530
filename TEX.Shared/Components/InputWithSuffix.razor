@namespace TEX.Shared.Components
@inherits BootstrapComponentBase
<div class="input-group">
    <BootstrapInputGroupLabel DisplayText="@DisplayText" ShowRequiredMark="@Required" />
    <div @attributes="@AdditionalAttributes" class="@ClassString">

        <CascadingValue Value="this" IsFixed="true">
            @ChildContent
        </CascadingValue>
        <span class="input-group-text">@Unit</span>
    </div>
</div>

@code {
    /// <summary>
    /// 获取/设置 Value
    /// </summary>

    [Parameter]
    public RenderFragment ChildContent { get; set; }
    /// <summary>
    /// 单位文本
    /// </summary>
    [Parameter]
    public string Unit { get; set; }
    /// <summary>
    /// 显示文本
    /// </summary>
    [Parameter]
    public string DisplayText { get; set; }

    [Parameter]
    public bool Required { get; set; } = true;

    /// <summary>
    /// 获取 class 字符串
    /// </summary>
    protected virtual string? ClassString => CssBuilder.Default("input-group")
        .AddClassFromAttributes(AdditionalAttributes)
        .Build();
}