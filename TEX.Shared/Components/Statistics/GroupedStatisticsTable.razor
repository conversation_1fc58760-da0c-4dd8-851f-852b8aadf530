@using TEX.Model.Statistics
@namespace BootstrapBlazor.Components

<Card IsShadow="true" HeaderText="@Title">
    <BodyTemplate>
        @if (IsLoading)
        {
            <div class="text-center p-4">
                <Spinner Color="Color.Primary" />
                <div class="mt-2">加载中...</div>
            </div>
        }
        else if (StatisticsData == null || !StatisticsData.Any())
        {
            <Empty Text="暂无数据" />
        }
        else
        {
            <Table TItem="GroupedStatisticsResult" 
                   Items="@StatisticsData" 
                   IsPagination="false" 
                   IsStriped="true" 
                   IsBordered="true"
                   ShowSkeleton="false"
                   >
                <TableColumns>
                    <TableColumn @bind-Field="@context.GroupName" Text="@GroupColumnTitle" Width="200" />
                    <TableColumn @bind-Field="@context.TotalPcs" Text="件数" Align="Alignment.Right" FormatString="N0" />
                    <TableColumn @bind-Field="@context.TotalWeight" Text="重量(KG)" Align="Alignment.Right" FormatString="N1" />
                    <TableColumn @bind-Field="@context.TotalMeters" Text="米数" Align="Alignment.Right" FormatString="N1" />
                    <TableColumn @bind-Field="@context.TotalYards" Text="码数" Align="Alignment.Right" FormatString="N1" />
                    <TableColumn @bind-Field="@context.TotalBills" Text="单据数" Align="Alignment.Right" FormatString="N0" />
                    <TableColumn @bind-Field="@context.TotalLots" Text="批次数" Align="Alignment.Right" FormatString="N0" />
                </TableColumns>
                <RowButtonTemplate>
                    <div style="padding-right:10px;">
                        <TableCellButton Size="Size.ExtraSmall" Color="Color.Primary" Icon="fas fa-search-plus" Text="查询" OnClick="() => OnDrillDown(context)" />

                    </div>
                </RowButtonTemplate>
                <TableFooter>
                    <tr class="table-info">
                        <td><strong>合计</strong></td>
                        <td class="text-end"><strong>@TotalStatistics.TotalPcs.ToString("N0")</strong></td>
                        <td class="text-end"><strong>@TotalStatistics.TotalWeight.ToString("N1")</strong></td>
                        <td class="text-end"><strong>@TotalStatistics.TotalMeters.ToString("N1")</strong></td>
                        <td class="text-end"><strong>@TotalStatistics.TotalYards.ToString("N1")</strong></td>
                        <td class="text-end"><strong>@TotalStatistics.TotalBills.ToString("N0")</strong></td>
                        <td class="text-end"><strong>@TotalStatistics.TotalLots.ToString("N0")</strong></td>
                        <td></td>
                    </tr>
                </TableFooter>
            </Table>
        }
    </BodyTemplate>
</Card>

@code {
    /// <summary>
    /// 表格标题
    /// </summary>
    [Parameter]
    public string Title { get; set; } = "分组统计";

    /// <summary>
    /// 分组列标题
    /// </summary>
    [Parameter]
    public string GroupColumnTitle { get; set; } = "分组";

    /// <summary>
    /// 统计数据
    /// </summary>
    [Parameter]
    public IEnumerable<GroupedStatisticsResult> StatisticsData { get; set; }

    /// <summary>
    /// 是否加载中
    /// </summary>
    [Parameter]
    public bool IsLoading { get; set; }

    /// <summary>
    /// 行点击事件
    /// </summary>
    [Parameter]
    public EventCallback<GroupedStatisticsResult> OnRowClick { get; set; }

    /// <summary>
    /// 钻取事件
    /// </summary>
    [Parameter]
    public EventCallback<GroupedStatisticsResult> OnDrillDownClick { get; set; }

    /// <summary>
    /// 合计统计
    /// </summary>
    private StatisticsResult TotalStatistics => new StatisticsResult
    {
        TotalPcs = StatisticsData?.Sum(x => x.TotalPcs) ?? 0,
        TotalWeight = StatisticsData?.Sum(x => x.TotalWeight) ?? 0,
        TotalMeters = StatisticsData?.Sum(x => x.TotalMeters) ?? 0,
        TotalYards = StatisticsData?.Sum(x => x.TotalYards) ?? 0,
        TotalBills = StatisticsData?.Sum(x => x.TotalBills) ?? 0,
        TotalLots = StatisticsData?.Sum(x => x.TotalLots) ?? 0
    };


    private async Task OnDrillDown(GroupedStatisticsResult row)
    {
        if (OnDrillDownClick.HasDelegate)
        {
            await OnDrillDownClick.InvokeAsync(row);
        }
    }
}

<style>
    .table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
        cursor: pointer;
    }
    
    .table-info {
        background-color: rgba(13, 202, 240, 0.1) !important;
    }
</style>
