@using TEX.Model.Statistics
@namespace BootstrapBlazor.Components

<div class="@ColumnClass">
    <Card IsShadow="true" style="margin: 0 2px; cursor: pointer;" @onclick="OnCardClick">
        <BodyTemplate>
            <div class="row align-items-center no-gutters" style="font-size: 0.9rem;">
                <div class="col me-2">
                    <div class="text-@IconColor fw-bold mb-1 h6">
                        <i class="@IconClass me-2"></i>
                        <span>@Title</span>
                    </div>
                    <div class="text-dark fw-bold h4">
                        @if (IsLoading)
                        {
                            <div class="placeholder-glow">
                                <span class="placeholder col-6"></span>
                            </div>
                        }
                        else
                        {
                            <CountUp Value="@DisplayValue"></CountUp>
                            @if (!string.IsNullOrEmpty(Unit))
                            {
                                <small class="text-muted ms-1">@Unit</small>
                            }
                        }
                    </div>
                    @if (!string.IsNullOrEmpty(SubTitle))
                    {
                        <div class="text-muted small">@SubTitle</div>
                    }
                </div>
                <div class="col-auto">
                    <div class="text-@IconColor">
                        <i class="@IconClass fa-2x opacity-25"></i>
                    </div>
                </div>
            </div>
        </BodyTemplate>
    </Card>
</div>

@code {
    /// <summary>
    /// 标题
    /// </summary>
    [Parameter]
    public string Title { get; set; } = "";

    /// <summary>
    /// 副标题
    /// </summary>
    [Parameter]
    public string SubTitle { get; set; } = "";

    /// <summary>
    /// 显示值
    /// </summary>
    [Parameter]
    public decimal DisplayValue { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Parameter]
    public string Unit { get; set; } = "";

    /// <summary>
    /// 图标类名
    /// </summary>
    [Parameter]
    public string IconClass { get; set; } = "fas fa-chart-bar";

    /// <summary>
    /// 图标颜色
    /// </summary>
    [Parameter]
    public string IconColor { get; set; } = "primary";

    /// <summary>
    /// 列样式类
    /// </summary>
    [Parameter]
    public string ColumnClass { get; set; } = "col-md-3 col-lg-3";

    /// <summary>
    /// 是否加载中
    /// </summary>
    [Parameter]
    public bool IsLoading { get; set; }

    /// <summary>
    /// 点击事件
    /// </summary>
    [Parameter]
    public EventCallback OnClick { get; set; }

    /// <summary>
    /// 钻取参数
    /// </summary>
    [Parameter]
    public object DrillDownData { get; set; }

    private async Task OnCardClick()
    {
        if (OnClick.HasDelegate)
        {
            await OnClick.InvokeAsync();
        }
    }
}

<style>
    .statistics-card:hover {
        transform: translateY(-2px);
        transition: transform 0.2s ease-in-out;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style>
