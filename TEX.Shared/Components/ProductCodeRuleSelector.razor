@namespace TEX.Shared.Components
@using TEX.ViewModel.Models.ProductVMs

@inherits ComponentBase;

@implements IResultDialog

<div class="input-group">
    <Select Items="@CodeRules" @bind-Value="@item" />
    <Button Color="Color.Secondary" OnClick="@OnRuleSelected" Text="关闭" />

</div>

<style>
    .rule-list {
        min-height: 300px;
        overflow-y: auto;
    }
</style>

@code {
    private List<SelectedItem> CodeRules { get; set; } = new List<SelectedItem>();
    private SelectedItem item { get; set; }

    [Parameter]
    public Product Model { get; set; }

    [Parameter]
    public EventCallback<string> OnCodeGenerated { get; set; }

    [Inject]
    private WtmBlazorContext WtmBlazor { get; set; }

    //TODO:  本想弹出命名规则列表,选择规则后命名,但是更应该在配置中选择好命名规则,否则命名方式太混乱

    protected override async Task OnInitializedAsync()
    {
        CodeRules = await WtmBlazor.Api.CallItemsApi("/api/dictitem/getdictitemsbydictname/ProductCodeRule");
        await base.OnInitializedAsync();
    }



    private async Task OnRuleSelected()
    {
        if (item == null) return;

        var code = NamedRuleHelper.GenerateProductCode(item.Value, Model);
        await OnCodeGenerated.InvokeAsync(code);

    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {

        }
    }
} 