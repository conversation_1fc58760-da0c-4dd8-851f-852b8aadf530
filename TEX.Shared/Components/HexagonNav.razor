@namespace TEX.Shared.Components
@using BootstrapBlazor.Components;
@using MenuItem=BootstrapBlazor.Components.MenuItem;
@using WalkingTec.Mvvm.Core.Support.Json;
@inject NavigationManager NavigationManager

<div class="pt-tablecell relative">
    <div class="hex-container clear">
        @if (NavItems != null)
        {
            @foreach (var item in NavItems)
            {
                <div class="hexagon-item">
                    <div class="hex-item">
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="hex-item">
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div @onmouseover="@(() => ShowSubMenu(item))" 
                         @onmouseout="@(() => HideSubMenu())" 
                         class="hex-content" 
                         style="cursor:pointer;">
                        <span class="hex-content-inner">
                            <span class="icon">
                                <i class="@item.Icon @(HasChildren(item) ? "submenu-open" : "")"></i>
                            </span>
                            <span class="title @(HasChildren(item) ? "submenu-open" : "")">@item.Text</span>
                        </span>
                        <svg viewBox="0 0 173.20508075688772 200" height="200" width="174" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path d="M86.60254037844386 0L173.20508075688772 50L173.20508075688772 150L86.60254037844386 200L0 150L0 50Z" fill="#1e2530"></path>
                        </svg>
                        
                        @if (currentItem == item && HasChildren(item))
                        {
                            <div class="submenu-container">
                                @foreach (var subItem in GetChildren(item))
                                {
                                    <div class="submenu-item" @onclick="@(() => NavigateToPage(subItem.Url))">
                                        <i class="@subItem.Icon"></i>
                                        <span>@subItem.Text</span>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            }
        }
    </div>
</div>

@code {
    [Parameter]
    public List<MenuItem> NavItems { get; set; } = new();

    [CascadingParameter]
    public LoginUserInfo UserInfo { get; set; }

    private MenuItem currentItem;
    private Dictionary<string, List<WalkingTec.Mvvm.Core.Support.Json.SimpleMenuApi>> menuTree = new();

    protected override void OnInitialized()
    {
        if (!NavItems.Any() && UserInfo?.Attributes != null)
        {
            var usermenu = UserInfo.Attributes["Menus"] as SimpleMenuApi[];
            if (usermenu != null)
            {
                // 构建菜单树
                var parentMenus = usermenu.Where(x => x.ParentId == null).ToList();
                NavItems = parentMenus.Select(x => new MenuItem { Text = x.Text, Icon = x.Icon, Url = x.Url }).ToList();
                
                // 保存子菜单数据
                foreach (var menu in parentMenus)
                {
                    var children = usermenu.Where(x => x.ParentId == menu.Id).ToList();
                    if (children.Any())
                    {
                        menuTree[menu.Text] = children;
                    }
                }
            }
        }
    }

    private void ShowSubMenu(MenuItem item)
    {
        currentItem = item;
        StateHasChanged();
    }

    private void HideSubMenu()
    {
        currentItem = null;
        StateHasChanged();
    }

    private bool HasChildren(MenuItem item)
    {
        return menuTree.ContainsKey(item.Text) && menuTree[item.Text].Any();
    }

    private List<SimpleMenuApi> GetChildren(MenuItem item)
    {
        return menuTree.ContainsKey(item.Text) ? menuTree[item.Text] : new List<SimpleMenuApi>();
    }

    private void NavigateToPage(string url)
    {
        if (!string.IsNullOrEmpty(url))
        {
            NavigationManager.NavigateTo(url, forceLoad: true);
        }
    }
} 