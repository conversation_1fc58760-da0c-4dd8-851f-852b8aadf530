<Button OnClick="HandleClick" Color="Value? Color.Success : Color.Secondary" Text="@(Value? OnText : OffText)" />

@code {
    [Parameter]
    public bool Value { get; set; }

    [Parameter]
    public EventCallback<bool> ValueChanged { get; set; }

    [Parameter]
    public string OnText { get; set; } = "开";

    [Parameter]
    public string OffText { get; set; } = "关";

    private Color color = Color.Success;

    private async Task HandleClick()
    {
        Value = !Value;
        await ValueChanged.InvokeAsync(Value);
    }

}
