@using Microsoft.AspNetCore.Components.Forms

<div class="input-group">
    <BootstrapInputGroupLabel DisplayText="@DisplayText" ShowRequiredMark="@ShowRequiredMark" />
    <div class="input-group">
        <BootstrapInput @bind-Value="Value" ShowLabel="false" OnValueChanged="@OnValueChanged"></BootstrapInput>
        <Button Icon="@ButtonIcon" OnClick="@OnButtonClick">@ButtonText</Button>
    </div>
</div>

@code {
    [Parameter]
    public string DisplayText { get; set; } = String.Empty;
    [Parameter]
    public string ButtonText { get; set; } = String.Empty;
    [Parameter]
    public string ButtonIcon { get; set; } = "fa-solid fa-edit";

    [Parameter]
    public string Value { get; set; }

    [Parameter]
    public EventCallback<string> ValueChanged { get; set; }

    [Parameter]
    public bool ShowRequiredMark { get; set; } = true;

    [Parameter]
    public EventCallback OnButtonClick { get; set; }

    //TODO ������ʾ������֤,����
    private async Task OnValueChanged(string value)
    {
        Value = value;
        if (ValueChanged.HasDelegate)
            await ValueChanged.InvokeAsync(value);
    }
} 