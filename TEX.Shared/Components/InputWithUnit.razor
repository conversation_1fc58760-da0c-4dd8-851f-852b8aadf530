@namespace TEX.Shared.Components
@inherits BootstrapComponentBase
@typeparam TValue

<div class="input-group">
    <BootstrapInputGroupLabel DisplayText="@DisplayText" ShowRequiredMark="@Required" />
    <div @attributes="@AdditionalAttributes" class="@ClassString">
        <BootstrapInputNumber @bind-Value="@Value" ShowLabel="false" IsDisabled="@Disabled" OnValueChanged="@OnValueChanged" />
        <span class="input-group-text">@Unit</span>
    </div>
</div>


@code {
    [Parameter]
    public string? Unit { get; set; }

    [Parameter]
    public TValue? Value { get; set; }

    [Parameter]
    public EventCallback<TValue?> ValueChanged { get; set; }

    [Parameter]
    public string DisplayText { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool Required { get; set; }


    protected virtual string? ClassString => CssBuilder.Default("input-group")
        .AddClassFromAttributes(AdditionalAttributes)
        .Build();

    private async Task OnValueChanged(TValue newValue)
    {
        Value = newValue;
        await ValueChanged.InvokeAsync(Value);
    }

}