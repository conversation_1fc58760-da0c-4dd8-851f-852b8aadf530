/* 遮罩层样式 */
.overlay {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

/* 相对定位容器 */
.relative {
    position: relative;
}

/* 清除浮动工具类 */
.clear:before, .clear:after {
    content: " ";
    display: table;
}

.clear:after {
    clear: both;
}

/* 蜂窝容器样式 */
.hex-container {
    display: block;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    padding: 20px;
    max-width: 900px; /* 限制容器最大宽度，确保每行最多4个蜂窝 */
}

/* 单个蜂窝项样式 */
.hexagon-item {
    position: relative;
    width: 200px;
    height: 173.20508px; /* 高度 = 宽度 * cos(30°) */
    float: left;
    margin-left: -29px; /* 负边距实现蜂窝堆叠效果 */
    z-index: 0;
    transform: rotate(30deg); /* 旋转形成蜂窝形状 */
    transition: all 0.3s ease;
    cursor: pointer;
}

/* 每行第一个蜂窝的样式 */
.hexagon-item:nth-child(4n + 1) {
    margin-left: 0;
    clear: left; /* 清除浮动，确保正确换行 */
}

/* 第二行及以后的蜂窝位置调整 */
.hexagon-item:nth-child(n + 5) {
    margin-top: 0px;
}

.hexagon-item:nth-child(n + 5) {
    transform: rotate(30deg) translate(62px, -63px); /* 位置偏移实现交错效果 */
}

/* 蜂窝边框容器 */
.hex-item {
    position: absolute;
    top: 0;
    left: 50px;
    width: 100px;
    height: 173.20508px;
}

/* 蜂窝边框的基础样式 */
.hex-item div {
    position: absolute;
    width: 100px;
    height: 173.20508px;
    transform-origin: center center;
}

/* 三个边框div的旋转角度，形成六边形 */
.hex-item div:nth-child(1) { transform: rotate(0deg); }
.hex-item div:nth-child(2) { transform: rotate(60deg); }
.hex-item div:nth-child(3) { transform: rotate(120deg); }

/* 边框线条样式 */
.hex-item div::before,
.hex-item div::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 3px;
    background-color: #1e2530;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
}

/* 上边框位置 */
.hex-item div::before {
    top: 0;
    left: 0;
}

/* 下边框位置 */
.hex-item div::after {
    bottom: 0;
    left: 0;
}

/* 内层边框样式 */
.hex-item:first-child {
    z-index: 0;
    transform: scale(0.9); /* 缩小形成内边框效果 */
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* 外层边框样式 */
.hex-item:last-child {
    z-index: 1;
    transform: scale(0.9);
    opacity: 0; /* 初始透明 */
    transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 内容区域样式 */
.hex-content {
    color: #fff;
    display: block;
    height: 180px;
    margin: 0 auto;
    position: relative;
    text-align: center;
    transform: rotate(-30deg); /* 抵消父元素的旋转 */
    width: 156px;
    z-index: 2;
}

/* 内容居中定位 */
.hex-content-inner {
    left: 50%;
    margin: -3px 0 0 2px;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

/* 图标样式 */
.icon {
    display: block;
    font-size: 36px;
    line-height: 30px;
    margin-bottom: 11px;
}

/* 标题文字样式 */
.title {
    display: block;
    font-family: "Open Sans", sans-serif;
    font-size: 18px;
    letter-spacing: 1px;
    line-height: 24px;
    text-transform: uppercase;
    opacity: 1;
}

/* 悬停状态 - 提升层级 */
.hexagon-item:hover {
    z-index: 100;
}

/* 悬停时内层边框动画 */
.hexagon-item:hover .hex-item:first-child {
    transform: scale(1.2);
}

/* 悬停时外层边框动画 */
.hexagon-item:hover .hex-item:last-child {
    transform: scale(1.3);
    opacity: 1;
}

/* 悬停时边框颜色变化 */
.hexagon-item:hover .hex-item div::before,
.hexagon-item:hover .hex-item div::after {
    background-color: #409eff;
}

/* 悬停时图标颜色变化 */
.hexagon-item:hover .icon i:not(.submenu-open) {
    color: #409eff;
    transition: 0.6s;
}

/* 背景SVG样式 */
.hex-content svg {
    left: -7px;
    position: absolute;
    top: -13px;
    transform: scale(0.87);
    z-index: -1;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
}

/* 响应式布局适配 */
@media only screen and (max-width: 767px) {
    .hex-container {
        max-width: 100%;
    }

    .hexagon-item {
        float: none;
        margin: 0 auto 50px;
        transform: rotate(30deg) !important;
    }
    
    .hexagon-item:nth-child(n) {
        margin-left: auto;
        margin-top: 0;
        clear: none;
        transform: rotate(30deg) !important;
    }
}

/* 容器清除浮动 */
.hex-container:before,
.hex-container:after {
    content: " ";
    display: table;
}

.hex-container:after {
    clear: both;
}

/* 文字悬停动画效果 */
.hexagon-item:hover .title:not(.submenu-open) {
    -webkit-animation: focus-in-contract 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) forwards;
    animation: focus-in-contract 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) forwards;
}

/* Webkit浏览器文字动画关键帧 */
@-webkit-keyframes focus-in-contract {
    0% {
        letter-spacing: 1em;
        -webkit-filter: blur(12px);
        filter: blur(12px);
        opacity: 0;
    }
    100% {
        letter-spacing: 1px;
        -webkit-filter: blur(0px);
        filter: blur(0px);
        opacity: 1;
    }
}

/* 标准文字动画关键帧 */
@keyframes focus-in-contract {
    0% {
        letter-spacing: 1em;
        -webkit-filter: blur(12px);
        filter: blur(12px);
        opacity: 0;
    }
    100% {
        letter-spacing: 1px;
        -webkit-filter: blur(0px);
        filter: blur(0px);
        opacity: 1;
    }
}

.submenu-container {
    position: absolute;
    background: #2a2f3b;
    border-radius: 5px;
    padding: 10px 0;
    min-width: 200px;
    z-index: 1000;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.submenu-item {
    padding: 8px 20px;
    color: #fff;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 10px;
}

.submenu-item:hover {
    background-color: #3a3f4b;
    cursor: pointer;
}

.submenu-item i {
    width: 20px;
    text-align: center;
}

.hex-content {
    position: relative;
    z-index: 99;
}

.hex-content:hover {
    z-index: 999;
}