@page "/Password"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.One" RowType="RowType.Inline">
        <BootstrapInput @bind-Value="@Model.ITCode" />
        <BootstrapInput @bind-Value="@Model.OldPassword" type="password" />
        <BootstrapInput @bind-Value="@Model.NewPassword" type="password" />
        <BootstrapInput @bind-Value="@Model.NewPasswordComfirm" type="password" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>
@code {
    private ChangePasswordVM Model = new ChangePasswordVM();
    private ValidateForm vform { get; set; }



    protected override void OnInitialized()
    {
        Model.ITCode = UserInfo.ITCode;
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_account/ChangePassword", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.POST);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
