using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using TEX.Shared.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using Console = System.Console;
using Npgsql.Internal.Postgres;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Security.Policy;
using Microsoft.JSInterop;
using Microsoft.JSInterop.Infrastructure;

namespace TEX.Shared.Shared;


public partial class WSPrint : ComponentBase, IAsyncDisposable
{
    [Inject] private WebSocketService WebSocketService { get; set; }
    [Inject] private ILogger<WSPrint> Logger { get; set; }
    [Inject] private ToastService Toast { get; set; }
    [Inject]
    private IJSRuntime IJSRuntime { get; set; }

    [Parameter] public string PrintData { get; set; }
    [Parameter] public string DataTypeName { get; set; }
    [Parameter] public string PrintFileName { get; set; }
    [Parameter] public EventCallback OnPrintSuccess { get; set; }
    [Parameter] public EventCallback<string> OnPrintError { get; set; }

    private bool _isConnected = false;
    private bool _connecting;
    private string _selectedPrinter { get; set; }= string.Empty;
    private string _selectedTemplate { get; set; }
    private int _copies { get; set; } = 1;
    private bool _isPreview { get; set; } = false;
    private List<SelectedItem> printers = new();
    private List<SelectedItem> templates = new();
    private int heartbeat = 0;

    private bool CanPrint => _isConnected &&
                            !string.IsNullOrEmpty(PrintData);
    private DotNetObjectReference<WSPrint> _objRef;
    private bool _disposed;

    private  IJSRuntime _jsRuntime;
    protected override async Task OnInitializedAsync()
    {
        try
        {
            _jsRuntime = IJSRuntime;
            _objRef = DotNetObjectReference.Create(this);
            await base.OnInitializedAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "初始化失败");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !_disposed)
        {
            try 
            {
                await InitializeWebSocketClientAsync();
                await ConnectWSAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "初始化WebSocket失败");
            }
        }
    }

    private async Task InitializeWebSocketClientAsync()
    {
        try
        {
            if (_disposed) return;
            await _jsRuntime.InvokeAsync<IJSVoidResult>("initWebSocketPrintClient", _objRef);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "初始化WebSocket客户端失败");
            throw;
        }
    }

    private async Task ConnectWSAsync()
    {
        if (_connecting || _disposed) return;

        try
        {
            _connecting = true;
            await InvokeAsync(StateHasChanged);

            if (!_disposed)
            {
                await _jsRuntime.InvokeAsync<IJSVoidResult>("connectWebSocket");
                await Task.Delay(300);
                await GetPrintersAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接失败: {ex.Message}", "text-error");
            Logger.LogError(ex, "连接WebSocket失败");
        }
        finally
        {
            _connecting = false;
            if (!_disposed)
            {
                await InvokeAsync(StateHasChanged);
            }
        }
    }

    private async Task GetPrintersAsync()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("sendMessage", new WebSocketMessageBase
            { 
                MessageType = MessageType.GetPrinters 
            });

        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取打印机列表失败: {ex.Message}", "text-error");
        }
    }

    private async Task PrintAsync()
    {
        if (!CanPrint) return;

        try
        {
            var request = new PrintRequestMessage
            {
                PrinterName = _selectedPrinter ?? printers.First().Value,
                PrintFileName = PrintFileName,
                DataTypeName = DataTypeName,
                PrintData = PrintData,
                Copies = _copies,
                IsPreview = _isPreview
            };

            await _jsRuntime.InvokeVoidAsync("sendMessage", request);
            Console.WriteLine("正在处理打印请求...", "text-info");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发送打印请求失败: {ex.Message}", "text-error");
            await OnPrintError.InvokeAsync(ex.Message);
        }
    }

    [JSInvokable]
    public void OnConnected()
    {
        _isConnected = true;
        Console.WriteLine("已连接到打印服务", "text-success");
        InvokeAsync(StateHasChanged);
    }

    [JSInvokable]
    public void OnDisconnected()
    {
        _isConnected = false;
        Console.WriteLine("与打印服务断开连接", "text-warning");
        InvokeAsync(StateHasChanged);
    }

    [JSInvokable]
    public void OnError(string error)
    {
        Console.WriteLine($"错误: {error}", "text-error");
        InvokeAsync(StateHasChanged);
    }

    [JSInvokable]
    public async Task OnMessage(string data)
    {
        try
        {
            if (_disposed) return;

            // 先解析基础消息以获取类型
            var baseMessage = System.Text.Json.JsonSerializer.Deserialize<WebSocketMessageBase>(data);
            
            switch (baseMessage.MessageType)
            {
                case MessageType.Pong:
                    // 处理心跳响应
                    heartbeat++;
                    Console.WriteLine($"第{heartbeat}次心跳");
                    break;

                case MessageType.PrinterList:
                    var printerListMsg = System.Text.Json.JsonSerializer.Deserialize<PrinterListMessage>(data);
                    printers = printerListMsg.Printers.Select(p => new SelectedItem
                    {
                        Text = p,
                        Value = p
                    }).ToList();
                    Console.WriteLine($"获取到{printerListMsg.Printers.Length}台打印机", "text-info");
                    break;

                case MessageType.TemplateList:
                    var templateListMsg = System.Text.Json.JsonSerializer.Deserialize<TemplateListMessage>(data);
                    templates = templateListMsg.Templates.Select(p => new SelectedItem
                    {
                        Text = p.Name,
                        Value = p.Name
                    }).ToList();
                    Console.WriteLine($"获取到{templateListMsg.Templates.Length}个模板", "text-info");
                    break;

                case MessageType.PrintResponse:
                    var printResponseMsg = System.Text.Json.JsonSerializer.Deserialize<PrintStatusMessage>(data);
                    if (printResponseMsg.IsSuccess)
                    {
                        Console.WriteLine($"打印成功: {printResponseMsg.Message}", "text-success");
                        await OnPrintSuccess.InvokeAsync();
                    }
                    else
                    {
                        Console.WriteLine($"打印失败: {printResponseMsg.Message}", "text-error");
                        await OnPrintError.InvokeAsync(printResponseMsg.Message);
                    }
                    break;

                case MessageType.PrintTaskStatus:
                    var statusMsg = System.Text.Json.JsonSerializer.Deserialize<PrintTaskStatusMessage>(data);
                    if (statusMsg.Status == "Completed")
                    {
                        Console.WriteLine("打印任务完成", "text-success");
                    }
                    else if (!string.IsNullOrEmpty(statusMsg.ErrorMessage))
                    {
                        Console.WriteLine($"打印任务异常: {statusMsg.ErrorMessage}", "text-error");
                        await OnPrintError.InvokeAsync(statusMsg.ErrorMessage);
                    }
                    break;

                case MessageType.Error:
                    var errorMsg = System.Text.Json.JsonSerializer.Deserialize<ErrorMessage>(data);
                    Console.WriteLine($"错误 ({errorMsg.ErrorCode}): {errorMsg.Details}", "text-error");
                    Logger.LogWarning($"WebSocket错误:收到未知消息类型: {baseMessage.MessageType}, 内容: {data}");
                    await OnPrintError.InvokeAsync(errorMsg.Details);
                    break;

                default:
                    Logger.LogWarning($"收到未知消息类型: {baseMessage.MessageType}, 内容: {data}");
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理消息失败: {ex.Message}", "text-error");
            Logger.LogError(ex, "处理WebSocket消息失败，消息内容: {0}", data);
        }
        finally
        {
            if (!_disposed)
            {
                await InvokeAsync(StateHasChanged);
            }
        }
    }


    public async ValueTask DisposeAsync()
    {
        try
        {
            if (!_disposed)
            {
                _disposed = true;
                
                try
                {
                    if (_isConnected)
                    {
                        await DisconnectAsync();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "断开WebSocket连接失败");
                }

                try
                {
                    _objRef?.Dispose();
                    _objRef = null;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "释放DotNetObjectReference失败");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "DisposeAsync error");
        }
    }

    private async Task DisconnectAsync()
    {
        try
        {
            if (!_disposed && _isConnected)
            {
                _isConnected = false;
                await _jsRuntime.InvokeVoidAsync("disconnectWebSocket");

                // 清空列表
                printers.Clear();
                templates.Clear();
                _selectedPrinter = string.Empty;
                _selectedTemplate = string.Empty;
                
                if (!_disposed)
                {
                    await InvokeAsync(StateHasChanged);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"断开连接失败: {ex.Message}", "text-error");
            Logger.LogError(ex, "断开WebSocket连接失败");
            throw; // 重新抛出异常以便上层处理
        }
    }

    private class Template
    {
        public string Name { get; set; }
        public DateTime LastModified { get; set; }
    }

}
