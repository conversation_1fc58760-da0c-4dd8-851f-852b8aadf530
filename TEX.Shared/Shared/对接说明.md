# WebSocket打印服务对接说明

## 1. 基础信息

### 1.1 连接信息
- WebSocket服务地址: `ws://localhost:55555/ws`
- 心跳间隔: 5秒
- 心跳超时: 15秒
- 重连间隔: 5秒
- 最大重连次数: 5次

### 1.2 消息格式
所有消息都遵循以下基本格式:
```json
{
    "messageType": "MessageType枚举值",
    "其他字段": "值"
}
```

### 1.3 MessageType枚举
```csharp
public enum MessageType
{
    Ping,           // 心跳请求
    Pong,           // 心跳响应
    GetPrinters,    // 获取打印机列表请求
    PrinterList,    // 打印机列表响应
    PrintRequest,   // 打印请求
    PrintResponse,  // 打印响应
    PrintTaskStatus,// 打印任务状态
    GetTemplates,   // 获取模板列表请求
    TemplateList,   // 模板列表响应
    TemplateUpload, // 上传模板请求
    TemplateDelete, // 删除模板请求
    Error          // 错误消息
}
```

## 2. 功能模块

### 2.1 连接管理

#### 2.1.1 连接建立
- 客户端通过WebSocket连接到服务器
- 连接成功后自动开始心跳检测

#### 2.1.2 心跳机制
客户端发送:
```json
{
    "messageType": "Ping"
}
```

服务端响应:
```json
{
    "messageType": "Pong",
    "connectionId": "连接ID",
    "timestamp": "2024-01-18T10:30:00.000Z",
    "status": "Connected",
    "latency": 15.6,
    "messagesReceived": 100,
    "messagesSent": 95
}
```

### 2.2 打印功能

#### 2.2.1 获取打印机列表
请求:
```json
{
    "messageType": "GetPrinters"
}
```

响应:
```json
{
    "messageType": "PrinterList",
    "printers": ["打印机1", "打印机2"]
}
```

#### 2.2.2 打印请求
请求:
```json
{
    "messageType": "PrintRequest",
    "printerName": "打印机名称",
    "printFileName": "模板名称",
    "printData": "JSON格式的打印数据",
    "copies": 1,
    "isPreview": false
}
```

响应:
```json
{
    "messageType": "PrintResponse",
    "isSuccess": true,
    "jobId": "打印任务ID",
    "message": "打印成功",
    "printedCopies": 1
}
```

#### 2.2.3 打印任务状态
```json
{
    "messageType": "PrintTaskStatus",
    "taskId": "任务ID",
    "status": "状态",
    "createdAt": "2024-01-18T10:30:00.000Z",
    "startedAt": "2024-01-18T10:30:01.000Z",
    "completedAt": "2024-01-18T10:30:02.000Z",
    "errorMessage": "错误信息(如果有)"
}
```

### 2.3 模板管理

#### 2.3.1 获取模板列表
请求:
```json
{
    "messageType": "GetTemplates"
}
```

响应:
```json
{
    "messageType": "TemplateList",
    "templates": [
        {
            "name": "模板1",
            "lastModified": "2024-01-18T10:30:00.000Z"
        }
    ]
}
```

#### 2.3.2 上传模板
请求:
```json
{
    "messageType": "TemplateUpload",
    "templateName": "模板名称",
    "content": "Base64编码的模板内容"
}
```

响应:
```json
{
    "messageType": "Error",
    "errorCode": "错误代码",
    "message": "错误信息",
    "details": "详细信息"
}
```
或成功响应:
```json
{
    "success": true,
    "message": "模板上传成功"
}
```

#### 2.3.3 删除模板
请求:
```json
{
    "messageType": "TemplateDelete",
    "templateName": "模板名称"
}
```

响应:
```json
{
    "success": true,
    "message": "模板删除成功"
}
```
或错误响应:
```json
{
    "messageType": "Error",
    "errorCode": "TemplateNotFound",
    "message": "模板不存在",
    "details": "请检查模板名称是否正确"
}
```

## 3. 错误处理

### 3.1 错误消息格式
```json
{
    "messageType": "Error",
    "errorCode": "错误代码",
    "message": "错误信息",
    "details": "详细信息"
}
```

### 3.2 常见错误代码
1. 连接相关
   - ConnectionTimeout: 连接超时
   - HeartbeatTimeout: 心跳超时
   - ConnectionLimitExceeded: 超过最大连接数

2. 打印相关
   - PrinterNotFound: 打印机不存在
   - PrinterOffline: 打印机离线
   - InvalidPrintData: 打印数据格式错误
   - TemplateMissing: 模板不存在

3. 模板相关
   - TemplateNotFound: 模板不存在
   - InvalidTemplateFormat: 模板格式错误
   - TemplateUploadFailed: 模板上传失败
   - TemplateDeleteFailed: 模板删除失败

## 4. 最佳实践

### 4.1 连接管理
1. 实现自动重连机制
   - 连接断开后自动重试
   - 使用指数退避策略
   - 限制最大重试次数

2. 心跳处理
   - 定时发送心跳(5秒)
   - 监控心跳响应
   - 超时后重连(15秒)

### 4.2 消息处理
1. 错误处理
   - 捕获所有可能的异常
   - 合理处理超时
   - 记录错误日志

2. 数据验证
   - 验证消息格式
   - 检查必要字段
   - 验证数据类型

### 4.3 资源管理
1. 连接资源
   - 及时清理断开的连接
   - 释放相关资源
   - 避免资源泄漏

2. 模板文件
   - 定期清理临时文件
   - 控制文件大小
   - 验证文件格式

## 5. 调试建议

### 5.1 开发环境配置
1. 使用WebSocket调试工具
2. 启用详细日志
3. 使用测试数据

### 5.2 常见问题排查
1. 连接问题
   - 检查网络连接
   - 验证服务地址
   - 查看心跳状态

2. 打印问题
   - 确认打印机状态
   - 验证模板存在
   - 检查数据格式

3. 模板问题
   - 验证文件格式
   - 检查文件大小
   - 确认存储位置

## 6. 示例代码

### 6.1 C#客户端
```csharp
// 连接服务器
var client = new ClientWebSocket();
await client.ConnectAsync(new Uri("ws://localhost:55555/ws"), CancellationToken.None);

// 发送心跳
var heartbeat = new WebSocketMessageBase { MessageType = MessageType.Ping };
await SendMessageAsync(client, heartbeat);

// 发送打印请求
var printRequest = new PrintRequestMessage
{
    PrinterName = "打印机名称",
    PrintFileName = "模板名称",
    PrintData = "JSON格式数据",
    Copies = 1,
    IsPreview = false
};
await SendMessageAsync(client, printRequest);

// 接收消息
var buffer = new byte[4096];
while (client.State == WebSocketState.Open)
{
    var result = await client.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
    if (result.MessageType == WebSocketMessageType.Text)
    {
        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
        ProcessMessage(message);
    }
}
```

### 6.2 JavaScript客户端
```javascript
// 连接服务器
const ws = new WebSocket('ws://localhost:55555/ws');

// 心跳处理
const heartbeat = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ 
            messageType: 'Ping' 
        }));
    }
}, 5000);

// 发送打印请求
function sendPrintRequest(printer, template, data) {
    ws.send(JSON.stringify({
        messageType: 'PrintRequest',
        printerName: printer,
        printFileName: template,
        printData: JSON.stringify(data),
        copies: 1,
        isPreview: false
    }));
}

// 消息处理
ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    switch (message.messageType) {
        case 'PrintResponse':
            handlePrintResponse(message);
            break;
        case 'Error':
            handleError(message);
            break;
    }
};

// 错误处理
ws.onerror = (error) => {
    console.error('WebSocket错误:', error);
};

// 连接关闭处理
ws.onclose = () => {
    clearInterval(heartbeat);
    // 实现重连逻辑
};
``` 