@using TEX.Shared.Services
@inherits ComponentBase
@implements IAsyncDisposable



<Row ItemsPerRow="ItemsPerRow.Twelve" RowType="RowType.Inline">
    @* <Switch Value="@_isConnected"
                    OnColor="Color.Success"
                    OffColor="Color.Danger"
                    OnText="已连接"
                    OffText="未连接"
                    ShowLabel="true"
                    DisplayText="打印服务"
                    /> *@

    <Row RowType="RowType.Inline" ColSpan="4">
        

            <Select TValue="string"
                    @bind-Value="@_selectedPrinter"
                    Items="@printers"
                    ShowLabel="true"
                    DisplayText="打印机"
                    PlaceHolder="请选择打印机" />

        
    </Row>
    @* <Row RowType="RowType.Inline" ColSpan="4">
        @if (templates?.Any() == true)
        {
            <Select TValue="string"
                    @bind-Value="_selectedTemplate"
                    Items="templates"
                    ShowLabel="true"
                    DisplayText="模板"
                    PlaceHolder="请选择模板" />
        }

    </Row> *@
    <Row RowType="RowType.Inline" ColSpan="4">
        <BootstrapInputNumber @bind-Value="_copies"
                              Min="1" Max="100"
                              ShowLabel="true"
                              DisplayText="份数" />
    </Row>
    <Row RowType="RowType.Inline" ColSpan="2">
        <TEX.Shared.Components.CustomSwitchButton @bind-Value="_isPreview" OffText="预览: 关" OnText="预览: 开" />

    </Row>
    <Row RowType="RowType.Inline" ColSpan="2">
        <Button Color="Color.Success" OnClick="PrintAsync" Icon="fa-solid fa-print" Text="打印" IsDisabled="!CanPrint" />
    </Row>
</Row>

