@page "/login/{tenant?}"

@using TEX.ViewModel.HomeVMs
@inherits BasePage

<div class="d-flex flex-column app-layout-login ">
    <div class="flex-fill  login-layout app-login-back-@bgnumber">
        <header class="login-header">
            <img src="/images/logo.png" alt="" height="48">
        </header>
        <div class="login-content">
            <div class="login-form" style="min-width:400px;min-height:300px;width:400px;">
                <h2 class="mb-5" style="text-align:center;">@tenantName 纺织管理系统</h2>
                <ValidateForm Model="@Model" OnValidSubmit="@Submit">
                    <Row ItemsPerRow="ItemsPerRow.One" RowType="RowType.Inline">
                        <BootstrapInput @bind-Value="@Model.ITCode" />
                        <BootstrapInput @bind-Value="@Model.Password" type="password" />
                        @*@if (WtmBlazor?.ConfigInfo?.EnableTenant == true && _tenant is null)
                        {
                            <BootstrapInput @bind-Value="@Model.Tenant" PlaceHolder="@WtmBlazor.Localizer["Login.InputTenant"]" />
                        }*@
                        <Button ButtonType="ButtonType.Submit" Text="@WtmBlazor.Localizer["Login.Login"]" style="width:100%;margin-top:20px;" IsAsync="true" />
                    </Row>
                </ValidateForm>
            </div>
        </div>
    </div>
</div>



@code {
    [Parameter]
    public bool? LoginSuccess { get; set; }
    [Parameter]
    public EventCallback<bool?> LoginSuccessChanged { get; set; }
    [Parameter]
    public EventCallback OnUserInfoSet { get; set; }

    [Inject]
    private NavigationManager NavigationManager { get; set; }

    private int bgnumber;
    private LoginVM Model = new LoginVM();

    [Parameter]
    public string tenant { get; set; } 
    // 添加这个参数接受路由参数@page "/login/{tenant?}"
    //否则报错:Object of type 'TEX.Shared.Shared.Login' does not have a property matching the name 'tenant'.

    private string _tenant;
    private string tenantName;


    // protected override void OnInitialized()
    // {
    //     base.OnInitialized();
    //}

    protected override async Task OnInitializedAsync()
    {
        bgnumber = new Random().Next(1, 6);
        if (WtmBlazor.ConfigInfo.IsQuickDebug == true)
        {
            Model.ITCode = "admin";
            Model.Password = "000000";
        }
        // 从路由参数中获取Tenant值
        var uri = new Uri(NavigationManager.Uri);
        var segments = uri.Segments;
        _tenant = segments.Length > 2 ? segments[2].Trim('/') : null;// segments[2] 是租户参数

        // 从本地存储读取租户值
        var savedTenant = await GetLocalStorage<string>("savedTenant");

        // if (!string.IsNullOrEmpty(_tenant))
        // {
        //     Model.Tenant = _tenant == "hs" ? "8" : _tenant == "yjf" ? "999" : null;
        //     tenantName = _tenant == "hs" ? "浩顺" : _tenant == "yjf" ? "裕金丰" : "";




        // }
        // else if (!string.IsNullOrEmpty(savedTenant) && savedTenant!="null")
        // {
        //     Model.Tenant = savedTenant;
        //     _tenant = savedTenant;
        //     tenantName = savedTenant == "8" ? "浩顺" : savedTenant == "999" ? "裕金丰" : "";
        // }
        SetTenantAndName(_tenant, savedTenant, ref tenantName);
    }
    private void SetTenantAndName(string _tenant, string savedTenant,  ref string tenantName)
    {
        if (!string.IsNullOrEmpty(_tenant))
        {
            switch (_tenant)
            {
                case "hs":
                    Model.Tenant = "8";
                    tenantName = "浩顺";
                    break;
                case "yjf":
                    Model.Tenant = "999";
                    tenantName = "裕金丰";
                    break;
                case "zsb":
                    Model.Tenant = "866";
                    tenantName = "众思博";
                    break;
                default:
                    Model.Tenant = null;
                    _tenant = null;
                    tenantName = "";
                    break;
            }
        }
        else if (!string.IsNullOrEmpty(savedTenant) && savedTenant != "null")
        {
            Model.Tenant = savedTenant;
            _tenant = savedTenant;
            switch (savedTenant)
            {
                case "8":
                    tenantName = "浩顺";
                    break;
                case "999":
                    tenantName = "裕金丰";
                    break;
                case "866":
                    tenantName = "众思博";
                    break;
                default:
                    tenantName = "";
                    break;
            }
        }
    }
    private async Task Submit(EditContext context)
    {
        var rv = await WtmBlazor.Api.CallAPI<DynamicData>("/api/_account/loginjwt", HttpMethodEnum.POST, new { Account = Model.ITCode, Password = Model.Password, Tenant = Model.Tenant });
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            var token = rv.Data.Fields["access_token"].ToString();
            var rtoken = rv.Data.Fields["refresh_token"].ToString();
            await SetToken(token, rtoken);
            var userinfo = await WtmBlazor.Api.CallAPI<LoginUserInfo>("/api/_account/CheckUserInfo", HttpMethodEnum.GET, new { });

            await SetLocalStorage("savedTenant", Model.Tenant); // 登录成功后保存租户到本地存储
            await SetLocalStorage("tenantName", tenantName);

            if (userinfo.StatusCode == System.Net.HttpStatusCode.OK)
            {
                await SetUserInfo(userinfo.Data);
                await OnUserInfoSet.InvokeAsync();
                await LoginSuccessChanged.InvokeAsync(true);
            }
            else
            {
                await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.Errors.Message[0]);
            }
        }
        else
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.Errors?.Message[0]);
        }
    }
}
