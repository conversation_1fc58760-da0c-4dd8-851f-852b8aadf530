@page "/Greige/Pattern/Create"
@using TEX.Shared.Components
@using TEX.ViewModel.Greige.PatternVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <BootstrapInput @bind-Value="@Model.Entity.PatternName" />
        <BootstrapInput @bind-Value="@Model.Entity.Customer" />
        @* <div class="input-group">
            <BootstrapInput @bind-Value="@Model.Entity.CodeNo" />
            <Button Icon="fa fa-search" OnClick="@OnButtonClick" />
        </div> *@
        <div class="input-group">
            <BootstrapInputGroupLabel DisplayText="花型编号" ShowRequiredMark="true" />
            <div class="input-group">
                <BootstrapInput @bind-Value="@Model.Entity.CodeNo" ShowLabel="false"></BootstrapInput>
                <Button Icon="fa fa-search" OnClick="@OnButtonClick"> 生成</Button>
            </div>
        </div>
        <BootstrapInput Value="@DisplayName" DisplayText="名称" />

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.Description" />
        <BootstrapInput @bind-Value="@Model.Entity.Requirements" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div class="col-12 col-sm-6" style="margin-top: 10px;">
        <WTUploadFile @bind-Value="@Model.Entity.Images" LabelText="图片"/>
        @* <ButtonUpload TValue="string" IsMultiple="true"  OnDelete="@(fileName => Task.FromResult(true))" DefaultFileList="@DefaultFormatFileList"></ButtonUpload> *@
    </div>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private PatternVM Model = new PatternVM();
    private ValidateForm vform { get; set; }

    private string DisplayName => GenPatternName();

    protected override async Task OnInitializedAsync()
    {
        await GetMaxCodeNo();
        await base.OnInitializedAsync();
    }

    private async Task OnButtonClick()
    {
        await GetMaxCodeNo();
    }
    private async Task GetMaxCodeNo()
    {
        var rv = await WtmBlazor.Api.CallAPI("/api/Pattern/GetMaxCodeNo");
        var i = rv.Data as string;
        
        // 添加调试信息
        // System.Console.WriteLine($"API返回的原始数据: {rv.Data}");
        // System.Console.WriteLine($"数据类型: {rv.Data?.GetType().FullName}");
        // System.Console.WriteLine($"转换后的字符串值: {i}");//值为: "534",由双引号包着
        
        // 移除字符串中的引号
        i = i?.Trim('"');
        
        int p;
        if (int.TryParse(i, out p))
        {
            //System.Console.WriteLine($"成功解析为整数: {p}");
            Model.Entity.CodeNo = p;
        }
    }

    private string GenPatternName()
    {
        string name;
        if (String.IsNullOrEmpty(Model.Entity.Customer)) return string.Empty;

        var n = PinYinHelper.GetPinYinFirstLetters(Model.Entity.Customer);
        name = n + Model.Entity.CodeNo + "-" + Model.Entity.PatternName;
        return name;

    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/Pattern/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
