@page "/Greige/Pattern"
@using TEX.ViewModel.Greige.PatternDetailVMs
@using TEX.ViewModel.Greige.PatternVMs;
@using TEX.Shared.Pages.Greige.PatternDetail
@inherits BasePage
@attribute [ActionDescription("提花花型", "TEX.Controllers,Pattern")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <WTDateRange @bind-Value="@SearchModel.CreateDate"  />
            <BootstrapInput @bind-Value="@SearchModel.CodeNo"  />
            <BootstrapInput @bind-Value="@SearchModel.PatternName"  />
            <BootstrapInput @bind-Value="@SearchModel.Customer"  />
            <BootstrapInput @bind-Value="@SearchModel.Requirements"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="Pattern_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.DisplayName" Text="名称">
            <Template Context="value" >
                @{
                    var p = PinYinHelper.GetPinYinFirstLetters(value.Row.Customer)+value.Row.DisplayName;
                    // <Display  TValue="string" Value="@p" />
                    <div>@p</div>
                }
            </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.PatternName"  />
        <TableColumn @bind-Field="@context.Customer"  />
        <TableColumn @bind-Field="@context.Description"  />
        <TableColumn @bind-Field="@context.Requirements"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    
    <TableToolbarTemplate>
        @if (IsAccessable("/api/Pattern/Add"))
        {
            <TableToolbarButton TItem="Pattern_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/Pattern/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="Pattern_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/Pattern/Import"))
        {
            <TableToolbarButton TItem="Pattern_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/Pattern/ExportExcel"))
        {
            <TableToolbarButton TItem="Pattern_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/Pattern/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/Pattern/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/Pattern/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
    <DetailRowTemplate>
        @{
            var SearchModel = new PatternDetailSearcher();
            SearchModel.PatternId=context.ID;
        }
        <PatternDetailList SearchModel="@SearchModel" IsBanned="true"/>
    </DetailRowTemplate>
</Table>

@* <ListView TItem="Pattern_View" IsPagination="true"  OnQueryAsync="@OnSearch" Height="620px"> *@
<ListView TItem="Pattern_View" IsPagination="true" Items="dataSource">
    <HeaderTemplate>
        <div>花型列表</div>
    </HeaderTemplate>
    <BodyTemplate>
        <Card>
            <BodyTemplate>
                @* <img src="@context.Images?.FirstOrDefault().File.FileData" /> *@
                    <div>@context.DisplayName</div>
            </BodyTemplate>
        </Card>
    </BodyTemplate>
</ListView>

@code{

    private PatternSearcher SearchModel = new PatternSearcher();
    private Table<Pattern_View> dataTable;
    private IEnumerable<Pattern_View> dataSource = new List<Pattern_View>();

    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<Pattern_View>> OnSearch(QueryPageOptions opts)
    {
        
        var rv=await StartSearch<Pattern_View>("/api/Pattern/Search", SearchModel, opts);

        dataSource = rv.Items;
        return rv;
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<Pattern_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(Pattern_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(Pattern_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/Pattern/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(Pattern_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/Pattern/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<Pattern_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/Pattern/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/Pattern/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<Pattern_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
