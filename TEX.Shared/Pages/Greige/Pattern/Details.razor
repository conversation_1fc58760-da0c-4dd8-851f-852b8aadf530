@page "/Greige/Pattern/Details/{id}"
@using TEX.Shared.Pages.Greige.PatternDetail
@using TEX.ViewModel.Greige.PatternDetailVMs
@using TEX.ViewModel.Greige.PatternVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        <Display @bind-Value="@Model.Entity.CodeNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.PatternName" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Customer" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Description" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.CreateDate" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Requirements" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Remark" ShowLabel="true" />
        <Button Text="查看图片" OnClick="ShowImagePreviewer"></Button>
    </Row>

    <PatternDetailList SearchModel="@SearchModel" IsBanned="true"/>
    @if (isShow)
    {
        <div class="images-item" style="margin:16px">
            @if (Model.Entity.Images?.Any() == true)
            {
                <ImageViewer Url="@(PreviewList.FirstOrDefault() ?? string.Empty)"
                             PreviewList="@PreviewList" />
            }
            else
            {
                <Empty Text="暂无图片" />
            }
        </div>
    }


    @* <Table TItem="PatternDetail_View" @bind-Items="@PatternDetail_ViewList" IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false"
           ShowDefaultButtons="false" ShowEditButton="false"
           TableSize="TableSize.Compact" IsFixedHeader="true" Height="140"
           style="margin-top:10px">
        <TableColumns>
            <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
            <TableColumn @bind-Field="@context.PatternCode" Text="花稿名称" >
                <Template Context="requirementcontext">
                    @($"{context.MachineInch ?? 0}寸 * {context.MachineTotalNeedles ?? 0}针 - {context.MachineSpec} - {context.JacquardFeed}工位")
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.PatternVersion" />
            <TableColumn @bind-Field="@context.FabricCategory" />
            <TableColumn @bind-Field="@context.PatternRepeatWidth" />
            <TableColumn @bind-Field="@context.PatternRepeatHeight" />
            <TableColumn @bind-Field="@context.RequiredFullWidth" />
            <TableColumn @bind-Field="@context.RequiredGsm" />
            <TableColumn @bind-Field="@context.PatternWeftPoint" />
            <TableColumn @bind-Field="@context.PatternWarpPoint" />
            <TableColumn @bind-Field="@context.MachineInch" Text="机台信息">
                <Template Context="greigecontext">
                    @($"{context.MachineInch ?? 0}寸 * {context.MachineTotalNeedles ?? 0}针 - {context.MachineSpec} - {context.JacquardFeed}工位")
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="坯布记录">
                <Template Context="greigecontext">
                    @($"{context.GreigeRepeatWidth ?? 0} * {context.GreigeRepeatHeight ?? 0}CM - {context.GreigeWidth ?? 0} - {context.GreigeGsm ?? 0}")
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.FabricRepeatWidth" Text="成品记录">
                <Template Context="Fabriccontext">
                    @($"{context.FabricRepeatWidth ?? 0} * {context.FabricRepeatHeight ?? 0}CM - {context.FabricFullWidth ?? 0} - {context.FabricGsm ?? 0}")
                </Template>
            </TableColumn>
        </TableColumns>
    </Table> *@
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private PatternVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    private IEnumerable<PatternDetail_View> PatternDetail_ViewList { get; set; } = new List<PatternDetail_View>();
    private PatternDetailSearcher SearchModel = new PatternDetailSearcher();

    protected override async Task OnInitializedAsync()
    {

        var rv = await WtmBlazor.Api.CallAPI<PatternVM>($"/api/Pattern/{id}");
        Model = rv.Data;
        SearchModel.PatternId = Model.Entity.ID;
        // PatternDetail_ViewList = Model.Entity.DetailList.Select(x => new PatternDetail_View()
        // {
        //     ID = x.ID,
        //     PatternId = x.PatternId,
        //     PatternName_view = Model.Entity.PatternName,
        //     PatternCode = x.PatternCode,
        //     PatternVersion = x.PatternVersion,
        //     FabricCategory = x.FabricCategory,
        //     PatternRepeatWidth = x.PatternRepeatWidth,
        //     PatternRepeatHeight = x.PatternRepeatHeight,
        //     RequiredFullWidth = x.RequiredFullWidth,
        //     RequiredGsm = x.RequiredGsm,
        //     RequiredCuttableWidth = x.RequiredCuttableWidth,
        //     MachineInch = x.MachineInch,
        //     MachineTotalNeedles = x.MachineTotalNeedles,
        //     JacquardFeed = x.JacquardFeed,
        //     PatternWeftPoint = x.PatternWeftPoint,
        //     PatternWarpPoint = x.PatternWarpPoint,
        //     GreigeRepeatWidth = x.GreigeRepeatWidth,
        //     GreigeRepeatHeight = x.GreigeRepeatHeight,
        //     GreigeWidth = x.GreigeWidth,
        //     GreigeGsm = x.GreigeGsm
        //
        // });
        base.OnInitialized();
    }

    public void OnClose()
    {
        // 清理资源
        PreviewList.Clear();
        imageCache.Clear();
        CloseDialog();
    }
    private List<string> PreviewList { get; } = [];

    private bool isShow = false;

    private Dictionary<string, string> imageCache = new Dictionary<string, string>();

    private bool _isLoading = false;
    private string _errorMessage = string.Empty;

    /// <summary>
    /// 显示图片预览器
    /// </summary>
    private async Task ShowImagePreviewer()
    {
        try
        {
            if (_isLoading) return;

            if (!isShow)
            {
                _isLoading = true;
                await LoadImages();
                _isLoading = false;
            }

            isShow = !isShow;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _errorMessage = $"加载图片失败: {ex.Message}";
            _isLoading = false;
            await WtmBlazor.Toast.Error("错误", _errorMessage);
        }
    }

    /// <summary>
    /// 加载所有图片
    /// </summary>
    private async Task LoadImages()
    {
        // 检查是否有图片需要加载
        if (Model.Entity.Images == null || !Model.Entity.Images.Any())
        {
            return;
        }
        // 只在缓存为空时加载图片
        if (!imageCache.Any())
        {
            PreviewList.Clear();
            var loadTasks = Model.Entity.Images.Select(photo => LoadImage(photo.FileId));
            await Task.WhenAll(loadTasks);
        }
    }

    /// <summary>
    /// 加载单张图片
    /// </summary>
    /// <param name="fileId">图片文件ID</param>
    private async Task LoadImage(Guid fileId)
    {
        try
        {
            if (imageCache.ContainsKey(fileId.ToString()))// 检查缓存中是否已存在
            {
                return;
            }

            var rv = await WtmBlazor.Api.CallAPI<byte[]>($"/api/_file/GetFile/{fileId}",
                HttpMethodEnum.GET,
                new Dictionary<string, string> {
                    {"width", "500"},
                    {"height", "500"}// 限制图片尺寸,优化加载性能
                    });

            if (rv.StatusCode == System.Net.HttpStatusCode.OK && rv.Data != null)
            {
                var base64 = Convert.ToBase64String(rv.Data);// 转换为base64图片格式
                var imageUrl = $"data:image/jpeg;base64,{base64}";
                imageCache[fileId.ToString()] = imageUrl; // 存入缓存
                PreviewList.Add(imageUrl);// 添加到预览列表
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"加载图片 {fileId} 失败: {ex.Message}";
            await WtmBlazor.Toast.Error("错误", _errorMessage);
        }
    }
}