@page "/Greige/Pattern/Edit/{id}"
@using TEX.ViewModel.Greige.PatternVMs;
@using TEX.ViewModel.Greige.PatternDetailVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Inline">

            <DateTimePicker @bind-Value="@Model.Entity.CreateDate"  />
            <BootstrapInput @bind-Value="@Model.Entity.PatternName"  />
            <BootstrapInput @bind-Value="@Model.Entity.Customer"  />
            <BootstrapInput @bind-Value="@Model.Entity.Description"  />
            <BootstrapInput @bind-Value="@Model.Entity.Requirements"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    @if (isShow)
    {
        <div class="images-item" style="margin:16px">
            @if (Model.Entity.Images?.Any() == true)
            {
                <ImageViewer Url="@(PreviewList.FirstOrDefault() ?? string.Empty)"
                             PreviewList="@PreviewList" />
            }
            else
            {
                <Empty Text="暂无图片" />
            }
        </div>
    }
    <Table TItem="PatternDetail_View" @bind-Items="@PatternDetail_ViewList" IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false"
           ShowDefaultButtons="false" ShowEditButton="false"
           TableSize="TableSize.Compact" IsFixedHeader="true" Height="140"
           style="margin-top:10px">
        <TableColumns>
            <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" Width="100" />
            <TableColumn @bind-Field="@context.PatternCode" Width="60">

            </TableColumn>
            <TableColumn @bind-Field="@context.PatternVersion" Width="50" />
            <TableColumn @bind-Field="@context.FabricCategory" Width="50"/>
            @* <TableColumn @bind-Field="@context.PatternRepeatWidth" />
            <TableColumn @bind-Field="@context.PatternRepeatHeight" />
            <TableColumn @bind-Field="@context.RequiredFullWidth" />
            <TableColumn @bind-Field="@context.RequiredGsm" />
            <TableColumn @bind-Field="@context.MachineInch" />
            <TableColumn @bind-Field="@context.MachineTotalNeedles" />
            <TableColumn @bind-Field="@context.JacquardFeed" />
            <TableColumn @bind-Field="@context.PatternWeftPoint" />
            <TableColumn @bind-Field="@context.PatternWarpPoint" /> *@
            <TableColumn @bind-Field="@context.MachineInch" Text="机台信息" Align="Alignment.Center">
                <Template Context="mac">
                    @($"{mac.Row.MachineInch ?? 0}寸 * {mac.Row.MachineTotalNeedles ?? 0}针 - {mac.Row.MachineSpec} - {mac.Row.JacquardFeed}工位")
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="要求尺寸">
                <Template Context="required">
                    @($"{required.Row.PatternRepeatWidth ?? 0}*{required.Row.PatternRepeatHeight ?? 0}CM - {required.Row.RequiredFullWidth ?? 0}CM - {required.Row.RequiredGsm ?? 0}G")
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="花稿尺寸" Align="Alignment.Center">
                <Template Context="pattern">
                    @{

                        var weftPerCM = pattern.Row.PatternRepeatWidth == null ? 0 : (double)(pattern.Row.PatternWeftPoint / pattern.Row.PatternRepeatWidth.Value);
                        var wrapPerCM = pattern.Row.PatternRepeatHeight == null ? 0 : (double)(pattern.Row.PatternWarpPoint / pattern.Row.PatternRepeatHeight.Value);
                        <div>
                            <div>
                                @($"{pattern.Row.PatternWeftPoint ?? 0} * {pattern.Row.PatternWarpPoint ?? 0} -密度: {weftPerCM:F2} * {wrapPerCM:F2}")
                            </div>
                        </div>
                    }
                </Template>
            </TableColumn>
            @* <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="坯布规格">
                <Template Context="greigecontext">
                    @($"{context.GreigeRepeatWidth ?? 0}-{context.GreigeRepeatHeight ?? 0} - {context.GreigeWidth ?? 0} - {context.GreigeGsm ?? 0}")
                </Template>
            </TableColumn> *@
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private PatternVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    private IEnumerable<PatternDetail_View> PatternDetail_ViewList { get; set; } = new List<PatternDetail_View>();


    protected override async Task OnInitializedAsync()
    {
        var rv = await WtmBlazor.Api.CallAPI<PatternVM>($"/api/Pattern/{id}");
        Model = rv.Data;
        PatternDetail_ViewList = Model.Entity.DetailList.Select(x => new PatternDetail_View()
        {
            ID = x.ID,
            PatternId = x.PatternId,
            PatternName_view = Model.Entity.PatternName,
            PatternCode = x.PatternCode,
            PatternVersion = x.PatternVersion,
            FabricCategory = x.FabricCategory,
            PatternRepeatWidth = x.PatternRepeatWidth,
            PatternRepeatHeight = x.PatternRepeatHeight,
            RequiredFullWidth = x.RequiredFullWidth,
            RequiredGsm = x.RequiredGsm,
            RequiredCuttableWidth = x.RequiredCuttableWidth,
            MachineInch = x.MachineInch,
            MachineTotalNeedles = x.MachineTotalNeedles,
            JacquardFeed = x.JacquardFeed,
            PatternWeftPoint = x.PatternWeftPoint,
            PatternWarpPoint = x.PatternWarpPoint,
            GreigeRepeatWidth = x.GreigeRepeatWidth,
            GreigeRepeatHeight = x.GreigeRepeatHeight,
            GreigeWidth = x.GreigeWidth,
            GreigeGsm = x.GreigeGsm

        });
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        Model.Entity.DetailList = PatternDetail_ViewList as List<PatternDetail>;
        await PostsForm(vform, $"/api/Pattern/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }
    private List<string> PreviewList { get; } = [];

    private bool isShow = false;

    private Dictionary<string, string> imageCache = new Dictionary<string, string>();

    private bool _isLoading = false;
    private string _errorMessage = string.Empty;

    /// <summary>
    /// 显示图片预览器
    /// </summary>
    private async Task ShowImagePreviewer()
    {
        try
        {
            if (_isLoading) return;

            if (!isShow)
            {
                _isLoading = true;
                await LoadImages();
                _isLoading = false;
            }

            isShow = !isShow;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _errorMessage = $"加载图片失败: {ex.Message}";
            _isLoading = false;
            await WtmBlazor.Toast.Error("错误", _errorMessage);
        }
    }

    /// <summary>
    /// 加载所有图片
    /// </summary>
    private async Task LoadImages()
    {
        // 检查是否有图片需要加载
        if (Model.Entity.Images == null || !Model.Entity.Images.Any())
        {
            return;
        }
        // 只在缓存为空时加载图片
        if (!imageCache.Any())
        {
            PreviewList.Clear();
            var loadTasks = Model.Entity.Images.Select(photo => LoadImage(photo.FileId));
            await Task.WhenAll(loadTasks);
        }
    }

    /// <summary>
    /// 加载单张图片
    /// </summary>
    /// <param name="fileId">图片文件ID</param>
    private async Task LoadImage(Guid fileId)
    {
        try
        {
            if (imageCache.ContainsKey(fileId.ToString()))// 检查缓存中是否已存在
            {
                return;
            }

            var rv = await WtmBlazor.Api.CallAPI<byte[]>($"/api/_file/GetFile/{fileId}",
                HttpMethodEnum.GET,
                new Dictionary<string, string> {
                    {"width", "500"},
                    {"height", "500"}// 限制图片尺寸,优化加载性能
                    });

            if (rv.StatusCode == System.Net.HttpStatusCode.OK && rv.Data != null)
            {
                var base64 = Convert.ToBase64String(rv.Data);// 转换为base64图片格式
                var imageUrl = $"data:image/jpeg;base64,{base64}";
                imageCache[fileId.ToString()] = imageUrl; // 存入缓存
                PreviewList.Add(imageUrl);// 添加到预览列表
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"加载图片 {fileId} 失败: {ex.Message}";
            await WtmBlazor.Toast.Error("错误", _errorMessage);
        }
    }
}
