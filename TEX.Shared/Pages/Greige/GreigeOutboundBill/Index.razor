@page "/Greige/GreigeOutboundBill"
@using TEX.ViewModel.Greige.GreigeOutboundBillVMs;
@inherits BasePage
@attribute [ActionDescription("坯布出库", "TEX.Controllers,GreigeOutboundBill")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.BillNo"  />
            <WTDateRange @bind-Value="@SearchModel.OutboundDate"  />
            <BootstrapInput @bind-Value="@SearchModel.Purpose"  />
            <Select @bind-Value="@SearchModel.ReceiverId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.DyeingFactoryId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.Warehouse"  />
            <BootstrapInput @bind-Value="@SearchModel.TransportMethod"  />
            <BootstrapInput @bind-Value="@SearchModel.DeliveryAddress"  />
            <BootstrapInput @bind-Value="@SearchModel.Remark"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="GreigeOutboundBill_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.BillNo"  />
        <TableColumn @bind-Field="@context.OutboundDate" FormatString="yyyy-MM-dd HH: mm: ss" />
        <TableColumn @bind-Field="@context.Purpose"  />
        <TableColumn @bind-Field="@context.CompanyName_view"  />
        <TableColumn @bind-Field="@context.CompanyName_view2"  />
        <TableColumn @bind-Field="@context.Warehouse"  />
        <TableColumn @bind-Field="@context.TransportMethod"  />
        <TableColumn @bind-Field="@context.DeliveryAddress"  />
        <TableColumn @bind-Field="@context.TotalRolls"  />
        <TableColumn @bind-Field="@context.TotalWeight"  />
        <TableColumn @bind-Field="@context.TotalMeters"  />
        <TableColumn @bind-Field="@context.TotalYards"  />
        <TableColumn @bind-Field="@context.AuditStatus"  />
        <TableColumn @bind-Field="@context.AuditedBy"  />
        <TableColumn @bind-Field="@context.AuditedComment"  />
        <TableColumn @bind-Field="@context.TenantCode"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/GreigeOutboundBill/Add"))
        {
            <TableToolbarButton TItem="GreigeOutboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/GreigeOutboundBill/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="GreigeOutboundBill_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/GreigeOutboundBill/Import"))
        {
            <TableToolbarButton TItem="GreigeOutboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/GreigeOutboundBill/ExportExcel"))
        {
            <TableToolbarButton TItem="GreigeOutboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/GreigeOutboundBill/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/GreigeOutboundBill/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/GreigeOutboundBill/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private GreigeOutboundBillSearcher SearchModel = new GreigeOutboundBillSearcher();
    private Table<GreigeOutboundBill_View> dataTable;

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/GreigeOutboundBill/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<GreigeOutboundBill_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<GreigeOutboundBill_View>("/api/GreigeOutboundBill/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<GreigeOutboundBill_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(GreigeOutboundBill_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(GreigeOutboundBill_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/GreigeOutboundBill/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(GreigeOutboundBill_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/GreigeOutboundBill/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<GreigeOutboundBill_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/GreigeOutboundBill/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/GreigeOutboundBill/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<GreigeOutboundBill_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
