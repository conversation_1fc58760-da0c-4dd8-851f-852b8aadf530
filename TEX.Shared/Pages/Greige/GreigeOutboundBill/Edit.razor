@page "/Greige/GreigeOutboundBill/Edit/{id}"
@using TEX.ViewModel.Greige.GreigeOutboundBillVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.BillNo"  />
            <DateTimePicker @bind-Value="@Model.Entity.OutboundDate"  />
            <BootstrapInput @bind-Value="@Model.Entity.Purpose"  />
            <Select @bind-Value="@Model.Entity.ReceiverId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.DyeingFactoryId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Warehouse"  />
            <BootstrapInput @bind-Value="@Model.Entity.TransportMethod"  />
            <BootstrapInput @bind-Value="@Model.Entity.DeliveryAddress"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalRolls"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalWeight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalMeters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalYards"  />
            <Select @bind-Value="@Model.Entity.AuditStatus"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.AuditedBy"  />
            <BootstrapInput @bind-Value="@Model.Entity.AuditedComment"  />
            <BootstrapInput @bind-Value="@Model.Entity.TenantCode"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private GreigeOutboundBillVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/GreigeOutboundBill/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<GreigeOutboundBillVM>($"/api/GreigeOutboundBill/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/GreigeOutboundBill/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
