@page "/Greige/GreigeRoll/Details/{id}"
@using TEX.ViewModel.Greige.GreigeRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.ProductId" Lookup="@AllProducts"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.KnittingPlanId" Lookup="@AllKnittingPlans"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.InboundBillId" Lookup="@AllGreigeInboundBills"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OutboundBillId" Lookup="@AllGreigeOutboundBills"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OperationType"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OperationRemark"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.BatchNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.MachineId"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.RollNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.InboundWeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.InboundMeters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.InboundYards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.InboundBillNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.QcWeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.QcMeters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.QcYards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OutboundWeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OutboundMeters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OutboundYards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OutboundBillNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Grade"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Worker"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Inspector"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Status"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private GreigeRollVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProducts = new List<SelectedItem>();

    private List<SelectedItem> AllKnittingPlans = new List<SelectedItem>();

    private List<SelectedItem> AllGreigeInboundBills = new List<SelectedItem>();

    private List<SelectedItem> AllGreigeOutboundBills = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetProducts", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllKnittingPlans = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetKnittingPlans", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllGreigeInboundBills = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetGreigeInboundBills", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllGreigeOutboundBills = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetGreigeOutboundBills", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<GreigeRollVM>($"/api/GreigeRoll/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
