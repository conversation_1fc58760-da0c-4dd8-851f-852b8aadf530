@page "/Greige/GreigeRoll"
@using TEX.ViewModel.Greige.GreigeRollVMs;
@inherits BasePage
@attribute [ActionDescription("坯布管理", "TEX.Controllers,GreigeRoll")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="GreigeRoll_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.ProductName_view"  />
        <TableColumn @bind-Field="@context.BillNo_view"  />
        <TableColumn @bind-Field="@context.BillNo_view2"  />
        <TableColumn @bind-Field="@context.BillNo_view3"  />
        <TableColumn @bind-Field="@context.OperationType"  />
        <TableColumn @bind-Field="@context.OperationRemark"  />
        <TableColumn @bind-Field="@context.BatchNo"  />
        <TableColumn @bind-Field="@context.MachineId"  />
        <TableColumn @bind-Field="@context.RollNo"  />
        <TableColumn @bind-Field="@context.InboundWeight"  />
        <TableColumn @bind-Field="@context.InboundMeters"  />
        <TableColumn @bind-Field="@context.InboundYards"  />
        <TableColumn @bind-Field="@context.InboundBillNo"  />
        <TableColumn @bind-Field="@context.QcWeight"  />
        <TableColumn @bind-Field="@context.QcMeters"  />
        <TableColumn @bind-Field="@context.QcYards"  />
        <TableColumn @bind-Field="@context.OutboundWeight"  />
        <TableColumn @bind-Field="@context.OutboundMeters"  />
        <TableColumn @bind-Field="@context.OutboundYards"  />
        <TableColumn @bind-Field="@context.OutboundBillNo"  />
        <TableColumn @bind-Field="@context.Grade"  />
        <TableColumn @bind-Field="@context.Worker"  />
        <TableColumn @bind-Field="@context.Inspector"  />
        <TableColumn @bind-Field="@context.Status"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/GreigeRoll/Add"))
        {
            <TableToolbarButton TItem="GreigeRoll_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/GreigeRoll/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="GreigeRoll_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/GreigeRoll/Import"))
        {
            <TableToolbarButton TItem="GreigeRoll_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/GreigeRoll/ExportExcel"))
        {
            <TableToolbarButton TItem="GreigeRoll_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/GreigeRoll/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/GreigeRoll/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/GreigeRoll/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private GreigeRollSearcher SearchModel = new GreigeRollSearcher();
    private Table<GreigeRoll_View> dataTable;


    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<GreigeRoll_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<GreigeRoll_View>("/api/GreigeRoll/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<GreigeRoll_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(GreigeRoll_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(GreigeRoll_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/GreigeRoll/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(GreigeRoll_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/GreigeRoll/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<GreigeRoll_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/GreigeRoll/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/GreigeRoll/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<GreigeRoll_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
