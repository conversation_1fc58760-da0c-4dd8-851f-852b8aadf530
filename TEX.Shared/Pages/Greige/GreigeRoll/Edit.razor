@page "/Greige/GreigeRoll/Edit/{id}"
@using TEX.ViewModel.Greige.GreigeRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.ProductId" Items="@AllProducts" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.KnittingPlanId" Items="@AllKnittingPlans" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.InboundBillId" Items="@AllGreigeInboundBills" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.OutboundBillId" Items="@AllGreigeOutboundBills" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.OperationType"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.OperationRemark"  />
            <BootstrapInput @bind-Value="@Model.Entity.BatchNo"  />
            <BootstrapInput @bind-Value="@Model.Entity.MachineId"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.RollNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.InboundWeight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.InboundMeters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.InboundYards"  />
            <BootstrapInput @bind-Value="@Model.Entity.InboundBillNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.QcWeight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.QcMeters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.QcYards"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.OutboundWeight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.OutboundMeters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.OutboundYards"  />
            <BootstrapInput @bind-Value="@Model.Entity.OutboundBillNo"  />
            <BootstrapInput @bind-Value="@Model.Entity.Grade"  />
            <BootstrapInput @bind-Value="@Model.Entity.Worker"  />
            <BootstrapInput @bind-Value="@Model.Entity.Inspector"  />
            <BootstrapInput @bind-Value="@Model.Entity.Status"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private GreigeRollVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProducts = new List<SelectedItem>();

    private List<SelectedItem> AllKnittingPlans = new List<SelectedItem>();

    private List<SelectedItem> AllGreigeInboundBills = new List<SelectedItem>();

    private List<SelectedItem> AllGreigeOutboundBills = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetProducts", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllKnittingPlans = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetKnittingPlans", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllGreigeInboundBills = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetGreigeInboundBills", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllGreigeOutboundBills = await WtmBlazor.Api.CallItemsApi("/api/GreigeRoll/GetGreigeOutboundBills", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<GreigeRollVM>($"/api/GreigeRoll/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/GreigeRoll/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
