@page "/Greige/KnittingProcess"
@using TEX.ViewModel.Greige.KnittingProcessVMs;
@inherits BasePage
@attribute [ActionDescription("织造工艺", "TEX.Controllers,KnittingProcess")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@SearchModel.ProcessCode"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.FinishedWeight"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.FinishedWidth"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.FinishedPileHeight"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>

<Table @ref="dataTable" TItem="KnittingProcess_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.CreateTime" Text="创建日期"  FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.ProcessCode"  />
        <TableColumn @bind-Field="@context.Category_view"  />
        <TableColumn @bind-Field="@context.MachineInfo.MachineType"  />
        <TableColumn @bind-Field="@context.MachineInfo.MachineDiameter"  />
        <TableColumn @bind-Field="@context.MachineInfo.Gauge"  />
        <TableColumn @bind-Field="@context.Weight" FormatString="0" />
        <TableColumn @bind-Field="@context.Width" FormatString="0" />
        <TableColumn @bind-Field="@context.PileLength" FormatString="0" />
        <TableColumn @bind-Field="@context.FinishedWeight" FormatString="0" />
        <TableColumn @bind-Field="@context.FinishedWidth" FormatString="0" />
        <TableColumn @bind-Field="@context.FinishedPileHeight" FormatString="0" />
        @* <TableColumn @bind-Field="@context.YarnInfoJson"  /> *@
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/KnittingProcess/Add"))
        {
            <TableToolbarButton TItem="KnittingProcess_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/KnittingProcess/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="KnittingProcess_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/KnittingProcess/Import"))
        {
            <TableToolbarButton TItem="KnittingProcess_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/KnittingProcess/ExportExcel"))
        {
            <TableToolbarButton TItem="KnittingProcess_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/KnittingProcess/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/KnittingProcess/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/KnittingProcess/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private KnittingProcessSearcher SearchModel = new KnittingProcessSearcher();
    private Table<KnittingProcess_View> dataTable;


    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<KnittingProcess_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<KnittingProcess_View>("/api/KnittingProcess/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<KnittingProcess_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(KnittingProcess_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(KnittingProcess_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/KnittingProcess/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(KnittingProcess_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/KnittingProcess/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<KnittingProcess_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/KnittingProcess/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/KnittingProcess/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<KnittingProcess_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
