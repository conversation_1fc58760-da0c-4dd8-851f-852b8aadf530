@page "/Greige/KnittingProcess/Details/{id}"
@using TEX.ViewModel.Greige.KnittingProcessVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <Select @bind-Value="@Model.Entity.CategoryId"
                Items="Categories" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" IsDisabled="true" />
        <Display @bind-Value="@Model.Entity.FinishedWeight" />
        <Display @bind-Value="@Model.Entity.FinishedWidth" />
        <Display @bind-Value="@Model.Entity.FinishedPileHeight" />
        <Display @bind-Value="@Model.Entity.ProcessCode" />
        <Display @bind-Value="@Model.Entity.Weight" />
        <Display @bind-Value="@Model.Entity.Width" />
        <Display @bind-Value="@Model.Entity.PileLength" />

    </Row>
    <br />
    <Row RowType="RowType.Inline" ColSpan="4">
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <br />
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <Select @bind-Value="@machineInfo.MachineType" IsDisabled="true" />
        <Display @bind-Value="@machineInfo.MachineDiameter" />
        <Display @bind-Value="@machineInfo.Gauge" />
        <Display @bind-Value="@machineInfo.NeedleRows" />
        <Display @bind-Value="@machineInfo.NeedleArrangement" />
        <Display @bind-Value="@machineInfo.TotalNeedles" />
        <Display @bind-Value="@machineInfo.TotalCourses" />
    </Row>
    <Table TItem="YarnInfo" @bind-Items="@yarnInfoList" IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false"
           ShowDefaultButtons="false" ShowEditButton="false"
           TableSize="TableSize.Compact" IsFixedHeader="true" Height="300"
           style="margin-top:15px">
        <TableExtensionToolbarTemplate>
            <Button Color="Color.Secondary" IsOutline="false" IsDisabled="true">纱支信息</Button>
        </TableExtensionToolbarTemplate>
        <TableColumns>
            <TableColumn @bind-Field="@context.YarnType" IsPopover="true" />
            <TableColumn @bind-Field="@context.YarnSpec" IsPopover="true" />
            @* IsPopover="true"强制显示弹出框 *@
            <TableColumn @bind-Field="@context.YarnUsage" IsPopover="true" />
            <TableColumn @bind-Field="@context.YarnCount" Width="60" />
            <TableColumn @bind-Field="@context.YarnLength" Width="60" />
            <TableColumn @bind-Field="@context.ConsumptionRatio" Width="70" />
            <TableColumn @bind-Field="@context.YarnBatchNo" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>
<style>
    /* Table工具栏强制显示在左侧 */
    div.float-end.table-toolbar-button.btn-group.table-column-right {
        float: left !important;
    }
    /* 修复Select显示太靠右侧 */
    .form-select.is-valid:not([multiple]):not([size]) {
        padding-inline-start: 1rem;
    }
</style>
@code {

    private KnittingProcessVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> Categories { get; set; }
    private IEnumerable<YarnInfo> yarnInfoList = new List<YarnInfo>();
    private MachineInfo machineInfo = new();

    protected override async Task OnInitializedAsync()
    {
        Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);
        var rv = await WtmBlazor.Api.CallAPI<KnittingProcessVM>($"/api/KnittingProcess/{id}");
        Model = rv.Data;
        machineInfo = Model.Entity.MachineInfo;
        yarnInfoList = Model.Entity.YarnInfoList;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
