@page "/Greige/KnittingProcess/Create"
@using TEX.Shared.Components
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Greige.KnittingProcessVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        @* <PurchaseOrderSelector @bind-SelectedOrderId="@OrderId" 
                               AllPurchaseOrders="@AllPurchaseOrders" OnSelectedOrder="@OnOrderSelected" />*@
        <Select @bind-Value="@Model.Entity.CategoryId"
        Items="Categories" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@Model.Entity.FinishedWeight" Step="1" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="GSM" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@Model.Entity.FinishedWidth" Step="1" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="CM(有效)" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@Model.Entity.FinishedPileHeight" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="MM" />
        </BootstrapInputGroup>

        @* <BootstrapInput @bind-Value="@Model.Entity.ProcessCode" /> *@

        <InputWithButton DisplayText="工艺编码"
        @bind-Value="@Model.Entity.ProcessCode"
        OnButtonClick="@RuleReNamed" ButtonText="生成"/>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight" Step="10" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="GSM" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@Model.Entity.Width" Step="5" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="CM" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@Model.Entity.PileLength" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="MM" />
        </BootstrapInputGroup>

    </Row>
    <Divider/>
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <Select @bind-Value="@machineInfo.MachineType" />
        <InputWithSuffix  DisplayText="机台尺寸" Unit="寸">
            <BootstrapInputNumber @bind-Value="@machineInfo.MachineDiameter" ShowLabel="false" Min="10" Max="60" />
        </InputWithSuffix>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@machineInfo.Gauge" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="G" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@machineInfo.NeedleRows" PlaceHolder="针排数" Min="2" Max="4" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="排" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@machineInfo.TotalNeedles" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="针" />
        </BootstrapInputGroup>
        <BootstrapInputGroup>
            <BootstrapInputNumber @bind-Value="@machineInfo.TotalCourses" ShowLabel="true" />
            <BootstrapInputGroupLabel DisplayText="路" />
        </BootstrapInputGroup>

        <BootstrapInput @bind-Value="@machineInfo.NeedleArrangement" PlaceHolder="排针方式" />
    </Row>
    <br />
    <Row RowType="RowType.Inline" ColSpan="4">
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <Divider />
    <Table TItem="YarnInfo" @bind-Items="@yarnInfoList" IsStriped="true" IsBordered="true" ShowRefresh="false"
    ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
    ShowDefaultButtons="true" ShowEditButton="false" ShowExtendEditButton="true"
    EditMode="EditMode.InCell" TableSize="TableSize.Compact" IsFixedHeader="true" Height="300"
    ShowExtendDeleteButton="true" ShowToastAfterSaveOrDeleteModel="false" style="margin-top:15px">
        <TableExtensionToolbarTemplate>
            <Button Color="Color.Secondary" IsOutline="false" IsDisabled="true">填写纱支信息</Button>
        </TableExtensionToolbarTemplate>
        <TableColumns>
            <TableColumn @bind-Field="@context.YarnType" IsPopover="true" />
            <TableColumn @bind-Field="@context.YarnSpec" IsPopover="true" />
            @* IsPopover="true"强制显示弹出框 *@
            <TableColumn @bind-Field="@context.YarnUsage" IsPopover="true" />
            <TableColumn @bind-Field="@context.YarnCount" Width="60" />
            <TableColumn @bind-Field="@context.YarnLength" Width="60" />
            <TableColumn @bind-Field="@context.ConsumptionRatio" Width="70" />
            <TableColumn @bind-Field="@context.YarnBatchNo" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>
<style>
    /* Table工具栏强制显示在左侧 */
    div.float-end.table-toolbar-button.btn-group.table-column-right {
    float: left !important;
    }
    /* 修复Select显示太靠右侧 */
    .form-select.is-valid:not([multiple]):not([size]) {
    padding-inline-start: 1rem;
    }
</style>

@code {

    private Guid OrderId { get; set; }
    private KnittingProcessVM Model = new KnittingProcessVM();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> Categories { get; set; }
    private IEnumerable<YarnInfo> yarnInfoList = new List<YarnInfo>();
    private MachineInfo machineInfo = new();
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {
        Model.Entity.MachineInfo = new();
        Model.Entity.YarnInfoList = new();
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);
        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        Model.Entity.YarnInfoList = yarnInfoList.ToList();
        Model.Entity.MachineInfo = machineInfo;
        await PostsForm(vform, "/api/KnittingProcess/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }
    private async Task RuleReNamed()
    {
        if (Model.Entity.CategoryId ==0){
            vform.SetError<KnittingProcess>(f => f.CategoryId, "请先选择产品类别");
            return;
        }
        if (Model.Entity.FinishedWeight == 0)
        {
            vform.SetError<KnittingProcess>(f => f.FinishedWeight, "请先填写成品克重");
            return;
        }
        if (Model.Entity.FinishedWidth == 0 )
        {
            vform.SetError<KnittingProcess>(f => f.FinishedWidth, "请先填写成品门幅");
            return;
        }
        
        if (Model.Entity.FinishedWidth > 0 && Model.Entity.FinishedWeight > 0)
        {
            //var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/DictItem/GetDictItemsByDictName/ProductCodeRule");
            //var rule = rv.Data[2];//第三条规则为产品代码规则
            //Model.Entity.ProcessCode = NamedRuleHelper.GenerateKnittingProcessCode(rule.Text, Model.Entity);

            string name = Categories.FirstOrDefault(c => c.Value == Model.Entity.CategoryId.ToString())?.Text;

            name = name.Replace("绒", "");
            name=PinYinHelper.GetPinYinFirstLetters(name ?? "");
            var width = Model.Entity.FinishedWidth.ToString().Substring(Model.Entity.FinishedWidth.ToString().Length - 2);
            var weight = Model.Entity.FinishedWeight.ToString().Substring(0, 2);
            name = name + width + weight;
            Model.Entity.ProcessCode = name;
        }
        else
        {

            await WtmBlazor.Toast.Show(new ToastOption()
                {
                    Title = "提示",
                    Content = "请先输入产品名称、门幅、克重，再生成产品代码。"
                });
        }
    }

    private async Task OnOrderSelected(PurchaseOrder_View order)
    {
        var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{order.ID.ToString()}");
        var p = rv.Data;
        var product = p.Entity.Product;
        Model.Entity.CategoryId = product.CategoryId;
        StateHasChanged();
    }
}
