@page "/Greige/KnittingProcess/Edit/{id}"
@using TEX.ViewModel.Greige.KnittingProcessVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <Select @bind-Value="@Model.Entity.CategoryId"
                Items="Categories" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.FinishedWeight" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.FinishedWidth" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.FinishedPileHeight" />
        <BootstrapInput @bind-Value="@Model.Entity.ProcessCode" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.Weight" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.Width" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.PileLength" />

    </Row>
    <br />
    <Row RowType="RowType.Inline" ColSpan="4">
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <br />
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <Select @bind-Value="@machineInfo.MachineType" />
        <BootstrapInputNumber @bind-Value="@machineInfo.MachineDiameter" PlaceHolder="机台尺寸" Min="10" Max="60" />
        <BootstrapInputNumber @bind-Value="@machineInfo.Gauge" PlaceHolder="针距" Min="2" Max="40" />
        <BootstrapInputNumber @bind-Value="@machineInfo.NeedleRows" PlaceHolder="针排数" Min="2" Max="4" />
        <BootstrapInput @bind-Value="@machineInfo.NeedleArrangement" PlaceHolder="排针方式" />
        <BootstrapInputNumber @bind-Value="@machineInfo.TotalNeedles" PlaceHolder="总针数" />
        <BootstrapInputNumber @bind-Value="@machineInfo.TotalCourses" PlaceHolder="总路数" />
    </Row>
    <Table TItem="YarnInfo" @bind-Items="@yarnInfoList" IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
           ShowDefaultButtons="true" ShowEditButton="false" ShowExtendEditButton="true"
           EditMode="EditMode.InCell" TableSize="TableSize.Compact" IsFixedHeader="true" Height="300"
           ShowExtendDeleteButton="true" ShowToastAfterSaveOrDeleteModel="false" style="margin-top:15px">
        <TableExtensionToolbarTemplate>
            <Button Color="Color.Secondary" IsOutline="false" IsDisabled="true">填写纱支信息</Button>
        </TableExtensionToolbarTemplate>
        <TableColumns>
            <TableColumn @bind-Field="@context.YarnType" IsPopover="true" />
            <TableColumn @bind-Field="@context.YarnSpec" IsPopover="true" />
            @* IsPopover="true"强制显示弹出框 *@
            <TableColumn @bind-Field="@context.YarnUsage" IsPopover="true" />
            <TableColumn @bind-Field="@context.YarnCount" Width="60" />
            <TableColumn @bind-Field="@context.YarnLength" Width="60" />
            <TableColumn @bind-Field="@context.ConsumptionRatio" Width="70" />
            <TableColumn @bind-Field="@context.YarnBatchNo" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private KnittingProcessVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> Categories { get; set; }
    private IEnumerable<YarnInfo> yarnInfoList = new List<YarnInfo>();
    private MachineInfo machineInfo = new();

    protected override async Task OnInitializedAsync()
    {
        Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);
        var rv = await WtmBlazor.Api.CallAPI<KnittingProcessVM>($"/api/KnittingProcess/{id}");
        Model = rv.Data;
        machineInfo = Model.Entity.MachineInfo;
        yarnInfoList = Model.Entity.YarnInfoList;
    }

    private async Task Submit(EditContext context)
    {
        Model.Entity.YarnInfoList = yarnInfoList.ToList();
        Model.Entity.MachineInfo = machineInfo;
        await PostsForm(vform, $"/api/KnittingProcess/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
