@page "/Greige/GreigeInboundBill"
@using TEX.ViewModel.Greige.GreigeInboundBillVMs;
@inherits BasePage
@attribute [ActionDescription("坯布入库", "TEX.Controllers,GreigeInboundBill")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.BillNo"  />
            <WTDateRange @bind-Value="@SearchModel.InboundDate"  />
            <Select @bind-Value="@SearchModel.KnittingFactoryId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.Warehouse"  />
            <Select @bind-Value="@SearchModel.AuditStatus"  PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="GreigeInboundBill_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.BillNo"  />
        <TableColumn @bind-Field="@context.InboundDate" FormatString="yyyy-MM-dd HH: mm: ss" />
        <TableColumn @bind-Field="@context.CompanyName_view"  />
        <TableColumn @bind-Field="@context.Warehouse"  />
        <TableColumn @bind-Field="@context.TotalRolls"  />
        <TableColumn @bind-Field="@context.TotalWeight"  />
        <TableColumn @bind-Field="@context.TotalMeters"  />
        <TableColumn @bind-Field="@context.TotalYards"  />
        <TableColumn @bind-Field="@context.AuditStatus"  />
        <TableColumn @bind-Field="@context.AuditedBy"  />
        <TableColumn @bind-Field="@context.AuditedComment"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/GreigeInboundBill/Add"))
        {
            <TableToolbarButton TItem="GreigeInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/GreigeInboundBill/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="GreigeInboundBill_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/GreigeInboundBill/Import"))
        {
            <TableToolbarButton TItem="GreigeInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/GreigeInboundBill/ExportExcel"))
        {
            <TableToolbarButton TItem="GreigeInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/GreigeInboundBill/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/GreigeInboundBill/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/GreigeInboundBill/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private GreigeInboundBillSearcher SearchModel = new GreigeInboundBillSearcher();
    private Table<GreigeInboundBill_View> dataTable;

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/GreigeInboundBill/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<GreigeInboundBill_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<GreigeInboundBill_View>("/api/GreigeInboundBill/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<GreigeInboundBill_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(GreigeInboundBill_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(GreigeInboundBill_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/GreigeInboundBill/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(GreigeInboundBill_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/GreigeInboundBill/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<GreigeInboundBill_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/GreigeInboundBill/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/GreigeInboundBill/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<GreigeInboundBill_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
