@page "/Greige/GreigeInboundBill/Details/{id}"
@using TEX.ViewModel.Greige.GreigeInboundBillVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.BillNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.InboundDate"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.KnittingFactoryId" Lookup="@AllCompanys"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Warehouse"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalRolls"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalWeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalMeters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalYards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.AuditStatus"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.AuditedBy"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.AuditedComment"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private GreigeInboundBillVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/GreigeInboundBill/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<GreigeInboundBillVM>($"/api/GreigeInboundBill/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
