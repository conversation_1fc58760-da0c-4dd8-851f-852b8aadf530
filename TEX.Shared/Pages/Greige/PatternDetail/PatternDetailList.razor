@using TEX.ViewModel.Greige.PatternDetailVMs;
@inherits BasePage

<Table @ref="dataTable" TItem="PatternDetail_View" OnQueryAsync="OnSearch" IsPagination="!IsBanned" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="!IsBanned" IsMultipleSelect="!IsBanned" TableSize="_tableSize"
       ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.PatternName_view" Text="花稿名称" >
            <Template Context="mac">
                @{
                    var p = GenPatternName(mac.Row);
                    @p;
                }
            </Template>
        </TableColumn>
        @* <TableColumn @bind-Field="@context.PatternName_view"  />
        <TableColumn @bind-Field="@context.PatternCode"  />
        <TableColumn @bind-Field="@context.PatternVersion"  /> *@
        <TableColumn @bind-Field="@context.FabricCategory"  />
        @* <TableColumn @bind-Field="@context.PatternWeftPoint"  />
        <TableColumn @bind-Field="@context.PatternWarpPoint"  /> *@
        <TableColumn @bind-Field="@context.MachineInch" Text="机台信息" Align="Alignment.Center">
                <Template Context="mac">
                    @($"{mac.Row.MachineInch ?? 0}寸 * {mac.Row.MachineTotalNeedles ?? 0}针 - {mac.Row.MachineSpec} - {mac.Row.JacquardFeed}工位")
                </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="要求尺寸">
                <Template Context="required">
                    @($"{required.Row.PatternRepeatWidth ?? 0}*{required.Row.PatternRepeatHeight ?? 0}CM - {required.Row.RequiredFullWidth ?? 0}CM - {required.Row.RequiredGsm ?? 0}G")
                </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="花稿尺寸" Align="Alignment.Center">
                <Template Context="pattern">
                    @{
                    
                    var weftPerCM = pattern.Row.PatternRepeatWidth == null ? 0 : (double)(pattern.Row.PatternWeftPoint / pattern.Row.PatternRepeatWidth.Value);
                    var wrapPerCM = pattern.Row.PatternRepeatHeight == null ? 0 : (double)(pattern.Row.PatternWarpPoint / pattern.Row.PatternRepeatHeight.Value);
                    <div>
                        <div>
                            @($"{pattern.Row.PatternWeftPoint ?? 0} * {pattern.Row.PatternWarpPoint ?? 0} -密度: {weftPerCM:F2} * {wrapPerCM:F2}")
                        </div>
                    </div>
                    }
                </Template>
        </TableColumn>
        @if (!IsBanned){
        <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="坯布记录">
                <Template Context="greige">
                    @($"{greige.Row.GreigeRepeatWidth ?? 0} * {greige.Row.GreigeRepeatHeight ?? 0}CM - {greige.Row.GreigeWidth ?? 0} - {greige.Row.GreigeGsm ?? 0}")
                </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.FabricRepeatWidth" Text="成品记录">
                <Template Context="fabric">
                    @($"{fabric.Row.FabricRepeatWidth ?? 0} * {fabric.Row.FabricRepeatHeight ?? 0}CM - {fabric.Row.FabricFullWidth ?? 0} - {fabric.Row.FabricGsm ?? 0}")
                </Template>
        </TableColumn>
        }
        <TableColumn @bind-Field="@context.VersionModified"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/PatternDetail/Add"))
        {
            <TableToolbarButton TItem="PatternDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/PatternDetail/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="PatternDetail_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/PatternDetail/Import"))
        {
            <TableToolbarButton TItem="PatternDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/PatternDetail/ExportExcel"))
        {
            <TableToolbarButton TItem="PatternDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/PatternDetail/Edit") && !IsBanned)
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/PatternDetail/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/PatternDetail/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClick="() => OnAddPlusClick(context)" />
            }
            @if (IsAccessable("/api/PatternDetail/BatchDelete") && !IsBanned)
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{
    [Parameter]
    public PatternDetailSearcher SearchModel { get; set; }
    
    [Parameter]
    public bool RefreshTrigger { get; set; }

    [Parameter]
    public bool IsBanned{ get; set; }

    private Table<PatternDetail_View> dataTable;
    private bool _lastRefreshTrigger;
    private bool _isFirstRender = true;
    private TableSize _tableSize = TableSize.Compact;

    protected override async Task OnInitializedAsync()
    {
        _tableSize = IsBanned ? TableSize.Compact : TableSize.Normal;
        await base.OnInitializedAsync();
        _lastRefreshTrigger = RefreshTrigger;
    }
    private string GenPatternName(PatternDetail_View Model)
    {
        string name;
        string feed;
        feed = Model.JacquardFeed == 2 ? "D" : "T";
        var n = PinYinHelper.GetPinYinFirstLetters(Model.Customer_view);
        name = n + Model.PatternCode + "-" + feed + Model.PatternVersion + "-" + Model.PatternName_view;
        return name;
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_isFirstRender)
        {
            _isFirstRender = false;
            return;
        }

        if (RefreshTrigger != _lastRefreshTrigger)
        {
            _lastRefreshTrigger = RefreshTrigger;
            //DoSearch();
            await dataTable.QueryAsync();
        }
    }

    private async Task<QueryData<PatternDetail_View>> OnSearch(QueryPageOptions opts)
    {
        if (SearchModel == null) SearchModel=new PatternDetailSearcher();
        return await StartSearch<PatternDetail_View>("/api/PatternDetail/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<PatternDetail_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }
    private async Task OnAddPlusClick(PatternDetail_View item)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"], x => x.PDView==item) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }
    private async Task OnEditClick(PatternDetail_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(PatternDetail_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/PatternDetail/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(PatternDetail_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/PatternDetail/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<PatternDetail_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/PatternDetail/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/PatternDetail/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<PatternDetail_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
