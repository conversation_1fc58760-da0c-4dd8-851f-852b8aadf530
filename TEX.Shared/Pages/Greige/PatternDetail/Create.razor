@page "/Greige/PatternDetail/Create"
@using TEX.ViewModel.Greige.PatternDetailVMs;
@using TEX.ViewModel.Greige.PatternVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        @if(PDView == null)
        {
            
        <Select @bind-Value="@Model.Entity.PatternId" Items="@AllPatterns" OnSelectedItemChanged="@OnPatternChanged" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        }
        else
        {
            <Display Value="@PDView.PatternName_view" ShowLabel="true" DisplayText="花型名称" />
        }
        <BootstrapInputNumber @bind-Value="@Model.Entity.PatternCode" />
        <BootstrapInput @bind-Value="@Model.Entity.PatternVersion" />
        <Display Value="@fullname" ShowLabel="true" DisplayText="花稿全称" />
        <BootstrapInput @bind-Value="@Model.Entity.FabricCategory" />

        <BootstrapInput @bind-Value="@Model.Entity.VersionModified" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div class="d-flex" style="margin-top: 20px; align-items: center; justify-content: center; gap: 2px; ">
        <Card IsShadow="true" HeaderText="机台信息" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.MachineInch" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.MachineTotalNeedles" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.MachineSpec" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.JacquardFeed" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="基本要求" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.PatternRepeatWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.PatternRepeatHeight" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredFullWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />

        <Card IsShadow="true" HeaderText="花稿信息" Color="Color.Primary" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.PatternWeftPoint" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.PatternWarpPoint" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredFullWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="坯布记录" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.GreigeRepeatWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.GreigeRepeatHeight" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.GreigeWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.GreigeGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="成品记录" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.FabricRepeatWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.FabricRepeatHeight" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.FabricFullWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.FabricGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
    </div>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    [Parameter]
    public PatternDetail_View PDView { get; set; }

    private PatternDetailVM Model = new PatternDetailVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllPatterns = new List<SelectedItem>();
    public string fullname { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (PDView != null)
        {
            Model.Entity.PatternId = PDView.PatternId;
            Model.Entity.PatternCode = PDView.PatternCode;
            Model.Entity.PatternVersion = PDView.PatternVersion + 1;
            Model.Entity.FabricCategory = PDView.FabricCategory;
            Model.Entity.PatternRepeatHeight= PDView.PatternRepeatHeight;
            Model.Entity.PatternRepeatWidth = PDView.PatternRepeatWidth;
            Model.Entity.RequiredFullWidth = PDView.RequiredFullWidth;
            Model.Entity.RequiredGsm = PDView.RequiredGsm;
            Model.Entity.MachineInch = PDView.MachineInch;
            Model.Entity.MachineTotalNeedles = PDView.MachineTotalNeedles;
            Model.Entity.MachineSpec = PDView.MachineSpec;
            Model.Entity.JacquardFeed = PDView.JacquardFeed;


            fullname = GenPatternName(PDView.Customer_view, PDView.PatternCode.ToString(), Model.Entity.PatternVersion.ToString(), PDView.PatternName_view);
        }
        else
        {
            
        AllPatterns = await WtmBlazor.Api.CallItemsApi("/api/PatternDetail/GetPatterns", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        }


        await base.OnInitializedAsync();
    }


    //提取字符串中的数字，如果没有数字则返回0
    private int ExtractNumber(string input)
    {
        if (string.IsNullOrEmpty(input)) return 0;
        var numbers = System.Text.RegularExpressions.Regex.Matches(input, @"\d+")
            .Cast<System.Text.RegularExpressions.Match>()
            .Select(m => m.Value)
            .ToList();
        
        return numbers.Any() ? int.Parse(numbers.First()) : 0;
    }
    private async Task OnPatternChanged(SelectedItem item)
    {
        var rv = await WtmBlazor.Api.CallAPI<PatternVM>($"/api/Pattern/{item.Value}");
        var pD = rv.Data;
        if (pD == null) return;
        Model.Entity.PatternId = pD.Entity.ID;
        Model.Entity.PatternCode = pD.Entity.CodeNo;
        var name = pD.Entity.PatternName;
        if (pD.Entity.DetailList?.Count > 0)
        {
            //Model.Entity.PatternVersion = (pD.Entity.DetailList.Max(x => ExtractNumber(x.PatternVersion)) + 1).ToString();
            Model.Entity.PatternVersion = pD.Entity.DetailList.Max(x =>x.PatternVersion) + 1;
        }
        else
        {
            Model.Entity.PatternVersion = 1;
        }
        fullname = GenPatternName(pD.Entity.Customer, pD.Entity.CodeNo.ToString(), Model.Entity.PatternVersion.ToString(), pD.Entity.PatternName);
        StateHasChanged();
    }

    private string GenPatternName(string customer, string patternCode, string patternVersion, string patternName)
    {
        string name = string.Empty;
        string feed;
        feed = Model.Entity.JacquardFeed == 2 ? "D" : "T";
        var n = customer == null ? "" : PinYinHelper.GetPinYinFirstLetters(customer);
        name = n + patternCode + "-" + feed + patternVersion + "-" + patternName;
        return name;
    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/PatternDetail/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
