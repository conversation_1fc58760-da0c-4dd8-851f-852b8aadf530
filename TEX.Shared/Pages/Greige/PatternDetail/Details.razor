@page "/Greige/PatternDetail/Details/{id}"
@using TEX.ViewModel.Greige.PatternDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        <Display @bind-Value="@Model.CreateDate" ShowLabel="true" />
        <Display Value="@fullname" ShowLabel="true" DisplayText="花稿全称" />
        <Display @bind-Value="@Model.PatternName_view" ShowLabel="true" />
        <Display @bind-Value="@Model.PatternCode" ShowLabel="true" />
        <Display @bind-Value="@Model.PatternVersion" ShowLabel="true" />
        <Display @bind-Value="@Model.FabricCategory" ShowLabel="true" />
        @* <Display @bind-Value="@Model.PatternRepeatWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.PatternRepeatHeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.RequiredFullWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.RequiredGsm"   ShowLabel="true"/>
            <Display @bind-Value="@Model.RequiredCuttableWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.MachineInch"   ShowLabel="true"/>
            <Display @bind-Value="@Model.MachineTotalNeedles"   ShowLabel="true"/>
            <Display @bind-Value="@Model.MachineSpec"   ShowLabel="true"/>
            <Display @bind-Value="@Model.JacquardFeed"   ShowLabel="true"/>
            <Display @bind-Value="@Model.PatternWeftPoint"   ShowLabel="true"/>
            <Display @bind-Value="@Model.PatternWarpPoint"   ShowLabel="true"/>
            <Display @bind-Value="@Model.GreigeRepeatWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.GreigeRepeatHeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.GreigeWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.GreigeGsm"   ShowLabel="true"/>
            <Display @bind-Value="@Model.FabricRepeatWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.FabricRepeatHeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.FabricFullWidth"   ShowLabel="true"/>
            <Display @bind-Value="@Model.FabricGsm"   ShowLabel="true"/> *@
        <Display @bind-Value="@Model.VersionModified" ShowLabel="true" />
        <Display @bind-Value="@Model.Remark" ShowLabel="true" />
    </Row>
    <div class="d-flex" style="margin-top: 20px; justify-content: center; gap: 2px;">
        <Card IsShadow="true" HeaderText="机台信息" IsCollapsible="false" Collapsed="false" style="width:180px">
            <BodyTemplate>
                <Display @bind-Value="@Model.MachineInch" ShowLabel="true" />
                <Display @bind-Value="@Model.MachineTotalNeedles" ShowLabel="true" />
                <Display @bind-Value="@Model.MachineSpec" ShowLabel="true" />
                <Display @bind-Value="@Model.JacquardFeed" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="基本要求" IsCollapsible="false" Collapsed="false" style="width:180px">
            <BodyTemplate>
                <Display @bind-Value="@Model.PatternRepeatWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.PatternRepeatHeight" ShowLabel="true" />
                <Display @bind-Value="@Model.RequiredFullWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.RequiredGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />

        <Card IsShadow="true" HeaderText="花稿信息" IsCollapsible="false" Collapsed="false" style="width:180px">
            <BodyTemplate>
                <Display @bind-Value="@Model.PatternWeftPoint" ShowLabel="true" />
                <Display @bind-Value="@Model.PatternWarpPoint" ShowLabel="true" />
                <Display @bind-Value="@Model.RequiredFullWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.RequiredGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="坯布记录" IsCollapsible="false" Collapsed="false" style="width:180px">
            <BodyTemplate>
                <Display @bind-Value="@Model.GreigeRepeatWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.GreigeRepeatHeight" ShowLabel="true" />
                <Display @bind-Value="@Model.GreigeWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.GreigeGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="成品记录" IsCollapsible="false" Collapsed="false" style="width:180px">
            <BodyTemplate>
                <Display @bind-Value="@Model.FabricRepeatWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.FabricRepeatHeight" ShowLabel="true" />
                <Display @bind-Value="@Model.FabricFullWidth" ShowLabel="true" />
                <Display @bind-Value="@Model.FabricGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
    </div>

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private PatternDetail_View Model = new();
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    public string fullname => GenPatternName();

    private List<SelectedItem> AllPatterns = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPatterns = await WtmBlazor.Api.CallItemsApi("/api/PatternDetail/GetPatterns", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<PatternDetail_View>($"/api/PatternDetail/GetPatternDetail_View/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private string GenPatternName()
    {
        string name;
        string feed;
        feed = Model.JacquardFeed == 2 ? "D" : "T";
        var n = PinYinHelper.GetPinYinFirstLetters(Model.Customer_view);
        name = n + Model.PatternCode + "-" + feed + Model.PatternVersion + "-" + Model.PatternName_view;
        return name;
    }

}
