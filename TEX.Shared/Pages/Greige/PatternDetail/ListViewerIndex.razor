@page "/Greige/PatternDetail/ListViewerIndex"
@using TEX.ViewModel.Greige.PatternDetailVMs;
@inherits BasePage

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <WTDateRange @bind-Value="@SearchModel.CreateDate"  />
            <Select @bind-Value="@SearchModel.PatternId" Items="@AllPatterns" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInputNumber @bind-Value="@SearchModel.PatternCode"  />
            <BootstrapInput @bind-Value="@SearchModel.PatternVersion"  />
            <BootstrapInput @bind-Value="@SearchModel.FabricCategory"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.RequiredFullWidth"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.RequiredGsm"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.MachineTotalNeedles"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.JacquardFeed"  />
            <BootstrapInput @bind-Value="@SearchModel.VersionModified"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>

<ListView TItem="PatternDetail_View" IsPagination="true" PageItems="4" OnQueryAsync="@OnSearch" Height="620px">
        <HeaderTemplate>
            <div>花型列表</div>
        </HeaderTemplate>
        <BodyTemplate>
            <Card>
                <BodyTemplate>
                    <img src="@context.Images.FirstOrDefault().File.FileData" />
                    <div >@context.PatternCode + @context.PatternName_view</div>
                    <div >$"{@context.PatternRepeatWidth} * {@context.PatternRepeatHeight}CM - {@context.RequiredFullWidth}CM - {@context.RequiredGsm} G - {@context.PatternWeftPoint} * {@context.PatternWarpPoint}"</div>
                </BodyTemplate>
            </Card>
        </BodyTemplate>
</ListView>

<Table @ref="dataTable" TItem="PatternDetail_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd HH: mm: ss" />
        <TableColumn @bind-Field="@context.PatternName_view"  />
        <TableColumn @bind-Field="@context.PatternCode"  />
        <TableColumn @bind-Field="@context.PatternVersion"  />
        <TableColumn @bind-Field="@context.FabricCategory"  />
        <TableColumn @bind-Field="@context.PatternRepeatWidth"  />
        <TableColumn @bind-Field="@context.PatternRepeatHeight"  />
        <TableColumn @bind-Field="@context.RequiredFullWidth"  />
        <TableColumn @bind-Field="@context.RequiredGsm"  />
        <TableColumn @bind-Field="@context.PatternWeftPoint"  />
        <TableColumn @bind-Field="@context.PatternWarpPoint"  />
        <TableColumn @bind-Field="@context.MachineInch" Text="机台信息">
                <Template Context="greigecontext">
                    @($"{context.MachineInch ?? 0}寸 * {context.MachineTotalNeedles ?? 0}针 - {context.MachineSpec} - {context.JacquardFeed}工位")
                </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.GreigeRepeatWidth" Text="坯布记录">
                <Template Context="greigecontext">
                    @($"{context.GreigeRepeatWidth ?? 0} * {context.GreigeRepeatHeight ?? 0}CM - {context.GreigeWidth ?? 0} - {context.GreigeGsm ?? 0}")
                </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.FabricRepeatWidth" Text="成品记录">
                <Template Context="Fabriccontext">
                    @($"{context.FabricRepeatWidth ?? 0} * {context.FabricRepeatHeight ?? 0}CM - {context.FabricFullWidth ?? 0} - {context.FabricGsm ?? 0}")
                </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.VersionModified"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/PatternDetail/Add"))
        {
            <TableToolbarButton TItem="PatternDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/PatternDetail/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="PatternDetail_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/PatternDetail/Import"))
        {
            <TableToolbarButton TItem="PatternDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/PatternDetail/ExportExcel"))
        {
            <TableToolbarButton TItem="PatternDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/PatternDetail/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/PatternDetail/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/PatternDetail/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private PatternDetailSearcher SearchModel = new PatternDetailSearcher();
    private Table<PatternDetail_View> dataTable;

    private List<SelectedItem> AllPatterns = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPatterns = await WtmBlazor.Api.CallItemsApi("/api/PatternDetail/GetPatterns", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<PatternDetail_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<PatternDetail_View>("/api/PatternDetail/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<PatternDetail_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(PatternDetail_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(PatternDetail_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/PatternDetail/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(PatternDetail_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/PatternDetail/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<PatternDetail_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/PatternDetail/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/PatternDetail/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<PatternDetail_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
