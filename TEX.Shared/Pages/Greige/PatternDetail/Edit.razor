@page "/Greige/PatternDetail/Edit/{id}"
@using TEX.ViewModel.Greige.PatternDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">

    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <Select @bind-Value="@Model.Entity.PatternId" Items="@AllPatterns" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.PatternCode" />
        <BootstrapInput @bind-Value="@Model.Entity.PatternVersion" />
        <BootstrapInput @bind-Value="@Model.Entity.FabricCategory" />
        <BootstrapInput @bind-Value="@Model.Entity.VersionModified" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div class="d-flex" style="margin-top: 20px; align-items: center; justify-content: center; gap: 2px; ">
        <Card IsShadow="true" HeaderText="机台信息" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.MachineInch" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.MachineTotalNeedles" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.MachineSpec" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.JacquardFeed" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="基本要求" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.PatternRepeatWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.PatternRepeatHeight" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredFullWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />

        <Card IsShadow="true" HeaderText="花稿信息" Color="Color.Primary" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.PatternWeftPoint" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.PatternWarpPoint" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredFullWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.RequiredGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="坯布记录" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.GreigeRepeatWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.GreigeRepeatHeight" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.GreigeWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.GreigeGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
        <Divider IsVertical="true" />
        <Card IsShadow="true" HeaderText="成品记录" IsCollapsible="false" Collapsed="false">
            <BodyTemplate>
                <BootstrapInput @bind-Value="@Model.Entity.FabricRepeatWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.FabricRepeatHeight" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.FabricFullWidth" ShowLabel="true" />
                <BootstrapInput @bind-Value="@Model.Entity.FabricGsm" ShowLabel="true" />
            </BodyTemplate>
        </Card>
    </div>

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private PatternDetailVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllPatterns = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPatterns = await WtmBlazor.Api.CallItemsApi("/api/PatternDetail/GetPatterns", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<PatternDetailVM>($"/api/PatternDetail/{id}");
        Model = rv.Data;
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/PatternDetail/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
