@page "/Greige/PatternDetail"
@using TEX.ViewModel.Greige.PatternDetailVMs;
@inherits BasePage
@attribute [ActionDescription("提花稿", "TEX.Controllers,PatternDetail")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <WTDateRange @bind-Value="@SearchModel.CreateDate"  />
            <Select @bind-Value="@SearchModel.PatternId" Items="@AllPatterns" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInputNumber @bind-Value="@SearchModel.PatternCode"  />
            <BootstrapInput @bind-Value="@SearchModel.Customer"  />
            <BootstrapInput @bind-Value="@SearchModel.PatternVersion"  />
            <BootstrapInput @bind-Value="@SearchModel.FabricCategory"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.RequiredFullWidth"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.RequiredGsm"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.MachineTotalNeedles"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.JacquardFeed"  />
            <BootstrapInput @bind-Value="@SearchModel.VersionModified"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>

<PatternDetailList SearchModel="@SearchModel" RefreshTrigger ="@Refresh"/>

@code{

    private PatternDetailSearcher SearchModel = new PatternDetailSearcher();
    private Table<PatternDetail_View> dataTable;

    private List<SelectedItem> AllPatterns = new List<SelectedItem>();

    private bool Refresh = false;

    protected override async Task OnInitializedAsync()
    {
        AllPatterns = await WtmBlazor.Api.CallItemsApi("/api/PatternDetail/GetPatterns", placeholder: WtmBlazor.Localizer["Sys.All"]);
        await base.OnInitializedAsync();
    }

    private void DoSearch()
    {
        Refresh = !Refresh;
        //dataTable.QueryAsync();
    }

}
