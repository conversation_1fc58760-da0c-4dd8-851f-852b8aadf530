@page "/Greige/KnittingPlan/Create"
@using System.Net.WebSockets
@using System.Threading;
@using Newtonsoft.Json
@using System.Text
@using TEX.Model;
@using TEX.Shared.Components
@using TEX.Shared.Pages.Components
@using TEX.Shared.Services
@using TEX.ViewModel.Greige.KnittingPlanVMs;
@using TEX.ViewModel.Greige.KnittingProcessVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
        <SelectOrderTable @bind-Value="@porder" OnSelectedChanged="OnSelectedChanged" />
        <BootstrapInput @bind-Value="@Model.Entity.ProductName" IsDisabled="true" />
        <DateTimePicker @bind-Value="@Model.Entity.PlanDate" />
        <DateTimePicker @bind-Value="@Model.Entity.DeliveryDate" />

        <InputWithUnit @bind-Value="@OrderWeight" DisplayText="成品总重" Unit="KG" Required="false" Disabled="true" />
        <SelectKnittingProcess @bind-Value="@KnittingProcess" OnSelectedChanged="OnSelectedKnittingProcessChanged" />
        @* <Select @bind-Value="@Model.Entity.KnittingProcessId" ShowSearch="true"  Items="@AllKnittingProcesss" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/> *@
        <BootstrapInputNumber @bind-Value="@Model.Entity.WeightPerPiece" />
        @* <InputWithUnit @bind-Value="@LossRate" DisplayText="预计损耗" Unit="%" FormatString="P2" onchange="@OnLossRateChanged" /> *@
        <div class="input-group">
            <BootstrapInputGroupLabel DisplayText="预计损耗" ShowRequiredMark="false" />
            <div class="input-group">
                <BootstrapInputNumber @bind-Value="@LossRate" ShowLabel="false" OnValueChanged="@OnLossRateChanged" />
                <span class="input-group-text">%</span>
            </div>
        </div>
        <InputWithUnit @bind-Value="@Model.Entity.TotalWeight" DisplayText="坯布重量" Unit="KG" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.PieceCount" />
        <Select @bind-Value="@Model.Entity.KnittingFactoryId" Items="@AllKnittingFactory" IsPopover="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@Model.Entity.BatchNo" />

        <BootstrapInput @bind-Value="@Model.Entity.Requirements" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    @if (KnittingProcess is not null){
        <Divider Text="织造工艺" style="font-size:18px;margin-top:20px"/>
        <div style="margin-top:15px">
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
                <Display @bind-Value="@KnittingProcess.ProcessCode" />
                <Display @bind-Value="@KnittingProcess.FinishedWeight" />
                <Display @bind-Value="@KnittingProcess.FinishedWidth" />
                <Display @bind-Value="@KnittingProcess.FinishedPileHeight" />
                <Select @bind-Value="@KnittingProcess.MachineInfo.MachineType" IsDisabled="true" />
                <Display @bind-Value="@KnittingProcess.MachineInfo.MachineDiameter" />
                <Display @bind-Value="@KnittingProcess.MachineInfo.Gauge" />
                <Display @bind-Value="@KnittingProcess.MachineInfo.NeedleRows" />
                <Display @bind-Value="@KnittingProcess.MachineInfo.NeedleArrangement" />
                <Display @bind-Value="@KnittingProcess.MachineInfo.TotalNeedles" />
                <Display @bind-Value="@KnittingProcess.MachineInfo.TotalCourses" />
                <Display @bind-Value="@KnittingProcess.Remark" />
            </Row>
            <Table TItem="YarnInfo" @bind-Items="@YarnInfoList" IsStriped="true" IsBordered="true" ShowRefresh="false"
                   ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false"
                   ShowDefaultButtons="false" ShowEditButton="false"
                   TableSize="TableSize.Compact" IsFixedHeader="true" Height="140"
                   style="margin-top:10px">
                <TableColumns>
                    <TableColumn @bind-Field="@context.YarnType" IsPopover="true" />
                    <TableColumn @bind-Field="@context.YarnSpec" IsPopover="true" />
                    <TableColumn @bind-Field="@context.YarnUsage" IsPopover="true" />
                    <TableColumn @bind-Field="@context.YarnCount" Width="60" />
                    <TableColumn @bind-Field="@context.YarnLength" Width="60" />
                    <TableColumn @bind-Field="@context.ConsumptionRatio" Width="70" />
                    <TableColumn @bind-Field="@context.YarnBatchNo" />
                    <TableColumn @bind-Field="@context.Remark" />
                </TableColumns>
            </Table>
        </div>
    }

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private KnittingPlanVM Model = new KnittingPlanVM();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllKnittingFactory = new List<SelectedItem>();

    private List<SelectedItem> AllKnittingProcesss = new List<SelectedItem>();
    public PurchaseOrder_View porder { get; set; } = new();//给订单选择绑定
    public KnittingProcess_View KnittingProcess { get; set; }
    private IEnumerable<YarnInfo> YarnInfoList { get; set; }

    private int? OrderWeight { get; set; }
    private decimal? LossRate { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllKnittingFactory = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetKnittingFactories", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/KnittingPlan/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    //订单选择后查询产品名称和规格
    private async Task OnSelectedChanged(PurchaseOrder_View item)
    {
        if (item is not null)
        {
            Model.Entity.PurchaseOrderId = item.ID;
            Model.Entity.ProductName = item.PurchaseOrder_Product;
            OrderWeight = (int)item.PurchaseOrder_TotalWeight;

            StateHasChanged();
            await Task.CompletedTask;
        }
    }

    private async Task OnSelectedKnittingProcessChanged(KnittingProcess_View item)
    {
        if (item is not null)
        {
            Model.Entity.KnittingProcessId = item.ID;
            YarnInfoList = KnittingProcess.YarnInfoList;
            StateHasChanged();
            await Task.CompletedTask;
        }
    }
    private async Task OnLossRateChanged(decimal? value)
    {
        if (value > 0 && OrderWeight.HasValue)
        {
            Model.Entity.TotalWeight = CalculateTotalWeight(value, OrderWeight.Value, Model.Entity.WeightPerPiece);
            if (Model.Entity.WeightPerPiece.HasValue && Model.Entity.WeightPerPiece > 0) 
                Model.Entity.PieceCount = (int)Math.Ceiling(Model.Entity.TotalWeight.Value / Model.Entity.WeightPerPiece.Value);
        }
        await Task.CompletedTask;
    }
    private decimal? CalculateTotalWeight(decimal? lossRate, decimal orderWeight, decimal? weightPerPiece)
    {
        if (OrderWeight.HasValue)
        {
            if (lossRate.HasValue && lossRate > 0)
            {
                if (weightPerPiece.HasValue && weightPerPiece > 0)
                {
                    var result = Math.Ceiling((orderWeight * (100 + lossRate.Value) / 100) / weightPerPiece.Value);
                    return result * weightPerPiece;
                }
                else
                {
                    return (decimal)Math.Ceiling(orderWeight * (100 + lossRate ?? 0m) / 100);
                }
            }
            else
            {
                return orderWeight;
            }
        }
        else
        {
            return null;
        }
    }
}
