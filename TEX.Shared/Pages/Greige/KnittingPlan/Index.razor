@page "/Greige/KnittingPlan"
@using TEX.Shared.Services
@using TEX.ViewModel.Greige.KnittingPlanVMs;
@using System.Net.WebSockets
@using System.Threading;
@using Newtonsoft.Json
@using TEX.Model;
@using System.Text;


@inherits BasePage
@attribute [ActionDescription("织造计划", "TEX.Controllers,KnittingPlan")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@SearchModel.PlanDate" />
            <Select @bind-Value="@SearchModel.PurchaseOrderId" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.KnittingFactoryId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.ProductName" />
            <Select @bind-Value="@SearchModel.KnittingProcessId" Items="@AllKnittingProcesss" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
        </Row>
    </ValidateForm>
</WTSearchPanel>

<Table @ref="dataTable" TItem="KnittingPlan_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.BillNo" />
        <TableColumn @bind-Field="@context.PlanDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.OrderNo_view" />
        <TableColumn @bind-Field="@context.KnittingFactory_view" />
        <TableColumn @bind-Field="@context.ProductName" />
        <TableColumn @bind-Field="@context.ProcessCode_view" />
        <TableColumn @bind-Field="@context.PieceCount" />
        <TableColumn @bind-Field="@context.TotalWeight" />
        <TableColumn @bind-Field="@context.WeightPerPiece" />
        <TableColumn @bind-Field="@context.BatchNo" />
        <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.AuditStatus" />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/KnittingPlan/Add"))
        {
            <TableToolbarButton TItem="KnittingPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/KnittingPlan/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="KnittingPlan_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }

        @if (IsAccessable("/api/KnittingPlan/Import"))
        {
            <TableToolbarButton TItem="KnittingPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/KnittingPlan/ExportExcel"))
        {
            <TableToolbarButton TItem="KnittingPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/KnittingPlan/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/KnittingPlan/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/KnittingPlan/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>


