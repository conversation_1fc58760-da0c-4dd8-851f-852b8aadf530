@page "/Greige/KnittingPlan/Details/{id}"
@using System.Net.WebSockets
@using System.Text
@using System.Threading
@using Microsoft.Extensions.Logging
@using Newtonsoft.Json
@using TEX.Shared.Components
@using TEX.Shared.Services
@using TEX.ViewModel.Greige.KnittingPlanVMs;
@using TEX.ViewModel.Greige.KnittingProcessVMs

@inherits BasePage


<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <Display @bind-Value="@Model.Entity.BillNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.PlanDate" FormatString="yyyy-MM-dd" ShowLabel="true" />
        <Display @bind-Value="@porder.OrderNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.KnittingFactoryId" Lookup="@AllKnittingFactory" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.ProductName" ShowLabel="true" />
        <Display @bind-Value="@KnittingProcess.ProcessCode" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.PieceCount" ShowLabel="true" />
        <InputWithUnit @bind-Value="@OrderWeight" DisplayText="成品总重" Unit="KG" Required="false" Disabled="true" />
        <div class="input-group">
            <BootstrapInputGroupLabel DisplayText="预计损耗" ShowRequiredMark="false" />
            <div class="input-group">
                <BootstrapInputNumber @bind-Value="@LossRate" ShowLabel="false" IsDisabled="true" />
                <span class="input-group-text">%</span>
            </div>
        </div>
        <Display @bind-Value="@Model.Entity.TotalWeight" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.WeightPerPiece" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.BatchNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.DeliveryDate" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Requirements" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.AuditStatus" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Remark" ShowLabel="true" />
    </Row>
    <Divider Text="织造工艺" style="font-size:18px;margin-top:20px" />
    <div style="margin-top:15px">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <Display @bind-Value="@KnittingProcess.ProcessCode" />
            <Display @bind-Value="@KnittingProcess.FinishedWeight" />
            <Display @bind-Value="@KnittingProcess.FinishedWidth" />
            <Display @bind-Value="@KnittingProcess.FinishedPileHeight" />
            <Select @bind-Value="@KnittingProcess.MachineInfo.MachineType" IsDisabled="true" />
            <Display @bind-Value="@KnittingProcess.MachineInfo.MachineDiameter" />
            <Display @bind-Value="@KnittingProcess.MachineInfo.Gauge" />
            <Display @bind-Value="@KnittingProcess.MachineInfo.NeedleRows" />
            <Display @bind-Value="@KnittingProcess.MachineInfo.NeedleArrangement" />
            <Display @bind-Value="@KnittingProcess.MachineInfo.TotalNeedles" />
            <Display @bind-Value="@KnittingProcess.MachineInfo.TotalCourses" />
            <Display @bind-Value="@KnittingProcess.Remark" />
        </Row>
        <Table TItem="YarnInfo" @bind-Items="@YarnInfoList" IsStriped="true" IsBordered="true" ShowRefresh="false"
        ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false"
        ShowDefaultButtons="false" ShowEditButton="false"
        TableSize="TableSize.Compact" IsFixedHeader="true" Height="140"
        style="margin-top:10px">
            <TableColumns>
                <TableColumn @bind-Field="@context.YarnType" IsPopover="true" />
                <TableColumn @bind-Field="@context.YarnSpec" IsPopover="true" />
                <TableColumn @bind-Field="@context.YarnUsage" IsPopover="true" />
                <TableColumn @bind-Field="@context.YarnCount" Width="60" />
                <TableColumn @bind-Field="@context.YarnLength" Width="60" />
                <TableColumn @bind-Field="@context.ConsumptionRatio" Width="70" />
                <TableColumn @bind-Field="@context.YarnBatchNo" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
        </Table>
    </div>
    <div class="row mt-3">
        <div class="col-12 col-sm-10">
            <WSPrint printData="@printdata" PrintFileName="KnitPlan.grf" DataTypeName="KnittingPlanDTO" />
        </div>

        <div class="col-12 col-sm-2">
            <Button Color="Color.Secondary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        </div>
    </div>
</ValidateForm>

@code {
    private KnittingPlanVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private string printdata = "";

    private List<SelectedItem> AllKnittingFactory = new List<SelectedItem>();
    public KnittingProcess_View KnittingProcess { get; set; } = new();
    private IEnumerable<YarnInfo> YarnInfoList { get; set; } = new List<YarnInfo>();
    public PurchaseOrder_View porder { get; set; } = new();//给订单选择绑定
    private int OrderWeight { get; set; } = 0;
    private decimal LossRate { get; set; } = 0;

    [Inject]
    private ILogger<KnittingPlanVM> Logger { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllKnittingFactory = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetKnittingFactories", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<KnittingPlanVM>($"/api/KnittingPlan/{id}");
        Model = rv.Data;

        var po = await StartSearch<PurchaseOrder_View>("/api/Models/PurchaseOrder/SearchPurchaseOrder", new PurchaseOrderSearcher()
            {
                OrderId = Model.Entity.PurchaseOrderId,
            }, new QueryPageOptions());

        var kp = await WtmBlazor.Api.CallAPI<KnittingProcess_View>($"api/KnittingProcess/GetView/{Model.Entity.KnittingProcessId}");

        if (kp.StatusCode == System.Net.HttpStatusCode.OK)
        {
            KnittingProcess = kp.Data ?? new KnittingProcess_View();
            if (KnittingProcess.YarnInfoList.Count>0) YarnInfoList = KnittingProcess.YarnInfoList;
            Logger.LogInformation("YarnInfoList: " + YarnInfoList.Count());
        }
        if (po.Items is not null)
        {
            porder = po.Items.FirstOrDefault();
            OrderWeight = (int)porder.PurchaseOrder_TotalWeight;
            if (OrderWeight > 0)
                LossRate = Math.Round((Model.Entity.TotalWeight / OrderWeight * 100m - 100m).Value, 1);
        }

        var p = await WtmBlazor.Api.CallAPI<string>($"/api/KnittingPlan/getknitplandto/{Model.Entity.ID}");
        printdata = p.Data;

    }

    public void OnClose()
    {
        CloseDialog();
    }


    /*
    [Inject]
    private WebSocketService WebSocketService { get; set; }
    [Inject]
    private ILogger<KnittingPlanVM> Logger { get; set; }
    private TaskCompletionSource<bool> _printResponseReceived;

    private async Task SendPrintRequest2()
            {
            try
            {
            // 初始化响应标志
            _printResponseReceived = new TaskCompletionSource<bool>();

            // 确保之前的连接已经断开
            if (WebSocketService.IsConnected)
                {
                await WebSocketService.DisconnectAsync();
            }

            if (!WebSocketService.IsConnected)
                {
                await WebSocketService.ConnectAsync("ws://127.0.0.1:55555");
                // 等待连接稳定
                await Task.Delay(100);
            }

            // 注册消息处理
            //WebSocketService.OnMessageReceived += HandleMessage;

                // 准备并发送打印数据
                try
                {
                var jsonMessage = await PrepareData();
                Logger.LogInformation("准备发送打印数据: {Length} 字节", jsonMessage.Length);
               // await WebSocketService.SendMessageAsync(jsonMessage);

                // 等待响应，设置超时时间为10秒
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                await using (cts.Token.Register(() => _printResponseReceived.TrySetCanceled()))
                        {
                        try
                        {
                        await _printResponseReceived.Task;
                    }
                    catch (OperationCanceledException)
                        {
                        await WtmBlazor.Toast.Warning("打印请求超时，未收到响应");
            }
            }
            }
            catch (Exception ex)
                {
                Logger.LogError(ex, "发送打印请求失败");
                await WtmBlazor.Toast.Error("发送打印请求失败: " + ex.Message);
            }
            }
            finally
            {
            // 清理工作：取消消息处理器注册并断开连接
            //WebSocketService.OnMessageReceived -= HandleMessage;
            if (WebSocketService.IsConnected)
                {
                await WebSocketService.DisconnectAsync();
    }
    }
    }

    private async Task<string> PrepareData()
            {
            try
            {
            var plan = await WtmBlazor.Api.CallAPI<string>($"/api/KnittingPlan/getknitplandto/{Model.Entity.ID}");
            if (string.IsNullOrEmpty(plan?.Data))
                {
                throw new Exception("获取打印数据失败");
            }

            var printRequest = new PrintRequestMessage
                    {
                    MessageType = MessageType.PrintRequest,
                    PrintFileName = "knitplan3.frx",
                    //ClassName = "KnittingPlan",
                    Copies = 1,
                    PrintData = "[" + plan.Data + "]",
            IsPreview = true
            };

            return JsonConvert.SerializeObject(printRequest);
        }
        catch (Exception ex)
            {
            Logger.LogError(ex, "准备打印数据失败");
            throw new Exception("准备打印数据失败: " + ex.Message);
    }
    }
    private void HandleMessage(string message)
            {
            try
            {
            var response = JsonConvert.DeserializeObject<PrintStatusMessage>(message);
            if (response is not null)
                {
                InvokeAsync(async () =>
                {
                string msg = response.IsSuccess ? "成功" : "失败";
                 await WtmBlazor.Toast.Success($"打印{msg}", response.Message);
                });
                //string msg = response.IsSuccess ? "成功" : "失败";
                //WtmBlazor.Toast.Success($"打印{msg}", $"{response.ErrorMessage}");

                // 标记已收到响应
                _printResponseReceived?.TrySetResult(true);
        }
        }
        catch (Exception ex)
            {
            Logger.LogError(ex, "处理打印响应消息失败");
            WtmBlazor.Toast.Error("处理打印响应失败");
            _printResponseReceived?.TrySetException(ex);
    }
    }
    */
}
