using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.WebSockets;
using System.Numerics;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TEX.Shared.Services;
using TEX.ViewModel.Greige.KnittingPlanVMs;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Greige.KnittingPlan
{
    public partial class Index : BasePage
    {
        [Inject]
        private WebSocketService WebSocketService { get; set; }

        private KnittingPlanSearcher SearchModel = new KnittingPlanSearcher();
        private Table<KnittingPlan_View> dataTable;

        private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

        private List<SelectedItem> AllCompanys = new List<SelectedItem>();

        private List<SelectedItem> AllKnittingProcesss = new List<SelectedItem>();


        protected override async Task OnInitializedAsync()
        {

            AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/KnittingPlan/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);

            AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/KnittingPlan/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

            AllKnittingProcesss = await WtmBlazor.Api.CallItemsApi("/api/KnittingPlan/GetKnittingProcesss", placeholder: WtmBlazor.Localizer["Sys.All"]);
            //await Connect();        
            await base.OnInitializedAsync();
        }

        private async Task<QueryData<KnittingPlan_View>> OnSearch(QueryPageOptions opts)
        {
            return await StartSearch<KnittingPlan_View>("/api/KnittingPlan/Search", SearchModel, opts);
        }

        private void DoSearch()
        {
            dataTable.QueryAsync();
        }

        private async Task OnCreateClick(IEnumerable<KnittingPlan_View> items)
        {
            //if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
            if (await OpenDialog<Create>("�½�֯��ƻ�") == DialogResult.Yes)
            {
                await dataTable.QueryAsync();
            }
        }

        private async Task OnEditClick(KnittingPlan_View item)
        {
            if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
            {
                await dataTable.QueryAsync();
            }
        }

        private async Task OnDetailsClick(KnittingPlan_View item)
        {
            await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
        }


        private async Task OnBatchDeleteClick()
        {
            if (dataTable.SelectedRows?.Any() == true)
            {
                await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/KnittingPlan/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
                await dataTable.QueryAsync();
            }
            else
            {
                await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
            }
        }

        private async Task OnDeleteClick(KnittingPlan_View item)
        {
            await PostsData(new List<string> { item.ID.ToString() }, $"/api/KnittingPlan/batchdelete", (s) => "Sys.OprationSuccess");
            await dataTable.QueryAsync();
        }


        private async Task OnExportClick(IEnumerable<KnittingPlan_View> items)
        {
            if (dataTable.SelectedRows?.Any() == true)
            {
                await Download("/api/KnittingPlan/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
            }
            else
            {
                await Download("/api/KnittingPlan/ExportExcel", SearchModel);
            }
        }
        private async Task OnImportClick(IEnumerable<KnittingPlan_View> items)
        {
            if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
            {
                await dataTable.QueryAsync();
            }
        }
    }
}