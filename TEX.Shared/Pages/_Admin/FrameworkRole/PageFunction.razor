@page "/_Admin/FrameworkRole/PageFunction/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Roles" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Inline">
        <Display @bind-Value="@Roles.Entity.RoleCode" ShowLabel="true" />
        <Display @bind-Value="@Roles.Entity.RoleName" ShowLabel="true" />
    </Row>

    @if (Roles?.Pages != null)
    {
        <Table @ref="table" Items="Roles.Pages" TItem="Page_View" IsPagination="false" IsBordered="true" ShowRefresh="false"
               ShowDefaultButtons="false" style="margin-top:10px;">
            <TableColumns>
                <TableColumn @bind-Field="@context.Name" Width="300">
                    <Template Context="name">
                        @{
                            var row = (Page_View)name.Row;
                            <div style="margin-left:@(row.Level*20)px">@name.Value</div>

                        }

                    </Template>

                </TableColumn>
                <TableColumn @bind-Field="@context.Actions" Width="800" AllowTextWrap="true">
                    <Template Context="actions">
                        @{
                            var row = (Page_View)actions.Row;
                            if (row.IsFolder == true)
                            {
                                <CheckboxList style="margin-left:10px;" @bind-Value="row.Actions" Items="AllActions[row.ID]" ShowLabel="false" ShowBorder="false" OnSelectedChanged="(x, y) => SetSub(row)" />
                            }
                            else
                            {
                                <CheckboxList style="margin-left:10px;" @bind-Value="row.Actions" Items="AllActions[row.ID]" ShowLabel="false" ShowBorder="false" OnSelectedChanged="(x, y) => SetParent(row)" />

                            }
                        }
                    </Template>
                </TableColumn>
            </TableColumns>
        </Table>
    }
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

<style>
    .table-cell {
        display: inline;
    }
</style>
@code {
    private FrameworkRoleMDVM2 Roles = new FrameworkRoleMDVM2();
    private Dictionary<Guid, List<SelectedItem>> AllActions;
    private ValidateForm vform { get; set; }
    private Table<Page_View> table;
    [Parameter]
    public string id { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var rv = await WtmBlazor.Api.CallAPI<FrameworkRoleMDVM2>($"/api/_FrameworkRole/GetPageActions/{id}");
        AllActions = new Dictionary<Guid, List<SelectedItem>>();
        foreach (var item in rv.Data.Pages)
        {
            AllActions.Add(item.ID, item.AllActions.ToBBItems());
        }
        Roles = rv.Data;
    }

    private async Task SetSub(Page_View row)
    {
        if (row.IsFolder == true)
        {
            foreach (var item in Roles.Pages)
            {
                if (item.ParentID == row.ID)
                {
                    AllActions[item.ID].ForEach(x => x.Active = AllActions[row.ID].Any(x => x.Active));
                    item.Actions = AllActions[item.ID].Where(x => x.Active == true).Select(x => x.Value).ToList().ConvertAll<Guid>(x => new Guid(x));
                    await SetSub(item);
                }
            }
        }
    }

    private async Task SetParent(Page_View row)
    {
        var parent = Roles.Pages.Where(x => x.ID == row.ParentID).FirstOrDefault();
        if (parent != null)
        {
            AllActions[parent.ID].ForEach(x => x.Active = true);
            parent.Actions = AllActions[parent.ID].Where(x => x.Active == true).Select(x => x.Value).ToList().ConvertAll<Guid>(x => new Guid(x));
            await SetParent(parent);
        }
    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_FrameworkRole/EditPrivilege", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }
}
