@page "/_Admin/FrameworkRole"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs;
@inherits BasePage
@attribute [ActionDescription("MenuKey.RoleManagement", "WalkingTec.Mvvm.Admin.Api,FrameworkRole")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@SearchModel.RoleCode" />
            <BootstrapInput @bind-Value="@SearchModel.RoleName" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="FrameworkRole" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowExtendEditButton="false" ShowExtendDeleteButton="false" ShowDefaultButtons="false" style="margin-top:10px;" AllowResizing="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.RoleCode" Width="180" />
        <TableColumn @bind-Field="@context.RoleName" />
        <TableColumn @bind-Field="@context.RoleRemark" />

    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/_FrameworkRole/Add") && UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarButton TItem="FrameworkRole" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/_FrameworkRole/BatchDelete")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarPopConfirmButton TItem="FrameworkRole" Color="Color.Danger"
                                      Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        @if (IsAccessable("/api/_FrameworkRole/Import")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarButton TItem="FrameworkRole" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/_FrameworkRole/ExportExcel")&&  UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarButton TItem="FrameworkRole" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
            @if (IsAccessable("/api/_FrameworkRole/Edit")&&  UserInfo.Attributes["IsMainHost"].ToString() == "False")
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/_FrameworkRole/{id}")&&  UserInfo.Attributes["IsMainHost"].ToString() == "False")
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info-circle" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="() => OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/_FrameworkRole/BatchDelete")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
            @if (IsAccessable("/api/_FrameworkRole/EditPrivilege"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Warning" Icon="fa fa-key" Text="@WtmBlazor.Localizer["_Admin.PageFunction"]" OnClick="() => OnPageFunctionClick(context)" />
            }
    </RowButtonTemplate>
</Table>

@code{

    private FrameworkRoleSearcher SearchModel = new FrameworkRoleSearcher();
    private Table<FrameworkRole> dataTable;



    private async Task<QueryData<FrameworkRole>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<FrameworkRole>("/api/_FrameworkRole/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<FrameworkRole> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(FrameworkRole item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnPageFunctionClick(FrameworkRole item)
    {
        await OpenDialog<PageFunction>(WtmBlazor.Localizer["_Admin.PageFunction"], x => x.id == item.ID.ToString());
    }

    private async Task OnDetailsClick(FrameworkRole item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/_FrameworkRole/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(FrameworkRole item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/_FrameworkRole/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<FrameworkRole> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/_FrameworkRole/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/_FrameworkRole/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<FrameworkRole> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
