@page "/_Admin/FrameworkTenant/Edit/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two">
        <BootstrapInput @bind-Value="@Model.Entity.TCode" />
        <BootstrapInput @bind-Value="@Model.Entity.TName" />
        <BootstrapInput @bind-Value="@Model.Entity.TDb" />
        <Select @bind-Value="@Model.Entity.TDbType" />
        <BootstrapInput @bind-Value="@Model.Entity.DbContext" />
        <BootstrapInput @bind-Value="@Model.Entity.TDomain" />
        <Select @bind-Value="@Model.Entity.EnableSub" Items="WtmBlazor.GlobalSelectItems.BoolItems"/>
        <Select @bind-Value="@Model.Entity.Enabled" Items="WtmBlazor.GlobalSelectItems.BoolItems"/>
    </Row>

    <Row ItemsPerRow="ItemsPerRow.Two">
        <Select @bind-Value="@Model.AdminRoleCode" Items="@AllParents" />
        <div style="padding-top:35px;">@WtmBlazor.Localizer["_Admin.TenantRole"]</div>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>
@code {
    private FrameworkTenantVM Model = new FrameworkTenantVM();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> AllParents = new List<SelectedItem>();
    [Parameter]
    public string id { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllParents = await WtmBlazor.Api.CallItemsApi("/api/_account/GetFrameworkRoles", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        var rv = await WtmBlazor.Api.CallAPI<FrameworkTenantVM>($"/api/_FrameworkTenant/{id}");
        Model = rv.Data;
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_FrameworkTenant/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
