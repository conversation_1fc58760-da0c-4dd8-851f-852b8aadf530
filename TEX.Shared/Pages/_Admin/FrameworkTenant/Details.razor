@page "/_Admin/FrameworkTenant/Details/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs;
@inherits BasePage


<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Two">
        <Display @bind-Value="@Model.Entity.TCode" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.TName" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.TDb" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.TDbType" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.DbContext" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.TDomain" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.EnableSub" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Enabled" ShowLabel="true" />
    </Row>

    <Row ItemsPerRow="ItemsPerRow.Two">
        <Display @bind-Value="@Model.AdminRoleCode" Lookup="@AllParents" ShowLabel="true" />
    </Row>

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="关闭" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {
    private FrameworkTenantVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    private List<SelectedItem> AllParents = new List<SelectedItem>();

    protected override async Task OnInitializedAsync()
    {
        AllParents = await WtmBlazor.Api.CallItemsApi("/api/_account/GetFrameworkRoles", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        var rv = await WtmBlazor.Api.CallAPI<FrameworkTenantVM>($"/api/_FrameworkTenant/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
