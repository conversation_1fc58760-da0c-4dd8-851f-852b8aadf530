
@page "/_Admin/FrameworkUser/BatchEdit"
@using TEX.ViewModel._Admin.FrameworkUserVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
  <div style="margin-bottom:10px;"> @WtmBlazor.Localizer["Sys.BatchEditConfirm"]</div>
  <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
    <BootstrapInput @bind-Value="@Model.LinkedVM.Email"/>
    <Select @bind-Value="@Model.LinkedVM.Gender" Id="g4212dfbea319408b9a484ecbf0f90bec"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.CellPhone"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.HomePhone"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Address"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.ZipCode"/>
    <MultiSelect @bind-Value="@Model.LinkedVM.SelectedFrameworkUserRolesIDs" Items="AllFrameworkUserRoles" Id="gcca9c6bcb95e4980be0720700c193ae9"/>
    <MultiSelect @bind-Value="@Model.LinkedVM.SelectedFrameworkUserGroupsIDs" Items="AllFrameworkUserGroups" Id="g309b0bbd31e148119fe500e71e49f88c"/>
  </Row>

  <div class="modal-footer table-modal-footer">
    <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]"/>
    <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]"/>
  </div>

</ValidateForm>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private FrameworkUserBatchVM Model = new FrameworkUserBatchVM();
    private ValidateForm vform { get; set; }
    
    private async Task Submit(EditContext context)
    {
        Model.Ids = ids;
        await PostsForm(vform, "/api/_Admin/FrameworkUser/BatchEdit", (s) => WtmBlazor.Localizer["Sys.BatchEditSuccess", s], method: HttpMethodEnum.POST);
    }
            

    public void OnClose()
    {
        CloseDialog();
    }
    
    private List<SelectedItem> AllFrameworkUserRoles = new List<SelectedItem>();
    private List<SelectedItem> AllFrameworkUserGroups = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        
        
        AllFrameworkUserRoles = await WtmBlazor.Api.CallItemsApi("/api/_Admin/FrameworkUser/GetFrameworkRoles");
        AllFrameworkUserRoles.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });
        AllFrameworkUserGroups = await WtmBlazor.Api.CallItemsApi("/api/_Admin/FrameworkUser/GetFrameworkGroups");
        AllFrameworkUserGroups.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });

        await base.OnInitializedAsync();
    }
}
