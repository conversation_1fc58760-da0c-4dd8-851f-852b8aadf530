
@page "/_Admin/FrameworkUser/Index"
@using TEX.ViewModel._Admin.FrameworkUserVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage
@attribute [ActionDescription("_Page._Admin.FrameworkUser.Index", "TEX._Admin.Controllers,FrameworkUser")]

<WTSearchPanel OnSearch="@FrameworkUserListVMDoSearch">
  <ValidateForm Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
      <BootstrapInput @bind-Value="@Model.Searcher.ITCode"/>
      <BootstrapInput @bind-Value="@Model.Searcher.Name"/>
      <Select @bind-Value="@Model.Searcher.IsValid" Items="@WtmBlazor.GlobalSelectItems.SearcherBoolItems" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g59c7b0ef2c01454c95fd46882ae985ff"/>
    </Row>

  </ValidateForm>
</WTSearchPanel>


<Table @ref="FrameworkUserListVMdataTable" TItem="TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View" OnQueryAsync="FrameworkUserListVMOnSearchFrameworkUser" 
                IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" 
                ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
                ShowDefaultButtons="false" IsPagination="true">
  <TableColumns>
    <TableColumn @bind-Field="@context.FrameworkUser_ITCode" Text="@WtmBlazor.Localizer["Sys.Account"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_Name" Text="@WtmBlazor.Localizer["_Admin.Name"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_Gender" Text="@WtmBlazor.Localizer["_Admin.Gender"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_CellPhone" Text="@WtmBlazor.Localizer["_Admin.CellPhone"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_Role" Text="@WtmBlazor.Localizer["_Admin.Role"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_Group" Text="@WtmBlazor.Localizer["_Admin.Group"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_IsValid" Text="@WtmBlazor.Localizer["_Admin.IsValid"]" />
    <TableColumn @bind-Field="@context.FrameworkUser_Photo" Text="@WtmBlazor.Localizer["_Admin.Photo"]">
      <Template Context="data">
        <Avatar @key="data.Value" Size="Size.ExtraSmall" GetUrlAsync="()=>WtmBlazor.GetBase64Image(data.Value.ToString(), 150, 150)" />
      </Template>
    </TableColumn>
  </TableColumns>

  <RowButtonTemplate>
    @if (IsAccessable("/api/_Admin/FrameworkUser/Edit"))
    {
        <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-pencil-square" Color="Color.Warning" Text="@WtmBlazor.Localizer["Sys.Edit"]"  OnClick="()=>OnEditClick(context)" />
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/{id}"))
    {
        <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="@WtmBlazor.Localizer["Page.详情"]"  OnClick="()=>OnDetailsClick(context)" />
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/BatchDelete"))
    {
        <PopConfirmButton OnConfirm="() => OnDeleteClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" 
            ConfirmButtonColor="Color.Danger" />
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/Edit"))
    {
        <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-unlock-alt" Color="Color.Warning" Text="@WtmBlazor.Localizer["Login.ChangePassword"]"  OnClick="()=>OnPasswordClick(context)" />
    }
  </RowButtonTemplate>
  <TableToolbarTemplate>
    @if (IsAccessable("/api/_Admin/FrameworkUser/Create"))
    {
        <TableToolbarButton TItem="TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View" Icon="fa fa-plus" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Create"]"  OnClickCallback="(x)=>OnCreateClick()" />
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/Import"))
    {
        <TableToolbarButton TItem="TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View" Icon="fa fa-tasks" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Import"]"  OnClickCallback="(x)=>OnImportClick()" />
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/BatchDelete"))
    {
        <TableToolbarPopConfirmButton TItem="TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View"
             Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" 
            OnConfirm="@OnBatchDeleteClick" ConfirmButtonColor="Color.Danger"/>
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/BatchEdit"))
    {
        <TableToolbarButton TItem="TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View" Icon="fa fa-pencil-square" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.BatchEdit"]"  OnClickCallback="(x)=>OnBatchEditClick()" />
    }
    @if (IsAccessable("/api/_Admin/FrameworkUser/FrameworkUserExportExcel"))
    {
        <TableToolbarButton TItem="TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View" Icon="fa fa-arrow-circle-down" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="(x)=>OnExportClick()" IsAsync="true" />
    }
  </TableToolbarTemplate>

</Table>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private FrameworkUserListVM Model = new FrameworkUserListVM();
    private Table<TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View> FrameworkUserListVMdataTable;
    private async Task FrameworkUserListVMDoSearch()
    {
        await FrameworkUserListVMdataTable.QueryAsync();
    }
    private async Task<QueryData<TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View>> FrameworkUserListVMOnSearchFrameworkUser(QueryPageOptions opts)
    {
            
        return await StartSearch<TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View>("/api/_Admin/FrameworkUser/SearchFrameworkUser", Model.Searcher, opts);
    }
    private async Task OnCreateClick()
    {
        var id = FrameworkUserListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Create>(@WtmBlazor.Localizer["Sys.Create"], x => x.id == (id ?? ""), isMax:false) == DialogResult.Yes)
        {
            await FrameworkUserListVMdataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View item)
    {
        if (await OpenDialog<Edit>(@WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString(), isMax:false) == DialogResult.Yes)
        {
            await FrameworkUserListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View item)
    {
        if (await OpenDialog<Details>(@WtmBlazor.Localizer["Page.详情"], x => x.id == item.ID.ToString(), isMax:false) == DialogResult.Yes)
        {
            await FrameworkUserListVMdataTable.QueryAsync();
        }
    }

    private async Task OnImportClick()
    {
        var id = FrameworkUserListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Import>(@WtmBlazor.Localizer["Sys.Import"], x => x.id == (id ?? ""), isMax:false) == DialogResult.Yes)
        {
            await FrameworkUserListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDeleteClick(TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/_Admin/FrameworkUser/batchdelete", (s) => "Sys.OprationSuccess");
        await FrameworkUserListVMdataTable.QueryAsync();
    }

    private async Task OnBatchDeleteClick()
    {
        if (FrameworkUserListVMdataTable.SelectedRows?.Any() == true)
        {
            await PostsData(FrameworkUserListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList(), $"/api/_Admin/FrameworkUser/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await FrameworkUserListVMdataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnBatchEditClick()
    {
        if (FrameworkUserListVMdataTable.SelectedRows?.Any() == true)
        {
            if (await OpenDialog<BatchEdit>(WtmBlazor.Localizer["Sys.BatchEdit"], x => x.ids == FrameworkUserListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToArray(), isMax:false) == DialogResult.Yes)
            {
                await FrameworkUserListVMdataTable.QueryAsync();
            }
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnExportClick()
    {
        if (FrameworkUserListVMdataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/_Admin/FrameworkUser/FrameworkUserExportExcelByIds", FrameworkUserListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/_Admin/FrameworkUser/FrameworkUserExportExcel", Model.Searcher);
        }
    }

    private async Task OnPasswordClick(TEX.ViewModel._Admin.FrameworkUserVMs.FrameworkUser_View item)
    {
        if (await OpenDialog<Password>(@WtmBlazor.Localizer["Login.ChangePassword"], x => x.id == item.ID.ToString(), isMax:false) == DialogResult.Yes)
        {
            await FrameworkUserListVMdataTable.QueryAsync();
        }
    }

    
    protected override async Task OnInitializedAsync()
    {
        
        

        await base.OnInitializedAsync();
    }
}
