
@page "/_Admin/FrameworkUser/Password"
@using TEX.ViewModel._Admin.FrameworkUserVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit" >
  <BootstrapInput @bind-Value="@Model.Entity.Password" type="password"/>
  <div class="modal-footer table-modal-footer">
    <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]"/>
    <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]"/>
  </div>

</ValidateForm>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private FrameworkUserVM Model = new FrameworkUserVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {
                        
        
        await PostsForm(vform, "/api/_Admin/FrameworkUser/Edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }
    
    protected override async Task OnInitializedAsync()
    {
        
        
        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<FrameworkUserVM>($"/api/_Admin/FrameworkUser/{id}");
            Model = rv.Data;
        }

        await base.OnInitializedAsync();
    }
}
