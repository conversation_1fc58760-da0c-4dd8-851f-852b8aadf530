@page "/_Admin/ActionLog"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.ActionLogVMs;
@using System.ComponentModel.DataAnnotations;
@inherits BasePage
@attribute [ActionDescription("MenuKey.ActionLog", "WalkingTec.Mvvm.Admin.Api,ActionLog")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <MultiSelect @bind-Value="@SearchModel.LogType" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.ITCode" />
            <BootstrapInput @bind-Value="@SearchModel.ActionUrl" />
            <WTDateRange @bind-Value="@SearchModel.ActionTime" />
            <BootstrapInput @bind-Value="@SearchModel.IP" />
            <BootstrapInput @bind-Value="@SearchModel.Duration" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="ActionLog" ShowLineNo="true" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false" AllowResizing="true"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowExtendEditButton="false" ShowExtendDeleteButton="false" FixedExtendButtonsColumn="true" ShowDefaultButtons="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.LogType" Width="120" />
        <TableColumn @bind-Field="@context.ModuleName" Width="120" />
        <TableColumn @bind-Field="@context.ActionName" Width="120" />
        <TableColumn @bind-Field="@context.ITCode" Width="120" />
        <TableColumn @bind-Field="@context.ActionUrl" Width="200" />
        <TableColumn @bind-Field="@context.ActionTime" Width="180" Sortable="true" FormatString="yyyy-MM-dd HH:mm:ss" />
        <TableColumn @bind-Field="@context.Duration" Width="120" Formatter="IntFormatter" />
        <TableColumn @bind-Field="@context.IP" Width="120" />
        @*<TableColumn @bind-Field="@context.Remark" Width="320"  AllowTextWrap="true" />*@
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/_ActionLog/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="ActionLog" Color="Color.Danger"
                                      Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        @if (IsAccessable("/api/_ActionLog/ExportExcel"))
        {
            <TableToolbarButton TItem="ActionLog" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
            @if (IsAccessable("/api/_ActionLog/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info-circle" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="() => OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/_ActionLog/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
    </RowButtonTemplate>
</Table>

@code{

    private ActionLogSearcher SearchModel = new ActionLogSearcher();
    private Table<ActionLog> dataTable;


    private async Task<QueryData<ActionLog>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<ActionLog>("/api/_ActionLog/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private static Task<string> IntFormatter(object d)
    {
        var data = (d as TableColumnContext<ActionLog, object>).Value;
        var val = (double?)data;
        return Task.FromResult(val?.ToString("F2") ?? "");
    }



    private async Task OnDetailsClick(ActionLog item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/_ActionLog/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(ActionLog item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/_ActionLog/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<ActionLog> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/_ActionLog/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/_ActionLog/ExportExcel", SearchModel);
        }
    }

}
