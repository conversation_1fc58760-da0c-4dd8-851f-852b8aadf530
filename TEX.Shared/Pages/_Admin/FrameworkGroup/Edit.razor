@page "/_Admin/FrameworkGroup/Edit/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <BootstrapInput @bind-Value="@Model.Entity.GroupCode" />
    <BootstrapInput @bind-Value="@Model.Entity.GroupName" />
    <Select @bind-Value="@Model.Entity.ParentId" Items="@AllParents" />
    <BootstrapInput @bind-Value="@Model.Entity.Manager" />
    <BootstrapInput @bind-Value="@Model.Entity.GroupRemark" />
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>
@code {
    private FrameworkGroupVM Model = null;
    private ValidateForm vform { get; set; }
    private List<SelectedItem> AllParents = new List<SelectedItem>();
    [Parameter]
    public string id { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllParents = await WtmBlazor.Api.CallItemsApi("/api/_FrameworkGroup/GetParents", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        var rv = await WtmBlazor.Api.CallAPI<FrameworkGroupVM>($"/api/_FrameworkGroup/{id}");
        Model = rv.Data;
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_FrameworkGroup/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
