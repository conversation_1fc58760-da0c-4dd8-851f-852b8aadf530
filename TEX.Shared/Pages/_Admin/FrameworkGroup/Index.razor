@page "/_Admin/FrameworkGroup"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs;
@inherits BasePage
@attribute [ActionDescription("MenuKey.GroupManagement", "WalkingTec.Mvvm.Admin.Api,FrameworkGroup")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@SearchModel.GroupCode" />
            <BootstrapInput @bind-Value="@SearchModel.GroupName" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="FrameworkGroup_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;" TreeNodeConverter="(x)=>WtmBlazor.DefaultTreeConverter(x)" IsTree="true" AllowResizing="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.GroupName" Width="220"/>
        <TableColumn @bind-Field="@context.GroupCode" Width="180" />
        <TableColumn @bind-Field="@context.ManagerName" Width="220"/>
        <TableColumn @bind-Field="@context.GroupRemark" />

    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/_FrameworkGroup/Add")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarButton TItem="FrameworkGroup_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/_FrameworkGroup/BatchDelete")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarPopConfirmButton TItem="FrameworkGroup_View" Color="Color.Danger"
                                      Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        @if (IsAccessable("/api/_FrameworkGroup/Import")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarButton TItem="FrameworkGroup_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/_FrameworkGroup/ExportExcel")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
        {
            <TableToolbarButton TItem="FrameworkGroup_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
            @if (IsAccessable("/api/_FrameworkGroup/Edit")&&  UserInfo.Attributes["IsMainHost"].ToString() == "False")
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/_FrameworkGroup/{id}")&&  UserInfo.Attributes["IsMainHost"].ToString() == "False")
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info-circle" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="() => OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/_FrameworkGroup/BatchDelete")&& UserInfo.Attributes["IsMainHost"].ToString() == "False")
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
    </RowButtonTemplate>
</Table>

@code{

    private FrameworkGroupSearcher SearchModel = new FrameworkGroupSearcher();
    private Table<FrameworkGroup_View> dataTable;



    private async Task<QueryData<FrameworkGroup_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearchTree<FrameworkGroup_View>("/api/_FrameworkGroup/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

        private async Task<IEnumerable<FrameworkGroup_View>> OnTreeExpand(FrameworkGroup_View data)
    {
        return await Task.FromResult(data.Children);
    }

    private async Task OnCreateClick(IEnumerable<FrameworkGroup_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(FrameworkGroup_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }


    private async Task OnDetailsClick(FrameworkGroup_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/_FrameworkGroup/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(FrameworkGroup_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/_FrameworkGroup/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<FrameworkGroup_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/_FrameworkGroup/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/_FrameworkGroup/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<FrameworkGroup_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
