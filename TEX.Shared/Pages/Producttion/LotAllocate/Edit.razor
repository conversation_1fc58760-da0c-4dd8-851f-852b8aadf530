@page "/Producttion/LotAllocate/Edit/{id}"
@using TEX.ViewModel.Producttion.LotAllocateVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.PlanDetailId" Items="@AllPlanDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <DateTimePicker @bind-Value="@Model.Entity.CreateDate"  />
            <BootstrapInput @bind-Value="@Model.Entity.ProductName"  />
            <BootstrapInput @bind-Value="@Model.Entity.Color"  />
            <BootstrapInput @bind-Value="@Model.Entity.ColorCode"  />
            <BootstrapInput @bind-Value="@Model.Entity.LotNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            @* <BootstrapInput @bind-Value="@Model.Entity.DyeingProcess"  /> *@
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private LotAllocateVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllPlanDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPlanDetails = await WtmBlazor.Api.CallItemsApi("/api/LotAllocate/GetPlanDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<LotAllocateVM>($"/api/LotAllocate/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/LotAllocate/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
