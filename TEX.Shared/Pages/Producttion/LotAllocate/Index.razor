@page "/Producttion/LotAllocate"
@using TEX.ViewModel.Producttion.LotAllocateVMs;
@inherits BasePage
@attribute [ActionDescription("配缸记录", "TEX.Controllers,LotAllocate")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <Select @bind-Value="@SearchModel.OrderId" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.DyeingFacId" Items="@AllDyeingFacs" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.PlanDetailId" Items="@AllPlanDetails" DisplayText="计划颜色" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <WTDateRange @bind-Value="@SearchModel.CreateDate"  />
            <BootstrapInput @bind-Value="@SearchModel.ProductName"  />
            <BootstrapInput @bind-Value="@SearchModel.Color"  />
            <BootstrapInput @bind-Value="@SearchModel.ColorCode"  />
            <BootstrapInput @bind-Value="@SearchModel.LotNo"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="LotAllocate_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.OrderNo_view" Filterable="true" />
        <TableColumn @bind-Field="@context.Product_View" Filterable="true" />
        <TableColumn @bind-Field="@context.DyeingProductName" Filterable="true" />
        <TableColumn @bind-Field="@context.Color" Filterable="true" />
        <TableColumn @bind-Field="@context.ColorCode" Filterable="true" />
        <TableColumn @bind-Field="@context.LotNo" Filterable="true" />
        <TableColumn @bind-Field="@context.Pcs"  />
        <TableColumn @bind-Field="@context.Meters"  />
        <TableColumn @bind-Field="@context.Weight"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/LotAllocate/Add"))
        {
            <TableToolbarButton TItem="LotAllocate_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/LotAllocate/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="LotAllocate_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/LotAllocate/Import"))
        {
            <TableToolbarButton TItem="LotAllocate_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/LotAllocate/ExportExcel"))
        {
            <TableToolbarButton TItem="LotAllocate_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/LotAllocate/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/LotAllocate/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/LotAllocate/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private LotAllocateSearcher SearchModel = new LotAllocateSearcher();
    private Table<LotAllocate_View> dataTable;

    private List<SelectedItem> AllPlanDetails = new List<SelectedItem>();
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllDyeingFacs = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        //AllDyeingFacs = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetDyeingFactorys", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllDyeingFacs = await WtmBlazor.Api.CallItemsApi("/api/DyeingPlan/GetDyeingFacInDyeingPlan", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllPlanDetails = await WtmBlazor.Api.CallItemsApi("/api/LotAllocate/GetPlanDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<LotAllocate_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<LotAllocate_View>("/api/LotAllocate/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<LotAllocate_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(LotAllocate_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(LotAllocate_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/LotAllocate/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(LotAllocate_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/LotAllocate/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<LotAllocate_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/LotAllocate/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/LotAllocate/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<LotAllocate_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
