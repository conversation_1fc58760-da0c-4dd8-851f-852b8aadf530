@* @page "/Producttion/LotAllocate/Create" *@
@using TEX.Shared.Pages.Components
@using TEX.Shared.Pages.Producttion.DyeingPlan
@using TEX.Shared.Pages.Producttion.PlanDetail
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.DyeingPlanVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs;
@using TEX.Model.Models;
@using TEX.ViewModel.Producttion.PlanDetailVMs
@inherits BasePage

@* <Stack IsRow="true" Justify="StackJustifyContent.Around" AlignItems="StackAlignItems.Stretch" >
    <StackItem> *@
<div class="d-flex">

    <div style="width:500px;border:1px;height:600px;margin:10px">
        <h5>请点击选择:</h5>
        <TreeView Items="Items" OnTreeItemClick="@OnTreeItemClick"></TreeView>
    </div>

    <Divider IsVertical="true" />
    @*     </StackItem>
    <StackItem > *@

    <div class="flex">
        <div style="height:300px">
            @* <DyeingPlanList @bind-Id="@OId"></DyeingPlanList> *@
            <Table @ref="dataTable" TItem="DyeingPlan_View" OnQueryAsync="OnSearch" IsPagination="false" IsStriped="true" IsBordered="true"
                   ShowRefresh="false" ShowToolbar="true" ShowDefaultButtons="false"
                   TableSize="TableSize.Compact" style="margin-top:10px;">
                <TableColumns>

                    <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
                    <TableColumn @bind-Field="@context.BillNo" />
                    <TableColumn @bind-Field="@context.OrderNo_view" />
                    <TableColumn @bind-Field="@context.ProductName_view" />
                    <TableColumn @bind-Field="@context.FinishingFactoryName_view" />
                    <TableColumn @bind-Field="@context.PlanBatch" Width="60" />
                    <TableColumn @bind-Field="@context.Light" Width="60" />
                    <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Right" FormatString="0" />
                    <TableColumn @bind-Field="@context.TotalQty" Align="Alignment.Right" FormatString="0" />
                    <TableColumn @bind-Field="@context.AuditStatus" Width="70">
                        <Template Context="v">
                            <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
                        </Template>
                    </TableColumn>
                    <TableColumn @bind-Field="@context.Remark" />
                </TableColumns>
                <DetailRowTemplate>
                    <PlanDetailTable Id="@context.ID"></PlanDetailTable>
                </DetailRowTemplate>
            </Table>
        </div>
        <br />
        <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

                <div class="@GroupFormClassString">
                    <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="true" />
                    <BootstrapInputGroup>
                        <Select @bind-Value="@Model.Entity.PlanDetailId" Items="@AllPlanDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                        <Button Icon="fa-solid fa-magnifying-glass" OnClick="@SelectPlan"></Button>
                    </BootstrapInputGroup>
                </div>
                @* <Select @bind-Value="@Model.Entity.PlanDetailId" Items="@AllPlanDetails" OnSelectedItemChanged="@OnPlanDetailSeclect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/> *@
                <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
                <BootstrapInput @bind-Value="@Model.Entity.ProductName" />
                <BootstrapInput @bind-Value="@Model.Entity.Color" />
                <BootstrapInput @bind-Value="@Model.Entity.ColorCode" />
                <BootstrapInput @bind-Value="@Model.Entity.LotNo" />
                <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs" />
                <BootstrapInputNumber @bind-Value="@Model.Entity.Meters" />
                <BootstrapInputNumber @bind-Value="@Model.Entity.Weight" />
                @* <BootstrapInput @bind-Value="@Model.Entity.DyeingProcess" /> *@
                <BootstrapInput @bind-Value="@Model.Entity.Remark" />
            </Row>
            <div class="modal-footer table-modal-footer">
                <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
                <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
            </div>
        </ValidateForm>
    </div>
</div>
@*     </StackItem>

</Stack> *@


@code {
    private List<TreeViewItem<DyeingPlanWithDetails_TreeView>> Items { get; set; } = new();
    private Table<DyeingPlan_View> dataTable;
    private DyeingPlanSearcher SearchModel = new DyeingPlanSearcher();

    [Inject]
    private DialogService DialogService { get; set; }//注入弹窗服务
    public PlanDetail_View planDetail { get; set; } = new();
    public DyeingPlan_View SelectDyeingPlan { get; set; } = new();

    private LotAllocateVM Model = new LotAllocateVM();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> AllPlanDetails = new List<SelectedItem>();

    private Guid? OId { get; set; } = Guid.Empty;
    private RowType FormRowType { get; set; }
    private string GroupFormClassString => CssBuilder.Default("row g-3").AddClass("form-inline", FormRowType == RowType.Inline).Build();

    private PlanDetail_View SelectedRows { get; set; }

    public async Task SelectPlan()
    {
        var result = await DialogService.ShowModal<DialogSelectDyeingPlan>(new ResultDialogOption()
            {
                Title = "请选择染色计划明细",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectDyeingPlan.SelectedDyeingPlan)] = SelectDyeingPlan,
                    [nameof(DialogSelectDyeingPlan.SelectedDyeingPlanChanged)] = EventCallback.Factory.Create<DyeingPlan_View>(this, v => SelectDyeingPlan = v)
                }
            });

        if (result == DialogResult.Yes)
        {

            //弹窗关闭后在这里执行更多其他操作
            Model.Entity.PlanDetailId = SelectDyeingPlan.DetailList.FirstOrDefault().ID;
            //await PlanDetailOnSelectPOrder(planDetail.ID.ToString());
        }
    }

    protected override async Task OnInitializedAsync()
    {
        LotAllocateSearcher searcher = new();
        QueryPageOptions opts = new();
        AllPlanDetails = await WtmBlazor.Api.CallItemsApi("/api/LotAllocate/GetPlanDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        //var rv = await WtmBlazor.Api.CallAPI<List<PurchaseOrderWithDetails_TreeView>>("/api/models/purchaseorder/getpordertreeitem");
        var rv = await WtmBlazor.Api.CallAPI<List<DyeingPlanWithDetails_TreeView>>("/api/DyeingPlan/getdyeingplantreeitem");

        Items = CascadingTree(rv.Data).ToList();

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/LotAllocate/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    //PlanDetail planDetail { get; set; }

    // private async Task OnPlanDetailSeclect(SelectedItem item)
    // {
    //     if (item.Value != "")
    //     {
    //         var rv = await WtmBlazor.Api.CallAPI<PlanDetail>($"/api/Models/Product/{item.Value}");
    //         var p = rv.Data;
    //         StateHasChanged();
    //     }
    // }


    public static IEnumerable<TreeViewItem<DyeingPlanWithDetails_TreeView>> CascadingTree(IEnumerable<DyeingPlanWithDetails_TreeView> items, TreeViewItem<PurchaseOrderWithDetails_TreeView> parent = null) => items.CascadingTree(null,
        (foo, parent) => foo.ParentID == parent?.Value.ID,
        foo => new TreeViewItem<DyeingPlanWithDetails_TreeView>(foo)
            {
                Text = foo.DisplayText,
                //Icon = foo.Icon,
                IsActive = foo.IsActive
            }).ToList();

    private Task OnTreeItemClick(TreeViewItem<DyeingPlanWithDetails_TreeView> item)
    {

        OId = item.Parent == null ? item.Value.ID : item.Value.ParentID;
        dataTable.QueryAsync();
        //StateHasChanged();

        return Task.CompletedTask;
    }

    private async Task<QueryData<DyeingPlan_View>> OnSearch(QueryPageOptions opts)
    {
        if (OId == Guid.Empty) { return null; }
        else
        {
            SearchModel.POrderId = OId;

            return await StartSearch<DyeingPlan_View>("/api/DyeingPlan/Search", SearchModel, opts);
        }
    }
}
