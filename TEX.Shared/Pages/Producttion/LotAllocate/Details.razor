@page "/Producttion/LotAllocate/Details/{id}"
@using TEX.ViewModel.Producttion.LotAllocateVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.PlanDetailId" Lookup="@AllPlanDetails"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.CreateDate"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ProductName"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Color"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ColorCode"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.LotNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Pcs"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Meters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Weight"   ShowLabel="true"/>
            @* <Display @bind-Value="@Model.Entity.DyeingProcess"   ShowLabel="true"/> *@
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private LotAllocateVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllPlanDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPlanDetails = await WtmBlazor.Api.CallItemsApi("/api/LotAllocate/GetPlanDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<LotAllocateVM>($"/api/LotAllocate/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
