@page "/Producttion/LotAllocate/Create"
@using System.Reflection
@using TEX.Shared.Pages.Components
@using TEX.Shared.Pages.Producttion.PlanDetail
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs;
@using TEX.Model.Models;
@using TEX.ViewModel.Producttion.PlanDetailVMs
@inherits BasePage


<Row RowType="RowType.Inline" ItemsPerRow="ItemsPerRow.Four">


    <div class="@GroupFormClassString">
        <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="true" />
        <BootstrapInputGroup>
            <Select @bind-Value="@detailSearcher.OrderDetailId" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <Button Icon="fa-solid fa-magnifying-glass" OnClick="@SelectPOrder"></Button>
        </BootstrapInputGroup>
    </div>


    <BootstrapInputGroup>
        <label class="form-label" required>染色计划</label>
        <Select @bind-Value="@Model.Entity.ID" Items="@AllPlanDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Button Icon="fa-solid fa-magnifying-glass" OnClick="@SelectPlanDetail"></Button>
    </BootstrapInputGroup>

    <Display @bind-Value="@Model.Entity.Color" ShowLabel="true" />
    <Display @bind-Value="@Model.Entity.ColorCode" ShowLabel="true" />
    <Display @bind-Value="@Model.Entity.Pcs" ShowLabel="true" />
    <Display @bind-Value="@Model.Entity.Qty" ShowLabel="true" />
</Row>
<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">

    <div style="margin:20px 0;">
        <Table TItem="LotAllocate" @bind-Items="@DetailList" ShowRefresh="false" 
               IsPagination="false" TableSize="TableSize.Compact" 
               ShowSkeleton="true" ShowFooter="true"
               EditMode="EditMode.InCell"
               ShowToolbar="true" ShowExtendButtons="true" 
               IsBordered="true" IsFixedHeader="true" Height="300">
            <TableColumns>
                <TableColumn @bind-Field="@context.CreateDate" FormatString="yy-MM-dd" />
                <TableColumn @bind-Field="@context.Color" />
                <TableColumn @bind-Field="@context.ColorCode" />
                <TableColumn @bind-Field="@context.LotNo" Width="80" />
                <TableColumn @bind-Field="@context.Pcs" Width="60" Align="Alignment.Right" />
                <TableColumn @bind-Field="@context.Meters" Width="90" Align="Alignment.Right" />
                <TableColumn @bind-Field="@context.Weight" Width="90" Align="Alignment.Right" />
                <TableColumn @bind-Field="@context.Remark" Width="90" Align="Alignment.Right" />
            </TableColumns>
            <TableFooter>
                <TableFooterCell Colspan="4" Text="合计:" />
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(LotAllocate.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotAllocate.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotAllocate.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotAllocate.Weight)" />
                <TableFooterCell />
            </TableFooter>


            @* <FooterTemplate>
                <tr style="text-align: center;font-weight:bold;">
                    <td colspan="4">
                        <div style="line-height: 3;">合计：</div>
                    </td>
                    <td>
                        <div>
                            共 @context.Count() 缸
                        </div>
                    </td>

                    <td>
                        <div>
                            @GetSum(context, "Pcs")
                        </div>
                    </td>
                    <td>
                        <div>
                            @GetSum(context, "Meters")
                        </div>
                    </td>
                    <td>
                        <div>
                            @GetSum(context, "Weight")
                        </div>
                    </td>
                    <td>
                    </td>
                </tr>
            </FooterTemplate> *@
        </Table>
    </div>
    @* <Row RowType="RowType.Inline" ItemsPerRow="ItemsPerRow.Six">
    <span style="font-weight:bold;font-size:16px;align:center;">合计:</span>

    <Display DisplayText="计数" Value="@DetailList.Count()" />
    <Display DisplayText="总匹数" Value="@DetailList.Sum(x=>x.Pcs)" />
    <Display DisplayText="总米数" Value="@DetailList.Sum(x=>x.Meters)" />
    <Display DisplayText="总重量" Value="@DetailList.Sum(x=>x.Weight)" />
    </Row> *@
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>



@code {
    // private Table<PlanDetail_View> dataTable;
    // private PlanDetailSearcher SearchModel { get; set; }
    [Inject]
    private DialogService DialogService { get; set; }//注入弹窗服务
                                                     // public PlanDetail_View planDetail { get; set; } = new();

    //使用反射对实体类的某一个字段求和
    private static decimal GetSum(IEnumerable<LotAllocate> items, string fieldName)
    {
        if (!items.Any()) return 0;
        PropertyInfo property = typeof(LotAllocate).GetProperty(fieldName);
        if (property == null) throw new ArgumentException("Field not found", fieldName);
        return items.Sum(i => Convert.ToDecimal(property.GetValue(i)));
    }
    private PlanDetailVM Model = new();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> AllPlanDetails = new List<SelectedItem>();

    private Guid? OId { get; set; } = Guid.Empty;
    private RowType FormRowType { get; set; }
    private string GroupFormClassString => CssBuilder.Default("row g-3").AddClass("form-inline", FormRowType == RowType.Inline).Build();

    private List<PlanDetail_View> SelectedRows { get; set; } = new();

    private PlanDetailSearcher detailSearcher { get; set; } = new();
    private PurchaseOrder purchaseOrder { get; set; }
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<LotAllocate> detailList = new();
    public IEnumerable<LotAllocate> DetailList
    {
        get { return detailList.AsEnumerable(); }
        set
        {
            detailList = value.ToList();
        }
    }
    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        Model.Entity.LotAllocateList = detailList;
        await PostsForm(vform, "/api/PlanDetail/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }


    // private async Task<QueryData<DyeingPlan_View>> OnSearch(QueryPageOptions opts)
    // {
    //     if (OId == Guid.Empty) { return null; }
    //     else
    //     {
    //         SearchModel.POrderId = OId;

    //         return await StartSearch<DyeingPlan_View>("/api/DyeingPlan/Search", SearchModel, opts);
    //     }
    // }

    //染色计划明细选择弹窗
    public PlanDetail_View plandetail { get; set; } = new();
    public async Task SelectPlanDetail()
    {
        var result = await DialogService.ShowModal<DialogSelectPlanDetail>(new ResultDialogOption()
            {
                Title = "请选择计划",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectPlanDetail.OrderId)] = detailSearcher.OrderDetailId,
                    [nameof(DialogSelectPlanDetail.SelectedPlanDetail)] = plandetail,
                    [nameof(DialogSelectPlanDetail.SelectedPlanDetailChanged)] = EventCallback.Factory.Create<PlanDetail_View>(this, v => plandetail = v)
                }
            });

        if (result == DialogResult.Yes)
        {
            Model.Entity.ID = plandetail.ID;
            Model.Entity = plandetail;

            // var od = new LotAllocate();
            // od.Color = Model.Entity.Color;
            // od.ColorCode = Model.Entity.ColorCode;
            // detailList.Insert(0, od);
            var rv = await WtmBlazor.Api.CallAPI<PlanDetailVM>($"/api/PlanDetail/{plandetail.ID}");
            Model = rv.Data;
            DetailList = Model.Entity.LotAllocateList;
            StateHasChanged();
        }
    }
    //订单选择弹窗
    public PurchaseOrder_View porder { get; set; } = new();
    public async Task SelectPOrder()
    {
        var result = await DialogService.ShowModal<DialogSelectPOrders>(new ResultDialogOption()
            {
                Title = "请选择订单",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectPOrders.SelectedPurchaseOrder)] = porder,
                    [nameof(DialogSelectPOrders.SelectedPurchaseOrderChanged)] = EventCallback.Factory.Create<PurchaseOrder_View>(this, v => porder = v)
                }
            });

        if (result == DialogResult.Yes)
        {
            detailSearcher.OrderDetailId = porder.ID;
        }
    }

    // private async Task<QueryData<PlanDetail_View>> OnSearch(QueryPageOptions opts)
    // {
    //     return await StartSearch<PlanDetail_View>("/api/PlanDetail/Search", SearchModel, opts);
    // }

    // private void DoSearch()
    // {
    //     dataTable.QueryAsync();
    // }

    //子表Excel模式,更新方法
    private async Task<LotAllocate> OnAddAsync()
    {

        //if (Model.Entity.ID != Guid.Empty)
        //if (detailSearcher.DyeingPlan_OrderId != null)
        if (Model.Entity.Color != null)
        {
            // Excel 模式下新建使用订单明细弹窗选择
            //await SelectPlanDetail();

            //返回null,点取消时才不会默认添加一行
            //return null;

            var od = new LotAllocate();
            od.ID = Model.Entity.ID;
            od.Color = Model.Entity.Color;
            od.ColorCode = Model.Entity.ColorCode;

            detailList.Insert(detailList.Count, od);
            return od;
        }
        else
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择染色计划!"]);
            return null;
        }

    }

    private Task<bool> OnSaveAsync(LotAllocate item, ItemChangedType changedType)
    {
        //Model.Entity.TotalQty = detailList.Sum(x => x.Qty);
        //Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
        // 对象已经更新
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<LotAllocate> items)
    {
        detailList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
}
