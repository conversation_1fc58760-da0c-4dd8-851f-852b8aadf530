@page "/Producttion/PlanDetail/Edit/{id}"
@using TEX.ViewModel.Producttion.PlanDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.DyeingPlanId" Items="@AllDyeingPlans" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Color"  />
            <BootstrapInput @bind-Value="@Model.Entity.ColorCode"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            <DateTimePicker @bind-Value="@Model.Entity.DeliveryDate"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.FinishingPrice"  />
            <BootstrapInput @bind-Value="@Model.Entity.GreigeBatch"  />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingProcess"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private PlanDetailVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllDyeingPlans = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDyeingPlans = await WtmBlazor.Api.CallItemsApi("/api/PlanDetail/GetDyeingPlans", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<PlanDetailVM>($"/api/PlanDetail/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/PlanDetail/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
