@page "/Producttion/PlanDetail/EditWithLotAllocate/{id}"
@using TEX.ViewModel.Producttion.PlanDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        @* <Select @bind-Value="@Model.Entity.DyeingPlanId" Items="@AllDyeingPlans" IsDisabled="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Select @bind-Value="@Model.Entity.OrderDetail.Color" Items="@AllOrderDetails" IsDisabled="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" /> *@
        <Display @bind-Value="@PD.POrder" />
        <Display @bind-Value="@PD.Product" />
        <Display @bind-Value="@Model.Entity.Color" />
        <Display @bind-Value="@Model.Entity.ColorCode" />
        <Display @bind-Value="@Model.Entity.Pcs"  />
        <Display @bind-Value="@Model.Entity.Meters" />
        <Display @bind-Value="@Model.Entity.Weight" />
        <Display @bind-Value="@Model.Entity.DeliveryDate" />
        <Display @bind-Value="@Model.Entity.GreigeBatch"  />
        <Display @bind-Value="@Model.Entity.DyeingProcess" />
        <Display @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <Table @bind-Items="LotAllocates" TItem="LotAllocate"
           EditMode="EditMode.InCell" IsPagination="false"
           IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="false"
           ShowExtendButtons="true" ShowEditButton="false"
           ShowToastAfterSaveOrDeleteModel="false" AlignCenterText="true"
           ShowDeleteButton="false" style="margin-top:10px;">
        <TableColumns>
            <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" Align="Alignment.Center" />
            @* <TableColumn @bind-Field="@context.Color" />
            <TableColumn @bind-Field="@context.ColorCode"/> *@
            <TableColumn @bind-Field="@context.LotNo" Align="Alignment.Center" />
            <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center" />
            <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center" />
            <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <br />
    <div class="modal-footer table-modal-footer" >
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Success" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Save"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private PlanDetailVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public PlanDetail_View PD { get; set; }

    private List<SelectedItem> AllDyeingPlans = new();

    private List<SelectedItem> AllOrderDetails = new();

    private IEnumerable<LotAllocate> LotAllocates;
    private List<LotAllocate> LotAllocateList = new();


    protected override async Task OnInitializedAsync()
    {
        //TODO:直接获取而不是查询全部Plans给选择
        //AllDyeingPlans = await WtmBlazor.Api.CallItemsApi("/api/PlanDetail/GetDyeingPlans", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        //AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<PlanDetailVM>($"/api/PlanDetail/{PD.ID}");
        Model = rv.Data;
        LotAllocates = Model.Entity.LotAllocateList;
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        //给每条记录重新赋值颜色色号
        // foreach (var item in LotAllocateList)
        // {
        //     item.Color = Model.Entity.Color;
        //     item.ColorCode = Model.Entity.ColorCode;
        // }

        Model.Entity.LotAllocateList = LotAllocates.ToList();
        await PostsForm(vform, $"/api/PlanDetail/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
