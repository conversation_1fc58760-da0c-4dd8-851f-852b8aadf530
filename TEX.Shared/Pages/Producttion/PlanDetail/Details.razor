@page "/Producttion/PlanDetail/Details/{id}"
@using TEX.ViewModel.Producttion.PlanDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.DyeingPlanId" Lookup="@AllDyeingPlans"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OrderDetailId" Lookup="@AllOrderDetails"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Color"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ColorCode"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Pcs"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Qty"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.QtyUnit"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.DeliveryDate"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.FinishingPrice"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.GreigeBatch"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.DyeingProcess"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private PlanDetailVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllDyeingPlans = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDyeingPlans = await WtmBlazor.Api.CallItemsApi("/api/PlanDetail/GetDyeingPlans", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<PlanDetailVM>($"/api/PlanDetail/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
