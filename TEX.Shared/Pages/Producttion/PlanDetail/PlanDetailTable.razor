@using TEX.ViewModel.Producttion.PlanDetailVMs;
@inherits BasePage

@if (PlanDetail_ViewList is not null)
{
    <Table Items="@PlanDetail_ViewList" TItem="PlanDetail_View" TableSize="TableSize.Compact" HeaderStyle="TableHeaderStyle.Light"
           IsPagination="false" IsStriped="false" IsBordered="true" IsMultipleSelect="false"
           ShowExtendButtons="true" ShowDefaultButtons="false"
           ShowExtendEditButton="false" ShowExtendDeleteButton="false"
           ShowEmpty="true" IsHideFooterWhenNoData="true"
           ShowToolbar="false" ShowFooter="true" style="margin:6px;">
        <TableColumns>

            <TableColumn @bind-Field="@context.Color" />
            <TableColumn @bind-Field="@context.ColorCode" />
            <TableColumn @bind-Field="@context.Pcs" />
            <TableColumn @bind-Field="@context.Meters" Visible="true" />
            <TableColumn @bind-Field="@context.Weight" Visible="true" />
            <TableColumn @bind-Field="@context.AllocatedMeters" Visible="true" />
            <TableColumn @bind-Field="@context.AllocatedMetersPercent" FormatString="P2" Visible="true" />
            <TableColumn @bind-Field="@context.AllocatedWeight" Visible="true" />
            <TableColumn @bind-Field="@context.AllocatedWeightPercent" FormatString="P2" Visible="true" />
            @* <TableTemplateColumn Text="米数配缸比" Visible="@IsAllocatedMetersVisible">
                <Template Context="v">
                    @((v.Row.Meters == 0 ? 0 : v.Row.AllocatedMeters / v.Row.Meters * 100).ToString("0.00") + "%")
                </Template>
            </TableTemplateColumn>

            <TableTemplateColumn Text="重量配缸比" Visible="@IsAllocatedWeightVisible">
                <Template Context="v">
                    @((v.Row.Weight == 0 ? 0 : v.Row.AllocatedWeight / v.Row.Weight * 100).ToString("0.00") + "%")
                </Template>
            </TableTemplateColumn> *@

            @* <TableColumn @bind-Field="@context.QtyUnit" /> *@
            @* <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yyyy-MM-dd" /> *@
            @* <TableColumn @bind-Field="@context.GreigeBatch" Width="80" /> *@
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>

        <RowButtonTemplate>
            @* 禁止修改:此处修改不合理,Plan总数量也不会更新<TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="修改" OnClick="() => OnEditClick(context)" /> *@
            <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-pencil-square" Text="配缸" OnClick="() => OnEditClickWithLotAllocate(context)" />

            @* 可以跳转,但是一直谈错误死循环 *@
            @* <a target="_self" href="/Producttion/DyeingPlan/Edit/{@context.ID}" :underline="false" >
        配缸
        </a> *@
        </RowButtonTemplate>
        <TableFooter>
            <TableFooterCell Text="合计:" />
            <TableFooterCell />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail_View.Pcs)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail_View.Meters)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail_View.Weight)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail_View.AllocatedMeters)" />
            <TableFooterCell  />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail_View.AllocatedWeight)"  />
            <TableFooterCell />
            <TableFooterCell colspan="3" />
        </TableFooter>
    </Table>
}
@code {

    [Parameter]
    public Guid Id { get; set; }

    [Inject]
    private MessageService MessageService { get; set; }

    [Parameter]
    public PlanDetail_View SelectedPlanDetail { get; set; }

    [Parameter]
    public EventCallback<PlanDetail_View> SelectedPlanDetailChanged { get; set; }

    private bool IsWeightVisible { get; set; } = false;
    private bool IsMetersVisible { get; set; } = false;
    private bool IsAllocatedWeightVisible { get; set; } = false;
    private bool IsAllocatedMetersVisible { get; set; } = false;

    private List<PlanDetail_View> Rows { get; set; } = new();
    private List<PlanDetail_View> SelectedRows { get; set; } = new();

    private PlanDetailSearcher SearchModel = new PlanDetailSearcher();
    private IEnumerable<PlanDetail_View> PlanDetail_ViewList;
    private List<PlanDetail_View> ViewList;


    private async Task DoubleClick(PlanDetail_View context)
    {
        SelectedPlanDetail = context;
        await SelectedPlanDetailChanged.InvokeAsync(SelectedPlanDetail);
    }

    protected override async Task OnInitializedAsync()
    {
        QueryPageOptions opts = new QueryPageOptions();
        SearchModel.DyeingPlanId = Id;
        var rv = await StartSearch<PlanDetail_View>("/api/PlanDetail/Search", SearchModel, opts);
        ViewList = rv.Items.ToList();


        //IsWeightVisible = ViewList.Sum(x => x.Weight) > 0 ? true : false;
        //IsMetersVisible = ViewList.Sum(x => x.Meters) > 0 ? true : false;
        //IsAllocatedWeightVisible = ViewList.Sum(x => x.AllocatedWeight) > 0 ? true : false;
        //IsAllocatedMetersVisible = ViewList.Sum(x => x.AllocatedMeters) > 0 ? true : false;
        PlanDetail_ViewList = rv.Items;
        await base.OnInitializedAsync();
    }


    private async Task OnEditClick(PlanDetail_View item)
    {
        await OpenDialog<Edit>("修改计划明细", x => x.id == item.ID.ToString());
    }

    private async Task OnEditClickWithLotAllocate(PlanDetail_View item)
    {
        await OpenDialog<EditWithLotAllocate>("新增配缸", x => x.PD == item);
    }
}
