@page "/Producttion/PlanDetail/Create"
@using TEX.ViewModel.Producttion.PlanDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.DyeingPlanId" Items="@AllDyeingPlans" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Color"  />
            <BootstrapInput @bind-Value="@Model.Entity.ColorCode"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Qty"  />
            <Select @bind-Value="@Model.Entity.QtyUnit"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <DateTimePicker @bind-Value="@Model.Entity.DeliveryDate"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.FinishingPrice"  />
            <BootstrapInput @bind-Value="@Model.Entity.GreigeBatch"  />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingProcess"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private PlanDetailVM Model = new PlanDetailVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllDyeingPlans = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDyeingPlans = await WtmBlazor.Api.CallItemsApi("/api/PlanDetail/GetDyeingPlans", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/PlanDetail/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
