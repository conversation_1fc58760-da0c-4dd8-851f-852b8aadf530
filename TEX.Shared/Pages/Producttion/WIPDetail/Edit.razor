@page "/Producttion/WIPDetail/Edit/{id}"
@using TEX.ViewModel.Producttion.WIPDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.Color"  />
            <BootstrapInput @bind-Value="@Model.Entity.LotNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private WIPDetailVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }


    protected override async Task OnInitializedAsync()
    {

        var rv = await WtmBlazor.Api.CallAPI<WIPDetailVM>($"/api/WIPDetail/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/WIPDetail/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
