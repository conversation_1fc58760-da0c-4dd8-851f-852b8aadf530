@page "/Producttion/WIPDetail/Details/{id}"
@using TEX.ViewModel.Producttion.WIPDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.Color"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.LotNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Pcs"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Meters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Weight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private WIPDetailVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }


    protected override async Task OnInitializedAsync()
    {

        var rv = await WtmBlazor.Api.CallAPI<WIPDetailVM>($"/api/WIPDetail/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
