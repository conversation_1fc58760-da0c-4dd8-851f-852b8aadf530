@page "/Producttion/LossRate"
@using TEX.ViewModel.Producttion.WIPDetailVMs;
@inherits BasePage
@attribute [ActionDescription("损耗率", "TEX.Controllers,WIPDetail")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.Color"  />
            <BootstrapInput @bind-Value="@SearchModel.LotNo"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="LossRate_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.Procedure"  />
        <TableColumn @bind-Field="@context.OrderNo" />
        <TableColumn @bind-Field="@context.Color" />
        <TableColumn @bind-Field="@context.LotNo" />
        <TableColumn @bind-Field="@context.Shipper"  />
        <TableColumn @bind-Field="@context.Receiver"  />
        <TableColumn @bind-Field="@context.ShipperMeters"  />
        <TableColumn @bind-Field="@context.ReceiverMeters"  />
        <TableColumn @bind-Field="@context.LossRate"  />
        
    </TableColumns>
    <TableToolbarTemplate>

    </TableToolbarTemplate>

</Table>

@code{

    private WIPDetailSearcher SearchModel = new WIPDetailSearcher();
    private Table<LossRate_View> dataTable;


    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<LossRate_View>> OnSearch(QueryPageOptions opts)
    {
        //TODO 尝试使用直接调用API方式查询损耗率的自定义表,结果失败,待改进
        //var rq = await WtmBlazor.Api.CallAPI<List<WIPDetail_View2>>($"/api/WIPDetail/SearchLossRate");
        // QueryData<WIPDetail_View2> q = new();
        // q.Items = rq.Data;

        var q = await StartSearch<LossRate_View>("/api/WIPDetail/SearchLossRate", SearchModel, opts);

        
        return q;
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

   

}
