@page "/Producttion/WIPDetail"
@using TEX.ViewModel.Producttion.WIPDetailVMs;
@inherits BasePage
@attribute [ActionDescription("半成品明细", "TEX.Controllers,WIPDetail")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.Color"  />
            <BootstrapInput @bind-Value="@SearchModel.LotNo"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="WIPDetail_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yy-MM-dd" />
        <TableColumn @bind-Field="@context.WIPNo"  />
        <TableColumn @bind-Field="@context.OrderNo"  />
        <TableColumn @bind-Field="@context.ProductName"  />
        <TableColumn @bind-Field="@context.Color"  />
        <TableColumn @bind-Field="@context.LotNo"  />
        <TableColumn @bind-Field="@context.Pcs"  />
        <TableColumn @bind-Field="@context.Meters"  />
        <TableColumn @bind-Field="@context.Weight"  />
        <TableColumn @bind-Field="@context.Shipper"  />
        <TableColumn @bind-Field="@context.Receiver"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/WIPDetail/Add"))
        {
            <TableToolbarButton TItem="WIPDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/WIPDetail/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="WIPDetail_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/WIPDetail/Import"))
        {
            <TableToolbarButton TItem="WIPDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/WIPDetail/ExportExcel"))
        {
            <TableToolbarButton TItem="WIPDetail_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/WIPDetail/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/WIPDetail/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/WIPDetail/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private WIPDetailSearcher SearchModel = new WIPDetailSearcher();
    private Table<WIPDetail_View> dataTable;


    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<WIPDetail_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<WIPDetail_View>("/api/WIPDetail/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<WIPDetail_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(WIPDetail_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(WIPDetail_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/WIPDetail/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(WIPDetail_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/WIPDetail/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<WIPDetail_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/WIPDetail/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/WIPDetail/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<WIPDetail_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
