@using TEX.Model.Models
@using TEX.Shared.Pages.Producttion.PlanDetail
@using TEX.ViewModel.Producttion.DyeingPlanVMs;
@inherits BasePage

<div>
    <Table @ref="dataTable" TItem="DyeingPlan_View" OnQueryAsync="OnSearch" IsPagination="false" IsStriped="true" IsBordered="true"
           ShowRefresh="false" ShowToolbar="true" ShowDefaultButtons="false"
           TableSize="TableSize.Compact" style="margin-top:10px;">
        <TableColumns>

            <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
            <TableColumn @bind-Field="@context.BillNo" />
            <TableColumn @bind-Field="@context.OrderNo_view" />
            <TableColumn @bind-Field="@context.ProductName_view" />
            <TableColumn @bind-Field="@context.FinishingFactoryName_view" Text=@WtmBlazor.Localizer["_FinishingFactory"] />
            <TableColumn @bind-Field="@context.PlanBatch" Width="60" />
            <TableColumn @bind-Field="@context.Light" Width="60" />
            <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Right" FormatString="0" />
            <TableColumn @bind-Field="@context.TotalQty" Align="Alignment.Right" FormatString="0" />
            <TableColumn @bind-Field="@context.AuditStatus" Width="70">
                <Template Context="v">
                    <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
        <DetailRowTemplate>
            <PlanDetailTable Id="@context.ID"></PlanDetailTable>
        </DetailRowTemplate>
    </Table>
</div>
@code {
    [Parameter]
    public Guid? Id { get; set; }

    [Parameter]
    public EventCallback<Guid?> IdChanged{ get; set; }

    private DyeingPlanSearcher SearchModel = new DyeingPlanSearcher();
    private Table<DyeingPlan_View> dataTable;

    private int k = 0;
    protected override async Task OnInitializedAsync()
    {
        k = k + 1;
        await base.OnInitializedAsync();
    }
    protected override void OnParametersSet()
    {
        if (k > 1)
        {
            dataTable.QueryAsync();
            StateHasChanged();
        }
        base.OnParametersSet();
    }
    private async Task<QueryData<DyeingPlan_View>> OnSearch(QueryPageOptions opts)
    {
        if (Id == Guid.Empty) { return null; }
        else
        {
            SearchModel.POrderId = Id;
            return await StartSearch<DyeingPlan_View>("/api/DyeingPlan/Search", SearchModel, opts);
        }
    }

}
