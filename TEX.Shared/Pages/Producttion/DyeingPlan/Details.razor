@page "/Producttion/DyeingPlan/Details/{id}"
@using TEX.ViewModel.Models.ProductVMs
@using TEX.ViewModel.Producttion.DyeingPlanVMs;
@using TEX.Model.Models;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row>
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

            <DateTimePicker @bind-Value="@Model.Entity.CreateDate" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.BillNo" />
            <Display @bind-Value="@Model.Entity.Version" />
            <Display @bind-Value="@Model.Entity.PlanBatch" />

            <Display @bind-Value="@Model.Entity.POrder.OrderNo" DisplayText="@WtmBlazor.Localizer["_POrder"]" />

            <Display Value="@product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" />
            <Display Value="@spec" DisplayText="@WtmBlazor.Localizer["_Spec"]" />

            <Display @bind-Value="@Model.Entity.FinishingProcess" />

        </Row>
        <Row ItemsPerRow="ItemsPerRow.Six" RowType="RowType.Inline">

            <Display @bind-Value="@Model.Entity.Light" />
            <Display @bind-Value="@Model.Entity.Light2" />
            <Display @bind-Value="@Model.Entity.Width" />
            <Display @bind-Value="@Model.Entity.GSM" />
            <Display @bind-Value="@Model.Entity.GreigeBatch" />

            <Display @bind-Value="@fac.CompanyName" DisplayText="@WtmBlazor.Localizer["_FinishingFactory"]" />
            <Display @bind-Value="@vender.CompanyName" DisplayText="@WtmBlazor.Localizer["_GreigeVender"]" />


            <Display @bind-Value="@Model.Entity.DyeingDemand" />
            <Display @bind-Value="@Model.Entity.PackDemand" />
            <Display @bind-Value="@Model.Entity.AdditionalDemend" />

            <Display @bind-Value="@Model.Entity.Remark" />

        </Row>
    </Row>

    <div style="margin:20px 0;">
        <Table TItem="PlanDetail" @bind-Items="@DetaiList"
               IsPagination="false" TableSize="TableSize.Compact" 
               IsStriped="true" IsBordered="true"  
               ShowToolbar="false" ShowExtendButtons="false" ShowSkeleton="true" 
               >
            <TableColumns>
                <TableColumn @bind-Field="@context.Color" />
                <TableColumn @bind-Field="@context.ColorCode" />
                <TableColumn @bind-Field="@context.Pcs" />
                <TableColumn @bind-Field="@context.Meters" Width="120" />
                <TableColumn @bind-Field="@context.Weight" Width="120" />
                <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yyyy-mm-dd" />
                <TableColumn @bind-Field="@context.GreigeBatch" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
        </Table>
    </div>
    <br />
    <br />
    <div class="modal-footer table-modal-footer">
        <div class="col-12 col-sm-10">
            <WSPrint printData="@printdata" PrintFileName="DyeingPlanHS.grf" />
        </div>
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private DyeingPlanVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private string printdata = "";

    private PurchaseOrder porder { get; set; } = new();
    private Product product { get; set; } = new();
    private Company fac { get; set; } = new();
    private Company vender { get; set; } = new();


    protected override async Task OnInitializedAsync()
    {
        var rv = await WtmBlazor.Api.CallAPI<DyeingPlanVM>($"/api/DyeingPlan/{id}");
        Model = rv.Data;
        DetaiList = Model.Entity.DetailList;

        // var o = await WtmBlazor.Api.CallAPI<ViewModel.Models.PurchaseOrderVMs.PurchaseOrderVM>($"/api/Models/PurchaseOrder/{Model.Entity.POrderId}");
        // var order = o.Data;
        // porder = order.Entity;
        // product = order.Entity.Product;

        if (Model.Entity.FinishingFactoryId != Guid.Empty)
        {
            var c = await WtmBlazor.Api.CallAPI<ViewModel.Models.CompanyVMs.CompanyVM>($"/api/Models/Company/{Model.Entity.FinishingFactoryId}");
            fac = c.Data.Entity;
        }

        //GreigeVenderId为可空类型
        if (Model.Entity.GreigeVenderId is not null)
        {
            var g = await WtmBlazor.Api.CallAPI<ViewModel.Models.CompanyVMs.CompanyVM>($"/api/Models/Company/{Model.Entity.GreigeVenderId}");
            vender = g.Data.Entity;
        }

        var pid = Model.Entity.POrder.ProductId;
        var r = await WtmBlazor.Api.CallAPI<ProductVM>($"/api/Models/Product/{pid}");
        var p = r.Data;
        product = p.Entity;

        var dyeingPlan = await WtmBlazor.Api.CallAPI<string>($"/api/DyeingPlan/getDyeingPlandto/{Model.Entity.ID}");
        printdata = dyeingPlan.Data;
    }


    private string spec
    {
        get
        {
            string sp = "";
            if (product.GSM != null && product.GSM != 0)
            {
                sp += +product.GSM + "Gsm";
            }
            if (product.Width != null && product.Width != 0)
            {
                sp += " - " + product.Width + "CM";
            }
            return sp;
        }
    }
    public void OnClose()
    {
        CloseDialog();
    }
    private IEnumerable<PlanDetail> detailList = new List<PlanDetail>();
    public IEnumerable<PlanDetail> DetaiList
    {
        get { return detailList; }
        set
        {
            detailList = value;
            Model.Entity.TotalQty = detailList.Sum(x => x.Qty);
            Model.Entity.Pcs = detailList.Sum(x => x.Pcs);

        }
    }
}
