@page "/Producttion/DyeingPlan/Create"
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Models.OrderDetailVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.DyeingPlanVMs;
@using TEX.Model.Models;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row>
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@Model.Entity.BillNo" />
            <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Version" Min="1" />
            <BootstrapInput @bind-Value="@Model.Entity.PlanBatch" />

            @* <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="true" /> 这个会有一个按钮样式,不好用
            <label class="form-label" required>订单号</label>等效上一行,加上required有红色*号 *@

            <SelectOrderTable @bind-Value="@porder" OnSelectedChanged="OnSelectedChanged" />
            @* <Select @bind-Value="@Model.Entity.POrderId" Items="@AllPurchaseOrders" OnSelectedItemChanged="@OnOrderSelect" ShowSearch="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <Button Icon="fa-solid fa-magnifying-glass" OnClick="@SelectPOrder"></Button>*@


            <BootstrapInput Value="@product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" IsDisabled="true" />
            <BootstrapInput Value="@porder.DyeingProductName" DisplayText="@WtmBlazor.Localizer["_DyeingProductName"]" />
            <BootstrapInput Value="@spec" DisplayText="@WtmBlazor.Localizer["_Spec"]" IsDisabled="true" />
        </Row>
        <Row ItemsPerRow="ItemsPerRow.Six" RowType="RowType.Inline">
            <Select @bind-Value="@Model.Entity.FinishingFactoryId" Items="@AllFinishingFactorys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <Select @bind-Value="@Model.Entity.Light" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <Select @bind-Value="@Model.Entity.Light2" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <BootstrapInput @bind-Value="@Model.Entity.Width" />
            <BootstrapInput @bind-Value="@Model.Entity.GSM" />
            <BootstrapInput @bind-Value="@Model.Entity.GreigeBatch" />
            <Select @bind-Value="@Model.Entity.GreigeVenderId" Items="@AllGreigeVenderCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <BootstrapInput @bind-Value="@Model.Entity.FinishingProcess" />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingDemand" />
            <BootstrapInput @bind-Value="@Model.Entity.PackDemand" />
            <BootstrapInput @bind-Value="@Model.Entity.AdditionalDemend" />
            <Row ColSpan="1" RowType="RowType.Inline">
                <BootstrapInput @bind-Value="@Model.Entity.Remark" />
            </Row>
        </Row>
    </Row>

    <div style="height:300px;margin:10px;">
        <Table TItem="PlanDetail" @bind-Items="@DetaiList" ShowRefresh="false"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true"
               ShowExtendButtons="true" ShowToastAfterSaveOrDeleteModel="false"
               ShowAddButton="true" EditMode="EditMode.InCell" IsBordered="true"
               ShowEditButton="false" ShowDeleteButton="false" ShowToolbar="true">
            <TableColumns>
                @* <TableColumn @bind-Field="@context.OrderDetailId" Lookup="@OrderDetails" Width="120" /> *@
                <TableColumn @bind-Field="@context.OrderDetailId" Lookup="@OrderDetails" Width="120">
                    @* <Template Context="v">
                    <Select @bind-Value="v" Items="@OrderDetails" />
                    </Template> *@
                    <EditTemplate Context="v">
                        <Select @bind-Value="v.OrderDetailId" Items="@OrderDetails" OnSelectedItemChanged="x=>OnOrderDetailSelect(x,v)" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Color" />
                <TableColumn @bind-Field="@context.ColorCode" />
                <TableColumn @bind-Field="@context.Pcs" FormatString="0" Align="Alignment.Right" Width="50" />
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Right" Width="100" />
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Right" Width="100" />
                <TableColumn @bind-Field="@context.QtyUnit" Width="60" />
                <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yy-mm-dd" Width="100" />
                <TableColumn @bind-Field="@context.GreigeBatch" Width="60" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>

            <TableToolbarBeforeTemplate>
                <div class="table-selection-info" style="display:inline-block;margin-right:10px;">
                    <div class="me-2" style="display:inline-block;margin-right:10px;">加损耗：</div>
                    <InputNumber @bind-Value="@LossRate" Step="1" Min="0" Max="100" style="border:solid 1px #dee2e6;text-align:center;display:inline-block;" />
                    <div class="me-2" style="display:inline-block;margin-right:10px;"> %</div>
                </div>
            </TableToolbarBeforeTemplate>
        </Table>
        @*         <Row ItemsPerRow="ItemsPerRow.Six" RowType="RowType.Inline" style="margin:6px;">
        <h6 style="align-content:center;">请设置损耗率:</h6>
        <BootstrapInputNumber @bind-Value="@LossRate" DisplayText="损耗率" Step="0.01" Min="0" Max="1" />
        </Row> *@
    </div>
    <div class="modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    private DyeingPlanVM Model = new DyeingPlanVM();
    private ValidateForm vform { get; set; }
    private int LossRate { get; set; } = 5;
    public PurchaseOrder_View porder { get; set; } = new();

    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    private List<SelectedItem> OrderDetails = new(); //用于下拉框选择订单颜色
    private IEnumerable<OrderDetail> OrderDetailList = new List<OrderDetail>();

    private List<SelectedItem> AllFinishingFactorys = new List<SelectedItem>();
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllGreigeVenderCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllGreigeVenderCompanys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetGreigeVenderCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        //AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllFinishingFactorys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        if (Model.Entity.POrderId == Guid.Empty)
        {
            vform.SetError<DyeingPlan>(f => f.POrderId, "订单为必填项");
            return;
        }
        if (Model.Entity.FinishingFactoryId == Guid.Empty)
        {
            vform.SetError<DyeingPlan>(f => f.FinishingFactoryId, "染整厂为必填项");
            return;
        }
        Model.Entity.DetailList = DetaiList.ToList();
        Model.Entity.Pcs = DetaiList.Sum(x => x.Pcs);
        Model.Entity.TotalQty = DetaiList.Sum(x => x.Qty);
        Model.Entity.TotalMeters = DetaiList.Sum(x => x.Meters);
        Model.Entity.TotalWeight = DetaiList.Sum(x => x.Weight);
        await PostsForm(vform, "/api/DyeingPlan/add", (s) => "Sys.OprationSuccess");
    }

    private List<PlanDetail> detailList = new List<PlanDetail>();
    public IEnumerable<PlanDetail> DetaiList
    {
        get { return detailList.AsEnumerable(); }
        set
        {
            detailList = value.ToList();
        }
    }

    private Product product { get; set; } = new();
    private string spec = "";

    //订单选择后查询产品名称和规格
    private async Task OnSelectedChanged(PurchaseOrder_View item)
    {
        if (item is not null)
        {
            await ProductOnSelectPOrder(item.ID.ToString());
        }
    }

    public async Task ProductOnSelectPOrder(string id)
    {
        Model.Entity.POrderId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
        var p = rv.Data;
        porder.AccountUnit = p.Entity.AccountUnit;
        porder.DyeingProductName = p.Entity.DyeingProductName;
        product = p.Entity.Product;
        OrderDetailList = p.Entity.OrderDetailList;
        OrderDetails = p.Entity.OrderDetailList.Select(x => new SelectedItem(x.ID.ToString(), x.Color)).ToList();
        spec = "";
        if ( product.GSM != 0)
        {
            spec += +product.GSM + "Gsm";
        }
        if (product.Width != 0)
        {
            spec += " - " + product.Width + "CM";
        }
        Model.Entity.Light = p.Entity.Light ?? LightEnum.D65;
        Model.Entity.Light2 = p.Entity.Light2;
        Model.Entity.Width = p.Entity.Product.Width;
        Model.Entity.GSM = p.Entity.Product.GSM ;


        StateHasChanged();
    }

    private async Task OnOrderDetailSelect(SelectedItem item, PlanDetail detail)
    {
        if (item is not null)
        {
            var od = OrderDetailList.FirstOrDefault(x => x.ID == Guid.Parse(item.Value));
            if (od is not null)
            {
                detail.OrderDetailId = od.ID;
                detail.Color = od.Color;
                detail.ColorCode = od.ColorCode;
                //detail.Qty = od.Meters ?? 0;
                detail.Remark = od.Remark;
                detail.Meters = od.Meters * (Decimal)(100 + LossRate) / 100 ?? 0;
                detail.Weight = od.KG * (Decimal)(100 + LossRate) / 100 ?? 0;
                if (porder.AccountUnit == AccountingUnitEnum.M) detail.Qty = od.Meters * (Decimal)(100 + LossRate) / 100 ?? 0;
                if (porder.AccountUnit == AccountingUnitEnum.KG)
                {
                    detail.Qty = od.KG * (Decimal)(100 + LossRate) / 100 ?? 0;
                    detail.QtyUnit = AccountingUnitEnum.KG;
                }
            }
        }
        await Task.FromResult(0);
    }

    public void OnClose()
    {
        CloseDialog();
    }
    /*
    //订单选择弹窗

    public async Task SelectPOrder()
        {
        var result = await WtmBlazor.Dialog.ShowModal<DialogSelectPOrders>(new ResultDialogOption()
                {
                Title = "请选择订单",
                ButtonYesText = "确定",
                Size = Size.Large,
                ShowHeaderCloseButton = false,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                    {
                    [nameof(DialogSelectPOrders.SelectedPurchaseOrder)] = porder,
                    [nameof(DialogSelectPOrders.SelectedPurchaseOrderChanged)] = EventCallback.Factory.Create<PurchaseOrder_View>(this, v => porder = v)
        }
        });

        if (result == DialogResult.Yes)
            {
            Model.Entity.POrderId = porder.ID;
            //StateHasChanged();
            await ProductOnSelectPOrder(porder.ID.ToString());//订单选择后查询产品名称和规格
    }
    }


    //订单明细选择弹窗
    private OrderDetail_View orderDetailSelected { get; set; } = new();
    public async Task SelectPOrderDetail()
        {

        orderDetailSelected.PurchaseOrderId = Model.Entity.POrderId;
        var result = await WtmBlazor.Dialog.ShowModal<DialogOrderDetails>(new ResultDialogOption()
                {
                Title = "请选择订单",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                    {
                    [nameof(DialogOrderDetails.SelectedOrderDetail)] = orderDetailSelected,
                    [nameof(DialogOrderDetails.SelectedOrderDetailChanged)] = EventCallback.Factory.Create<OrderDetail_View>(this, v => orderDetailSelected = v)
        }
        });

        if (result == DialogResult.Yes)
                            {


                                }
    }



    //子表Excel模式,更新方法
    private async Task OnAddAsync(IEnumerable<PlanDetail> items)
        {
        var od = new PlanDetail();
        if (Model.Entity.POrderId != Guid.Empty)
            {
            // Excel 模式下新建使用订单明细弹窗选择
            await SelectPOrderDetail();

            od.OrderDetailId = orderDetailSelected.ID;
            od.Color = orderDetailSelected.OrderDetail_Color;
            od.ColorCode = orderDetailSelected.OrderDetail_ColorCode;
            od.Qty = orderDetailSelected.OrderDetail_Meters ?? 0;
            detailList.Insert(0, od);

            // 输出日志信息
            //Logger.Log($"集合值变化通知 列: {DetaiList.Count} - 类型: Add");
            await Task.FromResult(od);
            //return od;
            }
            else
            {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择订单!"]);

    }

    }

    private Task<bool> OnSaveAsync(PlanDetail item, ItemChangedType changedType)
        {
        Model.Entity.TotalQty = detailList.Sum(x => x.Qty);
        Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
        // 对象已经更新
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<PlanDetail> items)
        {
        detailList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
    */
}
