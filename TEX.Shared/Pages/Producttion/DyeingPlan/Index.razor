@page "/Producttion/DyeingPlan"
@using TEX.Model.Models
@using TEX.Shared.Pages.Producttion.PlanDetail
@using TEX.ViewModel.Producttion.DyeingPlanVMs;
@inherits BasePage
@attribute [ActionDescription("染色计划", "TEX.Controllers,DyeingPlan")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <WTDateRange @bind-Value="@SearchModel.CreateDate" />
            <BootstrapInput @bind-Value="@SearchModel.BillNo" />
            <Select @bind-Value="@SearchModel.FinishingFactoryId" Items="@AllDyeingFactorys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.POrderId" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            @* <Select @bind-Value="@SearchModel.FabricId" Items="@AllProducts" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" /> *@
            <Select @bind-Value="@SearchModel.GreigeVenderId" Items="@AllGreigeVenders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.Remark" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="DyeingPlan_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.BillNo" />
        <TableColumn @bind-Field="@context.OrderNo_view" />
        <TableColumn @bind-Field="@context.ProductName_view" />
        <TableColumn @bind-Field="@context.FinishingFactoryName_view" Text=@WtmBlazor.Localizer["_FinishingFactory"] />
        <TableColumn @bind-Field="@context.PlanBatch" Width="60" />
        <TableColumn @bind-Field="@context.Light" Width="60" />
        @* <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Right" FormatString="0" /> *@
        @* <TableColumn @bind-Field="@context.TotalQty" Align="Alignment.Right" FormatString="0" /> *@
        <TableColumn @bind-Field="@context.TotalMeters" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.TotalWeight" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.AuditStatus" Width="70">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    @* <DetailRowTemplate>
        <PlanDetailTable Id="@context.ID"></PlanDetailTable>
    </DetailRowTemplate> *@
    <TableToolbarTemplate>
        @if (IsAccessable("/api/DyeingPlan/Add"))
        {
            <TableToolbarButton TItem="DyeingPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/DyeingPlan/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="DyeingPlan_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }

        @if (IsAccessable("/api/DyeingPlan/Import"))
        {
            <TableToolbarButton TItem="DyeingPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/DyeingPlan/ExportExcel"))
        {
            <TableToolbarButton TItem="DyeingPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/DyeingPlan/Edit") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/DyeingPlan/{id}") )
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info-circle" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/DyeingPlan/BatchDelete") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
            @if (IsAccessable("/api/DyeingPlan/BatchDelete"))
            {
                <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                                  ConfirmButtonColor="Color.Warning" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code {

    private DyeingPlanSearcher SearchModel = new DyeingPlanSearcher();
    private Table<DyeingPlan_View> dataTable;

    private List<SelectedItem> AllDyeingFactorys = new List<SelectedItem>();

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllGreigeVenders = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDyeingFactorys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetDyeingFactorys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllGreigeVenders = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetGreigeVenderCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<DyeingPlan_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<DyeingPlan_View>("/api/DyeingPlan/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<DyeingPlan_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    //加入审核功能
    private async Task OnAuditClick(DyeingPlan_View item)
    {
        //Controller参数前不加[Frombody]会报错InternalError,调试到API上,传过去的ID为null,在httpclient.PostAsync(url,content)时,id还是有值的
        await PostsData(item.ID, $"/api/DyeingPlan/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }

    private async Task OnEditClick(DyeingPlan_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(DyeingPlan_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/DyeingPlan/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(DyeingPlan_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/DyeingPlan/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<DyeingPlan_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/DyeingPlan/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/DyeingPlan/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<DyeingPlan_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
