@page "/Producttion/DyeingPlan/Edit/{id}"
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Models.OrderDetailVMs
@using TEX.ViewModel.Models.ProductVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.DyeingPlanVMs;
@using TEX.Model.Models;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row>
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            @* <BootstrapInput @bind-Value="@Model.Entity.BillNo" IsDisabled="true" /> *@
            <Select @bind-Value="@Model.Entity.FinishingFactoryId" Items="@AllFinishingFactorys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />

            <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />

            <BootstrapInput @bind-Value="@Model.Entity.PlanBatch" />
            <BootstrapInput @bind-Value="@Model.Entity.Remark" />
            <BootstrapInput @bind-Value="@OrderNo" DisplayText="@WtmBlazor.Localizer["_OrderNo"]" IsDisabled="true" />
            <BootstrapInput Value="@orderVM.Entity.Product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" IsDisabled="true" />
            <BootstrapInput Value="@orderVM.Entity.DyeingProductName" DisplayText="@WtmBlazor.Localizer["_DyeingProductName"]" IsDisabled="true" />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Version" />
        </Row>
        <Row ItemsPerRow="ItemsPerRow.Six" RowType="RowType.Inline">
            <Select @bind-Value="@Model.Entity.Light" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <Select @bind-Value="@Model.Entity.Light2" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Width" />
            <BootstrapInputNumber @bind-Value="@Model.Entity.GSM" />
            <BootstrapInput @bind-Value="@Model.Entity.GreigeBatch" />

            <Select @bind-Value="@Model.Entity.GreigeVenderId" Items="@AllGreigeVenderCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <BootstrapInput @bind-Value="@Model.Entity.FinishingProcess" />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingDemand" />
            <BootstrapInput @bind-Value="@Model.Entity.PackDemand" />
            <BootstrapInput @bind-Value="@Model.Entity.AdditionalDemend" />
            <Row ColSpan="2" RowType="RowType.Inline">

            </Row>
        </Row>
    </Row>


    <Table TItem="PlanDetail" @bind-Items="@DetailList" ShowRefresh="false"
           IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true"
           ShowExtendButtons="true" ShowToastAfterSaveOrDeleteModel="false" ShowFooter="true"
           ShowAddButton="true" EditMode="EditMode.InCell" IsBordered="true"
           ShowEditButton="false" ShowDeleteButton="false" ShowToolbar="true"
           IsFixedHeader="true" Height="300" style="margin:10px 0;">
        <TableColumns>
            <TableColumn @bind-Field="@context.OrderDetailId" Lookup="@OrderDetails" Width="160">
                <EditTemplate Context="v">
                    <Select @bind-Value="v.OrderDetailId" Items="@OrderDetails" OnSelectedItemChanged="x=>OnOrderDetailSelect(x,v)" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.ColorCode" Width="120" />
            <TableColumn @bind-Field="@context.Pcs" FormatString="0" Width="60" />
            <TableColumn @bind-Field="@context.Meters" Width="120" />
            <TableColumn @bind-Field="@context.Weight" Width="120" />
            <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yyyy-mm-dd" Width="160" />
            <TableColumn @bind-Field="@context.GreigeBatch" Width="80" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
        <TableFooter>
            <TableFooterCell />
            <TableFooterCell Text="合计:" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail.Pcs)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail.Meters)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(PlanDetail.Weight)" />
            <TableFooterCell />
        </TableFooter>
    </Table>

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    private DyeingPlanVM Model = new();
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    public string OrderNo { get; set; } = "";

    private int LossRate { get; set; } = 5;

    private PurchaseOrderVM orderVM { get; set; } = new();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    private List<SelectedItem> AllFinishingFactorys = new List<SelectedItem>();
    private List<SelectedItem> AllGreigeVenderCompanys = new List<SelectedItem>();

    private IEnumerable<OrderDetail> OrderDetailList = new List<OrderDetail>();
    private List<SelectedItem> OrderDetails = new();

    protected override async Task OnInitializedAsync()
    {
        orderVM.Entity.Product = new();
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        AllGreigeVenderCompanys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetGreigeVenderCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        AllFinishingFactorys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<DyeingPlanVM>($"/api/DyeingPlan/{id}");
        Model = rv.Data;
        DetailList = Model.Entity.DetailList;
        OrderNo = Model.Entity.POrder.OrderNo;

        await ProductOnSelectPOrder(Model.Entity.POrderId.ToString());
        //await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        Model.Entity.Version = Model.Entity.Version + 1;
        Model.Entity.DetailList = DetailList.ToList();
        Model.Entity.TotalQty = DetailList.Sum(x => x.Qty);
        Model.Entity.TotalMeters = DetailList.Sum(x => x.Meters);
        Model.Entity.TotalWeight = DetailList.Sum(x => x.Weight);

        await PostsForm(vform, $"/api/DyeingPlan/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    public IEnumerable<PlanDetail> DetailList = new List<PlanDetail>();

    //订单选择后查询产品名称和规格
    public async Task ProductOnSelectPOrder(string id)
    {
        Model.Entity.POrderId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
        var p = rv.Data;
        orderVM = p;
        OrderDetailList = p.Entity.OrderDetailList;
        OrderDetails = p.Entity.OrderDetailList.Select(x => new SelectedItem(x.ID.ToString(), x.Color)).ToList();
        Model.Entity.Light = p.Entity.Light ?? LightEnum.D65;
        Model.Entity.Light2 = p.Entity.Light2;

        //StateHasChanged();
    }

    private async Task OnOrderDetailSelect(SelectedItem item, PlanDetail detail)
    {
        if (item is not null)
        {
            var od = OrderDetailList.FirstOrDefault(x => x.ID == Guid.Parse(item.Value));
            if (od is not null)
            {
                detail.OrderDetailId = od.ID;
                detail.Color = od.Color;
                detail.ColorCode = od.ColorCode;
                detail.Remark = od.Remark;
                detail.Meters = od.Meters * (Decimal)(100 + LossRate) / 100 ?? 0;
                detail.Weight = od.KG * (Decimal)(100 + LossRate) / 100 ?? 0;
                if (orderVM.Entity.AccountUnit == AccountingUnitEnum.M) detail.Qty = od.Meters * (Decimal)(100 + LossRate) / 100 ?? 0;
                if (orderVM.Entity.AccountUnit == AccountingUnitEnum.KG)
                {
                    detail.Qty = od.KG * (Decimal)(100 + LossRate) / 100 ?? 0;
                    detail.QtyUnit = AccountingUnitEnum.KG;
                }
            }
        }
        await Task.FromResult(0);
    }
    //子表Excel模式,更新方法
    private async Task<PlanDetail> OnAddAsync()
    {
        var od = new PlanDetail();
        if (Model.Entity.POrderId != Guid.Empty)
        {
            // Excel 模式下新建使用订单明细弹窗选择

            //DetailList.Insert(0, od);

            // 输出日志信息
            //Logger.Log($"集合值变化通知 列: {DetaiList.Count} - 类型: Add");
            // Task.FromResult(od);
            return od;
        }
        else
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择订单!"]);
            return null;
        }

    }
}
