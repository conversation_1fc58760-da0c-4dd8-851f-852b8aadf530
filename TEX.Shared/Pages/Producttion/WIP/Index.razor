@page "/Producttion/WIP"
@using TEX.ViewModel.Producttion.WIPVMs;
@inherits BasePage
@attribute [ActionDescription("半成品管理", "TEX.Controllers,WIP")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.BillNo"  />
            <WTDateRange @bind-Value="@SearchModel.CreateDate" />
            <Select @bind-Value="@SearchModel.POrderId" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.ShipperId" Items="@AllShipper" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.ReceiverId" Items="@AllReceiver" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.AuditStatus"  PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="WIP_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.BillNo"  />
        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.OrderNo_view"  />
        <TableColumn @bind-Field="@context.Product_view" />
        <TableColumn @bind-Field="@context.Shipper_view" />
        <TableColumn @bind-Field="@context.Receiver_view"  />
        <TableColumn @bind-Field="@context.Pcs"  />
        <TableColumn @bind-Field="@context.Meters"  />
        <TableColumn @bind-Field="@context.Weight"  />
        <TableColumn @bind-Field="@context.Procedure"  />
        <TableColumn @bind-Field="@context.FinishingProcess"  />
        <TableColumn @bind-Field="@context.Remark"  />
        <TableColumn @bind-Field="@context.AuditStatus"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/WIP/Add"))
        {
            <TableToolbarButton TItem="WIP_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/WIP/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="WIP_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/WIP/Import"))
        {
            <TableToolbarButton TItem="WIP_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/WIP/ExportExcel"))
        {
            <TableToolbarButton TItem="WIP_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/WIP/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/WIP/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/WIP/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private WIPSearcher SearchModel = new WIPSearcher();
    private Table<WIP_View> dataTable;

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllReceiver = new List<SelectedItem>();

    private List<SelectedItem> AllShipper = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllShipper = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllReceiver = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<WIP_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<WIP_View>("/api/WIP/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<WIP_View> items)
    {
        if (await OpenDialog<Create>("新建委外加工") == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(WIP_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(WIP_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/WIP/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(WIP_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/WIP/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<WIP_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/WIP/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/WIP/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<WIP_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
