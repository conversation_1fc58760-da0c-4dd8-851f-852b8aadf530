@page "/Producttion/WIP/Edit/{id}"
@using System.Reflection
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Producttion.LotAllocateVMs
@using TEX.ViewModel.Producttion.WIPVMs;
@using TEX.Model.Models;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
        <Select @bind-Value="@Model.Entity.ShipperId" Items="@AllFinishingFactorys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Select @bind-Value="@Model.Entity.ReceiverId" Items="@AllFinishingFactorys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@Model.Entity.BillNo" DisplayText="工单号" />
        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" Format="yy-MM-dd" />

        <Select @bind-Value="@Model.Entity.POrderId" Items="@AllPurchaseOrders" OnSelectedItemChanged="OnOrderSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@product.ProductName" IsDisabled="true" />
        <BootstrapInput Value="@spec" DisplayText="规格" IsDisabled="true" />

        <BootstrapInput @bind-Value="@Model.Entity.FinishingProcess" />
        <Select @bind-Value="@Model.Entity.Procedure" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>

    <div style="margin:20px 0;">
        <Table TItem="WIPDetail" @bind-Items="@DetaiList" ShowRefresh="false" IsExcel="true" IsFixedFooter="true"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true" ShowFooter="true"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync" ShowDeleteButton="false"
               ShowToolbar="true" ShowExtendButtons="true" IsBordered="true" IsFixedHeader="true" Height="300">
            <TableColumns>
                <TableColumn @bind-Field="@context.Color" />
                <TableColumn @bind-Field="@context.LotNo" />
                <TableColumn @bind-Field="@context.Pcs" />
                <TableColumn @bind-Field="@context.Meters" />
                <TableColumn @bind-Field="@context.Weight" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <TableToolbarTemplate>
                <TableToolbarButton TItem="WIPDetail" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" />
            </TableToolbarTemplate>
            @* <TableFooter>
            <TableFooterCell Text="合计：" colspan="2" Align="@Align" />
            <TableFooterCell @key="context" Aggregate="@Aggregate" Field="@nameof(WIPDetail.Pcs)" />
            <TableFooterCell  Aggregate="@Aggregate" Field="@nameof(WIPDetail.Meters)" />
            <TableFooterCell  Aggregate="@Aggregate" Field="@nameof(WIPDetail.Weight)" />
            </TableFooter> *@
            <FooterTemplate>

                <tr style="text-align: center;font-weight:bold;">

                    <td colspan="2">
                        <div style="line-height: 2;">合计：</div>
                    </td>
                    <td>
                        <div>
                            共 @context.Count() 缸
                        </div>
                    </td>
                    <td colspan="1">

                        <div>
                            @GetSum(context, "Pcs")
                        </div>
                    </td>
                    <td>
                        <div>
                            @GetSum(context, "Meters")
                        </div>
                    </td>
                    <td>
                        <div>
                            @GetSum(context, "Weight")
                        </div>
                    </td>
                    <td>
                    </td>
                </tr>

            </FooterTemplate>
        </Table>
    </div>
    @* <br /> *@
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    [Inject]
    private DialogService DialogService { get; set; }//注入弹窗服务
    private WIPVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllFinishingFactorys = new List<SelectedItem>();

    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllFinishingFactorys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<WIPVM>($"/api/WIP/{id}");
        Model = rv.Data;
        DetaiList = Model.Entity.DetailList;
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        Model.Entity.DetailList = DetaiList.ToList();
        Model.Entity.Meters = detailList.Sum(x => x.Meters);
        Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
        Model.Entity.Weight = detailList.Sum(x => x.Weight);
        await PostsForm(vform, $"/api/WIP/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }
    private List<WIPDetail> detailList = new List<WIPDetail>();
    public IEnumerable<WIPDetail> DetaiList
    {
        get { return detailList; }
        set
        {
            detailList = value.ToList();
            // Model.Entity.Meters = detailList.Sum(x => x.Meters);
            // Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
            // Model.Entity.Weight = detailList.Sum(x => x.Weight);

        }
    }

    //使用反射对实体类的某一个字段求和
    private static decimal GetSum(IEnumerable<WIPDetail> items, string fieldName)
    {
        if (!items.Any()) return 0;
        PropertyInfo property = typeof(WIPDetail).GetProperty(fieldName);
        if (property == null) throw new ArgumentException("Field not found", fieldName);
        return items.Sum(i => Convert.ToDecimal(property.GetValue(i)));
    }

    //订单选择后查询产品名称和规格
    public async Task ProductOnSelectPOrder(string id)
    {
        Model.Entity.POrderId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallAPI<ViewModel.Models.PurchaseOrderVMs.PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
        var p = rv.Data;
        product = p.Entity.Product;
        spec = "";
        if (product.GSM != null && product.GSM != 0)
        {
            spec += +product.GSM + "Gsm";
        }
        if (product.Width != null && product.Width != 0)
        {
            spec += " - " + product.Width + "CM";
        }

        StateHasChanged();
    }

    //显示产品名称和规格
    private Product product { get; set; } = new();
    private string spec = string.Empty;
    private async Task OnOrderSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            await ProductOnSelectPOrder(item.Value);
        }
    }

    //子表Excel模式,更新方法
    private async Task<WIPDetail> OnAddAsync()
    {
        detailList.Insert(detailList.Count, new WIPDetail());
        await Task.CompletedTask;
        return null;
    }

    private Task<bool> OnSaveAsync(WIPDetail item, ItemChangedType changedType)
    {
        //Model.Entity.TotalQty = detailList.Sum(x => x.Qty);
        //Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
        // 对象已经更新
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<WIPDetail> items)
    {
        detailList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }

    private async Task<WIPDetail> OnSelectLotAsync()
    {


        if (Model.Entity.POrderId != Guid.Empty)
        {
            // Excel 模式下新建使用明细弹窗选择
            await SelectLot();

            //返回null,点取消时才不会默认添加一行
            return null;
        }
        else
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择订单!"]);
            return null;
        }

    }
    //配缸记录选择弹窗
    private List<LotAllocate_View> lotdetail { get; set; } = new();
    public async Task SelectLot()
    {
        var result = await DialogService.ShowModal<DialogSelectLot>(new ResultDialogOption()
            {
                Title = "请选择缸号",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectLot.OrderId)] = Model.Entity.POrderId.ToString(),
                    [nameof(DialogSelectLot.SelectedDetail)] = lotdetail,
                    [nameof(DialogSelectLot.SelectedDetailChanged)] = EventCallback.Factory.Create<List<LotAllocate_View>>(this, v => lotdetail = v)
                }
            });
        WIPDetail detail = new();
        if (result == DialogResult.Yes)
        {
            foreach (var item in lotdetail)
            {
                detail.Color = item.Color;
                detail.LotNo = item.LotNo;
                detail.Pcs = item.Pcs;
                detail.Meters = item.Meters;
                detail.Weight = item.Weight;
                detailList.Insert(detailList.Count, detail);
                detail = new();
            }
            await Task.CompletedTask;
        }
    }
}
