@page "/Producttion/WIP/Details/{id}"
@using System.Reflection
@using TEX.ViewModel.Producttion.WIPVMs;
@using TEX.Model.Models;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <Display @bind-Value="@Model.Entity.BillNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.CreateDate" ShowLabel="true" FormatString="yyyy-MM-dd" />
        <Display @bind-Value="@Model.Entity.POrderId" Lookup="@AllPurchaseOrders" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.ShipperId" Lookup="@AllFinishingFactorys" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.ReceiverId" Lookup="@AllFinishingFactorys" ShowLabel="true" />

        <Display @bind-Value="@Model.Entity.Procedure" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.FinishingProcess" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Remark" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Pcs" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Meters" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Weight" ShowLabel="true" />

        <Display @bind-Value="@Model.Entity.AuditStatus" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.AuditedBy" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.AuditedComment" ShowLabel="true" />

    </Row>

    <div style="height:315px;margin:20px 0;">
        <Table TItem="WIPDetail" @bind-Items="@DetaiList" IsFixedHeader="true"
               IsPagination="false" TableSize="TableSize.Compact" ShowFooter="true" IsFixedFooter="true"
               IsStriped="true" IsBordered="true" ShowEditButton="false"
               ShowToolbar="false" ShowExtendButtons="false" ShowSkeleton="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.Color" />
                <TableColumn @bind-Field="@context.LotNo" />
                <TableColumn @bind-Field="@context.Pcs" />
                <TableColumn @bind-Field="@context.Meters" />
                <TableColumn @bind-Field="@context.Weight" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <FooterTemplate>
                <tr style="text-align: center;font-weight:bold;">
                    <td colspan="1">
                        <div style="line-height: 2;">合计：</div>
                    </td>
                    <td>
                        <div>
                            共 @context.Count() 缸
                        </div>
                    </td>
                    <td colspan="1">

                        <div>
                            @GetSum(context, "Pcs")
                        </div>
                    </td>
                    <td>
                        <div>
                            @GetSum(context, "Meters")
                        </div>
                    </td>
                    <td>
                        <div>
                            @GetSum(context, "Weight")
                        </div>
                    </td>
                    @* <td >
                    </td> *@
                </tr>

            </FooterTemplate>

        </Table>

    </div>

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private WIPVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllFinishingFactorys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllFinishingFactorys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<WIPVM>($"/api/WIP/{id}");
        Model = rv.Data;
        DetaiList = Model.Entity.DetailList;
    }

    //使用反射对实体类的某一个字段求和
    private static decimal GetSum(IEnumerable<WIPDetail> items, string fieldName)
    {
        if (!items.Any()) return 0;
        PropertyInfo property = typeof(WIPDetail).GetProperty(fieldName);
        if (property == null) throw new ArgumentException("Field not found", fieldName);
        return items.Sum(i => Convert.ToDecimal(property.GetValue(i)));
    }

    public void OnClose()
    {
        CloseDialog();
    }
    private IEnumerable<WIPDetail> detailList = new List<WIPDetail>();
    public IEnumerable<WIPDetail> DetaiList
    {
        get { return detailList; }
        set
        {
            detailList = value;
            Model.Entity.Meters = detailList.Sum(x => x.Meters);
            Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
            Model.Entity.Weight = detailList.Sum(x => x.Weight);

        }
    }
}
