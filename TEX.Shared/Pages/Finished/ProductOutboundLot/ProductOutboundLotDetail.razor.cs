using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Finished.ProductOutboundLot
{
    public partial class ProductOutboundLotDetail: MyComponent
    {

        [Parameter]
        public Guid? OutboundBillId { get; set; }
        [Parameter]
        public Guid? OrderDetailId { get; set; }


        private ProductOutboundLotSearcher SearchModel = new ProductOutboundLotSearcher();


        private IEnumerable<LotSummary> Datas { get; set; }=new List<LotSummary>();

        protected override async Task OnInitializedAsync()
        {
            SearchModel.OutboundBillId = OutboundBillId;
            SearchModel.OrderDetailId = OrderDetailId;
            await base.OnInitializedAsync();
        }

        private async Task<QueryData<ProductOutboundLot_View>> OnSearch(QueryPageOptions opts)
        {

            return await StartSearch<ProductOutboundLot_View>("/api/ProductOutboundLot/Search", SearchModel, opts);
        }



    }
}