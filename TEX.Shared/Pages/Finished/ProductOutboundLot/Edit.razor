@page "/Finished/ProductOutboundLot/Edit/{id}"
@using TEX.ViewModel.Finished.ProductOutboundLotVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.OutboundBillId" Items="@AllProductOutboundBills" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Color"  />
            <BootstrapInput @bind-Value="@Model.Entity.ColorCode"  />
            <BootstrapInput @bind-Value="@Model.Entity.LotNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Yards"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ProductOutboundLotVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProductOutboundBills = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductOutboundBills = await WtmBlazor.Api.CallItemsApi("/api/ProductOutboundLot/GetProductOutboundBills", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundLotVM>($"/api/ProductOutboundLot/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/ProductOutboundLot/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
