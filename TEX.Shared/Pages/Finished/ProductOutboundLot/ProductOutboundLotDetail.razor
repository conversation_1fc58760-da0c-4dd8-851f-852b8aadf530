@using TEX.ViewModel.Finished.ProductOutboundLotVMs;
@inherits MyComponent;


<Table TItem="ProductOutboundLot_View" OnQueryAsync="OnSearch" IsPagination="false" IsStriped="true" IsBordered="true" ShowRefresh="false" TableSize="TableSize.Compact" HeaderStyle="TableHeaderStyle.None"
       ShowToolbar="true" ShowExtendButtons="false" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        @* <TableColumn @bind-Field="@context.OrderNo_view" /> *@
        <TableColumn @bind-Field="@context.Color_view" />
        <TableColumn @bind-Field="@context.LotNo" />
        <TableColumn @bind-Field="@context.Pcs" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableFooter>
        <TableFooterCell Text="合计:" />
        <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundLot_View.LotNo)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Pcs)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Meters)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Yards)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Weight)" />
        <TableFooterCell />
    </TableFooter>
</Table>
<br/>
