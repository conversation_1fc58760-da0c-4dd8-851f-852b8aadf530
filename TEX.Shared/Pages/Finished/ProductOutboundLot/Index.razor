@page "/Finished/ProductOutboundLot"
@using TEX.ViewModel.Finished.ProductOutboundLotVMs;
@inherits BasePage
@attribute [ActionDescription("成品出库明细", "TEX.Controllers,ProductOutboundLot")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <Select @bind-Value="@SearchModel.OutboundBillId" Items="@AllProductOutboundBills" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.Color"  />
            <BootstrapInput @bind-Value="@SearchModel.ColorCode"  />
            <BootstrapInput @bind-Value="@SearchModel.LotNo"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="ProductOutboundLot_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.OrderNo_view"  />
        <TableColumn @bind-Field="@context.Product_view"  />
        <TableColumn @bind-Field="@context.BillNo_view"  Text="出库单号"/>
        <TableColumn @bind-Field="@context.Color_view"  />
        <TableColumn @bind-Field="@context.Color"  />
        <TableColumn @bind-Field="@context.ColorCode"  />
        <TableColumn @bind-Field="@context.LotNo"  />
        <TableColumn @bind-Field="@context.Pcs"  />
        <TableColumn @bind-Field="@context.Weight"  />
        <TableColumn @bind-Field="@context.Meters"  />
        <TableColumn @bind-Field="@context.Yards"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/ProductOutboundLot/Add"))
        {
            <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/ProductOutboundLot/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="ProductOutboundLot_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/ProductOutboundLot/Import"))
        {
            <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/ProductOutboundLot/ExportExcel"))
        {
            <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/ProductOutboundLot/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/ProductOutboundLot/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/ProductOutboundLot/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private ProductOutboundLotSearcher SearchModel = new ProductOutboundLotSearcher();
    private Table<ProductOutboundLot_View> dataTable;

    private List<SelectedItem> AllProductOutboundBills = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductOutboundBills = await WtmBlazor.Api.CallItemsApi("/api/ProductOutboundLot/GetProductOutboundBills", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<ProductOutboundLot_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<ProductOutboundLot_View>("/api/ProductOutboundLot/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<ProductOutboundLot_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(ProductOutboundLot_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(ProductOutboundLot_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/ProductOutboundLot/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(ProductOutboundLot_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/ProductOutboundLot/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<ProductOutboundLot_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/ProductOutboundLot/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/ProductOutboundLot/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<ProductOutboundLot_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
