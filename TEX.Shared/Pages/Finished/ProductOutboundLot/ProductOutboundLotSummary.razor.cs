using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Finished.ProductOutboundLot
{
    public partial class ProductOutboundLotSummary: MyComponent
    {

        [Parameter]
        public Guid id { get; set; }

        [Parameter]
        public bool IsShowTotal { get; set; } = false;

        private ProductOutboundLotSearcher SearchModel = new ProductOutboundLotSearcher();
        private IEnumerable<LotSummary> Datas { get; set; }=new List<LotSummary>();

        protected override async Task OnInitializedAsync()
        {
            SearchModel.OutboundBillId = id;

            var rv = await OnSearch(new QueryPageOptions()
            {
                PageIndex = 1,
                PageItems = 1000
            });
            var items = rv.Items;

            var lotSummarys = items
                .GroupBy(x => new { x.Product_view, x.Color_view })
                .Select(g => new LotSummary()
                {
                    Product_view = g.Key.Product_view,
                    Color_view = g.Key.Color_view,
                    TotalLot = g.Select(x => x.LotNo).Distinct().Count(),
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalYards=g.Sum(x=>x.Yards)
                })
                .OrderBy(x => x.Product_view)
                .ThenBy(x => x.Color_view)
                .ToList();
            Datas = lotSummarys;

            await base.OnInitializedAsync();
        }

        private async Task<QueryData<ProductOutboundLot_View>> OnSearch(QueryPageOptions opts)
        {

            return await StartSearch<ProductOutboundLot_View>("/api/ProductOutboundLot/Search", SearchModel, opts);
        }



    }
}