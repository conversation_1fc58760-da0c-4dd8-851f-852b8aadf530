using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using WalkingTec.Mvvm.Core;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Finished.ProductOutboundLot
{
    public partial class ProductOutboundLotDetailTable
    {
        //[Inject]
        //public WtmBlazorContext WtmBlazor { get; set; }
        [Parameter]
        public Guid id { get; set; }

        [Parameter]
        public bool IsShowTotal { get; set; } = true;

        private ProductOutboundLotSearcher SearchModel = new ProductOutboundLotSearcher();

        private IEnumerable<LotSummary> Datas { get; set; } = new List<LotSummary>();
        protected override async Task OnInitializedAsync()
        {
            SearchModel.OutboundBillId = id;
            var rv = await StartSearch<ProductOutboundLot_View>("/api/ProductOutboundLot/Search", SearchModel, new QueryPageOptions());

            var items = rv.Items;

            var lotSummarys = items
                .GroupBy(x => new { x.OutboundBillId, x.OrderDetailId,x.OrderNo_view, x.Product_view, x.Color_view })
                .Select(g => new LotSummary()
                {
                    OrderNo_view=g.Key.OrderNo_view,
                    Product_view = g.Key.Product_view,
                    Color_view = g.Key.Color_view,
                    OutboundBillId=g.Key.OutboundBillId,
                    OrderDetailId=g.Key.OrderDetailId,
                    TotalLot = g.Select(x => x.LotNo).Distinct().Count(),
                    TotalPcs = g.Sum(x => x.Pcs),
                    TotalWeight = g.Sum(x => x.Weight),
                    TotalYards = g.Sum(x => x.Yards)
                })
                .OrderBy(x => x.Product_view)
                .ThenBy(x => x.Color_view)
                .ToList();
            Datas = lotSummarys;
            await base.OnInitializedAsync();
        }

        private async Task<QueryData<ProductOutboundLot_View>> OnSearch(QueryPageOptions opts)
        {
            
            var rv= await StartSearch<ProductOutboundLot_View>("/api/ProductOutboundLot/Search", SearchModel, opts);

            return rv;
        }
    }
}