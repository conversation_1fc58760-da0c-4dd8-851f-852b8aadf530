@page "/Finished/ProductOutboundLot/Details/{id}"
@using TEX.ViewModel.Finished.ProductOutboundLotVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.OutboundBillId" Lookup="@AllProductOutboundBills"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.OrderDetailId" Lookup="@AllOrderDetails"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Color"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ColorCode"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.LotNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Pcs"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Weight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Meters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Yards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductOutboundLotVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProductOutboundBills = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductOutboundBills = await WtmBlazor.Api.CallItemsApi("/api/ProductOutboundLot/GetProductOutboundBills", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundLotVM>($"/api/ProductOutboundLot/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
