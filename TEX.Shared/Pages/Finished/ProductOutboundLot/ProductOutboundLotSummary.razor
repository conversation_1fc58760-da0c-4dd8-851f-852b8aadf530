@using TEX.ViewModel.Finished.ProductOutboundLotVMs;
@inherits MyComponent;


<Table TItem="LotSummary" Items="@Datas" IsPagination="false" IsStriped="true" IsBordered="true" ShowRefresh="false" ShowFooter="@IsShowTotal" TableSize="TableSize.Compact" HeaderStyle="TableHeaderStyle.None"
ShowToolbar="true" ShowExtendButtons="false" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.Product_view" />
        <TableColumn @bind-Field="@context.Color_view" />
        <TableColumn @bind-Field="@context.TotalPcs" />
        <TableColumn @bind-Field="@context.TotalWeight" />
        <TableColumn @bind-Field="@context.TotalMeters" />
        <TableColumn @bind-Field="@context.TotalYards" />
    </TableColumns>
     <TableFooter>
        <TableFooterCell Text="合计:" />
        <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(LotSummary.TotalLot)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotSummary.TotalPcs)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotSummary.TotalWeight)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotSummary.TotalMeters)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(LotSummary.TotalYards)" />
    </TableFooter> 
</Table>

