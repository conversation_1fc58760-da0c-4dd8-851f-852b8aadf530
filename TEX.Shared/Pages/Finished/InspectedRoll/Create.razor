@page "/Finished/InspectedRoll/Create"
@using TEX.ViewModel.Finished.InspectedRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.CreateTime" Format="yyyy-MM-dd" />
            <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.InspectedLot"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.RollNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Yards"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Score"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalScore"  />
            <BootstrapInput @bind-Value="@Model.Entity.Grade"  />
            <BootstrapInput @bind-Value="@Model.Entity.ProcessName"  />
            <Select @bind-Value="@Model.Entity.AuditStatus"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private InspectedRollVM Model = new InspectedRollVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/InspectedRoll/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
