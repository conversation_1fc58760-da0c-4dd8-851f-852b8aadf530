@page "/Finished/InspectedRoll/Details/{id}"
@using TEX.ViewModel.Finished.InspectedRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

        <Display @bind-Value="@Model.Entity.OrderDetailId" Lookup="@AllOrderDetails" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.InspectedLot" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.RollNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Weight" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Meters" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Yards" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.FreeYards" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Score" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.TotalScore" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Grade" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.ProcessName" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.AuditStatus" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Remark" ShowLabel="true" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private InspectedRollVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/InspectedRoll/GetOrderDetailsOfInspected", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<InspectedRollVM>($"/api/InspectedRoll/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
