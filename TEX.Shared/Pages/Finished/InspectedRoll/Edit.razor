@page "/Finished/InspectedRoll/Edit/{id}"
@using TEX.ViewModel.Finished.InspectedRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.InspectedLot"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.RollNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Yards"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Score"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalScore"  />
            <BootstrapInput @bind-Value="@Model.Entity.Grade"  />
            <BootstrapInput @bind-Value="@Model.Entity.ProcessName"  />
            <Select @bind-Value="@Model.Entity.AuditStatus"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private InspectedRollVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<InspectedRollVM>($"/api/InspectedRoll/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/InspectedRoll/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
