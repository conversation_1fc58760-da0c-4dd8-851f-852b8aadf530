@page "/Finished/InspectedRoll/Label/"
@inherits BasePage
@* @using FastReport;
@using FastReport.Web; *@

@* <ReportViewer Width="100%" Height="100%" Report="Model.Report" /> *@
@* <iframe src="@report" height="600px" width="800px"></iframe> *@

<button onclick="window.open('/api/inspectedroll/roll')">测试报表2</button>
@code {

    
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();
    //HtmlObject report=new HtmlObject();

    protected override async Task OnInitializedAsync()
    {
        //var rv = await WtmBlazor.Api.CallAPI<HtmlObject>($"/api/InspectedRoll/Roll");
        //report=rv.Data;
        await base.OnInitializedAsync();
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
