@page "/Finished/InspectedRoll"
@using TEX.ViewModel.Finished.InspectedRollVMs;
@inherits BasePage
@attribute [ActionDescription("检验", "TEX.Controllers,InspectedRoll")]

@inject IJSRuntime JS
<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@SearchModel.CreateTime" />
            <Select @bind-Value="@SearchModel.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.OrderId"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.Score"  />
            <BootstrapInput @bind-Value="@SearchModel.Grade"  />
            <Select @bind-Value="@SearchModel.AuditStatus"  PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="InspectedRoll_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateTime" Format="yyyy-MM-dd"  />
        <TableColumn @bind-Field="@context.OrderNo"  />
        <TableColumn @bind-Field="@context.ProductName"  />
        <TableColumn @bind-Field="@context.Color_view"  />
        <TableColumn @bind-Field="@context.Spec"  />
        <TableColumn @bind-Field="@context.InspectedLot"  />
        <TableColumn @bind-Field="@context.RollNo"  />
        <TableColumn @bind-Field="@context.Weight"  />
        <TableColumn @bind-Field="@context.Meters"  />
        <TableColumn @bind-Field="@context.Yards"  />
        <TableColumn @bind-Field="@context.Score"  />
        <TableColumn @bind-Field="@context.TotalScore"  />
        <TableColumn @bind-Field="@context.FreeYards"  />
        <TableColumn @bind-Field="@context.Grade"  />
        <TableColumn @bind-Field="@context.ProcessName"  />
        <TableColumn @bind-Field="@context.AuditStatus"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/InspectedRoll/Add"))
        {
            <TableToolbarButton TItem="InspectedRoll_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/InspectedRoll/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="InspectedRoll_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/InspectedRoll/Import"))
        {
            <TableToolbarButton TItem="InspectedRoll_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/InspectedRoll/ExportExcel"))
        {
            <TableToolbarButton TItem="InspectedRoll_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/InspectedRoll/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/InspectedRoll/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/InspectedRoll/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private InspectedRollSearcher SearchModel = new InspectedRollSearcher();
    private Table<InspectedRoll_View> dataTable;

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/InspectedRoll/GetOrderDetailsOfInspected", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<InspectedRoll_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<InspectedRoll_View>("/api/InspectedRoll/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<InspectedRoll_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(InspectedRoll_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(InspectedRoll_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
        
        //await OpenDialog<Labels>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
        //await JS.InvokeVoidAsync("$.roll", "/api/InspectedRoll/Roll");
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/InspectedRoll/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(InspectedRoll_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/InspectedRoll/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<InspectedRoll_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/InspectedRoll/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/InspectedRoll/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<InspectedRoll_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
