@page "/Finished/ProductInboundBill/Edit/{id}"
@using System.Reflection
@using TEX.Model.Models
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Finished.ProductInboundBillVMs;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs
@inherits BasePage


<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.BillNo" />
        @* <div>
            <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="true" />
            <div class="input-group">
                <Select @bind-Value="@Model.Entity.POrderId" ShowSearch="true" ShowLabel="false" Items="@AllPurchaseOrders" OnSelectedItemChanged="@OnOrderSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <Button Icon="fa-solid fa-magnifying-glass" OnClick="@SelectPOrder"></Button>
            </div>
        </div> *@
        <BootstrapInputGroup>
            <label class="form-label" required>订单号</label>
            <SelectOrderTable @bind-Value="@porder" 
                OnSelectedChanged="OnSelectedChanged" />
        </BootstrapInputGroup>
        <Select @bind-Value="@Model.Entity.FinishingFactoryId" Items="@AllFinishingFactory" 
            PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Display @bind-Value="@Model.Entity.POrder.Product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" IsDisabled="true" />
        <BootstrapInput @bind-Value="@spec" 
            DisplayText="@WtmBlazor.Localizer["_Spec"]" IsDisabled="true" />
        <BootstrapInput @bind-Value="@Model.Entity.Warehouse" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    
    <InboundEditTemplate @bind-Bill="Model.Entity" />

    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ProductInboundBillVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    private PurchaseOrder_View porder {get;set;}

    [Inject]
    private DialogService DialogService { get; set; }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllFinishingFactory = new List<SelectedItem>();

    private async Task Submit(EditContext context)
    {
        if (Model.Entity.LotList.Any())
        {
            Model.Entity.Pcs = Model.Entity.LotList.Sum(x => x.Pcs);
            Model.Entity.Meters = Model.Entity.LotList.Sum(x => x.Meters);
            Model.Entity.Yards = Model.Entity.LotList.Sum(x => x.Yards);
            Model.Entity.Weight = Model.Entity.LotList.Sum(x => x.Weight);
        }
        await PostsForm(vform, $"/api/ProductInboundBill/EditWithLotAndRoll", (s) => "Sys.OprationSuccess", async (err) =>
      {
          // 使用PostsForm的错误处理回调来显示自定义通知
          if (err.Message != null && err.Message.Count > 0)
          {
              foreach (var key in err.Message)
              {
                  await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], key);
              }
          }
      }, method: HttpMethodEnum.PUT);
    }

    protected override async Task OnInitializedAsync()
    {

        AllFinishingFactory = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductInboundBillVM>($"/api/ProductInboundBill/{id}");
        Model = rv.Data;
        product = Model.Entity.POrder.Product;
        var width = Model.Entity.POrder.Product.Width == 0 ? "" : Model.Entity.POrder.Product.Width.ToString() + " CM";
        spec = Model.Entity.POrder.Product.GSM.ToString() + " GSM " + width;

        porder = new PurchaseOrder_View()
            {
                ID = Model.Entity.POrderId,
                OrderNo = Model.Entity.POrder.OrderNo,
                PurchaseOrder_OrderNo = Model.Entity.POrder.OrderNo,
                PurchaseOrder_CreateDate = Model.Entity.POrder.CreateDate,

            };
        //StateHasChanged();
        await base.OnInitializedAsync();
    }

    //订单选择后查询产品名称和规格
    private async Task OnSelectedChanged(PurchaseOrder_View item)
    {
        if (item is not null)
        {
            Model.Entity.POrderId=item.ID;
            await ProductOnSelectPOrder(item.ID.ToString());
            StateHasChanged();
            await WtmBlazor.Toast.Warning("订单变化警告:","订单改变后,请重新选择颜色,否则为默认颜色",true,false);
        }
    }


    public void OnClose()
    {
        CloseDialog();
    }


    private Product product { get; set; } = new();
    private string spec = "";

    //订单选择后查询产品名称和规格
    public async Task ProductOnSelectPOrder(string id)
    {
        Model.Entity.POrderId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
        var p = rv.Data;

        Model.Entity.POrder = p.Entity;
        Model.Entity.POrder.Product = p.Entity.Product;
        var width = Model.Entity.POrder.Product.Width == 0 ? "" : Model.Entity.POrder.Product.Width.ToString() + " CM";
        spec = Model.Entity.POrder.Product.GSM.ToString() + " GSM " + width;

    }
}
