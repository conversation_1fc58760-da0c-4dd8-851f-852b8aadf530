@page "/Finished/ProductInboundBill/Details/{id}"
@using System.Reflection
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Finished.ProductInboundBillVMs;
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">


        <Display @bind-Value="@Model.Entity.POrder.OrderNo" ShowLabel="true" />
        @* <Display @bind-Value="@Model.Entity.POrderId" Lookup="@AllPurchaseOrders" ShowLabel="true" /> *@
        <Display @bind-Value="@Model.Entity.POrder.Product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" IsDisabled="true" />
        <Display @bind-Value="@spec" DisplayText="@WtmBlazor.Localizer["_Spec"]" IsDisabled="true" />
        @* <Display @bind-Value="@Model.Entity.FinishingFactoryId" Lookup="@AllCompanys" ShowLabel="true" /> *@
        <Display @bind-Value="@Model.Entity.CreateDate" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.BillNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.FinishingFactory.CompanyName" DisplayText="@WtmBlazor.Localizer["_FinishingFactory"]" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Warehouse" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.AuditStatus" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.AuditedBy" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Remark" ShowLabel="true" />
    </Row>

    <Table TItem="ProductInboundLot" @bind-Items="@DetailList" ShowRefresh="false"
           TableSize="TableSize.Compact" ShowSkeleton="true"
           ShowToolbar="false" IsBordered="true" IsFixedHeader="true" Height="300" style="margin:16px 0;">
        <TableColumns>
            <TableColumn @bind-Field="@context.OrderDetailId" Text="@WtmBlazor.Localizer["_Color"]" Lookup="AllOrderDetails" />
            <TableColumn @bind-Field="@context.LotNo" />
            <TableColumn @bind-Field="@context.Pcs" />
            <TableColumn @bind-Field="@context.Weight" />
            <TableColumn @bind-Field="@context.Meters" />
            <TableColumn @bind-Field="@context.Yards" />
            <TableColumn @bind-Field="@context.Location" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
        @* <RowButtonTemplate>
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
        *@
    </Table>


    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <Display @bind-Value="@Model.Entity.Pcs" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Meters" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Yards" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Weight" ShowLabel="true" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductInboundBillVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    public string spec { get; set; }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllCompanys = new List<SelectedItem>();

    protected override async Task OnInitializedAsync()
    {
        var rv = await WtmBlazor.Api.CallAPI<ProductInboundBillVM>($"/api/ProductInboundBill/{id}");
        Model = rv.Data;
        var width = Model.Entity.POrder.Product.Width == 0 ? "" : Model.Entity.POrder.Product.Width.ToString() + " CM";
        spec = Model.Entity.POrder.Product.GSM.ToString() + " GSM " + width;
        DetailList = Model.Entity.LotList;
        await SearchOrderDetail();
    }

    //查询订单明细,供选择颜色
    private List<SelectedItem> AllOrderDetails = new();
    private async Task SearchOrderDetail()
    {
        OrderDetailDetailSearcher orderdetailSearcher = new();
        QueryPageOptions opts = new();
        orderdetailSearcher.PurchaseOrderId = Model.Entity.POrderId.ToString();
        var od = await StartSearch<OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", orderdetailSearcher, opts);
        AllOrderDetails = od.Items.Select(x => new SelectedItem
            {
                Text = x.Color,
                Value = x.ID.ToString()
            }).ToList();
    }
    private IEnumerable<ProductInboundLot> DetailList = new List<ProductInboundLot>();


    public void OnClose()
    {
        CloseDialog();
    }
    //编辑每缸卷数明细
    // private async Task OnDetailsClick(ProductInboundLot lot)
    // {
    //     if (lot.RollList is null) lot.RollList = new();
    //     var result = await WtmBlazor.Dialog.ShowModal<DialogEditRolls>(new ResultDialogOption()
    //         {
    //             Title = "缸号:" + lot.LotNo + "; 颜色:" + lot.Color,
    //             ButtonYesText = "确定",
    //             Size = Size.Large,
    //             ButtonYesIcon = "fa-solid fa-magnifying-glass",
    //             ComponentParameters = new Dictionary<string, object>
    //             {

    //                 [nameof(DialogEditRolls.lot)] = lot,
    //                 [nameof(DialogEditRolls.Detail)] = lot.RollList,
    //                 [nameof(DialogEditRolls.DetailChanged)] = EventCallback.Factory.Create<List<ProductInboundRoll>>(this, v => lot.RollList = v)
    //             }
    //         });

    //     if (result == DialogResult.Yes)
    //     {
    //         if (lot.RollList.Any())
    //         {
    //             lot.Pcs = lot.RollList.Count();
    //             lot.Meters = lot.RollList.Sum(x => x.Meters);
    //             lot.Yards = lot.RollList.Sum(x => x.Yards);
    //             lot.Weight = lot.RollList.Sum(x => x.Weight);
    //         }
    //     }
    // }
}