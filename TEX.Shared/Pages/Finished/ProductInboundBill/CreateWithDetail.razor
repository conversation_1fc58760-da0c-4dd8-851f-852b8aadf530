@page "/Finished/ProductInboundBill/CreateWithDetail"
@using System.Reflection
@using TEX.Model.Models
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Finished.ProductInboundBillVMs;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.BillNo" />
        <div>
            <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="true" />
            <div class="input-group">
                <Select @bind-Value="@Model.Entity.POrderId" ShowSearch="true" ShowLabel="false" Items="@AllPurchaseOrders" OnSelectedItemChanged="@OnOrderSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <Button Icon="fa-solid fa-magnifying-glass" OnClick="@SelectPOrder"></Button>
            </div>
        </div>

        <Select @bind-Value="@Model.Entity.FinishingFactoryId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@product.ProductName" DisplayText="@WtmBlazor.Localizer["_Product"]" IsDisabled="true" />
        <BootstrapInput @bind-Value="@spec" DisplayText="@WtmBlazor.Localizer["_Spec"]" IsDisabled="true" />
        <BootstrapInput @bind-Value="@Model.Entity.Warehouse" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>

    <Table TItem="ProductInboundLot" @bind-Items="@DetailList" ShowRefresh="false" EditDialogSize="Size.Medium"
    IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true" ShowFooter="true" IsFixedFooter="true"
    ShowToastAfterSaveOrDeleteModel="false" ShowEditButton="false" ShowDeleteButton="false"
    EditMode="EditMode.Popup" IsMultipleSelect="false"
    ShowToolbar="true" ShowExtendButtons="true" IsBordered="true" IsFixedHeader="true"
    IsHideFooterWhenNoData="true" Height="300" style="margin:16px 0;">
        <TableColumns>
            <TableColumn @bind-Field="@context.OrderDetailId" Text="@WtmBlazor.Localizer["_Color"]" Lookup="orderdetailselecteditems" />
            <TableColumn @bind-Field="@context.LotNo" />
            <TableColumn @bind-Field="@context.Pcs" />
            <TableColumn @bind-Field="@context.Weight" />
            <TableColumn @bind-Field="@context.Meters" />
            <TableColumn @bind-Field="@context.Yards" />
            <TableColumn @bind-Field="@context.Location" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
        <EditTemplate>
            <EditTemplateRollList @bind-Lot="context" OrderId="@Model.Entity.POrderId"/>
        </EditTemplate>
        <TableToolbarTemplate>
            <TableToolbarButton TItem="ProductInboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" />
        </TableToolbarTemplate>

        <FooterTemplate>
            <TableFooterCell />
            <TableFooterCell Text="合计:" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Pcs)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Meters)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Weight)" />
            <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Yards)" />
            <TableFooterCell Colspan="3" />
        </FooterTemplate>


    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
    </div>

</ValidateForm>

@code {

    private ProductInboundBillVM Model = new ProductInboundBillVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();

    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }
    List<ProductInboundRoll> rollDetailList = new List<ProductInboundRoll>();
    public IEnumerable<ProductInboundRoll> RollDetailList
    {
        get { return rollDetailList; }
        set
        {
            rollDetailList = value.ToList();
        }
    }
    private async Task Submit(EditContext context)
    {
        if (DetailList.Any())
        {
            var list = DetailList.Select(x => (ProductInboundLot)x).ToList();
            Model.Entity.LotList = list;
            Model.Entity.Pcs = list.Sum(x => x.Pcs);
            Model.Entity.Meters = list.Sum(x => x.Meters);
            Model.Entity.Yards = list.Sum(x => x.Yards);
            Model.Entity.Weight = list.Sum(x => x.Weight);
        }
        await PostsForm(vform, "/api/ProductInboundBill/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }
    private ProductInboundLot selectLot = new();
    private List<ProductInboundLot> detailList = new List<ProductInboundLot>();
    public IEnumerable<ProductInboundLot> DetailList
    {
        get { return detailList; }
        set
        {
            detailList = value.ToList();
            // Model.Entity.Meters = detailList.Sum(x => x.Meters);
            // Model.Entity.Yards = detailList.Sum(x => x.Yards);
            // Model.Entity.Weight = detailList.Sum(x => x.Weight);
        }
    }

    private Product product { get; set; } = new();
    private string spec = "";
    private async Task OnOrderSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            await ProductOnSelectPOrder(item.Value);
        }
    }

    //订单选择后查询产品名称和规格
    public async Task ProductOnSelectPOrder(string id)
    {
        Model.Entity.POrderId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
        var p = rv.Data;
        product = p.Entity.Product;
        spec = "";
        if (product.GSM != null && product.GSM != 0)
        {
            spec += +product.GSM + "Gsm";
        }
        if (product.Width != null && product.Width != 0)
        {
            spec += " - " + product.Width + "CM";
        }
        await SearchOrderDetail();
        StateHasChanged();
    }

    private async Task<ProductInboundLot> OnSelectLotAsync()
    {
        if (Model.Entity.POrderId != Guid.Empty)
        {
            // Excel 模式下新建使用明细弹窗选择
            await SelectLot();

            //返回null,点取消时才不会默认添加一行
            return null;
        }
        else
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择订单!"]);
            return null;
        }
    }

    //配缸记录选择弹窗
    private List<LotAllocate_View> lotdetail { get; set; } = new();
    public async Task SelectLot()
    {
        var result = await WtmBlazor.Dialog.ShowModal<DialogSelectLot>(new ResultDialogOption()
            {
                Title = "请选择缸号",
                ButtonYesText = "确定",
                Size = Size.Large,
                //HeaderTemplate="",
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectLot.OrderId)] = Model.Entity.POrderId.ToString(),
                    [nameof(DialogSelectLot.SelectedDetail)] = lotdetail,
                    [nameof(DialogSelectLot.SelectedDetailChanged)] = EventCallback.Factory.Create<List<LotAllocate_View>>(this, v => lotdetail = v)
                }
            });
        ProductInboundLot detail = new();
        if (result == DialogResult.Yes)
        {
            foreach (var item in lotdetail)
            {
                //detail.OrderDetailId = Guid.Parse(orderdetailselecteditems.Where(x => x.Text == item.Color).First().Value);
                detail.OrderDetailId = item.ColorId;
                detail.Color = item.Color;
                detail.LotNo = item.LotNo;
                //detail.Pcs = item.Pcs;
                //detail.Meters = item.Meters;
                //detail.Weight = item.Weight;
                detailList.Insert(detailList.Count, detail);
                detail = new();
            }
            await Task.CompletedTask;
        }
    }

    //订单选择弹窗
    public PurchaseOrder_View porder { get; set; } = new();
    public async Task SelectPOrder()
    {
        var result = await WtmBlazor.Dialog.ShowModal<DialogSelectPOrders>(new ResultDialogOption()
            {
                Title = "请选择订单",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectPOrders.SelectedPurchaseOrder)] = porder,
                    [nameof(DialogSelectPOrders.SelectedPurchaseOrderChanged)] = EventCallback.Factory.Create<PurchaseOrder_View>(this, v => porder = v)
                }
            });

        if (result == DialogResult.Yes)
        {
            Model.Entity.POrderId = porder.ID;
            await ProductOnSelectPOrder(porder.ID.ToString());//订单选择后查询产品名称和规格
        }
    }

    //子表缸号颜色选择框数据源
    private List<SelectedItem> orderdetailselecteditems = new();
    OrderDetailDetailSearcher orderdetailSearcher = new();
    private async Task SearchOrderDetail()
    {
        QueryPageOptions opts = new();
        orderdetailSearcher.PurchaseOrderId = Model.Entity.POrderId.ToString();
        //查询订单明细,供选择颜色
        var od = await StartSearch<OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", orderdetailSearcher, opts);
        orderdetailselecteditems = od.Items.Select(x => new SelectedItem
            {
                Text = x.Color,
                Value = x.ID.ToString()
            }).ToList();
    }


    //子表Excel模式,更新方法
    private async Task<ProductInboundLot> OnAddAsync()
    {
        // var od = new ProductInboundLot();
        // if (Model.Entity.POrderId != Guid.Empty)
        // {
        //     od.ID = Guid.NewGuid();
        //     detailList.Insert(0, od);
        //     return od;
        // }
        // else
        // {
        //     await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择订单!"]);
        await Task.CompletedTask;
        return null;
        // }

    }

    private Task<bool> OnSaveAsync(ProductInboundLot item, ItemChangedType changedType)
    {
        Model.Entity.Meters = detailList.Sum(x => x.Meters);
        Model.Entity.Weight = detailList.Sum(x => x.Weight);
        Model.Entity.Pcs = detailList.Sum(x => x.Pcs);
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundLot> items)
    {
        detailList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }

    //编辑每缸卷数明细
    private async Task OnDetailsClick(ProductInboundLot lot)
    {
        if (lot.RollList is null) lot.RollList = new();
        var result = await WtmBlazor.Dialog.ShowModal<DialogEditRolls>(new ResultDialogOption()
            {
                Title = "缸号:" + lot.LotNo + "; 颜色:" + lot.Color,
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogEditRolls.lot)] = lot, //Lot传进去后可查询已有明细
                    [nameof(DialogEditRolls.Detail)] = lot.RollList,
                    [nameof(DialogEditRolls.DetailChanged)] = EventCallback.Factory.Create<List<ProductInboundRoll>>(this, v => lot.RollList = v)
                }
            });

        if (result == DialogResult.Yes)
        {
            if (lot.RollList.Any())
            {
                lot.Pcs = lot.RollList.Count();
                lot.Meters = lot.RollList.Sum(x => x.Meters);
                lot.Yards = lot.RollList.Sum(x => x.Yards);
                lot.Weight = lot.RollList.Sum(x => x.Weight);
            }
        }
    }
}
