@using System.Diagnostics.CodeAnalysis;
@using TEX.Model.Models;
@inherits BasePage

<Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">

    <BootstrapInput @bind-Value="@lot.Color" />
    <BootstrapInput @bind-Value="@lot.ColorCode" />
    <BootstrapInput @bind-Value="@lot.LotNo" />
    <BootstrapInputNumber @bind-Value="@lot.Pcs" />
    <BootstrapInputNumber @bind-Value="@lot.Weight" />
    <BootstrapInputNumber @bind-Value="@lot.Meters" />
    <BootstrapInputNumber @bind-Value="@lot.Yards" />
    <BootstrapInput @bind-Value="@lot.Location" />
    <BootstrapInput @bind-Value="@lot.TenantCode" />
    <BootstrapInput @bind-Value="@lot.Remark" />
</Row>


@code {
    //ToDo:EFcore支持主子孙多级同时保存,子表中可以再增加一个孙表来实现:入库单-入库缸-入库卷

    [Parameter]
    public string id { get; set; }


    //父子组件双向绑定
    [Parameter]
    public ProductInboundLot Lot { get; set; }
    [Parameter]
    public EventCallback<ProductInboundLot> LotChanged { get; set; }
    //子组件中需要定义一个父组件传参类型相同的属性,用来接受传参和变化是通知父组件
    //InvokeAsync回调放OnParameterSet中是不可以的,会造成死循环

    
    public ProductInboundLot lot
    {
        get => Lot;
        set
        {
            //当数据修改后,和父传参不同则使用LotChanged事件回调,让父组件接收
            if (Lot != value)
                Lot = value;

            LotChanged.InvokeAsync(Lot);
        }
    }
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/ProductInboundBill/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        await base.OnInitializedAsync();
    }

}
