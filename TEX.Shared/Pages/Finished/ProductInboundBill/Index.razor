@page "/Finished/ProductInboundBill"
@using TEX.ViewModel.Finished.ProductInboundBillVMs;
@inherits BasePage
@attribute [ActionDescription("成品入库单", "TEX.Controllers,ProductInboundBill")]
@inject NavigationManager Navigation

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@SearchModel.CreateDate"  />
            <Select @bind-Value="@SearchModel.POrderId" ShowSearch="true" DisplayText="订单" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.FinishingFactoryId" ShowSearch="true" DisplayText="染整厂" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.Wearhouse"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="ProductInboundBill_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.CreateDate" Text="入库日期" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.BillNo"  />
        <TableColumn @bind-Field="@context.Customer_view" />
        <TableColumn @bind-Field="@context.CustomerOrderNo_view" />
        <TableColumn @bind-Field="@context.OrderNo_view"  />
        <TableColumn @bind-Field="@context.ProductName_view"  />
        <TableColumn @bind-Field="@context.Pcs"  />
        <TableColumn @bind-Field="@context.Weight"  />
        <TableColumn @bind-Field="@context.Meters"  />
        <TableColumn @bind-Field="@context.Yards"  />
        <TableColumn @bind-Field="@context.FinishingFactory_view" />
        <TableColumn @bind-Field="@context.Warehouse" />
        <TableColumn @bind-Field="@context.AuditStatus"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/ProductInboundBill/Add"))
        {
            <TableToolbarButton TItem="ProductInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/ProductInboundBill/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="ProductInboundBill_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/ProductInboundBill/Import"))
        {
            <TableToolbarButton TItem="ProductInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/ProductInboundBill/ExportExcel"))
        {
            <TableToolbarButton TItem="ProductInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/ProductInboundBill/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/ProductInboundBill/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/ProductInboundBill/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private ProductInboundBillSearcher SearchModel = new ProductInboundBillSearcher();
    private Table<ProductInboundBill_View> dataTable;

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.All"]);
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<ProductInboundBill_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<ProductInboundBill_View>("/api/ProductInboundBill/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<ProductInboundBill_View> items)
    {
        // if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        // {
        //     await dataTable.QueryAsync();
        // }
        Navigation.NavigateTo("/Finished/ProductInboundBill/Create"); //打开新页面
        await Task.CompletedTask;
    }

    private async Task OnEditClick(ProductInboundBill_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(ProductInboundBill_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["_ProductInboundBill"]+WtmBlazor.Localizer["_Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/ProductInboundBill/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(ProductInboundBill_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/ProductInboundBill/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<ProductInboundBill_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/ProductInboundBill/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/ProductInboundBill/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<ProductInboundBill_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
