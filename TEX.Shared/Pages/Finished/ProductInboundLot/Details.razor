@page "/Finished/ProductInboundLot/Details/{id}"
@using TEX.ViewModel.Finished.ProductInboundLotVMs;
@using TEX.Model.Models;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <Display @bind-Value="@Model.Entity.InboundBillId" Lookup="@AllProductInboundBills" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.OrderDetailId" Lookup="@AllOrderDetails" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Color" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.ColorCode" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.LotNo" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Location" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Remark" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Pcs" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Weight" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Meters" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Yards" ShowLabel="true" />

    </Row>
    <Table @bind-Items="DetailList" TableSize="TableSize.Compact" style="margin-top:15px;"
           IsStriped="true" IsBordered="true" ShowRefresh="false">
        <TableColumns>
            <TableColumn @bind-Field="@context.RollNo" />
            <TableColumn @bind-Field="@context.Weight" />
            <TableColumn @bind-Field="@context.Meters" />
            <TableColumn @bind-Field="@context.Yards" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductInboundLotVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProductInboundBills = new List<SelectedItem>();

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductInboundBills = await WtmBlazor.Api.CallItemsApi("/api/ProductInboundLot/GetProductInboundBills", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductInboundLotVM>($"/api/ProductInboundLot/{id}");
        Model = rv.Data;

        //查询明细
        ViewModel.Finished.ProductInboundRollVMs.ProductInboundRollSearcher rollSearcher = new();
        rollSearcher.LotId = Model.Entity.ID;
        QueryPageOptions opt = new();
        var rq = await StartSearch<ProductInboundRoll>("/api/ProductInboundRoll/Search", rollSearcher, opt);
        Detail = rq.Items.ToList();
    }

    public void OnClose()
    {
        CloseDialog();
    }

    public List<ProductInboundRoll> Detail { get; set; }
    public IEnumerable<ProductInboundRoll> DetailList
    {
        get { return Detail; }
        set
        {
            Detail = value.ToList();
        }
    }
}
