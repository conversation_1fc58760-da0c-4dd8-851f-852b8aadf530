@page "/Finished/ProductInboundLot/Edit/{id}"
@using System.Reflection
@using TEX.ViewModel.Finished.ProductInboundLotVMs;
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <Select @bind-Value="@Model.Entity.InboundBillId" Items="@AllProductInboundBills" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        @* <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" /> *@
        <BootstrapInput @bind-Value="@Model.Entity.Color" />
        <BootstrapInput @bind-Value="@Model.Entity.ColorCode" />
        <BootstrapInput @bind-Value="@Model.Entity.LotNo" />
        <BootstrapInput @bind-Value="@Model.Entity.Location" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
        <BootstrapInput @bind-Value="@Model.Entity.Pcs" IsDisabled="true"/>
        <BootstrapInput @bind-Value="@Model.Entity.Weight" IsDisabled="true" />
        <BootstrapInput @bind-Value="@Model.Entity.Meters" IsDisabled="true" />
        <BootstrapInput @bind-Value="@Model.Entity.Yards" IsDisabled="true" />
        
    </Row>
    <Table @bind-Items="DetailList"  TableSize="TableSize.Compact"
           IsStriped="true" IsBordered="true" IsExcel="true" ShowToolbar="true" ShowRefresh="false"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           ShowExtendButtons="true" ShowFooter="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.RollNo"  />
            <TableColumn @bind-Field="@context.Weight" />
            <TableColumn @bind-Field="@context.Meters" />
            <TableColumn @bind-Field="@context.Yards" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
        <FooterTemplate>
            <tr style="text-align: center;font-weight:bold;">
                <td colspan="2">
                    @* <div style="line-height: 2;"> *@
                        合计：共 @context.Count() 卷
                    @* </div> *@
                </td>
                <td>
                    <div>
                        @GetSum(context, "Meters")
                    </div>
                </td>
                <td>
                    <div>
                        @GetSum(context, "Yards")
                    </div>
                </td>
                <td>
                    <div>
                        @GetSum(context, "Weight")
                    </div>
                </td>
                <td>
                </td>
                <td>
                </td>

            </tr>

        </FooterTemplate>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ProductInboundLotVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    [Parameter]
    public List<ProductInboundRoll> Detail { get; set; }
    [Parameter]
    public EventCallback<List<ProductInboundRoll>> DetailChanged { get; set; }

    private List<SelectedItem> AllProductInboundBills = new List<SelectedItem>();
    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductInboundBills = await WtmBlazor.Api.CallItemsApi("/api/ProductInboundLot/GetProductInboundBills", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductInboundLotVM>($"/api/ProductInboundLot/{id}");
        Model = rv.Data;

        // ProductInboundRollSearcher rollSearcher = new();
        // rollSearcher.LotId = Model.Entity.ID;
        // QueryPageOptions opt = new();
        // var rq = await StartSearch<ProductInboundRoll>("/api/ProductInboundRoll/Search", rollSearcher, opt);
        // Detail = rq.Items.ToList();
        Detail = Model.Entity.RollList?? new List<ProductInboundRoll>();
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/ProductInboundLot/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }
    //使用反射对实体类的某一个字段求和
    private static decimal GetSum(IEnumerable<ProductInboundRoll> items, string fieldName)
    {
        if (!items.Any()) return 0;
        PropertyInfo property = typeof(ProductInboundRoll).GetProperty(fieldName);
        if (property == null) throw new ArgumentException("Field not found", fieldName);
        return items.Sum(i => Convert.ToDecimal(property.GetValue(i)));
    }
    public void OnClose()
    {
        CloseDialog();
    }
    public IEnumerable<ProductInboundRoll> DetailList
    {
        get { return Detail; }
        set
        {
            Detail = value.ToList();
        }
    }
    //子表Excel模式,更新方法
    private async Task<ProductInboundRoll> OnAddAsync()
    {
        var od = new ProductInboundRoll();
        if (Detail.Count > 0)
        {
            od.RollNo = Detail.Max(x => x.RollNo) + 1;
        }
        else
        {
            od.RollNo = 1;
        }
        Detail.Insert(Detail.Count(), od);
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        // 对象已经更新
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundRoll> items)
    {
        Detail.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
}
