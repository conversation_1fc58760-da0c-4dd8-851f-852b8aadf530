@page "/Finished/Statistic"
@using TEX.ViewModel.Finished.ProductInboundBillVMs;
@using TEX.Model.Statistics;
@using TEX.Shared.Components;
@using TEX.ViewModel.Finished.ProductStockVMs
@inherits BasePage
@attribute [ActionDescription("成品统计", "TEX.Controllers,ProductStatistic")]
@inject NavigationManager Navigation

<!-- 搜索面板 -->

<ValidateForm Model="@SearchModel">
    <Card IsShadow="true" HeaderText="查询条件">
        <HeaderTemplate>
            <span>这里是模板</span>
            <Button Color="Color.Primary"  Icon="fas fa-table" Text="查询" OnClick="DoSearch" style="margin:0 10px" />
            <Button Color="Color.Warning" Icon="fas fa-table" Text="清除" OnClick="ToggleDetailView" />
        </HeaderTemplate>
        <BodyTemplate>
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
                <Select @bind-Value="@SearchModel.CustomerId" ShowSearch="true" DisplayText="客户" Items="@AllCustomers" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
                <Select @bind-Value="@SearchModel.POrderId" ShowSearch="true" DisplayText="订单" Items="@AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
                @*<Select @bind-Value="@SearchModel.FinishingFactoryId" ShowSearch="true" DisplayText="染整厂" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
                 <WTDateRange @bind-Value="@SearchModel.CreateDate" /> *@
                <BootstrapInput @bind-Value="@SearchModel.Wearhouse" />
            </Row>
            
        </BodyTemplate>
    </Card>
</ValidateForm>


<!-- 统计卡片区域 -->
<div class="row g-3 mt-2">
    <StatisticsCard Title="总件数"
                    DisplayValue="@(CurrentStatistics?.TotalPcs ?? 0)"
                    Unit="件"
                    IconClass="fas fa-boxes"
                    IconColor="primary"
                    IsLoading="@IsLoadingStatistics"
                    OnClick='() => OnStatisticsCardClick("Pcs")' />

    <StatisticsCard Title="总重量"
                    DisplayValue="@(CurrentStatistics?.TotalWeight ?? 0)"
                    Unit="KG"
                    IconClass="fas fa-weight-hanging"
                    IconColor="success"
                    IsLoading="@IsLoadingStatistics"
                    OnClick='() => OnStatisticsCardClick("Weight")' />

    <StatisticsCard Title="总米数"
                    DisplayValue="@(CurrentStatistics?.TotalMeters ?? 0)"
                    Unit="米"
                    IconClass="fas fa-ruler-horizontal"
                    IconColor="warning"
                    IsLoading="@IsLoadingStatistics"
                    OnClick='() => OnStatisticsCardClick("Meters")' />

    <StatisticsCard Title="总码数"
                    DisplayValue="@(CurrentStatistics?.TotalYards ?? 0)"
                    Unit="码"
                    IconClass="fas fa-ruler"
                    IconColor="info"
                    IsLoading="@IsLoadingStatistics"
                    OnClick='() => OnStatisticsCardClick("Yards")' />
</div>




<!-- 明细列表（可切换显示） -->
@if (ShowDetailTable)
{
    <div class="mt-3">
        <Card IsShadow="true" HeaderText="入库明细列表">
            <BodyTemplate>
                <Table @ref="dataTable" TItem="ProductInboundBill_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
                       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false">
                    <TableColumns>
                        <TableColumn @bind-Field="@context.CreateDate" Text="入库日期" FormatString="yyyy-MM-dd" />
                        <TableColumn @bind-Field="@context.BillNo" />
                        <TableColumn @bind-Field="@context.Customer_view" />
                        <TableColumn @bind-Field="@context.CustomerOrderNo_view" />
                        <TableColumn @bind-Field="@context.OrderNo_view" />
                        <TableColumn @bind-Field="@context.ProductName_view" />
                        <TableColumn @bind-Field="@context.Pcs" />
                        <TableColumn @bind-Field="@context.Weight" />
                        <TableColumn @bind-Field="@context.Meters" />
                        <TableColumn @bind-Field="@context.Yards" />
                        <TableColumn @bind-Field="@context.FinishingFactory_view" />
                        <TableColumn @bind-Field="@context.Warehouse" />
                        <TableColumn @bind-Field="@context.AuditStatus" />
                        <TableColumn @bind-Field="@context.Remark" />
                    </TableColumns>
                    <TableToolbarTemplate>
                        @if (IsAccessable("/api/ProductInboundBill/Add"))
                        {
                            <TableToolbarButton TItem="ProductInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
                        }
                        @if (IsAccessable("/api/ProductInboundBill/BatchDelete"))
                        {
                            <TableToolbarPopConfirmButton TItem="ProductInboundBill_View" Color="Color.Primary"
                                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
                        }
                        @if (IsAccessable("/api/ProductInboundBill/ExportExcel"))
                        {
                            <TableToolbarButton TItem="ProductInboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
                        }
                    </TableToolbarTemplate>
                    <RowButtonTemplate>
                        <div style="padding-right:10px;">

                            @if (IsAccessable("/api/ProductInboundBill/BatchDelete"))
                            {
                                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
                            }
                        </div>
                    </RowButtonTemplate>
                </Table>
            </BodyTemplate>
        </Card>
    </div>
}

<!-- 钻取查询弹窗 -->
<Modal @ref="DrillDownModal">
    <ModalDialog Title="@DrillDownModalTitle" Size="Size.ExtraLarge">
        <BodyTemplate>
            @if (DrillDownData != null && DrillDownData.Any())
            {
                <Table TItem="ProductInboundBill_View" Items="@DrillDownData" IsPagination="true" IsStriped="true" IsBordered="true">
                    <TableColumns>
                        <TableColumn @bind-Field="@context.CreateDate" Text="入库日期" FormatString="yyyy-MM-dd" />
                        <TableColumn @bind-Field="@context.BillNo" />
                        <TableColumn @bind-Field="@context.Customer_view" />
                        <TableColumn @bind-Field="@context.CustomerOrderNo_view" />
                        <TableColumn @bind-Field="@context.OrderNo_view" />
                        <TableColumn @bind-Field="@context.ProductName_view" />
                        <TableColumn @bind-Field="@context.Pcs" />
                        <TableColumn @bind-Field="@context.Weight" />
                        <TableColumn @bind-Field="@context.Meters" />
                        <TableColumn @bind-Field="@context.Yards" />
                    </TableColumns>
                </Table>
            }
            else
            {
                <Empty Text="暂无明细数据" />
            }
        </BodyTemplate>
        <FooterTemplate>
            <Button Color="Color.Secondary" OnClick="() => DrillDownModal.Close()">关闭</Button>
        </FooterTemplate>
    </ModalDialog>
</Modal>

@code {
    // 原有字段
    private ProductStockSearcher SearchModel = new ();
    private Table<ProductInboundBill_View> dataTable;
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllCompanys = new List<SelectedItem>();
    private List<SelectedItem> AllCustomers = new List<SelectedItem>();

    // 统计功能相关字段
    private InboundStatisticsResult CurrentStatistics;
    private bool IsLoadingStatistics = false;
    private bool IsLoadingGroupedStatistics = false;
    private bool ShowDetailTable = false;


    // 钻取查询相关
    private Modal DrillDownModal;
    private string DrillDownModalTitle = "";
    private List<ProductInboundBill_View> DrillDownData = new List<ProductInboundBill_View>();

    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/ProductStatistic/GetStockOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        //AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetFinishingFactorys", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/getcustomercompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        // 初始加载统计数据
        await LoadStatisticsData();
        //await LoadGroupedStatisticsData();

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<ProductInboundBill_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<ProductInboundBill_View>("/api/ProductInboundBill/Search", SearchModel, opts);
    }

    private async void DoSearch()
    {
        // 重新加载统计数据
        await LoadStatisticsData();

        // 如果显示明细表格，也刷新明细数据
        if (ShowDetailTable && dataTable != null)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnCreateClick(IEnumerable<ProductInboundBill_View> items)
    {
        // if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        // {
        //     await dataTable.QueryAsync();
        // }
        Navigation.NavigateTo("/Finished/ProductInboundBill/Create"); //打开新页面
        await Task.CompletedTask;
    }




    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/ProductInboundBill/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(ProductInboundBill_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/ProductInboundBill/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<ProductInboundBill_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/ProductInboundBill/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/ProductInboundBill/ExportExcel", SearchModel);
        }
    }

    #region 统计功能方法

    /// <summary>
    /// 加载统计数据
    /// </summary>
    private async Task LoadStatisticsData()
    {
        IsLoadingStatistics = true;
        StateHasChanged();

        try
        {
            var response = await WtmBlazor.Api.CallAPI<InboundStatisticsResult>("/api/ProductInboundBill/GetStatistics", HttpMethodEnum.POST, SearchModel);
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                CurrentStatistics = response.Data;
            }
        }
        catch (Exception ex)
        {
            await WtmBlazor.Toast.Error("加载统计数据失败", ex.Message);
        }
        finally
        {
            IsLoadingStatistics = false;
            StateHasChanged();
        }
    }


    /// <summary>
    /// 统计卡片点击事件
    /// </summary>
    private async Task OnStatisticsCardClick(string cardType)
    {
        await WtmBlazor.Toast.Information("统计卡片点击", $"点击了{cardType}统计卡片，可以实现进一步的钻取功能");
    }

    /// <summary>
    /// 钻取查询事件
    /// </summary>
    private async Task OnDrillDownClick(GroupedStatisticsResult groupedResult)
    {
        try
        {
            var drillDownParams = new DrillDownParams
            {
                GroupKey = groupedResult.GroupKey,
                GroupType = groupedResult.GroupType,
                OriginalSearchParams = System.Text.Json.JsonSerializer.Serialize(SearchModel),
                PageIndex = 1,
                PageSize = 20
            };
            ProductInboundBillSearcher searcher = new ProductInboundBillSearcher()
            {

            };

            var response = await WtmBlazor.Api.CallAPI<QueryData<ProductInboundBill_View>>("/api/ProductInboundBill/GetDetailsByDrillDown", HttpMethodEnum.POST, drillDownParams);
            if (response.StatusCode == System.Net.HttpStatusCode.OK && response.Data?.Items != null)
            {
                DrillDownData = response.Data.Items.ToList();
                DrillDownModalTitle = $"{groupedResult.GroupName} - 明细数据";
                await DrillDownModal.Show();
            }
            else
            {
                await WtmBlazor.Toast.Warning("钻取查询", "暂无明细数据");
            }
        }
        catch (Exception ex)
        {
            await WtmBlazor.Toast.Error("钻取查询失败", ex.Message);
        }
    }

    /// <summary>
    /// 切换明细表格显示
    /// </summary>
    private void ToggleDetailView()
    {
        ShowDetailTable = !ShowDetailTable;
        StateHasChanged();
    }



    #endregion

}
