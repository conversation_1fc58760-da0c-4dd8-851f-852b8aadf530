@page "/Finished/ProductStock"
@using TEX.ViewModel.Finished.ProductStockVMs;
@inherits BasePage
@attribute [ActionDescription("成品库存", "TEX.Controllers,ProductStock")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <Select @bind-Value="@SearchModel.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.Wearhouse"  />
            <BootstrapInput @bind-Value="@SearchModel.Location"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="ProductStock_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.Customer_view" />
        <TableColumn @bind-Field="@context.OrderNo_view" />
        <TableColumn @bind-Field="@context.ProductName_view" />
        <TableColumn @bind-Field="@context.ProductSpec_view" />
        <TableColumn @bind-Field="@context.Color_view"  />
        <TableColumn @bind-Field="@context.TotalPcs"  />
        <TableColumn @bind-Field="@context.TotalWeight"  />
        <TableColumn @bind-Field="@context.TotalMeters"  />
        <TableColumn @bind-Field="@context.TotalYards"  />
        <TableColumn @bind-Field="@context.Wearhouse"  />
        <TableColumn @bind-Field="@context.Location"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/ProductStock/Add"))
        {
            <TableToolbarButton TItem="ProductStock_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
@*         @if (IsAccessable("/api/ProductStock/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="ProductStock_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/ProductStock/Import"))
        {
            <TableToolbarButton TItem="ProductStock_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        } *@
        @if (IsAccessable("/api/ProductStock/ExportExcel"))
        {
            <TableToolbarButton TItem="ProductStock_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/ProductStock/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/ProductStock/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private ProductStockSearcher SearchModel = new ProductStockSearcher();
    private Table<ProductStock_View> dataTable;

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<ProductStock_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<ProductStock_View>("/api/ProductStock/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<ProductStock_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(ProductStock_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(ProductStock_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    // private async Task OnBatchDeleteClick()
    // {
    //     if (dataTable.SelectedRows?.Any() == true)
    //     {
    //         await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/ProductStock/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
    //         await dataTable.QueryAsync();
    //     }
    //     else
    //     {
    //         await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
    //     }
    // }

    // private async Task OnDeleteClick(ProductStock_View item)
    // {
    //     await PostsData(new List<string> { item.ID.ToString() }, $"/api/ProductStock/batchdelete", (s) => "Sys.OprationSuccess");
    //     await dataTable.QueryAsync();
    // }


    private async Task OnExportClick(IEnumerable<ProductStock_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/ProductStock/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/ProductStock/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<ProductStock_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
