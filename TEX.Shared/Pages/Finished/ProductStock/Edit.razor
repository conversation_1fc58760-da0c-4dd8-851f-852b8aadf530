@page "/Finished/ProductStock/Edit/{id}"
@using TEX.ViewModel.Finished.ProductStockVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalPcs"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalWeight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalMeters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.TotalYards"  />
            <BootstrapInput @bind-Value="@Model.Entity.Wearhouse"  />
            <BootstrapInput @bind-Value="@Model.Entity.Location"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ProductStockVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductStockVM>($"/api/ProductStock/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/ProductStock/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
