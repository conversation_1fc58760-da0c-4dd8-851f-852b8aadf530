@page "/Finished/ProductStock/Details/{id}"
@using TEX.ViewModel.Finished.ProductStockVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.OrderDetailId" Lookup="@AllOrderDetails"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalPcs"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalWeight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalMeters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.TotalYards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Wearhouse"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Location"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductStockVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetOrderDetails", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductStockVM>($"/api/ProductStock/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
