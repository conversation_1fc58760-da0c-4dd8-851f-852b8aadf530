@page "/Finished/ProductOutboundBill/Edit/{id}"
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Finished.ProductOutboundBillVMs;
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.BillNo" IsDisabled="true" />
        <Select @bind-Value="@Model.Entity.CustomerId" Items="@AllCustomers" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" IsDisabled="true" />
        <Select @bind-Value="@Model.Entity.ReceiverId" Items="@AllReceivers" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.Pcs" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.Weight" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.Meters" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.Yards" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div style="margin:20px 0;">
        <Table TItem="ProductOutboundLot_View" @bind-Items="@DetailList" ShowRefresh="false" ShowExtendDeleteButton="false"
        OnAddAsync="@OnAddAsync" ShowDeleteButton="true"
        IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true" ShowFooter="true" IsFixedFooter="false"
        IsExcel="false" ShowDefaultButtons="true"
        ShowAddButton="false"
        ShowEditButton="false"
        IsHideFooterWhenNoData="true" IsMultipleSelect="true"
        ShowExtendButtons="true" ShowExtendEditButton="false"
        ShowToolbar="true" IsBordered="true"
        IsFixedHeader="true" Height="400" ExtendButtonColumnWidth="100">
            <TableColumns>
                <TableColumn @bind-Field="@context.OrderNo_view" Readonly="true" Width="120" />
                <TableColumn @bind-Field="@context.Product_view" Readonly="true" Width="120" />
                <TableColumn @bind-Field="@context.Color_view" Readonly="true" Width="60" />
                <TableColumn @bind-Field="@context.LotNo" Width="60" />
                <TableColumn @bind-Field="@context.Pcs" Width="40" />
                @if (@DetailList.Any(x => x.Meters > 0))
                {
                    <TableColumn @bind-Field="@context.Meters" Width="60" />
                }
                @if (@DetailList.Any(x => x.Yards > 0))
                {
                    <TableColumn @bind-Field="@context.Yards" Width="60" />
                }
                @if (@DetailList.Any(x => x.Weight > 0))
                {
                    <TableColumn @bind-Field="@context.Weight" Width="60" />
                }
                <TableColumn @bind-Field="@context.Remark" Width="80" />
            </TableColumns>
            <TableExtensionToolbarTemplate>
                <Switch Value="true" OnColor="Color.Success" OffColor="Color.Secondary" OnText="手工填写关" OffText="手工填写开" style="margin-right:8px"></Switch>
            </TableExtensionToolbarTemplate>
            <TableToolbarTemplate>
                <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" />
            </TableToolbarTemplate>
            <RowButtonTemplate>
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
                @* <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Text="复制" OnClick="()=>OnDetailsCopy(context)" /> *@
            </RowButtonTemplate>

            <TableFooter>
                <TableFooterCell />
                <TableFooterCell Text="合计:" />
                <TableFooterCell ColspanCallback="(items) => 2" />
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Pcs)" />
                @if (@DetailList.Any(x => x.Meters > 0))
                {
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Meters)" />

                }
                @if (@DetailList.Any(x => x.Yards > 0))
                {
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Yards)" />

                }
                @if (@DetailList.Any(x => x.Weight > 0))
                {
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Weight)" />
                }
                <TableFooterCell />
                <TableFooterCell />
            </TableFooter>
        </Table>
    </div>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ProductOutboundBillVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllReceivers = new List<SelectedItem>();

    private List<ProductOutboundLot_View> detailList = new List<ProductOutboundLot_View>();
    public IEnumerable<ProductOutboundLot_View> DetailList
    {
        get { return detailList; }
        set
        {
            detailList = value.ToList();
        }
    }

    private ProductOutboundLotSearcher SearchModel = new ProductOutboundLotSearcher();

    protected override async Task OnInitializedAsync()
    {
        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundBillVM>($"/api/ProductOutboundBill/{id}");
        Model = rv.Data;

        AllReceivers = await WtmBlazor.Api.CallItemsApi($"/api/DeliveryAddress/GetDeliveryAddress/{Model.Entity.CustomerId.ToString()}", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var bill = await WtmBlazor.Api.CallAPI<ProductOutboundBill_View>($"/api/ProductOutboundBill/GetWithRolls/{id}");

        detailList = bill.Data.Details;
        //detailList = await OnSearchLot();
        await base.OnInitializedAsync();
    }

    private async Task<List<ProductOutboundLot_View>> OnSearchLot()
    {
        SearchModel.OutboundBillId = Guid.Parse(id);
        var rv = await WtmBlazor.Api.CallSearchApi<ProductOutboundLot_View>("/api/ProductOutboundLot/Search", SearchModel, new QueryPageOptions());
        return rv.Data.Data;
    }

    private async Task Submit(EditContext context)
    {
        var list = DetailList.Select(x => (TEX.Model.Models.ProductOutboundLot)x).ToList();
        Model.Entity.LotList = list;
        Model.Entity.Pcs = list.Sum(x => x.Pcs);
        Model.Entity.Meters = list.Sum(x => x.Meters);
        Model.Entity.Yards = list.Sum(x => x.Yards);
        Model.Entity.Weight = list.Sum(x => x.Weight);
        await PostsForm(vform, $"/api/ProductOutboundBill/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    /// <summary>
    /// 选择库存按钮动作
    /// </summary>
    /// <returns></returns>
    private async Task<ProductOutboundLot_View> OnSelectLotAsync()
    {
        if (Model.Entity.CustomerId != Guid.Empty)
        {

            await SelectInboundLot(Model.Entity.CustomerId.ToString());

            //返回null,点取消时才不会默认添加一行
            return null;
        }
        else
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择订单!"]);
            return null;
        }
    }

    private List<ProductOutboundLot_View> SelectedLot { get; set; } = new();
    public async Task SelectInboundLot(string id)
    {
        var result = await WtmBlazor.Dialog.ShowModal<DialogOutSelectLot>(new ResultDialogOption()
            {
                Title = "请选择库存缸号",
                IsDraggable = true,
                IsCentered = true,
                IsKeyboard = true,
                ShowCloseButton = false,
                ShowMaximizeButton = true,
                ButtonYesText = "确定",
                Size = Size.ExtraExtraLarge,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(id)] = id,
                    [nameof(DialogOutSelectLot.SelectedLot)] = SelectedLot,
                    [nameof(DialogOutSelectLot.SelectedLotChanged)] = EventCallback.Factory.Create<List<ProductOutboundLot_View>>(this, v => SelectedLot = v)
                }
            });
        if (result == DialogResult.Yes)
        {
            if (SelectedLot.Any())
            {
                detailList.AddRange(SelectedLot);
            }
        }

    }

    private async Task OnDetailsCopy(ProductOutboundLot_View lot)
    {
        //只复制订单明细信息,不复制明细数据
        ProductOutboundLot_View newLot = new()
            {
                ID = Guid.NewGuid(),
                OrderDetailId = lot.OrderDetailId,
                OrderNo_view = lot.OrderNo_view,
                Product_view = lot.Product_view,
                Color = lot.Color,
                // LotNo = lot.LotNo,
                // Pcs = lot.Pcs,
                // Meters = lot.Meters,
                // Yards = lot.Yards,
                // Weight = lot.Weight,
                // Remark = lot.Remark
            };
        detailList.Insert(detailList.Count, newLot);
        await Task.CompletedTask;
    }

    //编辑每缸卷数明细
    private async Task OnDetailsClick(ProductOutboundLot_View lot)
    {
        if (lot.RollList is null) lot.RollList = new();
        var result = await WtmBlazor.Dialog.ShowModal<DialogEditOutRolls>(new ResultDialogOption()
            {
                Title = "缸号:" + lot.LotNo + "; 颜色:" + lot.Color,
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogEditOutRolls.lot)] = lot,
                    [nameof(DialogEditOutRolls.Detail)] = lot.RollList,
                    [nameof(DialogEditOutRolls.DetailChanged)] = EventCallback.Factory.Create<List<TEX.Model.Models.ProductOutboundRoll>>(this, v => lot.RollList = v)
                }
            });

        if (result == DialogResult.Yes)
        {
            if (lot.RollList.Any())
            {
                lot.Pcs = lot.RollList.Count();
                lot.Meters = lot.RollList.Sum(x => x.Meters);
                lot.Yards = lot.RollList.Sum(x => x.Yards);
                lot.Weight = lot.RollList.Sum(x => x.Weight);
            }
        }
    }

    private async Task OnCustomerSelect(SelectedItem item)
    {
        Model.Entity.ReceiverId = Guid.Empty;
        AllReceivers = await WtmBlazor.Api.CallItemsApi($"/api/DeliveryAddress/GetDeliveryAddress/{item.Value}", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        StateHasChanged();
    }

    //新增每缸卷数明细
    private async Task<ProductOutboundLot_View> OnAddAsync()
    {
        if (Model.Entity.CustomerId == Guid.Empty)
        {
            //return await Task.FromResult(new ProductOutboundLot_View());
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择客户!"]);
            return null;
        }
        ProductOutboundLot_View od = new();
        await SelectPOrderDetail();

        if (orderDetail_View is not null)
        {
            foreach (var item in orderDetail_View)
            {
                od = new ProductOutboundLot_View()
                    {
                        OrderDetailId = item.ID,
                        OrderNo_view = item.OrderDetail_PurchaseOrder,
                        Product_view = item.OrderDetail_ProductName,
                        Color = item.Color
                    };
                if (detailList is null) detailList = new();
                od.ID = Guid.NewGuid();
                detailList.Insert(DetailList.Count(), od);
            }

        }

        //有async关键字就需要加await
        return await Task.FromResult(od);
    }

    //订单明细选择弹窗
    public List<OrderDetail_View> orderDetail_View { get; set; }


    public async Task SelectPOrderDetail()
    {
        OrderDetailSearcher Searcher = new();
        Searcher.CustomerId = Model.Entity.CustomerId;
        var result = await WtmBlazor.Dialog.ShowModal<DialogSelectOrderDetail>(new ResultDialogOption()
            {
                Title = "请选择订单明细",
                ButtonYesText = "确定",
                Size = Size.Large,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(DialogSelectOrderDetail.Searcher)] = Searcher,
                    [nameof(DialogSelectOrderDetail.OrderDetail_View)] = orderDetail_View,
                    [nameof(DialogSelectOrderDetail.OrderDetail_ViewChanged)] = EventCallback.Factory.Create<List<OrderDetail_View>>(this, v => orderDetail_View = v)
                }
            });
        if (result == DialogResult.Yes)
        {

        }
    }
}
