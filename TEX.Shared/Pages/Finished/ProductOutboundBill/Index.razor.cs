using BootstrapBlazor.Components;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.ViewModel.Finished.ProductOutboundBillVMs;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using WalkingTec.Mvvm.Core;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Finished.ProductOutboundBill
{
    public partial class Index:BasePage
    {

        private ProductOutboundBillSearcher SearchModel = new ProductOutboundBillSearcher();
        private ProductOutboundLotSearcher LotSearchModel = new();
        private Table<ProductOutboundBill_View> dataTable;

        private List<SelectedItem> AllCustomers = new List<SelectedItem>();
        private List<SelectedItem> AllReceivers = new List<SelectedItem>();


        protected override async Task OnInitializedAsync()
        {

            AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
            AllReceivers = await WtmBlazor.Api.CallItemsApi($"/api/DeliveryAddress/GetDeliveryAddress", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
            await base.OnInitializedAsync();
        }

        private async Task<QueryData<ProductOutboundBill_View>> OnSearch(QueryPageOptions opts)
        {
            return await StartSearch<ProductOutboundBill_View>("/api/ProductOutboundBill/Search", SearchModel, opts);
        }



        private async Task<IEnumerable<ProductOutboundLot_View>> OnLotSearch(Guid id)
        {
            QueryPageOptions opts = new();
            LotSearchModel.OutboundBillId = id;
            var rv = await WtmBlazor.Api.CallAPI<List<ProductOutboundLot_View>>("/api/ProductOutboundLot/Search", HttpMethodEnum.POST, LotSearchModel);
            return rv.Data;
        }

        private void DoSearch()
        {
            dataTable.QueryAsync();
        }

        private async Task OnCreateClick(IEnumerable<ProductOutboundBill_View> items)
        {
            if (await OpenDialog<Create>("新建出库单")
            == DialogResult.Yes)
            {
                await dataTable.QueryAsync();
            }

            //Navigation.NavigateTo("/Finished/ProductOutboundBill/Create");//单独开页面,提交后页面不会自动关闭,交互不好

            //var op = new ResultDialogOption()
            //{
            //    Title = "新建出库单",
            //    ShowFooter = true,
            //    IsDraggable = true,
            //    ShowCloseButton = false,

            //};
            //op.Component = BootstrapDynamicComponent.CreateComponent<Create>(new Dictionary<string, object?>
            //{
            //    [nameof(Create.OnCloseAsync)] = new Func<Task>(() => op.CloseDialogAsync())
            //});
            //var result = await WtmBlazor.Dialog.ShowModal<Create>(op);
            //if (result == DialogResult.Yes)
            //{
            //    await dataTable.QueryAsync();
            //}

        }

        private async Task OnEditClick(ProductOutboundBill_View item)
        {
            if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
            {
                await dataTable.QueryAsync();
            }
        }

        private async Task OnDetailsClick(ProductOutboundBill_View item)
        {
            await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
        }

        private async Task OnBatchDeleteClick()
        {
            if (dataTable.SelectedRows?.Any() == true)
            {
                await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/ProductOutboundBill/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
                await dataTable.QueryAsync();
            }
            else
            {
                await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
            }
        }

        private async Task OnDeleteClick(ProductOutboundBill_View item)
        {
            await PostsData(new List<string> { item.ID.ToString() }, $"/api/ProductOutboundBill/batchdelete", (s) => "Sys.OprationSuccess");
            await dataTable.QueryAsync();
        }


        private async Task OnExportClick(IEnumerable<ProductOutboundBill_View> items)
        {
            if (dataTable.SelectedRows?.Any() == true)
            {
                await Download("/api/ProductOutboundBill/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
            }
            else
            {
                await Download("/api/ProductOutboundBill/ExportExcel", SearchModel);
            }
        }
        private async Task OnImportClick(IEnumerable<ProductOutboundBill_View> items)
        {
            if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
            {
                await dataTable.QueryAsync();
            }
        }

    }
}