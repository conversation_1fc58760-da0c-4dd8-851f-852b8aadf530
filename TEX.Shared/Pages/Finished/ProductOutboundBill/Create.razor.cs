using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Rebus.Retry;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection;
using System.Security.Policy;
using System.Threading.Tasks;
using TEX.Model.Models;
using TEX.Shared.Pages.Components;
using TEX.ViewModel.Finished.ProductOutboundBillVMs;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using TEX.ViewModel.Models.OrderDetailVMs;
using TEX.ViewModel.Producttion.LotAllocateVMs;
using WalkingTec.Mvvm.Core;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Finished.ProductOutboundBill
{
    public partial class Create : BasePage, IResultDialog
    {
        private ProductOutboundBillVM Model = new ProductOutboundBillVM();
        private ValidateForm vform { get; set; }
        private Modal OutModal { get; set; }//出库单选择库存弹窗实例

        private List<SelectedItem> AllCustomers = new List<SelectedItem>();
        private List<SelectedItem> AllReceivers = new List<SelectedItem>();

        private IEnumerable<ProductOutboundLot_View> SelectedLot { get; set; } = new List<ProductOutboundLot_View>();
        public bool IsDownload { get; private set; }
        [Parameter]
        public Func<Task> OnCloseAsync { get; set; }

        private Task ShowDialog(IEnumerable<ProductOutboundLot_View> items) => OutModal.Toggle();

        private async Task OnConfirm()
        {
            //if (SelectedLot.Any())
            //{
            //    detailList.AddRange(SelectedLot);
            //}
            detailList.Clear();
            detailList.AddRange(SelectedLot);
            StateHasChanged();

            await OutModal.Toggle();
        }
        protected override async Task OnInitializedAsync()
        {
            AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
            //AllReceivers = await WtmBlazor.Api.CallItemsApi("/api/DeliveryAddress/GetDeliveryAddress", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
            // var rv = await WtmBlazor.Api.CallAPI<List<PurchaseOrderWithDetails_TreeView>>($"/api/Models/PurchaseOrder/GetPOrderTreeItem/{"3cb04a71-99ae-11ee-9443-00e269212fa0"}"); //

            await base.OnInitializedAsync();
        }

        //使用反射对实体类的某一个字段求和
        private static decimal GetSum(IEnumerable<ProductOutboundLot_View> items, string fieldName)
        {
            if (!items.Any()) return 0;
            PropertyInfo property = typeof(ProductOutboundLot_View).GetProperty(fieldName);
            if (property == null) throw new ArgumentException("Field not found", fieldName);
            return items.Sum(i => Convert.ToDecimal(property.GetValue(i)));
        }
        private async Task Submit(EditContext context)
        {
            //收货方必填验证失效了,只能自定义验证了
            if (Model.Entity.ReceiverId == Guid.Empty)
            {
                vform.SetError<TEX.Model.Models.ProductOutboundBill>(f => f.ReceiverId, "收货方为必填项");
                return;
            }
            if (DetailList.Any())
            {
                var list = DetailList.Select(x => (TEX.Model.Models.ProductOutboundLot)x).ToList();
                Model.Entity.LotList = list;
                Model.Entity.Pcs = list.Sum(x => x.Pcs);
                Model.Entity.Meters = list.Sum(x => x.Meters);
                Model.Entity.Yards = list.Sum(x => x.Yards);
                Model.Entity.Weight = list.Sum(x => x.Weight);
            }
            //var s = await PostsForm(vform, "/api/ProductOutboundBill/add", (s) => "Sys.OprationSuccess");
            bool s = false;
            var rv = await WtmBlazor.Api.CallAPI("/api/ProductOutboundBill/add", HttpMethodEnum.POST, Model);
            if (rv.StatusCode == System.Net.HttpStatusCode.OK)
            {
                    await WtmBlazor.Toast.Success(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Success"]);
                
                CloseDialog(DialogResult.Yes);
                s= true;
            }
            else
            {
                if (rv.Errors == null)
                {
                    await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.StatusCode.ToString());
                }
                else
                {
                    //当自定义组件的验证失效时,可以增加自己的验证提示消息
                    await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], rv.Errors.GetFirstError());

                }
            }

            if (s && IsDownload)
            {
                await GenOutBill(Model.Entity.BillNo);
            }
        }

        private async Task OnValueChanged()
        {
            IsDownload = !IsDownload;
            await Task.CompletedTask;
        }

        public void OnClose()
        {
            CloseDialog();
            if (OnCloseAsync != null)
            {
                OnCloseAsync();
            }
        }
        private List<ProductOutboundLot_View> detailList = new List<ProductOutboundLot_View>();
        public IEnumerable<ProductOutboundLot_View> DetailList
        {
            get { return detailList; }
            set
            {
                detailList = value.ToList();
            }
        }

        /// <summary>
        /// 生成出库单xlsx下载
        /// </summary>
        /// <returns></returns>
        private async Task GenOutBill(string billNo)
        {
            var rv = await WtmBlazor.Api.CallAPI<ProductOutboundBill_View>($"/api/ProductOutboundBill/GetWithRollsByBillNo/{billNo}");
            if (rv.StatusCode == System.Net.HttpStatusCode.OK)
            {
                if (rv.Data is not null)
                {
                    try
                    {

                    var data = ExcelHelper.GenOutBillExcel(rv.Data);
                    var customerName = AllCustomers.FirstOrDefault(x => x.Value == Model.Entity.CustomerId.ToString()).Text;
                    //使用自定义的通用js方法下载,API使用Get方法成功
                    await JSRuntime.InvokeVoidAsync("downloadFile", data, "成品出库单-" + Model.Entity.BillNo + "-" + customerName + ".xlsx");

                    }
                    catch
                    {
                        //Logger.LogError("生成出库单失败!");
                    }
                }
            }
            else
            {
                await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["出库单下载失败!"]);
            }
        }

        /// <summary>
        /// 选择库存按钮动作
        /// </summary>
        /// <returns></returns>
        private async Task<ProductOutboundLot_View> OnSelectLotAsync()
        {
            if (Model.Entity.CustomerId != Guid.Empty)
            {

                await SelectInboundLot(Model.Entity.CustomerId.ToString());

                //返回null,点取消时才不会默认添加一行
                return null;
            }
            else
            {
                await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["请先选择客户!"]);
                return null;
            }
        }


        public async Task SelectInboundLot(string id)
        {
            var option = new ResultDialogOption()
            {
                Title = "请选择库存缸号",
                IsDraggable = true,
                IsCentered = true,
                IsKeyboard = true,
                ShowCloseButton = true,
                ShowHeaderCloseButton = false,
                ShowMaximizeButton = true,
                ButtonYesText = "确定",
                Size = Size.ExtraExtraLarge,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    [nameof(id)] = id,
                    [nameof(DialogOutSelectLot.SelectedLot)] = SelectedLot,
                    [nameof(DialogOutSelectLot.SelectedLotChanged)] = EventCallback.Factory.Create<List<ProductOutboundLot_View>>(this, v => SelectedLot = v)
                }
            };
            var result = await WtmBlazor.Dialog.ShowModal<DialogOutSelectLot>(option);
            if (result == DialogResult.Yes)
            {
                if (SelectedLot.Any())
                {
                    detailList.AddRange(SelectedLot);
                }
            }

        }

        private async Task OnCustomerSelect(SelectedItem item)
        {
            Model.Entity.ReceiverId = Guid.Empty;
            AllReceivers = await WtmBlazor.Api.CallItemsApi($"/api/DeliveryAddress/GetDeliveryAddress/{item.Value}", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
            StateHasChanged();
        }


        //[CascadingParameter]
        //[NotNull]
        //private Modal? Dialog { get; set; }
        //使用BB的弹窗方式才走这里,使用wtm框架的OpenDialog方式走OnClose()
        public async Task OnClose(DialogResult result)
        {
            //CloseDialog(result);
            if (OnCloseAsync != null)
            {
                await OnCloseAsync();
            }
            //await Dialog.OnCloseAsync();//关闭后无法再弹出本页面
            //await Dialog.Close();//关闭后页面变灰,无法操作
        }
    }
}