@page "/Finished/ProductOutboundBill"
@using TEX.Shared.Pages.Finished.ProductOutboundLot
@using TEX.ViewModel.Finished.ProductOutboundBillVMs;
@using TEX.ViewModel.Finished.ProductOutboundLotVMs;
@inherits BasePage
@attribute [ActionDescription("成品出库", "TEX.Controllers,ProductOutboundBill")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <WTDateRange @bind-Value="@SearchModel.CreateDate" />
            <BootstrapInput @bind-Value="@SearchModel.BillNo" />
            <Select @bind-Value="@SearchModel.CustomerId" Items="@AllCustomers" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.ReceiverId" Items="@AllReceivers" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.CustomerOrderNo" />
            <BootstrapInput @bind-Value="@SearchModel.Remark" />
            <Select @bind-Value="@SearchModel.AuditStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="ProductOutboundBill_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd HH: mm: ss" />
        <TableColumn @bind-Field="@context.BillNo" />
        <TableColumn @bind-Field="@context.Customer_view" />
        <TableColumn @bind-Field="@context.Receiver_view" />
        <TableColumn @bind-Field="@context.Pcs" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Remark" />
        <TableColumn @bind-Field="@context.AuditStatus" />
    </TableColumns>
    <DetailRowTemplate>
        <ProductOutboundLotDetailTable id="context.ID"/>
    </DetailRowTemplate>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/ProductOutboundBill/Add"))
        {
            <TableToolbarButton TItem="ProductOutboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/ProductOutboundBill/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="ProductOutboundBill_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }

        @if (IsAccessable("/api/ProductOutboundBill/Import"))
        {
            <TableToolbarButton TItem="ProductOutboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/ProductOutboundBill/ExportExcel"))
        {
            <TableToolbarButton TItem="ProductOutboundBill_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/ProductOutboundBill/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/ProductOutboundBill/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/ProductOutboundBill/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>
