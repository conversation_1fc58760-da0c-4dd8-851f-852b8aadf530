@page "/Finished/ProductOutboundBill/Create"
@using System.Reflection
@using BootstrapBlazor.Shared
@using TEX.Model.Models
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Finished.InspectedRollVMs
@using TEX.ViewModel.Finished.ProductOutboundBillVMs;
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <Select @bind-Value="@Model.Entity.CustomerId" Items="@AllCustomers" ShowSearch="true" OnSelectedItemChanged="@OnCustomerSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Select @bind-Value="@Model.Entity.ReceiverId" Items="@AllReceivers" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@Model.Entity.BillNo" Readonly="true"/>
        <Row ItemsPerRow="ItemsPerRow.One" RowType="RowType.Inline" >
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
        </Row>
    </Row>
    <div style="margin:20px 0;">
        <Table TItem="ProductOutboundLot_View" @bind-Items="@DetailList" ShowRefresh="false" ShowExtendDeleteButton="false"
               ShowDeleteButton="true"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true" ShowFooter="true" IsFixedFooter="false" 
               IsExcel="false" ShowDefaultButtons="false" 
               IsHideFooterWhenNoData="true" IsMultipleSelect="true"
               ShowExtendButtons="false" ShowExtendEditButton="false"
               ShowToolbar="true"  IsBordered="true"
               IsFixedHeader="true" Height="400"  ExtendButtonColumnWidth="100">
            <TableColumns>
                <TableColumn @bind-Field="@context.OrderNo_view" Readonly="true"  TextEllipsis="true" ShowTips="true" />
                <TableColumn @bind-Field="@context.Product_view" Readonly="true"  TextEllipsis="true" ShowTips="true" />
                <TableColumn @bind-Field="@context.Color" Readonly="true"  TextEllipsis="true" ShowTips="true" />
                <TableColumn @bind-Field="@context.LotNo"  TextEllipsis="true" ShowTips="true" />
                <TableColumn @bind-Field="@context.Pcs" Width="60" />
                <TableColumn @bind-Field="@context.Weight" Width="70" />
                <TableColumn @bind-Field="@context.Meters" Width="70" />
                <TableColumn @bind-Field="@context.Yards" Width="70" />
                <TableColumn @bind-Field="@context.Remark" TextEllipsis="true" ShowTips="true" />
            </TableColumns>
            @* <TableExtensionToolbarTemplate>
                <Switch Value="true" OnColor="Color.Success" OffColor="Color.Warning" OnText="手工填写关" OffText="手工填写开" style="margin-right:8px"></Switch>
            </TableExtensionToolbarTemplate> *@
            <TableToolbarTemplate>
                @* <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" /> *@
                <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" 
                Text="@WtmBlazor.Localizer["Sys.Select"]" OnClickCallback="@ShowDialog" IsDisabled="@(Model.Entity.CustomerId==Guid.Empty)" />

                <Modal @ref="OutModal">
                    <ModalDialog Title="选择出库单明细" IsCentered IsDraggable="true">
                        <BodyTemplate>
                            <DialogOutboundSelectLots CustomerId="@Model.Entity.CustomerId.ToString()" @bind-SelectedLot="@SelectedLot" />
                        </BodyTemplate>
                        <FooterTemplate>
                            <Button Text="@WtmBlazor.Localizer["Sys.OK"]" Icon="fa-regular fa-square-check" OnClick="@OnConfirm" />
                        </FooterTemplate>
                    </ModalDialog>
                </Modal>
            </TableToolbarTemplate>
            <RowButtonTemplate>
                @* <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" /> *@
                @* <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Text="复制" OnClick="()=>OnDetailsCopy(context)" /> *@
            </RowButtonTemplate>

            <TableFooter>
                <TableFooterCell />
                <TableFooterCell Text="合计:" />
                <TableFooterCell ColspanCallback="(items) => 2" />
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Yards)" />
                <TableFooterCell />
                
            </TableFooter>
        </Table>
    </div>
    <div class="modal-footer table-modal-footer">
        <Toggle Value="@IsDownload" ValueChanged="@OnValueChanged" OnText="是" OffText="否" DisplayText="下载出库单  " Color="Color.Success" Width="60" style="margin-right:16px" />
        @* <Button Color="Color.Primary" OnClick="GenOutBill" Icon="fa fa-save" Text="生成出库单" IsAsync="true" /> *@
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" IsAsync="true" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
        
    </div>
</ValidateForm>

