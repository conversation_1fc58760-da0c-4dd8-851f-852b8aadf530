@page "/Finished/ProductOutboundBill/Details/{id}"
@using TEX.Shared.Pages.Finished.ProductOutboundLot
@using TEX.ViewModel.Finished.ProductOutboundBillVMs;
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

        <Display @bind-Value="@BillInfo.CreateDate" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.BillNo" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Customer_view" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Receiver_view" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Pcs" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Meters" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Yards" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Weight" ShowLabel="true" />
        <Display @bind-Value="@BillInfo.Remark" ShowLabel="true" />
    </Row>
    <div style="margin:20px 0;">
        <Table TItem="ProductOutboundLot_View" @bind-Items="@DetailList" IsPagination="false" IsStriped="true" IsBordered="true" ShowRefresh="false" ShowFooter="true" TableSize="TableSize.Compact" HeaderStyle="TableHeaderStyle.None"
               ShowToolbar="true" ShowExtendButtons="false" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
            <TableColumns>

                <TableColumn @bind-Field="@context.OrderNo_view" />
                <TableColumn @bind-Field="@context.Product_view" />
                <TableColumn @bind-Field="@context.Color_view" />
                <TableColumn @bind-Field="@context.ColorCode" />
                <TableColumn @bind-Field="@context.LotNo" />
                <TableColumn @bind-Field="@context.Pcs" />

                <TableColumn @bind-Field="@context.Meters" Width="60" />
                <TableColumn @bind-Field="@context.Yards" Width="60" />
                <TableColumn @bind-Field="@context.Weight" Width="60" />

                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <TableFooter>
                <TableFooterCell Text="合计:" />
                <TableFooterCell ColspanCallback="(items) => 3" />
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Pcs)" />

                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Yards)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Weight)" />

                <TableFooterCell />
            </TableFooter>
        </Table>
    </div>
    <br>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="下载出库单" OnClick="GenOutBill" />
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductOutboundBillVM Model = new();
    private ProductOutboundBill_View BillInfo = new();
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    public Guid Id { get => Guid.Parse(id); }

    //private List<SelectedItem> AllCompanys = new List<SelectedItem>();
    private IEnumerable<ProductOutboundLot_View> DetailList = new List<ProductOutboundLot_View>();

    protected override async Task OnInitializedAsync()
    {
        //var rv = await WtmBlazor.Api.CallAPI<ProductOutboundBillVM>($"/api/ProductOutboundBill/{id}");
        //Model = rv.Data;
        //优化调用,只用一个API解决数据调用
        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundBill_View>($"/api/ProductOutboundBill/GetWithLotList/{id}");
        BillInfo = rv.Data;
        DetailList = BillInfo.Details;
    }

    public void OnClose()
    {
        CloseDialog();
    }

    public async Task GenOutBill()
    {
        await GenOutBill(BillInfo.ID);
    }

    private async Task GenOutBill(Guid productOutboundBillId)
    {
        //框架的Download方法Post参数到API为null
        //await Download("/api/ProductOutboundBill/DownloadOutBill","08dcee91-895e-41dd-8dcb-28d844ecde26");

        //var id = "08dcee91-895e-41dd-8dcb-28d844ecde26";
        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundBill_View>($"/api/ProductOutboundBill/GetWithRolls/{productOutboundBillId}");

        var bill = rv.Data;
        if (bill is not null)
        {
            var data = ExcelHelper.GenOutBillExcel(bill);

            //使用自定义的通用js方法下载,API使用Get方法成功
            await JSRuntime.InvokeVoidAsync("downloadFile", data, "成品出库单-" + BillInfo.BillNo + "-" + BillInfo.Customer_view + ".xlsx");
        }
    }

}
