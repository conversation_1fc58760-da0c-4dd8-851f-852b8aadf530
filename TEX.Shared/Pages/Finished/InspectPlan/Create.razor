@page "/Finished/InspectPlan/Create"
@using TEX.ViewModel.Finished.InspectPlanVMs;
@using TEX.Shared.Pages.Components;
@using TEX.ViewModel.Models.OrderDetailVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.PlanNo" IsDisabled="true" />
        <BootstrapInput @bind-Value="@Model.Entity.PlanBatch" />
        <Select @bind-Value="@Model.Entity.InspectionStandard" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <SelectOrderDetailTable @bind-Value="@orderDetail" OnSelectedChanged="OnSelectedChanged" />
        <BootstrapInput Value="@orderDetail.OrderDetail_Customer" DisplayText="客户" IsDisabled="@isDisabled" />
        <BootstrapInput @bind-Value="@orderDetail.OrderDetail_ProductName" IsDisabled="@isDisabled" />
        <BootstrapInput @bind-Value="@orderDetail.Color" IsDisabled="@isDisabled" />
        <BootstrapInput @bind-Value="@orderDetail.ColorCode" IsDisabled="@isDisabled" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.PlanQty" />
        <Select @bind-Value="@Model.Entity.QtyUnit" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private InspectPlanVM Model = new InspectPlanVM();
    private ValidateForm vform { get; set; }

    public OrderDetail_View orderDetail { get; set; } = new();

    private bool isDisabled = false;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        if (orderDetail.ID != Guid.Empty)
        {
            Model.Entity.OrderDetailId = orderDetail.ID;
        }
        await PostsForm(vform, "/api/InspectPlan/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    //订单选择后查询产品名称和规格
    private async Task OnSelectedChanged(OrderDetail_View item)
    {
        if (item is not null)
        {
            orderDetail.OrderDetail_PurchaseOrder = item.OrderDetail_PurchaseOrder;
            orderDetail.OrderDetail_ProductName = item.OrderDetail_ProductName;
            orderDetail.Color = item.Color;
            orderDetail.ColorCode = item.ColorCode ?? string.Empty;
            orderDetail.OrderDetail_Customer = item.OrderDetail_Customer;
            if (item.OrderDetail_AccountUnit == AccountingUnitEnum.M)
            {
                Model.Entity.QtyUnit = AccountingUnitEnum.M;
                if (item.Meters.HasValue)
                {
                    Model.Entity.PlanQty = (int)item.Meters;
                }
            }
            else if (item.OrderDetail_AccountUnit == AccountingUnitEnum.KG)
            {
                Model.Entity.QtyUnit = AccountingUnitEnum.KG;
                Model.Entity.PlanQty = (int)item.KG;
            }
            else
            {
                Model.Entity.QtyUnit = AccountingUnitEnum.Y;
                Model.Entity.PlanQty = (int)item.Yards;
            }

            isDisabled = true;
            StateHasChanged();
            await Task.Delay(10);
        }
    }
}
