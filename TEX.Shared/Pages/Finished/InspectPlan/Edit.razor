@page "/Finished/InspectPlan/Edit/{id}"
@using TEX.ViewModel.Finished.InspectPlanVMs;
@using TEX.ViewModel.Models.OrderDetailVMs;
@using TEX.Shared.Pages.Components;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
        <BootstrapInput @bind-Value="@Model.Entity.PlanNo" />
        <BootstrapInput @bind-Value="@Model.Entity.PlanBatch" />
        <Select @bind-Value="@Model.Entity.InspectionStandard" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <SelectOrderDetailTable @bind-Value="@orderDetail" OnSelectedChanged="OnSelectedChanged" />
        @* <BootstrapInput @bind-Value="@Model.Entity.OrderNo"  /> *@
        <BootstrapInput Value="@orderDetail.OrderDetail_Customer" DisplayText="客户" IsDisabled="@isDisabled" />
        <BootstrapInput @bind-Value="@orderDetail.OrderDetail_ProductName" IsDisabled="@isDisabled" />
        <BootstrapInput @bind-Value="@orderDetail.Color" IsDisabled="@isDisabled" />
        <BootstrapInput @bind-Value="@orderDetail.ColorCode" IsDisabled="@isDisabled" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.PlanQty" />
        <Select @bind-Value="@Model.Entity.QtyUnit" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Select @bind-Value="@Model.Entity.InspectStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <DateTimePicker @bind-Value="@Model.Entity.PlanFinishDate" />
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private InspectPlanVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();
    public OrderDetail_View orderDetail { get; set; } = new();
    private string customer = "";

    private bool isDisabled = false;

    protected override async Task OnInitializedAsync()
    {
        
        var rv = await WtmBlazor.Api.CallAPI<InspectPlanVM>($"/api/InspectPlan/{id}");
        Model = rv.Data;
        var oderDetail_view = await StartSearch<OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", new OrderDetailSearcher(){OrderDetailId=Model.Entity.OrderDetailId}, new QueryPageOptions());
        
        orderDetail= oderDetail_view.Items.FirstOrDefault();
        customer = orderDetail.OrderDetail_Customer ?? "";
        isDisabled=true;
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/InspectPlan/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    //订单选择后查询产品名称和规格
    private async Task OnSelectedChanged(OrderDetail_View item)
    {
        if (item is not null)
        {
            orderDetail.OrderDetail_PurchaseOrder = item.OrderDetail_PurchaseOrder;
            orderDetail.OrderDetail_ProductName = item.OrderDetail_ProductName;
            orderDetail.Color = item.Color;
            orderDetail.ColorCode = item.ColorCode;
            orderDetail.OrderDetail_Customer = item.OrderDetail_Customer;
            if (item.OrderDetail_AccountUnit == AccountingUnitEnum.M)
            {
                Model.Entity.QtyUnit = AccountingUnitEnum.M;
                Model.Entity.PlanQty = (int)item.Meters;
            }
            else if (item.OrderDetail_AccountUnit == AccountingUnitEnum.KG)
            {
                Model.Entity.QtyUnit = AccountingUnitEnum.KG;
                Model.Entity.PlanQty = (int)item.KG;
            }
            else
            {
                Model.Entity.QtyUnit = AccountingUnitEnum.Y;
                Model.Entity.PlanQty = (int)item.Yards;
            }

            isDisabled = true;
            StateHasChanged();
            await Task.Delay(10);
        }
    }

}
