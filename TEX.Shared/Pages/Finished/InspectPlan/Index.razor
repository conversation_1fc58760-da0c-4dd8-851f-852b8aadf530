@page "/Finished/InspectPlan"
@using TEX.ViewModel.Finished.InspectPlanVMs;
@inherits BasePage
@attribute [ActionDescription("检验计划单", "TEX.Controllers,InspectPlan")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.OrderNo" />
            <BootstrapInput @bind-Value="@SearchModel.ProductName" />
            <BootstrapInput @bind-Value="@SearchModel.Color" />
            <BootstrapInput @bind-Value="@SearchModel.ColorCode" />
            <Select @bind-Value="@SearchModel.InspectionStandard" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@SearchModel.InspectStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="InspectPlan_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateDate" FormatString="yy-MM-dd" />
        @* <TableColumn @bind-Field="@context.Color_view"  /> *@
        <TableColumn @bind-Field="@context.PlanNo" />
        <TableColumn @bind-Field="@context.OrderNo" />
        <TableColumn @bind-Field="@context.ProductName" />
        <TableColumn @bind-Field="@context.Color_view" />
        <TableColumn @bind-Field="@context.ColorCode" />
        <TableColumn @bind-Field="@context.PlanBatch" />
        <TableColumn @bind-Field="@context.PlanQty" />
        <TableColumn @bind-Field="@context.QtyUnit" />
        <TableColumn @bind-Field="@context.InspectionStandard" />
        <TableColumn @bind-Field="@context.InspectStatus" />
        <TableColumn @bind-Field="@context.PlanFinishDate" FormatString="yy-MM-dd" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/InspectPlan/Add"))
        {
            <TableToolbarButton TItem="InspectPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/InspectPlan/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="InspectPlan_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }

        @if (IsAccessable("/api/InspectPlan/Import"))
        {
            <TableToolbarButton TItem="InspectPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/InspectPlan/ExportExcel"))
        {
            <TableToolbarButton TItem="InspectPlan_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/InspectPlan/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/InspectPlan/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/InspectPlan/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code {

    private InspectPlanSearcher SearchModel = new InspectPlanSearcher();
    private Table<InspectPlan_View> dataTable;


    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<InspectPlan_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<InspectPlan_View>("/api/InspectPlan/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<InspectPlan_View> items)
    {
        if (await OpenDialog<Create>("检验计划单" + WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(InspectPlan_View item)
    {
        if (await OpenDialog<Edit>("检验计划单" + WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(InspectPlan_View item)
    {
        await OpenDialog<Details>("检验计划单" + WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/InspectPlan/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(InspectPlan_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/InspectPlan/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<InspectPlan_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/InspectPlan/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/InspectPlan/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<InspectPlan_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
