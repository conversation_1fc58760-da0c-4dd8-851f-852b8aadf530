@page "/Finished/InspectPlan/Details/{id}"
@using TEX.ViewModel.Finished.InspectPlanVMs;
@using TEX.ViewModel.Models.OrderDetailVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <Display @bind-Value="@Model.CreateDate" ShowLabel="true" FormatString="yyyy-MM-dd" />

        <Display @bind-Value="@Model.PlanNo" ShowLabel="true" />
        <Display @bind-Value="@Model.PlanBatch" ShowLabel="true" />
        <Display @bind-Value="@Model.InspectionStandard" ShowLabel="true" />
        <Display Value="@Model.Customer" DisplayText="客户" ShowLabel="true" />
        <Display @bind-Value="@Model.OrderNo" ShowLabel="true" />
        <Display @bind-Value="@Model.ProductName" ShowLabel="true" />
        <Display @bind-Value="@Model.GSM" ShowLabel="true" />
        <Display @bind-Value="@Model.Width" ShowLabel="true" />
        <Display @bind-Value="@Model.Color_view" ShowLabel="true" />
        <Display @bind-Value="@Model.ColorCode" ShowLabel="true" />
        <Display @bind-Value="@Model.PlanQty" ShowLabel="true" />
        <Display @bind-Value="@Model.QtyUnit" ShowLabel="true" />
        <Display @bind-Value="@Model.InspectStatus" ShowLabel="true" />
        <Display @bind-Value="@Model.PlanFinishDate" ShowLabel="true" />
        <Display @bind-Value="@Model.Remark" ShowLabel="true" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private InspectPlan_View Model { get; set; } = new();
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }


    protected override async Task OnInitializedAsync()
    {
        var rv = await WtmBlazor.Api.CallAPI<InspectPlan_View>($"/api/InspectPlan/GetInspectPlan_ViewById/{id}");
        if (rv.StatusCode != System.Net.HttpStatusCode.OK)
        {
           await WtmBlazor.Toast.Show(new ToastOption()
            {
                Title="查询错误",
                    Content = "没有查询到数据!"

            });
            return;
        }
        Model = rv.Data;
        
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
