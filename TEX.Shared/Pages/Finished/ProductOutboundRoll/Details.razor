@page "/Finished/ProductOutboundRoll/Details/{id}"
@using TEX.ViewModel.Finished.ProductOutboundRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.LotId" Lookup="@AllProductOutboundLots"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.RollNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Weight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Meters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Yards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Grade"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductOutboundRollVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProductOutboundLots = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductOutboundLots = await WtmBlazor.Api.CallItemsApi("/api/ProductOutboundRoll/GetProductOutboundLots", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundRollVM>($"/api/ProductOutboundRoll/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
