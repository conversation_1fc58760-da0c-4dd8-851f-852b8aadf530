@page "/Finished/ProductOutboundRoll/Create"
@using TEX.ViewModel.Finished.ProductOutboundRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.LotId" Items="@AllProductOutboundLots" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInputNumber @bind-Value="@Model.Entity.RollNo"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Weight"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Meters"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Yards"  />
            <BootstrapInput @bind-Value="@Model.Entity.Grade"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ProductOutboundRollVM Model = new ProductOutboundRollVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllProductOutboundLots = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductOutboundLots = await WtmBlazor.Api.CallItemsApi("/api/ProductOutboundRoll/GetProductOutboundLots", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/ProductOutboundRoll/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
