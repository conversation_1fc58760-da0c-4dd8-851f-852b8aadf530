@page "/Finished/ProductInboundRoll/Details/{id}"
@using TEX.ViewModel.Finished.ProductInboundRollVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.LotId" Lookup="@AllProductInboundLots"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.RollNo"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Weight"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Meters"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Yards"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Grade"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ProductInboundRollVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllProductInboundLots = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllProductInboundLots = await WtmBlazor.Api.CallItemsApi("/api/ProductInboundRoll/GetProductInboundLots", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<ProductInboundRollVM>($"/api/ProductInboundRoll/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
