@using TEX.ViewModel.Finished.InspectedRollVMs;
@inherits BasePage


<Table @ref="dataTable" TItem="InspectedRoll_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CreateTime" Format="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.OrderNo" />
        <TableColumn @bind-Field="@context.ProductName" />
        <TableColumn @bind-Field="@context.Color_view" />
        <TableColumn @bind-Field="@context.Spec" />
        <TableColumn @bind-Field="@context.InspectedLot" />
        <TableColumn @bind-Field="@context.RollNo" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Score" />
        <TableColumn @bind-Field="@context.TotalScore" />
        <TableColumn @bind-Field="@context.Grade" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/InspectedRoll/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="InspectedRoll_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/InspectedRoll/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string LotNo { get; set; }

    private InspectedRollSearcher SearchModel = new InspectedRollSearcher();
    private Table<InspectedRoll_View> dataTable;

    //private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {
        SearchModel.LotNo = LotNo;
        SearchModel.OrderDetailId = Guid.Parse(id);
        //AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/InspectedRoll/GetOrderDetailsOfInspected", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<InspectedRoll_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<InspectedRoll_View>("/api/InspectedRoll/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }
    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/InspectedRoll/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(InspectedRoll_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/InspectedRoll/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


}