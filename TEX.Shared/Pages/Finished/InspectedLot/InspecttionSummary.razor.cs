using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Security.Policy;
using System.Text.Json;
using System.Threading.Tasks;
using TEX.Model.Models;
using TEX.ViewModel.Finished.InspectedRollVMs;
using TEX.ViewModel.Finished.InspectPlanVMs;
using TEX.ViewModel.Finished.ProductInboundLotVMs;
using TEX.ViewModel.Finished.ProductOutboundBillVMs;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using TEX.ViewModel.Models;
using TEX.ViewModel.Models.PurchaseOrderVMs;
using WalkingTec.Mvvm.Core;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Finished.InspectedLot;

public partial class InspecttionSummary:BasePage
{
    private MessageService MessageService { get; set; }
    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private InspectDashboardData InspectDashData { get; set; } = new();
    private List<InspectedLot_View> SelectedRows { get; set; } = new();
    private List<TreeViewItem<PurchaseOrderWithDetails_TreeView>> TreeViewData { get; set; }

    //ѡ����ɫ��,���ն�Ӧ�ĸ׺��б�
    private List<ProductOutboundLot_View> selectedLot_ViewList { get; set; } = new();

    private IEnumerable<ProductOutboundLot_View> SelectedLot_ViewList
    {
        get => selectedLot_ViewList;
        set
        {
            selectedLot_ViewList = value.ToList();
        }
    }



    //��newһ����������
    private InspectedLotSearcher SearchModel = new();
    private ProductInboundLotSearcher ProductInboundLotSearchModel = new();

    private Table<InspectedLot_View> dataTable;

    private async Task DoSearch()
    {

        await dataTable.QueryAsync();
        //await inboundLotdataTable.QueryAsync();
    }

    public Guid CustomerId { get; set; }
    protected override async Task OnInitializedAsync()
    {
        //AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys");
        var rv = await WtmBlazor.Api.CallItemsApi("/api/InspectedRoll/GetInspectCustomer");
        AllCustomers = rv;
        var r=await WtmBlazor.Api.CallAPI<InspectDashboardData>("/api/InspectedRoll/GetInspectDashboardData");
        InspectDashData = r.Data;
        await base.OnInitializedAsync();
    }
    private async Task OnCustomerSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            TreeViewData = await GetTreeViewData(CustomerId);
            StateHasChanged();
        }
    }
    private async Task<List<TreeViewItem<PurchaseOrderWithDetails_TreeView>>> GetTreeViewData(Guid id)
    {

        // var rv = await WtmBlazor.Api.CallAPI < List<TreeViewItem<TreeFoo>>>($"/api/ProductOutboundBill/GetOrderTreeByCustomerId/{id}");
        // var r = rv.Data;
        var rv = await WtmBlazor.Api.CallAPI<List<PurchaseOrderWithDetails_TreeView>>($"/api/InspectedRoll/GetPOrderTreeItemByCustomerId/{id.ToString()}");
        var r = CascadingTree(rv.Data).ToList();

        return r;
    }

    public static IEnumerable<TreeViewItem<PurchaseOrderWithDetails_TreeView>> CascadingTree(IEnumerable<PurchaseOrderWithDetails_TreeView> items, TreeViewItem<PurchaseOrderWithDetails_TreeView> parent = null) => items.CascadingTree(null,
            (foo, parent) => foo.ParentId == parent?.Value.ID,
            foo => new TreeViewItem<PurchaseOrderWithDetails_TreeView>(foo)
            {
                Text = foo.Text,
                Icon = foo.Icon,
                IsActive = foo.IsActive
            }).ToList();

    private async Task OnTreeItemClick(TreeViewItem<PurchaseOrderWithDetails_TreeView> item)
    {
        if (item.Value.ParentId is not null)
        {
            SearchModel.OrderDetailId = item.Value.ID;
            ProductInboundLotSearchModel.OrderDetailId = item.Value.ID;

            if (dataTable is not null)
            {
                await DoSearch();
            }
            StateHasChanged();
        }
    }
    private async Task OnDoubleClickRow(InspectedLot_View context)
    {

        //StateHasChanged();
        await Task.CompletedTask;
    }

    //ѡ����ɫ��,��ȡ��Ӧ�ļ���׺��б�
    private async Task<QueryData<InspectedLot_View>> OnSearch(QueryPageOptions opts)
    {
        if (SearchModel.OrderDetailId is null) return null;//��ʼ��ҳ��ʱ����null,�����ѯ�����м�¼,SearchModel�Ѿ�new��,Ĭ�Ͽ��Բ�ѯ�����м�¼
        //opts.IsVirtualScroll = true;
        SearchModel.Limit = 1000;
        opts.PageItems=1000;
        var rv =await StartSearch<InspectedLot_View>("/api/InspectedRoll/SearchLot", SearchModel, opts);
        return rv;
    }


    private async Task OnExportClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            //var data = ExcelHelper.GenerateInspectReportNPOI("../Files/���鱨��.xlsx",dataTable.SelectedRows);

            //ʹ���Զ����ͨ��js��������,APIʹ��Get�����ɹ�
            //await JSRuntime.InvokeVoidAsync("downloadFile", data, "��Ʒ���鱨��-" + DateTime.Now.ToString("u") + ".xlsx");
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }
    private async Task OnExportExcelReportClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {

            var data = dataTable.SelectedRows.Select(x => x.OrderDetailId.ToString() + "," + x.LotNo).ToList();
            await Download("/api/InspectedRoll/ExportExcelReportByOrderDetailIdAndLotNo", data);
        }
        else
        {
            await Download("/api/InspectedRoll/ExportExcelReportByOrderDetailIdAndLotNo", SearchModel.OrderDetailId);
            // ׼����������
            var jsonContent = JsonSerializer.Serialize(SearchModel.OrderDetailId);

            //await Download("/api/InspectedRoll/ExportInspectReport", SearchModel.OrderDetailId);

            var url = WtmBlazor.GetServerUrl() + $"api/InspectedRoll/ExportInspectReport";
            // ��ȷ���� urlFuncs.download ����
            // ��һ��������url
            // �ڶ����������ַ�����������
            // ������������HTTP����(��ѡ,Ĭ��POST)
            await JSRuntime.InvokeVoidAsync("urlFuncs.download", url
                ,
                jsonContent,  // �Ѿ��ַ�������JSON����
                "POST");
        }
    }

    /// <summary>
    /// ���ɳ��ⵥxlsx����
    /// </summary>
    /// <returns></returns>
    private async Task GenInspectReport()
    {
        //��ܵ�Download����Post������APIΪnull
        //await Download("/api/ProductOutboundBill/DownloadOutBill","08dcee91-895e-41dd-8dcb-28d844ecde26");

        var id = "08dcee91-895e-41dd-8dcb-28d844ecde26";
        var rv = await WtmBlazor.Api.CallAPI<ProductOutboundBill_View>($"/api/ProductOutboundBill/GetWithRolls/{id}");

        var bill = rv.Data;
        if (bill is not null)
        {
            //var data = ExcelHelper.GenerateInspectReportNPOI(bill);

            //ʹ���Զ����ͨ��js��������,APIʹ��Get�����ɹ�
           // await JSRuntime.InvokeVoidAsync("downloadFile", data, "��Ʒ���ⵥ-" + DateTime.Now.ToString("u") + ".xlsx");
        }
    }

    private async Task OnDetailsClick(InspectedLot_View context)
    {
        //if (await OpenDialog<LotDetails>(@WtmBlazor.Localizer["Page.����"], x => x.id == context.OrderDetailId.ToString() && x.LotNo == context.LotNo, isMax: false) == DialogResult.Yes)
        if (await OpenDialog<LotDetails>(@WtmBlazor.Localizer["Page.����"], x => x.Lot_View==context, isMax: false) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }
}