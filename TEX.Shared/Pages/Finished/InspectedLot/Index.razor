@page "/Finished/InspectedLot"
@using TEX.ViewModel.Finished.InspectedRollVMs;

@inherits BasePage
@attribute [ActionDescription("检验汇总", "TEX.Controllers,InspectedRollSummary")]

@inject IJSRuntime JS

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@SearchModel.CreateTime" />
            <Select @bind-Value="@SearchModel.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.OrderId"  />
            <BootstrapInput @bind-Value="@SearchModel.Grade"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>


<Table @ref="dataTable" TItem="InspectedLot_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" 
       ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" 
       IsFixedHeader="true" Height="790" style="margin-top:6px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.OrderNo" />
        <TableColumn @bind-Field="@context.ProductName"  />
        <TableColumn @bind-Field="@context.Color"  />
        <TableColumn @bind-Field="@context.Spec"  />
        <TableColumn @bind-Field="@context.LotNo" />
        <TableColumn @bind-Field="@context.RollCount" />
        <TableColumn @bind-Field="@context.TotalWeight" />
        <TableColumn @bind-Field="@context.TotalMeters" />
        <TableColumn @bind-Field="@context.TotalYards" />
  
    </TableColumns>
 
    <RowButtonTemplate>
        @if (IsAccessable("/api/InspectedRoll/{id}"))
        {
            <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
        }
    </RowButtonTemplate>
</Table>

@code{

    private InspectedLotSearcher SearchModel = new ();
    private Table<InspectedLot_View> dataTable;

    private List<SelectedItem> AllOrderDetails = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllOrderDetails = await WtmBlazor.Api.CallItemsApi("/api/InspectedRoll/GetOrderDetailsOfInspected", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<InspectedLot_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<InspectedLot_View>("/api/InspectedRoll/SearchLot", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnDetailsClick(InspectedLot_View context)
    {
        if (await OpenDialog<Details>(@WtmBlazor.Localizer["Page.详情"], x => x.id == context.OrderDetailId.ToString() && x.LotNo == context.LotNo, isMax: false) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }
}
