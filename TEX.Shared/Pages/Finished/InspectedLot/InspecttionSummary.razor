@page "/Finished/InspectionSummary"
@using TEX.Model.Models
@using TEX.Shared.CustomeComponents
@using TEX.ViewModel.Finished.InspectedRollVMs
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs


@inherits BasePage
@attribute [ActionDescription("检验统计", "TEX.Controllers,InspectionSummary")]

<div>
    <div class="row" style="margin: 20px;">
        <ProgressCardShow Title1="当天检验码数" Title2="本周检验码数" Value1="@((InspectDashData?.InspectTodayTotalYards ?? 0))" Value2="@((InspectDashData?.InspectWeekTotalYards ?? 0))" ColumnClass="col-md-3 col-lg-3" />
        <ProgressCardShow Title1="当天检验米数" Title2="本周检验米数" Value1="@((InspectDashData?.InspectTodayTotalMeters ?? 0))" Value2="@((InspectDashData?.InspectWeekTotalMeters ?? 0))" ColumnClass="col-md-3 col-lg-3" />
        <ProgressCardShow Title1="本周检验码数" Title2="本月检验码数" Value1="@((InspectDashData?.InspectWeekTotalYards ?? 0))" Value2="@((InspectDashData?.InspectMonthTotalYards ?? 0))" ColumnClass="col-md-3 col-lg-3" />
        <ProgressCardShow Title1="本周检验米数" Title2="本月检验米数" Value1="@((InspectDashData?.InspectWeekTotalMeters ?? 0))" Value2="@((InspectDashData?.InspectMonthTotalMeters ?? 0))" ColumnClass="col-md-3 col-lg-3" />
    </div>
</div>

<Divider />

<div style="display: flex;margin:0px 32px;">
    <div class="parent-table">
        <Row RowType="RowType.Normal">
            <Select @bind-Value="@CustomerId" Items="AllCustomers" ShowSearch="true" OnSelectedItemChanged="OnCustomerSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        </Row>
        <TreeView TItem="PurchaseOrderWithDetails_TreeView" Items="@TreeViewData" ShowSearch="false" OnTreeItemClick="@OnTreeItemClick" ClickToggleNode="true" style="height:100%;margin-top:10px;" IsAccordion="true"></TreeView>
    </div>
    <div class="child-table">
        <div>
            <Table @ref="dataTable" TItem="InspectedLot_View" OnQueryAsync="OnSearch" IsPagination="true"
                   IsStriped="true" IsBordered="true" ShowRefresh="false" TableSize="TableSize.Compact"
                   ShowToolbar="true" IsMultipleSelect="true" AllowResizing="true"
                   ShowExtendButtons="true" ShowDefaultButtons="false"
                   ShowExtendEditButton="false" OnDoubleClickRowCallback="(context)=>OnDoubleClickRow(context)"
                   IsFixedFooter="true" ShowFooter="true" IsFixedHeader="true" Height="630"
                   @bind-SelectedRows="@SelectedRows" ShowExtendDeleteButton="false">
                <TableColumns>
                    <TableColumn @bind-Field="@context.OrderNo" Width="180" TextEllipsis="true" ShowTips="true" />
                    <TableColumn @bind-Field="@context.ProductName" Width="180" TextEllipsis="true" ShowTips="true"/>
                    <TableColumn @bind-Field="@context.Color" TextEllipsis="true" ShowTips="true" />
                    <TableColumn @bind-Field="@context.LotNo" />
                    <TableColumn @bind-Field="@context.RollCount" Width="80" />
                    <TableColumn @bind-Field="@context.TotalWeight" Width="80" />
                    <TableColumn @bind-Field="@context.TotalMeters" Width="80" />
                    <TableColumn @bind-Field="@context.TotalYards" Width="80" />
                </TableColumns>
                <TableToolbarTemplate>
                     @*<TableToolbarButton TItem="InspectedLot_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />*@

                    @if (IsAccessable("/api/InspectedRoll/ExportExcel"))
                    {
                        @* <TableToolbarPopConfirmButton TItem="InspectedLot_View" Color="Color.Primary" 
                                                      Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.Export"]" IsAsync="true"
                                                      OnConfirm="@OnExportClick" Content="@WtmBlazor.Localizer["确认导出吗?"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                                      ConfirmButtonText="@WtmBlazor.Localizer["Sys.Confirm"]" ConfirmButtonColor="Color.Danger" />*@
                        <TableToolbarPopConfirmButton TItem="InspectedLot_View" Color="Color.Primary"
                                                      Icon="fa fa-fw fa-trash" Text="导出检验报告" IsAsync="true"
                                                      OnConfirm="@OnExportExcelReportClick" Content="确认导出检验报告" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                                      ConfirmButtonText="确认导出检验报告吗?" ConfirmButtonColor="Color.Danger" />
                    }
                </TableToolbarTemplate>
                <RowButtonTemplate>
                    <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
                </RowButtonTemplate>
                <TableFooter>
                    <TableFooterCell />
                    <TableFooterCell Text="合计:" ColspanCallback="(context) => 3" />
                    <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(InspectedLot_View.LotNo)" />
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.RollCount)" />
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalWeight)" />
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalMeters)" />
                    <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalYards)" />
                </TableFooter>
            </Table>
        </div>
    </div>
</div>
<style>

    html, body {
        height: 100%;
        margin: 0;
    }

    .container {
        display: flex;
        height: calc(100% - 32px); /* 考虑到可能存在的上下边距 */
        width: calc(100% - 32px); /* 确保宽度为100% */
        margin: 16px;
    }

    .parent-table {
        width: 25%;
        margin-right: 16px;
        margin-top: 8px;
        display: flex;
        flex-direction: column;
    }

    .child-table {
        width: 75%;
        display: flex;
        flex-direction: column;
    }

    .tree-view-container,
    .table-container {
        flex-grow: 1;
    }
</style>


