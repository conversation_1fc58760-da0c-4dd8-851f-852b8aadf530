@using TEX.ViewModel.Finished.InspectedRollVMs;
@inherits BasePage

<Card IsShadow="true">
    <HeaderTemplate>
        <h5>检验缸号明细</h5>
    </HeaderTemplate>
    <BodyTemplate>
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <Display @bind-Value="@Lot_View.Customer" ShowLabel="true" />
            <Display @bind-Value="@Lot_View.OrderNo" ShowLabel="true" />
            <Display @bind-Value="@Lot_View.ProductName" ShowLabel="true" />
        </Row>
    </BodyTemplate>
</Card>

<Table @ref="dataTable" TItem="InspectedRoll_View" OnQueryAsync="OnSearch" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="false" IsPagination="false"
       ShowFooter="true" ShowLineNo="true" IsFixedHeader="true" Height="660"
       ShowExtendButtons="false" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.CreateTime" FormatString="yyyy-MM-dd HH:mm" Width="128" />
        <TableColumn @bind-Field="@context.Color_view" />
        <TableColumn @bind-Field="@context.InspectedLot" />
        <TableColumn @bind-Field="@context.RollNo" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Score" />
        <TableColumn @bind-Field="@context.TotalScore" />
        <TableColumn @bind-Field="@context.Grade" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableFooter>
        <TableFooterCell />
        <TableFooterCell Text="合计:" ColspanCallback="(context) => 3" />
        <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(InspectedRoll_View.RollNo)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedRoll_View.Weight)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedRoll_View.Meters)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedRoll_View.Yards)" />
        <TableFooterCell Colspan=4 />
    </TableFooter>
</Table>

@code {
    [Parameter]
    public InspectedLot_View Lot_View { get; set; }

    private InspectedRollSearcher SearchModel = new InspectedRollSearcher();
    private Table<InspectedRoll_View> dataTable;

    protected override async Task OnInitializedAsync()
    {
        SearchModel.LotNo = Lot_View.LotNo;
        SearchModel.OrderDetailId = Lot_View.OrderDetailId;
        SearchModel.Limit = 10000;
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<InspectedRoll_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<InspectedRoll_View>("/api/InspectedRoll/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }
}