@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;
@implements IResultDialog;

<Table @bind-Items="DetailList" TItem="ProductInboundRoll" TableSize="TableSize.Compact" 
       IsStriped="true" IsBordered="true" IsExcel="true" ShowToolbar="true" ShowRefresh="false"
       OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
       ShowExtendButtons="true" IsFixedHeader="true" Height="380" ShowFooter="true" ShowEmpty="true"
       IsMultipleSelect="false" IsHideFooterWhenNoData="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.RollNo" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <FooterTemplate>
        <TableFooterCell />
        <TableFooterCell Text="合计:" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(OrderDetail.Meters)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(OrderDetail.KG)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(OrderDetail.Yards)" />
        <TableFooterCell />
        <TableFooterCell ColspanCallback="(x)=>3"/>
    </FooterTemplate>
</Table>

@code {


    [Parameter]
    public ProductInboundLot lot { get; set; }
    [Parameter]
    public List<ProductInboundRoll> Detail { get; set; } 
    [Parameter]
    public EventCallback<List<ProductInboundRoll>> DetailChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }


    protected override async Task OnInitializedAsync()
    {
        if (lot.Pcs > 0)
        {
            //var response = await WtmBlazor.Api.CallAPI<string>($"/api/ProductInboundRoll/SearchByLotId/{lot.ID.ToString()}");
            //Detail = JsonConvert.DeserializeObject<List<ProductInboundRoll>>(response.Data);
            var rq = await WtmBlazor.Api.CallAPI<List<ProductInboundRoll>>($"/api/ProductInboundRoll/SearchByLotId/{lot.ID.ToString()}" );
            Detail = rq.Data;
        }
        await base.OnInitializedAsync();
    }



    public IEnumerable<ProductInboundRoll> DetailList
    {
        get { return Detail; }
        set
        {
            Detail = value.ToList();
        }
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (DetailChanged.HasDelegate)
            {
                await DetailChanged.InvokeAsync(Detail);
            }
        }
    }
    //子表Excel模式,更新方法
    private async Task<ProductInboundRoll> OnAddAsync()
    {
        var od = new ProductInboundRoll();
        if (Detail is null) Detail = new();
        if (Detail.Count > 0)
        {
            od.RollNo = Detail.Max(x => x.RollNo) + 1;
        }
        else
        {
            od.RollNo = 1;
        }
        Detail.Insert(Detail.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        // 对象已经更新
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private  Task<bool> OnDeleteAsync(IEnumerable<ProductInboundRoll> items)
    {
        Detail.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
}