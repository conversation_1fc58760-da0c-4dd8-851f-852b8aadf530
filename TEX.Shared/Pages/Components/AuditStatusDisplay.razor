@namespace BootstrapBlazor.Components
@using TEX.Model.Models

<Button IsDisabled="true" Color="@color" Size="Size.ExtraSmall">@status</Button>


    @code{

    public Color color { get; set; }


    [Parameter]
    public AuditStatusEnum statusEnum { get; set; }


    private string status{ get; set; }

    protected override void OnParametersSet()
    {
        switch (statusEnum)
        {
            case AuditStatusEnum.AuditedApproved:
                status = "已审核";
                color = Color.Success;
                break;
            case AuditStatusEnum.AuditedFailed:
                status = "审核失败";
                color = Color.Warning;
                break;
            case AuditStatusEnum.Cancelled:
                status = "已取消";
                color = Color.Danger;
                break;
            case AuditStatusEnum.Completed:
                status = "已完成";
                color = Color.Primary;
                break;
            default:
                status = "未审核";
                color = Color.Secondary;
                break;
        }
    }
    
    }

    