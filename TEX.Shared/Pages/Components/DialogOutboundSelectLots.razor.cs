using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using TEX.Model.Models;
using TEX.ViewModel.Finished.InspectedRollVMs;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using TEX.ViewModel.Models.PurchaseOrderVMs;
using WalkingTec.Mvvm.Core;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Components
{
    public partial class DialogOutboundSelectLots : ComponentBase, IResultDialog
    {
        [Parameter]
        public string CustomerId { get; set; }
        
        [Parameter]
        public IEnumerable<ProductOutboundLot_View> SelectedLot { get; set; }
        [Parameter]
        public EventCallback<IEnumerable<ProductOutboundLot_View>> SelectedLotChanged { get; set; }

        [Inject]
        public WtmBlazorContext WtmBlazor { get; set; }

        [Inject]
        [NotNull]
        private MessageService MessageService { get; set; }

        private List<InspectedLot_View> SelectedRows { get; set; } = new();
        private List<TreeViewItem<PurchaseOrderWithDetails_TreeView>> TreeViewData { get; set; }

        //选择颜色后,接收对应的缸号列表
        private List<ProductOutboundLot_View> selectedLot_ViewList { get; set; }= new();

        private async Task OnDoubleClickRow(InspectedLot_View context)
        {
            // InspectedRollSearcher searcher = new InspectedRollSearcher();
            // searcher.OrderDetailId = context.OrderDetailId;
            // searcher.LotNo = context.LotNo;

            //检查是否重复添加
            var exist = selectedLot_ViewList.Any(x => x.OrderDetailId == context.OrderDetailId && x.LotNo == context.LotNo);
            if (exist)
            {
                await MessageService.Show(new MessageOption()
                {
                    ShowMode = MessageShowMode.Single,
                    Content = $"缸号:{context.LotNo}已经存在,请勿重复添加!!",
                    Icon = "fa-solid fa-circle-info",
                    Color = Color.Warning,
                    ShowDismiss = true
                });
                return;
            }

            var rv = await WtmBlazor.Api.CallAPI<List<InspectedRoll>>($"/api/InspectedRoll/GetRollsByOrderDetailIdAndLotNo?orderDetailId={context.OrderDetailId}&lotNo={context.LotNo}");

            var list = rv.Data ?? new List<InspectedRoll>();

            if (list.Count == 0)
            {
                selectedLot_ViewList.Add(new ProductOutboundLot_View
                {
                    //不能使用检验缸号ID,当Bill作废时,这些ID依然存在,只是IsValid为0,会导致主键冲突
                    //ID = context.ID,//出库缸号ID使用检验缸号ID
                    ID = Guid.NewGuid(),
                    OrderDetailId = context.OrderDetailId,
                    OrderNo_view = context.OrderNo,
                    Product_view = context.ProductName,
                    Color = context.Color,
                    LotNo = context.LotNo,
                    Pcs = context.RollCount,
                    Meters = context.TotalMeters,
                    Yards = context.TotalYards,
                    Weight = context.TotalWeight,
                    Remark = "QC"

                });

            }
            else
            {
                selectedLot_ViewList.Add(new ProductOutboundLot_View
                {
                    ID = Guid.NewGuid(),
                    OrderDetailId = context.OrderDetailId,
                    OrderNo_view = context.OrderNo,
                    Product_view = context.ProductName,
                    Color = context.Color,
                    LotNo = context.LotNo,
                    Pcs = context.RollCount,
                    Meters = context.TotalMeters,
                    Yards = context.TotalYards,
                    Weight = context.TotalWeight,
                    Remark = "QC",
                    RollList = list.Select(x => new ProductOutboundRoll
                    {
                        ID = Guid.NewGuid(),
                        RollNo = x.RollNo,
                        Meters = x.Meters,
                        Yards = x.Yards,
                        Weight = x.Weight,
                        Remark = x.Remark

                    }).OrderBy(x => x.RollNo).ToList()
                });
            }
            SelectedLot = selectedLot_ViewList;
            UpdateParameters();

            StateHasChanged();
            await Task.CompletedTask;
        }


        private string parentTableClass = "parent-table-normal";
        private string childTableClass = "child-table-hidden";

        //不new一个报空引用
        private InspectedLotSearcher SearchModel = new();


        private Table<InspectedLot_View> dataTable;
        private async Task<QueryData<InspectedLot_View>> OnSearch(QueryPageOptions opts)
        {
            if (SearchModel.OrderDetailId is null) return null;//初始化页面时返回null,避免查询出所有记录,SearchModel已经new了,默认可以查询出所有记录
            return await StartSearch<InspectedLot_View>("/api/InspectedRoll/SearchLot", SearchModel, opts);
        }

        private async Task DoSearch()
        {
            await dataTable.QueryAsync();
        }


        protected override async Task OnInitializedAsync()
        {
            
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            if (CustomerId is not null)
            {
                TreeViewData = await GetTreeViewData(CustomerId);
            }
        }

        private async Task<List<TreeViewItem<PurchaseOrderWithDetails_TreeView>>> GetTreeViewData(string id)
        {
            var rv = await WtmBlazor.Api.CallAPI<List<PurchaseOrderWithDetails_TreeView>>($"/api/Models/PurchaseOrder/GetPOrderTreeItem/{id}");
            var r = CascadingTree(rv.Data).ToList();

            return r;
        }

        /// <summary>
        /// 树状数据层次化方法
        /// </summary>
        /// <param name="items">数据集合</param>
        /// <param name="parent">父级节点</param>
        public static IEnumerable<TreeViewItem<PurchaseOrderWithDetails_TreeView>> CascadingTree(IEnumerable<PurchaseOrderWithDetails_TreeView> items, TreeViewItem<PurchaseOrderWithDetails_TreeView> parent = null) => items.CascadingTree(null,
            (foo, parent) => foo.ParentId == parent?.Value.ID,
            foo => new TreeViewItem<PurchaseOrderWithDetails_TreeView>(foo)
            {
                Text = foo.Text,
                Icon = foo.Icon,
                IsActive = foo.IsActive
            }).ToList();

        private async Task OnTreeItemClick(TreeViewItem<PurchaseOrderWithDetails_TreeView> item)
        {
            if (item.Value.ParentId is not null)
            {
                SearchModel.OrderDetailId = item.Value.ID;

                if (dataTable is not null)
                {
                    await DoSearch();
                }
                StateHasChanged();
            }
        }


        public void UpdateParameters()
        {
            if (SelectedLotChanged.HasDelegate)
            {
                SelectedLotChanged.InvokeAsync(SelectedLot);
            }
        }

        public void OnClose()
        {
            if (SelectedLotChanged.HasDelegate)
            {
                SelectedLotChanged.InvokeAsync(SelectedLot);
            }
        }

        public async Task OnClosing(DialogResult result)
        {
            // 无论对话框是如何关闭的，都需要更新SelectedLot值
            if (SelectedLotChanged.HasDelegate)
            {
                await SelectedLotChanged.InvokeAsync(selectedLot_ViewList);
            }
        }


        public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
        {
            if (searcher != null)
            {
                searcher.IsEnumToString = false;
            }
            var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
            QueryData<T> data = new QueryData<T>();
            if (rv.StatusCode == System.Net.HttpStatusCode.OK)
            {
                data.Items = rv.Data?.Data;
                data.TotalCount = rv.Data?.Count ?? 0;
            }
            if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
            }
            return data;
        }

        //更新Roll

        //Roll孙表绑定数据
        List<ProductOutboundRoll> RollDetailList = new List<ProductOutboundRoll>();
        public IEnumerable<ProductOutboundRoll> rollDetailList
        {
            get { return RollDetailList; }
            set
            {
                RollDetailList = value.ToList();
            }
        }

        private Task<bool> OnDeleteRollAsync(IEnumerable<ProductOutboundRoll> items)
        {
            RollDetailList.RemoveAll(i => items.ToList().Contains(i));
            return Task.FromResult(true);
        }
        private Task<bool> OnDeleteLotAsync(IEnumerable<ProductOutboundLot> items)
        {
            selectedLot_ViewList.RemoveAll(i => items.ToList().Contains(i));
            UpdateParameters();
            return Task.FromResult(true);
        }

        ProductOutboundLot_View SelectLot { get; set; }//存储当前选择的Lot
        //行明细按钮点击控制显示Roll表格
        private void OnDetailsClick(ProductOutboundLot_View item)
        {
            RollDetailList = item.RollList ?? new List<ProductOutboundRoll>();
            if (SelectLot is not null && SelectLot.ID == item.ID)
            {
                //点击时,切换显示状态
                parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
                childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";

            }
            else
            {
                SelectLot = item;
                
                parentTableClass = "parent-table-compressed";
                childTableClass = "child-table-visible";
            }
            StateHasChanged();
        }

        private async Task OnLotRowClick(ProductOutboundLot_View item)
        {
            OnDetailsClick(item);
            await Task.CompletedTask;
        }

        private async Task SaveRolls()
        {
            var list = new List<ProductOutboundRoll>();

            //去除无数据Roll
            foreach (var item in RollDetailList)
            {
                if (item.Meters != 0 || item.Weight != 0 || item.Yards != 0)
                {
                    list.Add(item);
                }
            }
            SelectLot.RollList = list;

            SelectLot.Pcs = SelectLot.RollList.Count;
            SelectLot.Weight = SelectLot.RollList.Sum(x => x.Weight);
            SelectLot.Meters = SelectLot.RollList.Sum(x => x.Meters);
            SelectLot.Yards = SelectLot.RollList.Sum(x => x.Yards);
            int index = selectedLot_ViewList.FindIndex(x => x.ID == SelectLot.ID);
            selectedLot_ViewList[index] = SelectLot;

            parentTableClass = "parent-table-normal";
            childTableClass = "child-table-hidden";

            RollDetailList.Clear();
            //StateHasChanged(); //Click事件是EventCallback类型,本身会触发StateHasChanged，所以不需要再次触发
            await Task.CompletedTask;
        }

        public Task OnClose(DialogResult result)
        {
            if (SelectedLotChanged.HasDelegate)
            {
                SelectedLotChanged.InvokeAsync(selectedLot_ViewList);
            }
            return Task.CompletedTask;
        }
    }
}