@using System.Diagnostics.CodeAnalysis;
@using BootstrapBlazor.Shared
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.InspectedRollVMs
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@using TEX.ViewModel.Models.OrderDetailVMs;

@inherits ComponentBase;
@* @inherits BasePage *@
@implements IResultDialog

<Stack IsRow="true" style="height:47rem;">
    @* 左侧TreeView,用来选择订单明细 *@
    <StackItem class="parent-table">
        <TreeView TItem="PurchaseOrderWithDetails_TreeView" Items="@TreeViewData" ShowSearch="true" OnTreeItemClick="@OnTreeItemClick" ClickToggleNode="true" style="height:100%;" IsAccordion="true"></TreeView>
    </StackItem>
    @* 右侧上方使用Tab展示两个表格，用来显示订单明细的成品检验和成品入库信息 *@
    <StackItem class="child-table">
        <Stack Justify="StackJustifyContent.Around">
            <StackItem style="height:18rem;">

                <Tab IsCard="false" style="padding:6px;">
                    <TabItem Text="成品检验" Icon="fa-solid fa-user">
                        <Table @ref="dataTable" TItem="InspectedLot_View" OnQueryAsync="OnSearch" IsPagination="false" IsStriped="true" IsBordered="true" ShowRefresh="false" Height="300" TableSize="TableSize.Compact"
                               ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false" ShowDefaultButtons="false" ShowExtendEditButton="false"
                               OnDoubleClickRowCallback="(context)=>OnDoubleClickRow(context)" 
                               IsFixedFooter="true" ShowFooter="true" IsFixedHeader="true"
                               @bind-SelectedRows="@SelectedRows" ShowExtendDeleteButton="false">
                            <TableColumns>
                                @* <TableColumn @bind-Field="@context.OrderNo" />
                                <TableColumn @bind-Field="@context.ProductName" /> *@
                                <TableColumn @bind-Field="@context.Color" />
                                <TableColumn @bind-Field="@context.LotNo" />
                                <TableColumn @bind-Field="@context.RollCount" />
                                <TableColumn @bind-Field="@context.TotalMeters" />
                                <TableColumn @bind-Field="@context.TotalWeight" />
                                <TableColumn @bind-Field="@context.TotalYards" />
                            </TableColumns>
                            <TableFooter>
                                <TableFooterCell Text="合计:" />
                                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(InspectedLot_View.LotNo)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.RollCount)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalMeters)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalWeight)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalYards)" />
                            </TableFooter>
                        </Table>
                    </TabItem>
                    <TabItem Text="成品入库" Icon="fa-solid fa-user">
                        <Table @ref="inboundLotdataTable" TItem="ProductInboundLot_View" OnQueryAsync="InboundLotOnSearch" IsPagination="false" IsStriped="true" IsBordered="true" ShowRefresh="false" Height="300" TableSize="TableSize.Compact"
                               ShowToolbar="true" IsMultipleSelect="false" ShowExtendButtons="false" ShowDefaultButtons="false" ShowExtendEditButton="false"
                               OnDoubleClickRowCallback="(context)=>ProductInboundLotOnDoubleClickRow(context)" IsFixedFooter="true" ShowFooter="true" IsFixedHeader="true"
                               ShowExtendDeleteButton="false" style="margin-top:0px;">
                            <TableColumns>
                                @* <TableColumn @bind-Field="@context.OrderNo" />
                                <TableColumn @bind-Field="@context.ProductName" /> *@
                                <TableColumn @bind-Field="@context.Color" />
                                <TableColumn @bind-Field="@context.LotNo" />
                                <TableColumn @bind-Field="@context.Pcs" />
                                <TableColumn @bind-Field="@context.Meters" />
                                <TableColumn @bind-Field="@context.Weight" />
                                <TableColumn @bind-Field="@context.Yards" />
                            </TableColumns>
                            <TableFooter>
                                <TableFooterCell Text="合计:" />
                                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(InspectedLot_View.LotNo)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.RollCount)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalMeters)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalWeight)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(InspectedLot_View.TotalYards)" />

                            </TableFooter>
                        </Table>
                    </TabItem>
                </Tab>

            </StackItem>
            @* 右侧下方一个表格，用来展示选择的成品出库信息 *@
            <StackItem>
                @* 两个主子表格左右并排,主表60%，子表40% *@
                <div class="twotable-container">
                    <div class="main-table">
                        <Table TItem="ProductOutboundLot_View" ShowRefresh="false" EditDialogSize="Size.Medium" @bind-Items="SelectedLot_ViewList"
                               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true" ShowFooter="true"
                               ShowEditButton="false" ShowDeleteButton="true" IsFixedHeader="true" ShowExtendEditButton="false"
                               IsMultipleSelect="true" ShowExtendDeleteButton="false" OnClickRowCallback="(context)=>OnLotRowClick(context)"
                               ShowToolbar="true" IsFixedFooter="true" ShowExtendButtons="true" IsBordered="true"
                               Height="380">
                            <TableColumns>
                                <TableColumn @bind-Field="@context.Color" />
                                <TableColumn @bind-Field="@context.LotNo" />
                                <TableColumn @bind-Field="@context.Pcs" />
                                <TableColumn @bind-Field="@context.Meters" />
                                <TableColumn @bind-Field="@context.Weight" />
                                <TableColumn @bind-Field="@context.Yards" />
                            </TableColumns>
                            <RowButtonTemplate>
                                <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
                            </RowButtonTemplate>
                            <TableFooter>
                                <TableFooterCell Text="" />
                                <TableFooterCell Text="合计:" />
                                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundLot_View.LotNo)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Pcs)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Meters)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Weight)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Yards)" />
                                <TableFooterCell Colspan="1" />
                            </TableFooter>
                        </Table>
                    </div>

                    <div class="detail-table">
                        <Table @bind-Items="rollDetailList" TItem="ProductOutboundRoll"
                               IsExcel="true" ShowToolbar="true" ShowRefresh="false"
                               OnAddAsync="@OnAddRollAsync" OnSaveAsync="@OnSaveRollAsync" IsBordered="true" ShowFooter="true"
                               OnDeleteAsync="@OnDeleteRollAsync" ShowToastAfterSaveOrDeleteModel="false" TableSize="TableSize.Compact" IsMultipleSelect="true" ShowExtendButtons="false"
                               IsFixedFooter="true" IsFixedHeader="true" Height="380">
                            <TableColumns>
                                <TableColumn @bind-Field="@context.RollNo" />
                                <TableColumn @bind-Field="@context.Meters" />
                                <TableColumn @bind-Field="@context.Yards" />
                                <TableColumn @bind-Field="@context.Weight" />
                                <TableColumn @bind-Field="@context.Grade" />
                                <TableColumn @bind-Field="@context.Remark" />
                            </TableColumns>
                            <TableToolbarTemplate>
                                <TableToolbarButton Color="Color.Warning" Icon="fa fa-info-save" Text="保存" OnClick="@SaveRolls" />
                            </TableToolbarTemplate>
                            <TableFooter>
                                <TableFooterCell Text="合:" />
                                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundRoll.RollNo)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundRoll.Meters)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundRoll.Weight)" />
                                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundRoll.Yards)" />
                            </TableFooter>
                        </Table>
                    </div>
                </div>
            </StackItem>
        </Stack>

    </StackItem>

</Stack>
<style>
    .parent-table {
        width: 18%;
        margin: 0px 8px;
        /* flex-shrink: 0;  防止缩小 */
        display: inline-block;
    }

    .child-table {
        width: 82%;
        display: inline-block;
    }

    .parent-table-normal {
        width: 100%;
        display: inline-block;
    }

    .parent-table-compressed {
        width: 60%;
        display: inline-block;
    }

    .child-table-hidden {
        display: none;
    }

    .child-table-visible {
        width: 40%;
        display: inline-block;
    }

    .twotable-container {
        display: flex;
        /* gap: 16px; */
        margin-top: 8px;
    }

    .main-table {
        width: 60%;
        margin-left: 0px;
    }

    .detail-table {
        width: 40%;
        margin-right: 0px;
    }

    .tabs .tabs-body {
        padding: 0;
    }
</style>

