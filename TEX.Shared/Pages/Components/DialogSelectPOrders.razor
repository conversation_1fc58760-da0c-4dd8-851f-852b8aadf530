@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Models.PurchaseOrderVMs;

@inherits ComponentBase;
@implements IResultDialog;

<WTSearchPanel OnSearch="@PurchaseOrderListVMDoSearch">
    <ValidateForm Model="@Model">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@Model.Searcher.CreateDate" />
            <WTDateRange @bind-Value="@Model.Searcher.DeliveryDate" />
            <Select @bind-Value="@Model.Searcher.CustomerId" Items="AllCustomers" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g9f28b6462e5e49b19e7aeb160cacd187" ShowSearch="true" />
            <Select @bind-Value="@Model.Searcher.OrderId" Items="AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" ShowSearch="true" />
            <BootstrapInput @bind-Value="@Model.Searcher.CustomerOrderNo" />
            <BootstrapInput @bind-Value="@Model.Searcher.Merchandiser" />
            <Select @bind-Value="@Model.Searcher.OrderType" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g72d2e92de42140be8d2fe343bf966e79" />
            <Select @bind-Value="@Model.Searcher.ProductId" Items="AllProducts" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
        </Row>

    </ValidateForm>
</WTSearchPanel>
<div style="height:500px">
    <Table @ref="PurchaseOrderListVMdataTable" TItem="PurchaseOrder_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
           IsStriped="true" IsBordered="true" IsFixedHeader="true"
           ShowToolbar="false" IsMultipleSelect="true"
           SelectedRows="@SelectedRows" ShowDefaultButtons="false" IsPagination="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.PurchaseOrder_CreateDate" Text="@WtmBlazor.Localizer["Page.下单日期"]" FormatString="yyyy-MM-dd" />
            <TableColumn @bind-Field="@context.PurchaseOrder_Customer" Text="@WtmBlazor.Localizer["Page.客户"]" />
            <TableColumn @bind-Field="@context.PurchaseOrder_OrderNo" Text="@WtmBlazor.Localizer["Page.订单号"]" />
            <TableColumn @bind-Field="@context.PurchaseOrder_CustomerOrderNo" Text="@WtmBlazor.Localizer["Page.客户订单号"]" />
            <TableColumn @bind-Field="@context.PurchaseOrder_Product" Text="@WtmBlazor.Localizer["Page.产品名称"]" />
            <TableColumn @bind-Field="@context.PurchaseOrder_TotalMeters" Text="@WtmBlazor.Localizer["Page.总米数"]" Align="Alignment.Right" FormatString="0" />
            <TableColumn @bind-Field="@context.PurchaseOrder_TotalWeight" Text="@WtmBlazor.Localizer["Page.总重量"]" Align="Alignment.Right" FormatString="0" />
            <TableColumn @bind-Field="@context.PurchaseOrder_Light" Text="@WtmBlazor.Localizer["Page.主光源"]" />
        </TableColumns>
    </Table>
</div>
@code {
    [Parameter]
    public PurchaseOrder_View SelectedPurchaseOrder { get; set; }

    [Parameter]
    public EventCallback<PurchaseOrder_View> SelectedPurchaseOrderChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    [NotNull]
    private DialogService DialogServices { get; set; }

    [Inject]
    [NotNull]
    private MessageService MessageService { get; set; }

    private List<PurchaseOrder_View> SelectedRows { get; set; } = new();

    private PurchaseOrderListVM Model = new PurchaseOrderListVM();
    private Table<PurchaseOrder_View> PurchaseOrderListVMdataTable;
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();

    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);


        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys");
        AllCustomers.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts");
        AllProducts.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<PurchaseOrder_View>> OnQueryAsync(QueryPageOptions option)
    {
        return await StartSearch<PurchaseOrder_View>("/api/Models/PurchaseOrder/SearchAuthPurchaseOrder", Model.Searcher, option);
    }

    public async Task<bool> OnClosing(DialogResult result)
    {
        var ret = true;
        if (result == DialogResult.Yes && (!SelectedRows.Any() || SelectedRows.Count > 1))
        {
            await MessageService.Show(new MessageOption()
                {
                    Content = "请选择一个订单！"
                });
            ret = false;
        }
        return ret;
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (SelectedPurchaseOrderChanged.HasDelegate)
            {
                SelectedPurchaseOrder = SelectedRows.FirstOrDefault();
                await SelectedPurchaseOrderChanged.InvokeAsync(SelectedPurchaseOrder);
            }
        }
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
    private async Task PurchaseOrderListVMDoSearch()
    {
        await PurchaseOrderListVMdataTable.QueryAsync();
    }
}