@using Microsoft.AspNetCore.Components
@using System
@using System.ComponentModel.DataAnnotations;

@* 设置命名空间,调用才能识别 *@
@namespace Microsoft.AspNetCore.Components 
@typeparam T where T : Enum //约束泛型为枚举类型

<div>
    <MultiSelect @bind-Value="SelectedEnum" DisplayText="@Text" />
</div>

@code {
    [Parameter]
    public T TValue { get; set; } 

    [Parameter]
    public string Text { get; set; }

    [Parameter]
    public int Value { get; set; } = 0;

    [Parameter]
    public EventCallback<int> ValueChanged { get; set; }


    private List<T> SelectedEnum
    {
        get => IntToListEnum(Value);
        set {
            var x = ListEnumToInt(value);
            Value = x;
            ValueChanged.InvokeAsync(Value); 
        }
    }


    List<T> IntToListEnum(int i)
    {
        List<T> list = new List<T>();
        foreach (T option in Enum.GetValues(typeof(T)))
        {
            if ((i & (int)(object)option) == (int)(object)option)
            {
                list.Add(option);
            }
        }
        return list;
    }

    int ListEnumToInt(List<T> ops)
    {
        int result = 0;
        foreach (T option in ops)
        {
            result |= (int)(object)option;
        }
        return result;
    }
}