@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Models.PurchaseOrderVMs;
@inherits BasePage

@* <div class="input-group"> *@
@* <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="true" /> *@
@*  <div class="input-group">*@
<SelectObject TItem="PurchaseOrder_View" @bind-value="SelectedPurchaseOrder" DisplayText="订单号"
              GetTextCallback="GetTextCallback" DropdownMinWidth="900" Height="800">
    <div style="margin:8px;">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <BootstrapInputGroup>
                <label class="form-label">@WtmBlazor.Localizer["_CreateDate"]</label>
                <WTDateRange @bind-Value="@Model.Searcher.CreateDate" FormatString="yy-MM-dd" />
            </BootstrapInputGroup>
            <BootstrapInputGroup>
                <label class="form-label">@WtmBlazor.Localizer["_Customer"]</label>
                <Select @bind-Value="@Model.Searcher.CustomerId" Items="AllCustomers" ShowSearch="true" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            </BootstrapInputGroup>
            <BootstrapInputGroup>
                <label class="form-label">@WtmBlazor.Localizer["_CustomerOrderNo"]</label>
                <BootstrapInput @bind-Value="@Model.Searcher.CustomerOrderNo" />
            </BootstrapInputGroup>
            <BootstrapInputGroup>
                <label class="form-label">@WtmBlazor.Localizer["_OrderType"]</label>
                <Select @bind-Value="@Model.Searcher.OrderType" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            </BootstrapInputGroup>
            <BootstrapInputGroup>
                <label class="form-label">@WtmBlazor.Localizer["_Product"]</label>
                <Select @bind-Value="@Model.Searcher.ProductId" Items="AllProducts" ShowSearch="true" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            </BootstrapInputGroup>

            <div Style="display: inline-block;">
                <Button Color="Color.Primary" Icon="fa fa-search" Text="清除搜索" OnClick="@ClearSearch" />
                <Button Color="Color.Primary" Icon="fa fa-search" Text="@WtmBlazor.Localizer["Sys.Search"]" OnClick="@PurchaseOrderListVMDoSearch" />
            </div>
        </Row>
    </div>
    <div style="height:680px;margin-top:6px">
        <Table @ref="PurchaseOrderListVMdataTable" TItem="PurchaseOrder_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
               IsStriped="true" IsBordered="true" IsFixedHeader="true"
               ShowToolbar="false" IsMultipleSelect="false" ClickToSelect="true" OnDoubleClickRowCallback="item =>OnDoubleClickRow(item,context) "
               ShowDefaultButtons="false" IsPagination="true">
            <TableColumns Context="v">
                <TableColumn @bind-Field="@v.PurchaseOrder_CreateDate" FormatString="yyyy-MM-dd" />
                <TableColumn @bind-Field="@v.PurchaseOrder_Customer" TextEllipsis="true" ShowTips="true" />
                <TableColumn @bind-Field="@v.PurchaseOrder_OrderNo" TextEllipsis="true" ShowTips="true" />
                @* <TableColumn @bind-Field="@v.PurchaseOrder_CustomerOrderNo" /> *@
                <TableColumn @bind-Field="@v.PurchaseOrder_Product" TextEllipsis="true" ShowTips="true" />
                <TableColumn @bind-Field="@v.PurchaseOrder_TotalWeight" Align="Alignment.Right" FormatString="0.##" />
                <TableColumn @bind-Field="@v.PurchaseOrder_TotalMeters" Align="Alignment.Right" FormatString="0.##" />
                <TableColumn @bind-Field="@v.PurchaseOrder_TotalYards" Align="Alignment.Right" FormatString="0.##" />
                @* <TableColumn @bind-Field="@v.PurchaseOrder_Light" /> *@
            </TableColumns>
        </Table>
    </div>
</SelectObject>
@*   </div>
</div> *@

@code {
    [Parameter]
    public PurchaseOrder_View Value { get; set; }
    [Parameter]
    public EventCallback<PurchaseOrder_View> ValueChanged { get; set; }

    [Parameter]
    public Func<PurchaseOrder_View, Task> OnSelectedChanged { get; set; }


    public PurchaseOrder_View SelectedPurchaseOrder { get; set; }
    private PurchaseOrderListVM Model = new PurchaseOrderListVM();
    private Table<PurchaseOrder_View> PurchaseOrderListVMdataTable;
    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();

    private static string GetTextCallback(PurchaseOrder_View o) => o is null ? null : o.PurchaseOrder_OrderNo;

    protected override async Task OnInitializedAsync()
    {
        if (Value != null)
        {
            SelectedPurchaseOrder = Value;//当有值时,显示订单号
                                          //GetTextCallback(Value);
        }
        Model.Searcher.OrderType = OrderTypeEnum.Fabric;
        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        await base.OnInitializedAsync();
    }
    private async Task<QueryData<PurchaseOrder_View>> OnQueryAsync(QueryPageOptions option)
    {
        //if (FinishingFactoryId is null) //取消接收染厂参数
        {
            return await StartSearch<PurchaseOrder_View>("/api/Models/PurchaseOrder/SearchAuthPurchaseOrder", Model.Searcher, option);
        }
        // else
        // {
        //     var rv= await WtmBlazor.Api.CallAPI<List<PurchaseOrder_View>>("/api/Models/PurchaseOrder/SearchPurchaseOrderByFinishedFactory", HttpMethodEnum.POST, FinishingFactoryId);
        //     return new QueryData<PurchaseOrder_View>() { Items = rv.Data, TotalCount = rv.Data.Count };
        // }
    }

    private async Task PurchaseOrderListVMDoSearch()
    {
        await PurchaseOrderListVMdataTable.QueryAsync();
    }

    private async Task OnDoubleClickRow(PurchaseOrder_View item, ISelectObjectContext<PurchaseOrder_View> context)
    {
        context.SetValue(item);//给SelectObject组件默认提供的context赋值,用于GetTextCallback给组件显示值
        await ValueChanged.InvokeAsync(item); //给绑定对象

        await context.CloseAsync();//关闭下拉框
        if (OnSelectedChanged != null)
        {
            await OnSelectedChanged(item);//给回调方法传参
        }
    }
    private void ClearSearch()
    {
        Model.Searcher = new();
        PurchaseOrderListVMdataTable.QueryAsync();
        //StateHasChanged();
    }
}