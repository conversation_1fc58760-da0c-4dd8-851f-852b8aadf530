@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Greige.KnittingProcessVMs
@inherits BasePage

<SelectObject TItem="KnittingProcess_View" @bind-value="SelectedKnittingProcess" DisplayText="@WtmBlazor.Localizer["_KnittingProcess"]"
              GetTextCallback="GetTextCallback" DropdownMinWidth="900" Height="680">
    <div style="margin:8px;">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            @* <BootstrapInputGroup>
                <label class="form-label">@WtmBlazor.Localizer["_ProcessCode"]</label>
                <Select @bind-Value="@Model.Searcher.ProcessCode" Items="AllProcessCode" ShowSearch="true" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            </BootstrapInputGroup> *@
            <BootstrapInput @bind-Value="@Model.Searcher.ProcessCode" />
            <BootstrapInput @bind-Value="@Model.Searcher.FinishedWeight" />
            <BootstrapInput @bind-Value="@Model.Searcher.FinishedWidth" />

            <Row ItemsPerRow="ItemsPerRow.Two">
                <Button Color="Color.Primary" Icon="fa fa-search" Text="重置" OnClick="@ClearSearch" />
                <Button Color="Color.Primary" Icon="fa fa-search" Text="@WtmBlazor.Localizer["Sys.Search"]" OnClick="@PurchaseOrderListVMDoSearch" />
            </Row>
        </Row>
    </div>
    <div style="height:600px;margin:8px">
        <Table @ref="SelectKnittingProcessTable" TItem="KnittingProcess_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
               IsStriped="true" IsBordered="true" IsFixedHeader="true"
               ShowToolbar="false" IsMultipleSelect="false" ClickToSelect="true" OnDoubleClickRowCallback="item =>OnDoubleClickRow(item,context) "
               ShowDefaultButtons="false" IsPagination="true">
            <TableColumns Context="v">
                <TableColumn @bind-Field="@v.CreateTime" Text="创建日期" FormatString="yyyy-MM-dd" />
                <TableColumn @bind-Field="@v.Category_view" Width="100" />
                <TableColumn @bind-Field="@v.ProcessCode" />
                <TableColumn @bind-Field="@v.FinishedWeight" Width="80" />
                <TableColumn @bind-Field="@v.FinishedWidth" Width="80" />
                <TableColumn @bind-Field="@v.FinishedPileHeight" Width="80" />
                <TableColumn @bind-Field="@v.MachineInfo" Text="机台信息" Width="180">
                    <Template Context="m">
                        <span>@m.Row.MachineInfo.MachineType.GetEnumDisplayName()</span><span>@m.Row.MachineInfo.MachineDiameter</span>寸<span>@m.Row.MachineInfo.Gauge</span>针
                    </Template>
                </TableColumn>
            </TableColumns>
        </Table>
    </div>
</SelectObject>

@code {
    [Parameter]
    public KnittingProcess_View Value { get; set; }
    [Parameter]
    public EventCallback<KnittingProcess_View> ValueChanged { get; set; }
    [Parameter]
    public Func<KnittingProcess_View, Task> OnSelectedChanged { get; set; }
    public KnittingProcess_View SelectedKnittingProcess { get; set; }
    private KnittingProcessListVM Model = new KnittingProcessListVM();
    private Table<KnittingProcess_View> SelectKnittingProcessTable;
    //private List<SelectedItem> AllProcessCode = new List<SelectedItem>();


    private static string GetTextCallback(KnittingProcess_View o) => o is null ? null : o.ProcessCode;

    protected override async Task OnInitializedAsync()
    {
        if (Value != null)
        {
            SelectedKnittingProcess = Value;
        }
        //AllProcessCode = await WtmBlazor.Api.CallItemsApi("/api/KnittingPlan/GetKnittingProcesss", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        await base.OnInitializedAsync();
    }
    private async Task<QueryData<KnittingProcess_View>> OnQueryAsync(QueryPageOptions option)
    {
        return await StartSearch<KnittingProcess_View>("/api/knittingprocess/search", Model.Searcher, option);
    }

    private async Task PurchaseOrderListVMDoSearch()
    {
        await SelectKnittingProcessTable.QueryAsync();
    }

    private async Task OnDoubleClickRow(KnittingProcess_View item, ISelectObjectContext<KnittingProcess_View> context)
    {
        context.SetValue(item);//给SelectObject组件默认提供的context赋值,用于GetTextCallback给组件显示值
        await ValueChanged.InvokeAsync(item); //给绑定对象

        await context.CloseAsync();//关闭下拉框
        if (OnSelectedChanged != null)
        {
            await OnSelectedChanged(item);//给回调方法传参
        }
    }
    private void ClearSearch()
    {
        Model.Searcher = new();
        SelectKnittingProcessTable.QueryAsync();
        //StateHasChanged();
    }
}