@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;
@implements IResultDialog;

<Table @ref="ListVMdataTable" TItem="LotAllocate_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
       IsStriped="true" IsBordered="true" ClickToSelect="true"
       ShowToolbar="false" IsMultipleSelect="true" IsPagination="true"
       SelectedRows="@SelectedRows" ShowDefaultButtons="false" >
    <TableColumns>
        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.Product_View"  />
        <TableColumn @bind-Field="@context.DyeingProductName"  />
        <TableColumn @bind-Field="@context.Color"  />
        <TableColumn @bind-Field="@context.ColorCode"  />
        <TableColumn @bind-Field="@context.LotNo"  />
        <TableColumn @bind-Field="@context.Pcs" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
</Table>

@code {
    [Parameter]
    public string OrderId { get; set; }
    [Parameter]
    public List<LotAllocate_View> SelectedDetail { get; set; }
    [Parameter]
    public EventCallback<List<LotAllocate_View>> SelectedDetailChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    [NotNull]
    private DialogService DialogServices { get; set; }

    [Inject]
    [NotNull]
    private MessageService MessageService { get; set; }

    private List<LotAllocate_View> SelectedRows { get; set; } = new();

    private LotAllocateListVM Model = new();
    private Table<LotAllocate_View> ListVMdataTable;

    private async Task<QueryData<LotAllocate_View>> OnQueryAsync(QueryPageOptions option)
    {
        if (OrderId is not null) Model.Searcher.OrderId = Guid.Parse(OrderId);
        return await StartSearch<LotAllocate_View>("/api/LotAllocate/Search", Model.Searcher, option);
    }

    // public async Task<bool> OnClosing(DialogResult result)
    // {
    //     var ret = true;
        
    //     if (result == DialogResult.Yes && (!SelectedRows.Any() || SelectedRows.Count > 1))
    //     {
    //         await MessageService.Show(new MessageOption()
    //             {
    //                 Content = "请选择一项！"
    //             });
    //         ret = false;
    //     }
    //     return ret;
    // }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (SelectedDetailChanged.HasDelegate)
            {
                SelectedDetail = SelectedRows;
                await SelectedDetailChanged.InvokeAsync(SelectedDetail);
            }
        }
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
    private async Task PlanDetailListVMDoSearch()
    {
        await ListVMdataTable.QueryAsync();
    }
}