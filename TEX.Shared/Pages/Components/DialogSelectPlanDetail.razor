@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Producttion.PlanDetailVMs


@inherits ComponentBase;
@implements IResultDialog;


<Table @ref="PlanDetailListVMdataTable" TItem="PlanDetail_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
       IsStriped="true" IsBordered="true" ClickToSelect="true"
       ShowToolbar="false" IsMultipleSelect="false"
       SelectedRows="@SelectedRows" ShowDefaultButtons="false" >
    <TableColumns>
        <TableColumn @bind-Field="@context.BillNo_view" Text="计划单号" />
        <TableColumn @bind-Field="@context.POrder" />
        <TableColumn @bind-Field="@context.Product" />
        <TableColumn @bind-Field="@context.Color_view" />
        <TableColumn @bind-Field="@context.Pcs" />
        <TableColumn @bind-Field="@context.Qty" />
        <TableColumn @bind-Field="@context.QtyUnit" />
        <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.GreigeBatch" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
</Table>

@code {
    [Parameter]
    public Guid OrderId { get; set; }// = new();
    [Parameter]
    public PlanDetail_View SelectedPlanDetail { get; set; }
    [Parameter]
    public EventCallback<PlanDetail_View> SelectedPlanDetailChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    [NotNull]
    private DialogService DialogServices { get; set; }

    [Inject]
    [NotNull]
    private MessageService MessageService { get; set; }

    private List<PlanDetail_View> SelectedRows { get; set; } = new();

    private PlanDetailListVM Model = new PlanDetailListVM();
    private Table<PlanDetail_View> PlanDetailListVMdataTable;

    private async Task<QueryData<PlanDetail_View>> OnQueryAsync(QueryPageOptions option)
    {
        if (OrderId != Guid.Empty) Model.Searcher.OrderDetailId = OrderId;
        return await StartSearch<PlanDetail_View>("/api/PlanDetail/Search", Model.Searcher, option);
    }

    public async Task<bool> OnClosing(DialogResult result)
    {
        var ret = true;
        
        if (result == DialogResult.Yes && (!SelectedRows.Any() || SelectedRows.Count > 1))
        {
            await MessageService.Show(new MessageOption()
                {
                    Content = "请选择一项！"
                });
            ret = false;
        }
        return ret;
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (SelectedPlanDetailChanged.HasDelegate)
            {
                SelectedPlanDetail = SelectedRows.FirstOrDefault();
                await SelectedPlanDetailChanged.InvokeAsync(SelectedPlanDetail);
            }
        }
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
    private async Task PlanDetailListVMDoSearch()
    {
        await PlanDetailListVMdataTable.QueryAsync();
    }
}