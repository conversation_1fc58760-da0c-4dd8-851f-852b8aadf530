@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Models.OrderDetailVMs;
@inherits MyComponent

<SelectObject TItem="OrderDetail_View" @bind-value="SelectedOrderDetail" DisplayText="订单号"
GetTextCallback="GetTextCallback" DropdownMinWidth="1000" Height="780">
    @* <div style="display: flex;margin:0px 16px;"> *@
    <div class="container">
        <div class="parent-table">
            <TreeView TItem="PurchaseOrderWithDetails_TreeView" Items="@TreeViewData" ShowSearch="false" OnTreeItemClick="@OnTreeItemClick" ClickToggleNode="true" style="height:100%;margin-top:10px;" IsAccordion="true"></TreeView>
        </div>
        <div class="child-table">
            <Table @ref="OrderDetailListVMdataTable" TItem="OrderDetail_View"
            Height="726" IsFixedHeader="true" AllowResizing="true"
            OnQueryAsync="OnQueryAsync" OnDoubleClickRowCallback="item =>OnDoubleClickRow(item,context)"
            IsStriped="true" IsBordered="true" ShowToolbar="false" IsMultipleSelect="false" ClickToSelect="true"
            ShowDefaultButtons="false" IsPagination="true">
                <TableColumns Context="v">
                    <TableColumn @bind-Field="@v.OrderDetail_PurchaseOrder" Text="@WtmBlazor.Localizer["Page.订单号"]" Width="180"/>
                    <TableColumn @bind-Field="@v.OrderDetail_ProductSpec" Text="@WtmBlazor.Localizer["Page.规格"]" Width="100" TextEllipsis="true" ShowTips="true" />
                    <TableColumn @bind-Field="@v.Color" Text="@WtmBlazor.Localizer["Page.颜色"]" Width="120" TextEllipsis="true" ShowTips="true" />
                    @* <TableColumn @bind-Field="@v.EngColor" Text="颜色(英)" /> *@
                    <TableColumn @bind-Field="@v.ColorCode" Text="@WtmBlazor.Localizer["Page.色号"]" Width="100" TextEllipsis="true" ShowTips="true" />
                    <TableColumn @bind-Field="@v.Meters" Text="@WtmBlazor.Localizer["Page.米数"]" />
                    <TableColumn @bind-Field="@v.KG" Text="@WtmBlazor.Localizer["Page.重量"]" />
                    <TableColumn @bind-Field="@v.Yards" Text="@WtmBlazor.Localizer["Page.码数"]" />
                </TableColumns>
            </Table>
        </div>
    </div>
</SelectObject>


@code {
    [Parameter]
    public OrderDetail_View Value { get; set; }
    [Parameter]
    public EventCallback<OrderDetail_View> ValueChanged { get; set; }

    private Boolean isDetail = false;

    [Parameter]
    public Func<OrderDetail_View, Task> OnSelectedChanged { get; set; }


    public OrderDetail_View SelectedOrderDetail { get; set; }
    private OrderDetailListVM Model = new OrderDetailListVM();
    private Table<OrderDetail_View> OrderDetailListVMdataTable;

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllCustomers = new List<SelectedItem>();

    private List<TreeViewItem<PurchaseOrderWithDetails_TreeView>> TreeViewData { get; set; } = new();

    private static string GetTextCallback(OrderDetail_View o) => o is null ? null : o.OrderDetail_PurchaseOrder;

    protected override async Task OnInitializedAsync()
    {
        if (Value != null)
        {
            SelectedOrderDetail = Value;//当有值时,显示订单号
            //GetTextCallback(Value);
        };
        //Model.Searcher.OrderType = OrderTypeEnum.Fabric;
        //AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        //AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        await base.OnInitializedAsync();
    }
    
    private bool _isFirstRender = true;
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (_isFirstRender)
        {
            _isFirstRender = false;
            TreeViewData = await GetTreeViewData();
            StateHasChanged();
        }
        await base.OnAfterRenderAsync(firstRender);
    }
    private async Task<QueryData<OrderDetail_View>> OnQueryAsync(QueryPageOptions option)
    {
        return await StartSearch<OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", Model.Searcher, option);
    }

    private async Task PurchaseOrderListVMDoSearch()
    {
        await OrderDetailListVMdataTable.QueryAsync();
    }

    private async Task OnDoubleClickRow(OrderDetail_View item, ISelectObjectContext<OrderDetail_View> context)
    {
        context.SetValue(item);//给SelectObject组件默认提供的context赋值,用于GetTextCallback给组件显示值
        await ValueChanged.InvokeAsync(item); //给绑定对象

        await context.CloseAsync();//关闭下拉框
        if (OnSelectedChanged != null)
        {
            await OnSelectedChanged(item);//给回调方法传参
        }
    }
    private void ClearSearch()
    {
        Model.Searcher = new();
        OrderDetailListVMdataTable.QueryAsync();
        //StateHasChanged();
    }

    private async Task OnTreeItemClick(TreeViewItem<PurchaseOrderWithDetails_TreeView> item)
    {
        if (item.Value.ParentId is not null)
        {
            Model.Searcher.PurchaseOrderId = item.Value.ID;

            if (OrderDetailListVMdataTable is not null)
            {
                await OrderDetailListVMdataTable.QueryAsync();
            }
            StateHasChanged();
        }
    }

    private async Task<List<TreeViewItem<PurchaseOrderWithDetails_TreeView>>> GetTreeViewData()
    {
        var rv = await WtmBlazor.Api.CallAPI<List<PurchaseOrderWithDetails_TreeView>>($"/api/Models/PurchaseOrder/GetCustomerPOTreeItem");
        if (rv is not null && rv.Data is not null && rv.Data.Count > 0)
        {

            var r = CascadingTree(rv.Data).ToList();
            return r;
        }
        else
        {
            return new List<TreeViewItem<PurchaseOrderWithDetails_TreeView>>();
        }

    }
    public IEnumerable<TreeViewItem<PurchaseOrderWithDetails_TreeView>> CascadingTree(IEnumerable<PurchaseOrderWithDetails_TreeView> items, TreeViewItem<PurchaseOrderWithDetails_TreeView> parent = null) => items.CascadingTree(null,
            (foo, parent) => foo.ParentId == parent?.Value.ID,
            foo => new TreeViewItem<PurchaseOrderWithDetails_TreeView>(foo)
                {
                    Text = foo.Text,
                    Icon = foo.Icon,
                    IsActive = foo.IsActive
                }).ToList();
}