@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@using TEX.ViewModel.Finished.ProductOutboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;
@implements IResultDialog;

<Table @bind-Items="DetailList" TItem="ProductOutboundRoll" TableSize="TableSize.Compact" 
       IsStriped="true" IsBordered="true" IsExcel="true" ShowToolbar="true" ShowRefresh="false"
       OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
       ShowExtendButtons="true" IsFixedHeader="true" Height="400" ShowFooter="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.RollNo" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <FooterTemplate>
        <tr style="text-align: center;font-weight:bold;">
            <td colspan="2">
                <div style="line-height: 2;">
                    合计：共 @context.Count() 卷
                </div>
            </td>
            <td>
                <div>
                    @GetSum(context, "Meters")
                </div>
            </td>
            <td>
                <div>
                    @GetSum(context, "Yards")
                </div>
            </td>
            <td>
                <div>
                    @GetSum(context, "Weight")
                </div>
            </td>
            <td>
            </td>
            <td>
            </td>
 
        </tr>

    </FooterTemplate>
</Table>

@code {


    [Parameter]
    public ProductOutboundLot_View lot { get; set; }
    [Parameter]
    public List<ProductOutboundRoll> Detail { get; set; }
    [Parameter]
    public EventCallback<List<ProductOutboundRoll>> DetailChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }


    protected override async Task OnInitializedAsync()
    {
        if (lot.Pcs > 0)
        {
            var rq = await WtmBlazor.Api.CallAPI<List<ProductOutboundRoll>>($"/api/ProductOutboundRoll/SearchByLotId/{lot.ID.ToString()}" );
            Detail = rq.Data;
        }
        await base.OnInitializedAsync();
    }

    //使用反射对实体类的某一个字段求和
    private static decimal GetSum(IEnumerable<ProductOutboundRoll> items, string fieldName)
    {
        if (!items.Any()) return 0;
        PropertyInfo property = typeof(ProductOutboundRoll).GetProperty(fieldName);
        if (property == null) throw new ArgumentException("Field not found", fieldName);
        return items.Sum(i => Convert.ToDecimal(property.GetValue(i)));
    }

    public IEnumerable<ProductOutboundRoll> DetailList
    {
        get { return Detail; }
        set
        {
            Detail = value.ToList();
        }
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (DetailChanged.HasDelegate)
            {
                await DetailChanged.InvokeAsync(Detail);
            }
        }
    }
    //子表Excel模式,更新方法
    private async Task<ProductOutboundRoll> OnAddAsync()
    {
        var od = new ProductOutboundRoll();
        if (Detail.Count > 0)
        {
            od.RollNo = Detail.Max(x => x.RollNo) + 1;
        }
        else
        {
            od.RollNo = 1;
        }
        Detail.Insert(Detail.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductOutboundRoll item, ItemChangedType changedType)
    {
        // 对象已经更新
        // 输出日志信息
        //Logger.Log($"单元格变化通知 类: OrderDetail - 值: DateTime {item.DateTime}");
        return Task.FromResult(true);
    }

    private  Task<bool> OnDeleteAsync(IEnumerable<ProductOutboundRoll> items)
    {
        Detail.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
} 