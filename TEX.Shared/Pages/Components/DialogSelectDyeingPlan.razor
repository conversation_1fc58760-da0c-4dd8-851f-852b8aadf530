@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Producttion.DyeingPlanVMs
@using TEX.ViewModel.Producttion.PlanDetailVMs

@inherits ComponentBase;
@implements IResultDialog;


<Table @ref="DyeingPlanListVMdataTable" TItem="DyeingPlan_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
       IsStriped="true" IsBordered="true"
       ShowToolbar="false" IsMultipleSelect="false"
       SelectedRows="@SelectedRows" ShowDefaultButtons="false" IsPagination="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
        <TableColumn @bind-Field="@context.BillNo" />
        <TableColumn @bind-Field="@context.OrderNo_view" />
        <TableColumn @bind-Field="@context.ProductName_view" />
        <TableColumn @bind-Field="@context.FinishingFactoryName_view" Text=@WtmBlazor.Localizer["_FinishingFactory"] />
        <TableColumn @bind-Field="@context.PlanBatch" Width="60" />
        <TableColumn @bind-Field="@context.Light" Width="60" />
        <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.TotalQty" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.AuditStatus" Width="70">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <DetailRowTemplate>
        <TEX.Shared.Pages.Producttion.PlanDetail.PlanDetailTable Id="@context.ID" @bind-SelectedPlanDetail="pd"></TEX.Shared.Pages.Producttion.PlanDetail.PlanDetailTable>
    </DetailRowTemplate>
</Table>

@code {
    [Parameter]
    public DyeingPlan_View SelectedDyeingPlan { get; set; }
    public PlanDetail_View pd { get; set; } 

    [Parameter]
    public EventCallback<DyeingPlan_View> SelectedDyeingPlanChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    [NotNull]
    private DialogService DialogServices { get; set; }

    [Inject]
    [NotNull]
    private MessageService MessageService { get; set; }

    private List<DyeingPlan_View> SelectedRows { get; set; } = new();

    private DyeingPlanListVM Model = new DyeingPlanListVM();
    private Table<DyeingPlan_View> DyeingPlanListVMdataTable;
    private SearchHelper sc = new();

    private async Task<QueryData<DyeingPlan_View>> OnQueryAsync(QueryPageOptions option)
    {
        return await sc.StartSearch<DyeingPlan_View>("/api/DyeingPlan/Search", Model.Searcher, option);
        //return await StartSearch<DyeingPlan_View>("/api/DyeingPlan/Search", Model.Searcher, option);
    }

    public async Task<bool> OnClosing(DialogResult result)
    {
        var ret = true;
        if (result == DialogResult.Yes && (pd==null ))
        {
            await MessageService.Show(new MessageOption()
                {
                    Content = "请选择一个产品！"
                });
            ret = false;
        }
        // if (result == DialogResult.Yes && (!SelectedRows.Any() || SelectedRows.Count > 1))
        // {
        //     await MessageService.Show(new MessageOption()
        //         {
        //             Content = "请选择一个产品！"
        //         });
        //     ret = false;
        // }
        return ret;
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (SelectedDyeingPlanChanged.HasDelegate)
            {
                SelectedDyeingPlan = SelectedRows.FirstOrDefault();
                SelectedDyeingPlan.DetailList.Add(pd);
                await SelectedDyeingPlanChanged.InvokeAsync(SelectedDyeingPlan);
            }
        }
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
    private async Task DyeingPlanListVMDoSearch()
    {
        await DyeingPlanListVMdataTable.QueryAsync();
    }
}