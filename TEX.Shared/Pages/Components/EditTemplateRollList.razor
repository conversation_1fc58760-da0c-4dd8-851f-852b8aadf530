@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;
@implements IResultDialog

<Row ItemsPerRow="ItemsPerRow.Two">
    <Select @bind-Value="@Lot.OrderDetailId" Items="@AllOrderDetails" PlaceHolder="请选择" OnSelectedItemChanged="@OnSelectedItemChanged" />
    <BootstrapInput @bind-Value="@Lot.LotNo" />
    <BootstrapInput @bind-Value="@Lot.Location" />
    <BootstrapInput @bind-Value="@Lot.Remark" />
</Row>
<Table @bind-Items="DetailList" TItem="ProductInboundRoll" TableSize="TableSize.Compact"
       IsStriped="true" IsBordered="true" IsExcel="true" ShowToolbar="true" ShowRefresh="false"
       OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
       ShowExtendButtons="true" IsFixedHeader="true" Height="380" IsFixedFooter="true" 
       ShowFooter="true" ShowEmpty="true" style="margin-top:16px;"
       IsMultipleSelect="false" IsHideFooterWhenNoData="true" ExtendButtonColumnWidth="60">
    <TableColumns>
        <TableColumn @bind-Field="@context.RollNo" />
        <TableColumn @bind-Field="@context.Meters" />
        <TableColumn @bind-Field="@context.Weight" />
        <TableColumn @bind-Field="@context.Yards" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <FooterTemplate>
        
        <TableFooterCell Text="合计:" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Meters)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Weight)" />
        <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Yards)" />
        <TableFooterCell />
        <TableFooterCell />
    </FooterTemplate>
</Table>

@code {

    [Parameter]
    public Guid OrderId { get; set; }
    [Parameter]
    public ProductInboundLot Lot { get; set; } = new();
    [Parameter]
    public EventCallback<ProductInboundLot> LotChanged { get; set; }

    [Inject]
    private WtmBlazorContext WtmBlazor { get; set; }

    private List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();

    protected override async Task OnInitializedAsync()
    {
        if(Lot.RollList is null) Lot.RollList = new List<ProductInboundRoll>();
        var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/Models/OrderDetail/GetOrderDetailSelectListItemsByPurchaseOrderId/{OrderId}");
        AllOrderDetails = rv.Data;
        await base.OnInitializedAsync();
    }

    public IEnumerable<ProductInboundRoll> DetailList
    {
        get { return Lot.RollList; }
        set
        {
            Lot.RollList = value.ToList();
        }
    }

    //子表Excel模式,更新方法
    private async Task<ProductInboundRoll> OnAddAsync()
    {
        var od = new ProductInboundRoll();
        if (Lot.RollList is null) Lot.RollList = new();
        if (Lot.RollList.Count > 0)
        {
            od.RollNo = Lot.RollList.Max(x => x.RollNo) + 1;
        }
        else
        {
            od.RollNo = 1;
        }
        Lot.RollList.Insert(Lot.RollList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        Lot.Pcs = Lot.RollList.Count;
        Lot.Weight = Lot.RollList.Sum(x => x.Weight);
        Lot.Meters = Lot.RollList.Sum(x => x.Meters);
        Lot.Yards = Lot.RollList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundRoll> items)
    {
        Lot.RollList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Close)
        {

            //Lot.Color=
            Lot.Pcs = Lot.RollList.Count;
            Lot.Weight = Lot.RollList.Sum(x => x.Weight);
            Lot.Meters = Lot.RollList.Sum(x => x.Meters);
            Lot.Yards = Lot.RollList.Sum(x => x.Yards);
            
            //已经是双向绑定了,不需要回调
            // if (LotChanged.HasDelegate)
            // {
            //     await LotChanged.InvokeAsync(Lot);
            // }
        }
        await Task.CompletedTask;
    }

    private async Task OnSelectedItemChanged(SelectedItem item)
    {
        Lot.Color = item.Text;
        await Task.CompletedTask;
    }
}