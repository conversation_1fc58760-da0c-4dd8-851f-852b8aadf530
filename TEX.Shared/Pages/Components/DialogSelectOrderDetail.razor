@using System.Diagnostics.CodeAnalysis;
@using BootstrapBlazor.Shared
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.InspectedRollVMs
@using TEX.ViewModel.Models.OrderDetailVMs;

@inherits ComponentBase;
@* @inherits BasePage *@
@implements IResultDialog

<div>
    <Table  TItem="OrderDetail_View" OnQueryAsync="OnSearchOrderDetail"
           IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false"
           SelectedRows="SelectedRows" ClickToSelect="true"
           ShowToolbar="@isDetail" IsMultipleSelect="true" ShowExtendButtons="@isDetail"
           ShowDefaultButtons="false" IsPagination="@isDetail">
        <TableColumns>
            <TableColumn @bind-Field="@context.OrderDetail_PurchaseOrder" />
            <TableColumn @bind-Field="@context.OrderDetail_ProductName" />
            <TableColumn @bind-Field="@context.Color" />
            <TableColumn @bind-Field="@context.ColorCode" />
            <TableColumn @bind-Field="@context.Meters" />
            <TableColumn @bind-Field="@context.KG" />
            <TableColumn @bind-Field="@context.Yards" />
        </TableColumns>
    </Table>
</div>


