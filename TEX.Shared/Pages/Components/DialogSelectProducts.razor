@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Models.ProductVMs;

@inherits ComponentBase;
@implements IResultDialog;

<WTSearchPanel OnSearch="@ProductListVMDoSearch">
    <ValidateForm Model="@Model">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@Model.Searcher.ProductCode" />
            <BootstrapInput @bind-Value="@Model.Searcher.ProductName" />
            <BootstrapInput @bind-Value="@Model.Searcher.CategoryId" />
            <BootstrapInput @bind-Value="@Model.Searcher.Contents" />
            <BootstrapInput @bind-Value="@Model.Searcher.Spec" />
            <BootstrapInput @bind-Value="@Model.Searcher.GSM" />
            <BootstrapInput @bind-Value="@Model.Searcher.Width" />
        </Row>

    </ValidateForm>
</WTSearchPanel>
<Table @ref="ProductListVMdataTable" TItem="Product_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
       IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="false"
       SelectedRows="@SelectedRows" ShowDefaultButtons="true" IsPagination="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.Product_ProductCode" Text="@WtmBlazor.Localizer["Page.产品编码"]" />
        <TableColumn @bind-Field="@context.Product_ProductName" Text="@WtmBlazor.Localizer["Page.产品名称"]" />
        <TableColumn @bind-Field="@context.Product_Category" Text="@WtmBlazor.Localizer["Page.产品分类"]" />
        <TableColumn @bind-Field="@context.Product_Contents" Text="@WtmBlazor.Localizer["Page.成份"]" />
        <TableColumn @bind-Field="@context.Product_Spec" Text="@WtmBlazor.Localizer["Page.规格"]" />
        <TableColumn @bind-Field="@context.Product_GSM" Text="@WtmBlazor.Localizer["Page.平方克重"]" />
        <TableColumn @bind-Field="@context.Product_Width" Text="@WtmBlazor.Localizer["Page.有效门幅"]" />
    </TableColumns>
</Table>

@code{
    [Parameter]
    public Product_View SelectedProduct { get; set; }
                                                
    [Parameter]                                     
    public EventCallback<Product_View> SelectedProductChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    [NotNull]
    private DialogService DialogServices { get; set; }

    [Inject]
    [NotNull]
    private MessageService MessageService { get; set; }

    private List<Product_View> SelectedRows { get; set; } = new();

    private ProductListVM Model = new ProductListVM();
    private Table<TEX.ViewModel.Models.ProductVMs.Product_View> ProductListVMdataTable;

    private async Task<QueryData<Product_View>> OnQueryAsync(QueryPageOptions option)
    {
        return await StartSearch<Product_View>("/api/Models/Product/SearchProduct", Model.Searcher, option);
    }

    public async Task<bool> OnClosing(DialogResult result)
    {
        var ret = true;
        if (result == DialogResult.Yes && (!SelectedRows.Any() || SelectedRows.Count>1))
        {
            await MessageService.Show(new MessageOption()
                {
                    Content = "请选择一个产品！"
                });
            ret = false;
        }
        return ret;
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (SelectedProductChanged.HasDelegate)
            {
                SelectedProduct = SelectedRows.FirstOrDefault();
                await SelectedProductChanged.InvokeAsync(SelectedProduct);
            }
        }
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
    private async Task ProductListVMDoSearch()
    {
        await ProductListVMdataTable.QueryAsync();
    }
}