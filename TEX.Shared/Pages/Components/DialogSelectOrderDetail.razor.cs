using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TEX.ViewModel.Models.OrderDetailVMs;
using TEX.ViewModel.Producttion.LotAllocateVMs;
using WalkingTec.Mvvm.Core;
using WtmBlazorUtils;

namespace TEX.Shared.Pages.Components
{
    public partial class DialogSelectOrderDetail
    {
        [Parameter]
        public OrderDetailSearcher Searcher { get; set; }

        [Parameter]
        public List<OrderDetail_View> OrderDetail_View { get; set; }
        [Parameter]
        public EventCallback<List<OrderDetail_View>> OrderDetail_ViewChanged { get; set; }

        //private Table<OrderDetail_View> OrderDetailListVMdataTable;
        public bool isDetail = false;

        public List<OrderDetail_View> SelectedRows { get; set; } = new();
        [Inject]
        public WtmBlazorContext WtmBlazor { get; set; }

        //private async Task OrderDetailListVMDoSearch()
        //{
        //    if (Searcher is null) Searcher = new();

        //    await OrderDetailListVMdataTable.QueryAsync();
        //}
        private async Task<QueryData<OrderDetail_View>> OnSearchOrderDetail(QueryPageOptions opts)
        {
            return await StartSearch<OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", Searcher, opts);
        }




        public async Task OnClose(DialogResult result)
        {
            if (result == DialogResult.Yes)
            {
                if (OrderDetail_ViewChanged.HasDelegate)
                {
                    //await OrderDetail_ViewChanged.InvokeAsync(SelectedRows.FirstOrDefault());
                    await OrderDetail_ViewChanged.InvokeAsync(SelectedRows);
                }
            }
        }

        public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
        {
            if (searcher != null)
            {
                searcher.IsEnumToString = false;
            }
            var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
            QueryData<T> data = new QueryData<T>();
            if (rv.StatusCode == System.Net.HttpStatusCode.OK)
            {
                data.Items = rv.Data?.Data;
                data.TotalCount = rv.Data?.Count ?? 0;
            }
            if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
            }
            return data;
        }

    }
}