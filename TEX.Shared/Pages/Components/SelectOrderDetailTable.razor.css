.container {
    display: flex;
    height: calc(100% - 16px); /* 考虑到可能存在的上下边距 */
    /* width: calc(100% - 16px); */ /* 确保宽度为100% */
}

.parent-table {
    border: 1px solid gray;
    border-radius: 8px;
    width: 25%;
    margin-right: 8px;
    margin-top: 8px;
    display: flex;
    flex-direction: column;
}

.child-table {
    border: 1px solid gray;
    border-radius: 8px;
    width: 75%;
    margin-top: 8px;
    display: flex;
    flex-direction: column;
}

/*.tree-view-container,
.table-container {
    flex-grow: 1;
}*/
