@namespace TEX.Shared.Pages.Components
@using TEX.ViewModel.Models.PurchaseOrderVMs
@inherits ComponentBase

<div class="input-group">
    <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="@ShowRequiredMark" />
    <div class="input-group">
        <Select @bind-Value="SelectedOrderId" ShowSearch="true" ShowLabel="false" Items="AllPurchaseOrders"
        OnSelectedItemChanged="OnOrderSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Button Icon="fa-solid fa-magnifying-glass" OnClick="SelectOrder"></Button>
    </div>
</div>

@code {
    [Parameter]
    public Guid SelectedOrderId { get; set; }
    public PurchaseOrder_View SelectedOrder { get; set; } = new();

    [Parameter]
    public EventCallback<Guid> SelectedOrderIdChanged { get; set; }

    [Parameter]
    public bool ShowRequiredMark { get; set; } = false;

    [Parameter]
    public List<SelectedItem> AllPurchaseOrders { get; set; } = new List<SelectedItem>();

    [Parameter]
    public EventCallback<PurchaseOrder_View> OnSelectedOrder { get; set; } // 新增的回调事件

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    private async Task OnOrderSelect(SelectedItem item)
    {
        if (item != null)
        {
            SelectedOrderId = Guid.Parse(item.Value);
            if (SelectedOrder.ID == Guid.Empty || SelectedOrder.ID != SelectedOrderId) SelectedOrder.ID = SelectedOrderId;
            await SelectedOrderIdChanged.InvokeAsync(SelectedOrderId);
            await OnSelectedOrder.InvokeAsync(SelectedOrder); // 调用外部回调
        }
    }

    private async Task SelectOrder()
    {

        // 这里可以调用弹窗选择订单的逻辑
        var result = await WtmBlazor.Dialog.ShowModal<DialogSelectPOrders>(new ResultDialogOption()
            {
                Title = "请选择订单",
                ButtonYesText = "确定",
                Size = Size.Large,
                ShowHeaderCloseButton = false,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    ["SelectedPurchaseOrder"] = SelectedOrder,
                    ["SelectedPurchaseOrderChanged"] = EventCallback.Factory.Create<PurchaseOrder_View>(this, po => SelectedOrder = po)
                }
            });

        if (result == DialogResult.Yes)
        {
            // 处理选择后的逻辑
            SelectedOrderId=SelectedOrder.ID;
            //var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{SelectedOrderId}");
            await OnOrderSelect(new SelectedItem { Value = SelectedOrderId.ToString() });
        }
    }

}