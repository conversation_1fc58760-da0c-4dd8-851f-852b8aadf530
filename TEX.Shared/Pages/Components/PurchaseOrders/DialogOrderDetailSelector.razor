@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using TEX.Model.Models;
@using TEX.ViewModel.Models.OrderDetailVMs;

@inherits ComponentBase;
@implements IResultDialog;


<div style="height:360px">
<Table @ref="OrderDetailListVMdataTable" TItem="OrderDetail_View" OnQueryAsync="@OnQueryAsync" TableSize="TableSize.Compact"
           IsStriped="true" IsBordered="true" ShowToolbar="false" IsMultipleSelect="true"
           SelectedRows="@SelectedRows" ShowDefaultButtons="false" IsPagination="true">
    <TableColumns>
            <TableColumn @bind-Field="@context.Color" Text="@WtmBlazor.Localizer["Page.颜色"]" />
            <TableColumn @bind-Field="@context.ColorCode" Text="@WtmBlazor.Localizer["Page.色号"]" />
            <TableColumn @bind-Field="@context.Meters" FormatString="0" Align="Alignment.Right" Text="@WtmBlazor.Localizer["Page.米数"]" />
            <TableColumn @bind-Field="@context.KG" FormatString="0" Align="Alignment.Right" Text="@WtmBlazor.Localizer["Page.重量"]" />
            <TableColumn @bind-Field="@context.Yards" FormatString="0" Align="Alignment.Right" Text="@WtmBlazor.Localizer["_Yard"]" />
            <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
</Table>
</div>
@code{
    [Parameter]
    public OrderDetail_View SelectedOrderDetail { get; set; }

    [Parameter]                                     
    public EventCallback<OrderDetail_View> SelectedOrderDetailChanged { get; set; }

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    [NotNull]
    private DialogService DialogServices { get; set; }

    [Inject]
    [NotNull]
    private MessageService MessageService { get; set; }

    private List<OrderDetail_View> SelectedRows { get; set; } = new();

    private OrderDetailListVM Model = new OrderDetailListVM();
    private Table<OrderDetail_View> OrderDetailListVMdataTable;


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<OrderDetail_View>> OnQueryAsync(QueryPageOptions option)
    {
        //限制搜索指定订单号
        Model.Searcher.PurchaseOrderId = SelectedOrderDetail.PurchaseOrderId;
        return await StartSearch<OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", Model.Searcher, option);
    }

    public async Task<bool> OnClosing(DialogResult result)
    {
        var ret = true;
        //限制只能选择一条记录
        if (result == DialogResult.Yes && (!SelectedRows.Any() || SelectedRows.Count>1))
        {
            await MessageService.Show(new MessageOption()
                {
                    Content = "请选择一条记录！"
                });
            ret = false;
        }
        return ret;
    }

    public async Task OnClose(DialogResult result)
    {
        if (result == DialogResult.Yes)
        {
            if (SelectedOrderDetailChanged.HasDelegate)
            {
                SelectedOrderDetail = SelectedRows.FirstOrDefault();
                await SelectedOrderDetailChanged.InvokeAsync(SelectedOrderDetail);
            }
        }
    }


    public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
    {
        if (searcher != null)
        {
            searcher.IsEnumToString = false;
        }
        var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
        QueryData<T> data = new QueryData<T>();
        if (rv.StatusCode == System.Net.HttpStatusCode.OK)
        {
            data.Items = rv.Data?.Data;
            data.TotalCount = rv.Data?.Count ?? 0;
        }
        if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
        }
        return data;
    }
    private async Task OrderDetailListVMDoSearch()
    {
        await OrderDetailListVMdataTable.QueryAsync();
    }
}