@namespace TEX.Shared.Pages.Components
@using TEX.Shared.Pages.Components.PurchaseOrders
@using TEX.ViewModel.Models.OrderDetailVMs
@inherits ComponentBase


<div class="input-group">
    <BootstrapInputGroupLabel DisplayText="订单号" ShowRequiredMark="@ShowRequiredMark" />
    <div class="input-group">
        <Select @bind-Value="SelectedOrderDetailId" ShowSearch="true" ShowLabel="false" Items="AllOrderDetails"
        OnSelectedItemChanged="OnOrderSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        <Button Icon="fa-solid fa-magnifying-glass" OnClick="SelectOrder"></Button>
    </div>
</div>

@code {
    [Parameter]
    public Guid? SelectedOrderDetailId { get; set; }
    [Parameter]
    public EventCallback<Guid?> SelectedOrderDetailIdChanged { get; set; }

    [Parameter]
    public bool ShowRequiredMark { get; set; } = false;

    [Parameter]
    public List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();

    [Parameter]
    public EventCallback<OrderDetail_View> OnSelected { get; set; } // 新增的回调事件

    [Inject]
    public WtmBlazorContext WtmBlazor { get; set; }

    public OrderDetail_View SelectedOrderDetail { get; set; } = new();

    private async Task OnOrderSelect(SelectedItem item)
    {
        if (item != null)
        {
            if (Guid.TryParse(item.Value, out Guid parsedGuid))
            {
                SelectedOrderDetailId = parsedGuid;
                if (SelectedOrderDetail.ID == Guid.Empty || SelectedOrderDetail.ID != SelectedOrderDetailId)
                    SelectedOrderDetail.ID = parsedGuid;
                await SelectedOrderDetailIdChanged.InvokeAsync(SelectedOrderDetailId);
                await OnSelected.InvokeAsync(SelectedOrderDetail); // 调用外部回调
            }
            else
            {
                // 处理无法解析的情况
                // 例如，设置默认值，或者显示错误信息
                SelectedOrderDetailId = Guid.Empty;
                // 显示错误信息的逻辑
            }
        }
    }

    private async Task SelectOrder()
    {
        // 这里可以调用弹窗选择订单明细的逻辑
        var result = await WtmBlazor.Dialog.ShowModal<DialogOrderDetailSelector>(new ResultDialogOption()
            {
                Title = "请选择订单",
                ButtonYesText = "确定",
                Size = Size.Large,
                ShowHeaderCloseButton = false,
                ButtonYesIcon = "fa-solid fa-magnifying-glass",
                ComponentParameters = new Dictionary<string, object>
                {
                    ["SelectedOrderDetail"] = SelectedOrderDetail,
                    ["SelectedOrderDetailChanged"] = EventCallback.Factory.Create<OrderDetail_View>(this, po => SelectedOrderDetail = po)
                }
            });

        if (result == DialogResult.Yes)
        {
            // 处理选择后的逻辑
            SelectedOrderDetailId=SelectedOrderDetail.ID;
            //var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{SelectedOrderId}");
            await OnOrderSelect(new SelectedItem { Value = SelectedOrderDetailId.ToString() });
        }
    }

}