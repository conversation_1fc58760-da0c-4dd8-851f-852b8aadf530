@namespace TEX.Shared.Pages.Components
@using TEX.ViewModel.Models.OrderDetailVMs
@using System.ComponentModel.DataAnnotations
@inherits MyComponent;


<Table @ref="OrderDetailListVMdataTable" TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View" OnQueryAsync="OrderDetailListVMOnSearchOrderDetail" 
IsStriped="true" IsBordered="true"
ShowDefaultButtons="false"  TableSize="TableSize.Compact">
    <TableColumns>
        @* <TableColumn @bind-Field="@context.OrderDetail_PurchaseOrder" Text="@WtmBlazor.Localizer["Page.订单号"]" /> *@
        <TableColumn @bind-Field="@context.Color" Text="@WtmBlazor.Localizer["Page.颜色"]"  />
        @* <TableColumn @bind-Field="@context.OrderDetail_EngColor" Text="颜色(英)" Width="160" /> *@
        <TableColumn @bind-Field="@context.ColorCode" Text="@WtmBlazor.Localizer["Page.色号"]" />
        <TableColumn @bind-Field="@context.Meters" Text="@WtmBlazor.Localizer["Page.米数"]" FormatString="0" Align="Alignment.Right"/>
        <TableColumn @bind-Field="@context.KG" Text="@WtmBlazor.Localizer["Page.重量"]" FormatString="0.0" Align="Alignment.Right" />
        <TableColumn @bind-Field="@context.Yards" Text="@WtmBlazor.Localizer["Page.码数"]" FormatString="0" Align="Alignment.Right" />
        <TableColumn @bind-Field="@context.Price" Text="@WtmBlazor.Localizer["Page.单价"]" Visible="@isShowPrice"/>
        <TableColumn @bind-Field="@context.Amount" Text="@WtmBlazor.Localizer["Page.金额"]" Visible="@isShowPrice" />
    </TableColumns>

</Table>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }

    private bool isShowPrice=false;

    private OrderDetailListVM Model = new OrderDetailListVM();
    private Table<TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View> OrderDetailListVMdataTable;
    private async Task OrderDetailListVMDoSearch()
    {
        await OrderDetailListVMdataTable.QueryAsync();
    }
    private async Task<QueryData<TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View>> OrderDetailListVMOnSearchOrderDetail(QueryPageOptions opts)
    {
        var rv = await StartSearch<TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View>("/api/Models/PurchaseOrder/SearchOrderDetail", Model.Searcher, opts);
        
        //根据用户角色过滤字段权限
        var l=UserInfo.Roles.Where(x => x.RoleCode.StartsWith("8") || x.RoleCode.Contains("001")).ToList();

        isShowPrice = l.Count != 0? true:false;
        
        return rv;
    }
    
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        if (id is not null) Model.Searcher.PurchaseOrderId = Guid.Parse(id);
        await base.OnInitializedAsync();
    }
}
