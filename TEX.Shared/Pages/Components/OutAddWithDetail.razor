@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductOutboundLotVMs
@using TEX.ViewModel.Finished.ProductOutboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;

<div style="white-space: nowrap;">
    <div class="@parentTableClass">
        <Table TItem="ProductOutboundLot" @bind-Items="@DetailList" ShowRefresh="false" EditDialogSize="Size.Medium"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true" ShowFooter="true" IsFixedFooter="true" IsExcel="true"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync"
               OnDeleteAsync="@OnDeleteAsync" ShowToastAfterSaveOrDeleteModel="false"
               ShowEditButton="false" ShowDeleteButton="false" IsFixedHeader="true"
               IsMultipleSelect="false"
               ShowToolbar="true" ShowExtendButtons="true" IsBordered="true"
               IsHideFooterWhenNoData="true" Height="360" style="margin:16px 0;">
            <TableColumns>
                <TableColumn @bind-Field="@context.LotNo" />
                <TableColumn @bind-Field="@context.Pcs" />
                <TableColumn @bind-Field="@context.Meters" />
                <TableColumn @bind-Field="@context.Yards" />
                <TableColumn @bind-Field="@context.Weight" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <TableToolbarTemplate>
                @* <TableToolbarButton TItem="ProductOutboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" /> *@
            </TableToolbarTemplate>
            <RowButtonTemplate>
                <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
            </RowButtonTemplate>

            <FooterTemplate>
                <TableFooterCell Text="合计:" />
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Yards)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundLot_View.Weight)" />
                <TableFooterCell Colspan="3" />
            </FooterTemplate>


        </Table>
    </div>
    <div class="@childTableClass">

        <Table @bind-Items="rollDetailList" TItem="ProductOutboundRoll"
               IsExcel="true" ShowToolbar="true" ShowDeleteButton="false" ShowRefresh="false"
               OnAddAsync="@OnAddRollAsync" OnSaveAsync="@OnSaveRollAsync" IsBordered="true"
               OnDeleteAsync="@OnDeleteRollAsync" ShowToastAfterSaveOrDeleteModel="false" TableSize="TableSize.Compact" IsMultipleSelect="false" ShowExtendButtons="true"
               IsFixedHeader="true" Height="360">
            <TableColumns>
                <TableColumn @bind-Field="@context.RollNo" />
                <TableColumn @bind-Field="@context.Meters" />
                <TableColumn @bind-Field="@context.Yards" />
                <TableColumn @bind-Field="@context.Weight" />
                <TableColumn @bind-Field="@context.Remark" />
            </TableColumns>
            <TableToolbarTemplate>
                <TableToolbarButton Color="Color.Warning" Icon="fa fa-info-save" Text="保存" OnClick="@SaveRolls" />
            </TableToolbarTemplate>
            <FooterTemplate>
                <TableFooterCell Text="合计:" />
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductOutboundRoll.RollNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundRoll.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundRoll.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductOutboundRoll.Yards)" />
                <TableFooterCell Colspan="3" />
            </FooterTemplate>
        </Table>

    </div>
</div>
<style>
    .parent-table-normal {
        width: 100%;
        display: inline-block;
    }

    .parent-table-compressed {
        width: 60%;
        display: inline-block;
    }

    .child-table-hidden {
        display: none;
    }

    .child-table-visible {
        width: 40%;
        display: inline-block;
    }
</style>
@code {
    [Parameter]
    public string OrderDetailId { get; set; } 

    [Parameter]
    public List<ProductOutboundLot> LotList { get; set; }=new List<ProductOutboundLot>();
    [Parameter]
    public EventCallback<List<ProductOutboundLot>> LotListChanged { get; set; }


    [Parameter]
    public ProductOutboundBill Bill { get; set; } = new();
    [Parameter]
    public EventCallback<ProductOutboundBill> BillChanged { get; set; }


    private List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();
    bool isRollTableDisplay = false;//是否显示Roll表格
    ProductOutboundLot SelectedLot { get; set; }//存储当前选择的Lot

    private List<ProductOutboundLot> selectedRows { get; set; } = new();

    //Lot子表绑定数据
    public IEnumerable<ProductOutboundLot> DetailList
    {
        get { return LotList; }
        set
        {
            LotList = value.ToList();
        }
    }

    //Roll孙表绑定数据
    List<ProductOutboundRoll> RollDetailList = new List<ProductOutboundRoll>();
    public IEnumerable<ProductOutboundRoll> rollDetailList
    {
        get { return RollDetailList; }
        set
        {
            RollDetailList = value.ToList();
        }
    }


    protected override async Task OnInitializedAsync()
    {
        if (Bill.LotList is null) Bill.LotList = new List<ProductOutboundLot>();

        await base.OnInitializedAsync();
    }




    #region 更新Lot
    //Lot子表Excel模式,更新方法
    private async Task<ProductOutboundLot> OnAddAsync()
    {
        var od = new ProductOutboundLot();
        if (Bill.LotList is null) Bill.LotList = new();
        od.ID = Guid.NewGuid();
        Bill.LotList.Insert(Bill.LotList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductOutboundLot item, ItemChangedType changedType)
    {
        Bill.Pcs = Bill.LotList.Count;
        Bill.Weight = Bill.LotList.Sum(x => x.Weight);
        Bill.Meters = Bill.LotList.Sum(x => x.Meters);
        Bill.Yards = Bill.LotList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductOutboundLot> items)
    {
        Bill.LotList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
    #endregion


    #region 更新Roll
    //更新Roll
    private async Task<ProductOutboundRoll> OnAddRollAsync()
    {
        var od = new ProductOutboundRoll();
        if (RollDetailList is null) RollDetailList = new();
        od.ID = Guid.NewGuid();
        od.RollNo = RollDetailList.Count + 1;
        RollDetailList.Insert(RollDetailList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveRollAsync(ProductOutboundRoll item, ItemChangedType changedType)
    {
        // Bill.Pcs = RollDetailList.Count();
        // Bill.Weight = RollDetailList.Sum(x => x.Weight);
        // Bill.Meters = RollDetailList.Sum(x => x.Meters);
        // Bill.Yards = RollDetailList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteRollAsync(IEnumerable<ProductOutboundRoll> items)
    {
        RollDetailList.RemoveAll(i => items.ToList().Contains(i));
        return Task.FromResult(true);
    }
    private string parentTableClass = "parent-table-normal";
    private string childTableClass = "child-table-hidden";
    //行明细按钮点击控制显示Roll表格
    private void OnDetailsClick(ProductOutboundLot item)
    {
        if (isRollTableDisplay) { isRollTableDisplay = false; return; }
        isRollTableDisplay = true;
        SelectedLot = item;
        RollDetailList = item.RollList ?? new List<ProductOutboundRoll>();

        if (parentTableClass == "parent-table-normal")
        {
            parentTableClass = "parent-table-compressed";
            childTableClass = "child-table-visible";
        }
        else
        {
            parentTableClass = "parent-table-normal";
            childTableClass = "child-table-hidden";
        }
    }

    //保存Roll
    private async Task SaveRolls()
    {
        var list = new List<ProductOutboundRoll>();
        
        //去除空白Roll
        foreach (var item in RollDetailList)
        {
            if (item.Meters != 0 || item.Weight != 0 || item.Yards != 0)
            {
                list.Add(item);
            }
        }
        SelectedLot.RollList = list;
        SelectedLot.Color=AllOrderDetails.FirstOrDefault(x=>x.Value==SelectedLot.OrderDetailId.ToString())?.Text;
        SelectedLot.Pcs = SelectedLot.RollList.Count;
        SelectedLot.Weight = SelectedLot.RollList.Sum(x => x.Weight);
        SelectedLot.Meters = SelectedLot.RollList.Sum(x => x.Meters);
        SelectedLot.Yards = SelectedLot.RollList.Sum(x => x.Yards);
        int index = Bill.LotList.FindIndex(x => x.ID == SelectedLot.ID);
        Bill.LotList[index] = SelectedLot;
        isRollTableDisplay = false;
        parentTableClass = "parent-table-normal";
        childTableClass = "child-table-hidden";
        await Task.CompletedTask;
        //StateHasChanged(); //Click事件是EventCallback类型,本身会触发StateHasChanged，所以不需要再次触发
    }
    #endregion

}