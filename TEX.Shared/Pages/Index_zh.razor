@using TEX.Shared.Components
@inherits BasePage

<div class="index">
    <div class="welcome container">
        <div class="form-inline">
            <div class="row">
                <div class="col-12">
                    <div>
                        <h2 style="text-align:center;font-weight:600;font-family:'Microsoft YaHei UI'">
                            AI纺面料管理系统
                        </h2>
                        <div style="margin:50px">
                        </div>

                    </div>

                </div>

            </div>
        </div>

    </div>
    
@*     <Card>
        <BodyTemplate>
            <TEX.Shared.Components.HexagonNav />
        </BodyTemplate>
    </Card> *@

    <br />
    <TEX.Shared.CustomeComponents.OrderSummary />

</div>
@code {

    //[Parameter]
    //public Index.githubpoco model { get; set; } = new Index.githubpoco();
}
