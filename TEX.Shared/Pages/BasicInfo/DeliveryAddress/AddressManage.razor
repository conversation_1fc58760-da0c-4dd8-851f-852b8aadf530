@using TEX.Model.Models
@using TEX.ViewModel.BasicInfo.DeliveryAddressVMs;
@inherits BasePage




<Table @ref="dataTable" TItem="DeliveryAddress_View" OnQueryAsync="OnSearch"
       IsStriped="true" IsBordered="true" ShowRefresh="false" TableSize="TableSize.Compact"
       ShowToolbar="false" ShowExtendButtons="true"
       ShowExtendDeleteButton="false" ShowExtendEditButton="false"
       IsExcel="false" OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
       style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CompanyName" />
        <TableColumn @bind-Field="@context.ContactName" />
        <TableColumn @bind-Field="@context.Phone" />
        <TableColumn @bind-Field="@context.Address" />
        @* <TableColumn @bind-Field="@context.AuditStatus" Text="@WtmBlazor.Localizer["_AuditStatus"]">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn> *@
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableToolbarTemplate>
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="复制" OnClick="() => OnCopyClick(context)" />
        @* @if (IsAccessable("/api/DeliveryAddress/UpdateAuditField/{id}"))
                {
                    <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                    ConfirmButtonColor="Color.Warning" />
                } *@
    </RowButtonTemplate>
</Table>

@code {

    private DeliveryAddressSearcher SearchModel = new DeliveryAddressSearcher();
    private Table<DeliveryAddress_View> dataTable;
    private IEnumerable<DeliveryAddress_View> adresses { get; set; }
    private List<DeliveryAddress_View> Adresses
    {
        get => adresses as List<DeliveryAddress_View>;
        set => adresses = value;
    }

    [Parameter]
    public Company Company { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Company is not null)
        {
            SearchModel.AffiliationCompanyId = Company.ID;
        }
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<DeliveryAddress_View>> OnSearch(QueryPageOptions opts)
    {
        var rv = await StartSearch<DeliveryAddress_View>("/api/DeliveryAddress/Search", SearchModel, opts);
        adresses = rv.Items.ToList();
        return rv;
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCopyClick(DeliveryAddress_View item)
    {
        //复制DeliveryAddress_View的内容到剪贴板
        // 使用明确的换行符格式，确保JavaScript能正确处理
        var info = $"{item.CompanyName}\r\n{item.ContactName}\r\n{item.Phone}\r\n{item.Address}";

        await JSRuntime.InvokeVoidAsync("CopyTextToClipboard", info);
    }

    //加入审核功能
    private async Task OnAuditClick(DeliveryAddress_View item)
    {
        await PostsData(item.ID, $"/api/DeliveryAddress/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }

    private async Task<DeliveryAddress_View> OnAddAsync()
    {
        var od = new DeliveryAddress_View();
        Adresses.Insert(adresses.Count(), od);
        return await Task.FromResult(od);
    }

    private Task<bool> OnSaveAsync(DeliveryAddress_View item, ItemChangedType changedType)
    {

        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<DeliveryAddress_View> items)
    {
        Adresses.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
}
