@page "/BasicInfo/DeliveryAddress"
@using TEX.Model.Models
@using TEX.ViewModel.BasicInfo.DeliveryAddressVMs;
@inherits BasePage
@attribute [ActionDescription("货运地址", "TEX.Controllers,DeliveryAddress")]


@if (Company is null)
{
    <WTSearchPanel OnSearch="@DoSearch">
        <ValidateForm Model="@SearchModel">
            <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

                <BootstrapInput @bind-Value="@SearchModel.CompanyName" />
                <BootstrapInput @bind-Value="@SearchModel.ContactName" />
                <BootstrapInput @bind-Value="@SearchModel.Address" />
                <Select @bind-Value="@SearchModel.AffiliationCompanyId" Items="@AllCompanys" DisplayText="关联公司" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
                <BootstrapInput @bind-Value="@SearchModel.Remark" />
                <Select @bind-Value="@SearchModel.AuditStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />

            </Row>
        </ValidateForm>
    </WTSearchPanel>
}


<Table @ref="dataTable" TItem="DeliveryAddress_View" OnQueryAsync="OnSearch" 
IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDeleteButton="false"
ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false"
style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.CompanyName" />
        <TableColumn @bind-Field="@context.ContactName" />
        <TableColumn @bind-Field="@context.Phone" />
        <TableColumn @bind-Field="@context.Address" />
        <TableColumn @bind-Field="@context.CompanyName_view" Text="关联公司"/>
        <TableColumn @bind-Field="@context.AuditStatus" Text="@WtmBlazor.Localizer["_AuditStatus"]">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/DeliveryAddress/Add"))
        {
            <TableToolbarButton TItem="DeliveryAddress_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }

        @if (IsAccessable("/api/DeliveryAddress/Import") && Company is null)
        {
            <TableToolbarButton TItem="DeliveryAddress_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/DeliveryAddress/ExportExcel") && Company is null)
        {
            <TableToolbarButton TItem="DeliveryAddress_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }

    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (Company is null)
            {
                @if (IsAccessable("/api/DeliveryAddress/Edit") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
                {
                    <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
                }
                @if (IsAccessable("/api/DeliveryAddress/{id}"))
                {
                    <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info-circle" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
                }
                @if (IsAccessable("/api/DeliveryAddress/BatchDelete") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
                {
                    <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                    Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
                }
                @if (IsAccessable("/api/DeliveryAddress/UpdateAuditField/{id}"))
                {
                    <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                    ConfirmButtonColor="Color.Warning" />
                }
            }
            else
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="复制" OnClick="() => OnCopyClick(context)" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code {

    private DeliveryAddressSearcher SearchModel = new DeliveryAddressSearcher();
    private Table<DeliveryAddress_View> dataTable;

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();

    [Parameter]
    public Company Company { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Company is not null)
        {
            SearchModel.AffiliationCompanyId = Company.ID;
        }

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/DeliveryAddress/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<DeliveryAddress_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<DeliveryAddress_View>("/api/DeliveryAddress/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<DeliveryAddress_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnCopyClick(DeliveryAddress_View item)
    {
        //复制DeliveryAddress_View的内容到剪贴板
        // 使用明确的换行符格式，确保JavaScript能正确处理
        var info = $"{item.CompanyName}\r\n{item.ContactName}\r\n{item.Phone}\r\n{item.Address}";

        await JSRuntime.InvokeVoidAsync("CopyTextToClipboard", info);
    }
    private async Task OnEditClick(DeliveryAddress_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(DeliveryAddress_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/DeliveryAddress/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(DeliveryAddress_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/DeliveryAddress/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<DeliveryAddress_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/DeliveryAddress/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/DeliveryAddress/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<DeliveryAddress_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }
    //加入审核功能
    private async Task OnAuditClick(DeliveryAddress_View item)
    {
        await PostsData(item.ID, $"/api/DeliveryAddress/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }
}
