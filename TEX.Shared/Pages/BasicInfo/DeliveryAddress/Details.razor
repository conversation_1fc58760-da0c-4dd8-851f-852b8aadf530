@page "/BasicInfo/DeliveryAddress/Details/{id}"
@using TEX.ViewModel.BasicInfo.DeliveryAddressVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.CompanyName"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ContactName"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Phone"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Address"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.AffiliationCompanyId" Lookup="@AllCompanys"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <br />
    <Row ItemsPerRow="ItemsPerRow.One" RowType="RowType.Normal">
        <Textarea @bind-Value="@AddressDetail" rows="5" ShowLabel="true" DisplayText="详细地址:" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private DeliveryAddressVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();

    private string AddressDetail { get; set; }

    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/DeliveryAddress/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<DeliveryAddressVM>($"/api/DeliveryAddress/{id}");
        Model = rv.Data;
        //拼接详细地址,每个字段之间使用换行符分隔

        AddressDetail = Model.Entity.CompanyName + Environment.NewLine + Model.Entity.ContactName + Model.Entity.Phone + Environment.NewLine + Model.Entity.Address;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
