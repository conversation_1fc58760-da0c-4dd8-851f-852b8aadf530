@page "/BasicInfo/DeliveryAddress/Create"
@using TEX.ViewModel.BasicInfo.DeliveryAddressVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

        <BootstrapInput @bind-Value="@Model.Entity.CompanyName" />
        <BootstrapInput @bind-Value="@Model.Entity.ContactName" />
        <BootstrapInput @bind-Value="@Model.Entity.Phone" />
        @if(Company is not null)
        {
            <BootstrapInput @bind-Value="@Company.CompanyName"  IsDisabled="true" />
        }
        else
        {
            
        <Select @bind-Value="@Model.Entity.AffiliationCompanyId" Items="@AllCompanys" ShowSearch="true" IsPopover="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
        }
        <Row ColSpan="3">
            <BootstrapInput @bind-Value="@Model.Entity.Address" />
        </Row>
        <BootstrapInput @bind-Value="@Model.Entity.Remark" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private DeliveryAddressVM Model = new DeliveryAddressVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();

    [Parameter]
    public Company Company { get; set; }


    protected override async Task OnInitializedAsync()
    {
        if (Company is null)
        {
            AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/DeliveryAddress/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);
        }


        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/DeliveryAddress/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
