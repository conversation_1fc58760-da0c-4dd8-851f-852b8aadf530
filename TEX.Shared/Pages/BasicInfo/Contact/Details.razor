@page "/BasicInfo/Contact/Details/{id}"
@using TEX.ViewModel.BasicInfo.ContactVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.ContactName"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.AffiliationCompanyId" Lookup="@AllCompanys"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.PositionTitle"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.MobilePhone"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Address"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Phone"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Email"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.WeChat"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.QQ"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Fax"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private ContactVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/Contact/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<ContactVM>($"/api/Contact/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
