@page "/BasicInfo/Contact"
@using TEX.Model.Models
@using TEX.ViewModel.BasicInfo.ContactVMs;
@inherits BasePage
@attribute [ActionDescription("联系人", "TEX.Controllers,Contact")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.ContactName" />
            <Select @bind-Value="@SearchModel.AffiliationCompanyId" Items="@AllCompanys" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.MobilePhone" />
            <Select @bind-Value="@SearchModel.AuditStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <BootstrapInput @bind-Value="@SearchModel.Remark" />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="Contact_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.ContactName" />
        <TableColumn @bind-Field="@context.CompanyName_view" />
        <TableColumn @bind-Field="@context.PositionTitle" />
        <TableColumn @bind-Field="@context.MobilePhone" />
        <TableColumn @bind-Field="@context.Address" />
        <TableColumn @bind-Field="@context.Phone" />
        <TableColumn @bind-Field="@context.Email" />
        <TableColumn @bind-Field="@context.WeChat" />
        <TableColumn @bind-Field="@context.QQ" />
        <TableColumn @bind-Field="@context.Fax" />
        <TableColumn @bind-Field="@context.AuditStatus" Text="@WtmBlazor.Localizer["_AuditStatus"]">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/Contact/Add"))
        {
            <TableToolbarButton TItem="Contact_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/Contact/Import"))
        {
            <TableToolbarButton TItem="Contact_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/Contact/ExportExcel"))
        {
            <TableToolbarButton TItem="Contact_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }

    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/Contact/Edit") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-pencil-square" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/Contact/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info-circle" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/Contact/BatchDelete") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
            @if (IsAccessable("/api/Contact/UpdateAuditField/{id}"))
            {
                <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                                  ConfirmButtonColor="Color.Warning" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code {

    private ContactSearcher SearchModel = new ContactSearcher();
    private Table<Contact_View> dataTable;

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/Contact/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<Contact_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<Contact_View>("/api/Contact/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<Contact_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(Contact_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(Contact_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/Contact/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(Contact_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/Contact/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<Contact_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/Contact/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/Contact/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<Contact_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }
    //加入审核功能
    private async Task OnAuditClick(Contact_View item)
    {

        await PostsData(item.ID, $"/api/Contact/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }
}
