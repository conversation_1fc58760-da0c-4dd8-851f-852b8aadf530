@page "/BasicInfo/Contact/Create"
@using TEX.ViewModel.BasicInfo.ContactVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.ContactName"  />
            <Select @bind-Value="@Model.Entity.AffiliationCompanyId" Items="@AllCompanys" ShowSearch="true" IsPopover="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.PositionTitle"  />
            <BootstrapInput @bind-Value="@Model.Entity.MobilePhone"  />
            
            <BootstrapInput @bind-Value="@Model.Entity.Phone"  />
            <BootstrapInput @bind-Value="@Model.Entity.Email"  />
            <BootstrapInput @bind-Value="@Model.Entity.WeChat"  />
            <BootstrapInput @bind-Value="@Model.Entity.QQ"  />
            <BootstrapInput @bind-Value="@Model.Entity.Fax"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <br />
    <Row ItemsPerRow="ItemsPerRow.One" RowType="RowType.Normal">
        <BootstrapInput @bind-Value="@Model.Entity.Address" />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private ContactVM Model = new ContactVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllCompanys = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCompanys = await WtmBlazor.Api.CallItemsApi("/api/Contact/GetCompanys", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/Contact/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
