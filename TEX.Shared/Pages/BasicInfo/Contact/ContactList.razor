@using TEX.Model.Models
@using TEX.ViewModel.BasicInfo.ContactVMs;
@inherits BasePage

<Table @ref="dataTable" TItem="Contact_View" OnQueryAsync="OnSearch" IsStriped="true" IsBordered="true" ShowRefresh="false" TableSize="TableSize.Compact"
       ShowToolbar="false" ShowExtendButtons="true"
       ShowExtendDeleteButton="false" ShowExtendEditButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.ContactName" />
        <TableColumn @bind-Field="@context.CompanyName_view" />
        <TableColumn @bind-Field="@context.PositionTitle" />
        <TableColumn @bind-Field="@context.MobilePhone" />
        <TableColumn @bind-Field="@context.Address" />
        <TableColumn @bind-Field="@context.Phone" />
        <TableColumn @bind-Field="@context.Email" />
        <TableColumn @bind-Field="@context.WeChat" />
        <TableColumn @bind-Field="@context.QQ" />
        <TableColumn @bind-Field="@context.Fax" />
        @* <TableColumn @bind-Field="@context.AuditStatus" Text="@WtmBlazor.Localizer["_AuditStatus"]">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn> *@
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    <TableToolbarTemplate>
    </TableToolbarTemplate>
    <RowButtonTemplate>
    </RowButtonTemplate>
</Table>

@code {

    private ContactSearcher SearchModel = new ContactSearcher();
    private Table<Contact_View> dataTable;

    [Parameter]
    public Company Company { get; set; }
    protected override async Task OnInitializedAsync()
    {
        if (Company is not null)
        {
            SearchModel.AffiliationCompanyId = Company.ID;
        }
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<Contact_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<Contact_View>("/api/Contact/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    //加入审核功能
    private async Task OnAuditClick(Contact_View item)
    {

        await PostsData(item.ID, $"/api/Contact/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }
}
