@page "/Models/DictItem/Create"
@using TEX.ViewModel.Models.DictItemVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.DictTypeId" Items="@AllDictTypes" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.ItemName"  />
            <BootstrapInput @bind-Value="@Model.Entity.Description"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.DictOrder"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private DictItemVM Model = new DictItemVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllDictTypes = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDictTypes = await WtmBlazor.Api.CallItemsApi("/api/DictItem/GetDictTypes", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/DictItem/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
