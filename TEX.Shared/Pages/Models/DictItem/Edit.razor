@page "/Models/DictItem/Edit/{id}"
@using TEX.ViewModel.Models.DictItemVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Select @bind-Value="@Model.Entity.DictTypeId" Items="@AllDictTypes" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.ItemName"  />
            <BootstrapInput @bind-Value="@Model.Entity.Description"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.DictOrder"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private DictItemVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllDictTypes = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDictTypes = await WtmBlazor.Api.CallItemsApi("/api/DictItem/GetDictTypes", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<DictItemVM>($"/api/DictItem/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/DictItem/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
