@page "/Models/DictItem/Details/{id}"
@using TEX.ViewModel.Models.DictItemVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.DictTypeId" Lookup="@AllDictTypes"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ItemName"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Description"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.DictOrder"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private DictItemVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllDictTypes = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllDictTypes = await WtmBlazor.Api.CallItemsApi("/api/DictItem/GetDictTypes", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<DictItemVM>($"/api/DictItem/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
