@page "/Models/PurchaseOrder/Edit"
@using TEX.ViewModel.Models.ProductVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.Model.Models
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row>
            <Row ItemsPerRow="ItemsPerRow.Four">
                <Select @bind-Value="@Model.Entity.CustomerId" Items="AllCustomers" ShowSearch="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <Select @bind-Value="@Model.Entity.MerchandiserId" Items="AllMerchandiser" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
                <DateTimePicker @bind-Value="@Model.Entity.DeliveryDate" />

                <BootstrapInput @bind-Value="@Model.Entity.OrderNo" IsTrim="true" />
                <BootstrapInput @bind-Value="@Model.Entity.CustomerOrderNo" IsTrim="true" />
                <Select @bind-Value="@Model.Entity.Light" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <Select @bind-Value="@Model.Entity.Light2" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <Select @bind-Value="@Model.Entity.ProductId" Items="AllProducts" ShowSearch="true" OnSelectedItemChanged="OnProductSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <BootstrapInput Value="@spec" IsDisabled="true" />

                <Select @bind-Value="@Model.Entity.OrderType" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />

                <Select @bind-Value="@Model.Entity.AccountUnit" />
            </Row>
            <Row ItemsPerRow="ItemsPerRow.Four">
                <Select @bind-Value="@Model.Entity.PriceUnit" />

                <Row ColSpan="3">
                    <BootstrapInput @bind-Value="@Model.Entity.Remark" />
                </Row>

            </Row>
        </Row>

        <div style="height:300px;margin:20px 0;">
            <Table TItem="OrderDetail" IsFixedHeader="true" Height="300" ShowRefresh="false"
                   IsPagination="false" TableSize="TableSize.Compact" EditDialogSize=Size.Large ShowEditButton="false"
                   IsStriped="true" IsBordered="true" IsMultipleSelect="true" @bind-Items="@OrderDetails"
                   ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true" EditMode="EditMode.InCell" ShowToastAfterSaveOrDeleteModel="false">
                <TableColumns>
                    <TableColumn @bind-Field="@context.Color" Align="Alignment.Center" />
                    <TableColumn @bind-Field="@context.ColorCode" Align="Alignment.Center" />
                    <TableColumn @bind-Field="@context.Meters" FormatString="0" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.KG" FormatString="0" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.Yards" FormatString="0" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.Price" FormatString="0.00" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.Amount" FormatString="0.00" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.Remark" />
                </TableColumns>
                <EditTemplate>
                    <OrderDetailEditTemplate @bind-OrderDetail="context" accountUnit="@Model.Entity.AccountUnit"></OrderDetailEditTemplate>
                </EditTemplate>
            </Table>
        </div>
        <br />
        <br />
        <Row ItemsPerRow="ItemsPerRow.Four">
            <BootstrapInput @bind-Value="@Model.Entity.TotalMeters" IsDisabled="true" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.TotalYards" IsDisabled="true" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.TotalWeight" IsDisabled="true" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.TotalAmount" IsDisabled="true" FormatString="0.00" />
        </Row>
        <div class="modal-footer table-modal-footer">
            <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
            <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
        </div>
    </ValidateForm>
</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private PurchaseOrderVM Model = new PurchaseOrderVM();
    private ValidateForm vform { get; set; }
    private string spec
    {
        get
        {
            string sp = "";
            if (Model.Entity.Product == null) { return sp; }
            if (Model.Entity.Product.GSM != 0)
            {
                sp += Model.Entity.Product.Spec + " - " + Model.Entity.Product.GSM + "Gsm";
            }
            if (Model.Entity.Product.Width != 0)
            {
                sp += " - " + Model.Entity.Product.Width + "CM";
            }
            return sp;
        }
    }
    private async Task Submit(EditContext context)
    {

        Model.Entity.OrderDetailList = OrderDetails.ToList();

        await PostsForm(vform, "/api/Models/PurchaseOrder/Edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    private List<SelectedItem> AllMerchandiser = new List<SelectedItem>();
    //private IEnumerable<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
    protected override async Task OnInitializedAsync()
    {


        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys");
        AllCustomers.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts");
        AllProducts.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });

        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
            Model = rv.Data;
        }
        var r = await WtmBlazor.Api.CallAPI<ProductVM>($"/api/Models/Product/{Model.Entity.ProductId.ToString()}");
        var p = r.Data;
        product = p.Entity;

        OrderDetails = Model.Entity.OrderDetailList;
        AllMerchandiser = await WtmBlazor.Api.CallItemsApi($"/api/Contact/GetContactByCompanyId/{Model.Entity.CustomerId}");
        AllMerchandiser.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
        //spec = Model.Entity.Product.Spec;
        await base.OnInitializedAsync();
    }

    private IEnumerable<OrderDetail> _OrderDetails = new List<OrderDetail>();
    public IEnumerable<OrderDetail> OrderDetails
    {
        get { return _OrderDetails; }
        set
        {
            _OrderDetails = value;
            Model.Entity.TotalMeters = _OrderDetails.Sum(x => x.Meters);
            Model.Entity.TotalYards = _OrderDetails.Sum(x => x.Yards);
            Model.Entity.TotalWeight = _OrderDetails.Sum(x => x.KG);
            Model.Entity.TotalAmount = _OrderDetails.Sum(x => x.Amount);
        }
    }
    private Product product { get; set; } = new();
    private async Task OnProductSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<ProductVM>($"/api/Models/Product/{item.Value}");
            var p = rv.Data;
            product = p.Entity;
            Model.Entity.Product = product;
            StateHasChanged();
        }
    }

    private List<SelectedItem> Merchandiser = new List<SelectedItem>();
    private async Task OnCustomerSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            Merchandiser = await WtmBlazor.Api.CallItemsApi($"/api/Contact/GetContactByCompanyId/{item.Value}");
            Merchandiser.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
            StateHasChanged();
        }
    }
}
