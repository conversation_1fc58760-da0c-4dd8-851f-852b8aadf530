@page "/Models/PurchaseOrder/Index"
@using TEX.Model.Models
@using TEX.Shared.Pages.Components
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage
@attribute [ActionDescription("_Page.Models.PurchaseOrder.Index", "TEX.Models.Controllers,PurchaseOrder")]

<WTSearchPanel OnSearch="@PurchaseOrderListVMDoSearch" style="margin:10px">
    <ValidateForm Model="@Model">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@Model.Searcher.CreateDate" />
            <WTDateRange @bind-Value="@Model.Searcher.DeliveryDate" />
            <Select @bind-Value="@Model.Searcher.CustomerId" Items="AllCustomers" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g9f28b6462e5e49b19e7aeb160cacd187" ShowSearch="true" />
            <Select @bind-Value="@Model.Searcher.OrderId" Items="AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"  ShowSearch="true" />
            @* <BootstrapInput @bind-Value="@Model.Searcher.OrderNo" Id="g426053448" /> *@
            <BootstrapInput @bind-Value="@Model.Searcher.CustomerOrderNo" />
            <BootstrapInput @bind-Value="@Model.Searcher.Merchandiser" />
            <Select @bind-Value="@Model.Searcher.OrderType" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g72d2e92de42140be8d2fe343bf966e79" />
            <Select @bind-Value="@Model.Searcher.ProductId" Items="AllProducts" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
            <Select @bind-Value="@Model.Searcher.Light" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="gf8e08997e09a4ffcba0745cff1673a16" />
        </Row>

    </ValidateForm>
</WTSearchPanel>


<Table @ref="PurchaseOrderListVMdataTable" TItem="PurchaseOrder_View" OnQueryAsync="PurchaseOrderListVMOnSearchPurchaseOrder"
       IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ClickToSelect="true"
       ShowDefaultButtons="false" IsPagination="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.PurchaseOrder_CreateDate"  FormatString="yy-MM-dd" />
        <TableColumn @bind-Field="@context.PurchaseOrder_Customer" />
        <TableColumn @bind-Field="@context.PurchaseOrder_OrderNo" />
        <TableColumn @bind-Field="@context.PurchaseOrder_CustomerOrderNo"  />
        <TableColumn @bind-Field="@context.PurchaseOrder_Product"  />
        <TableColumn @bind-Field="@context.PurchaseOrder_TotalMeters" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.PurchaseOrder_TotalYards" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.PurchaseOrder_TotalWeight" Align="Alignment.Right" FormatString="0" />
        <TableColumn @bind-Field="@context.PurchaseOrder_Light" />
        <TableColumn @bind-Field="@context.AccountUnit"  Align="Alignment.Center" />
        @* <TableColumn @bind-Field="@context.PriceUnit" Text="@WtmBlazor.Localizer["Page.计价单位"]" Align="Alignment.Center" />
        <TableColumn @bind-Field="@context.PurchaseOrder_Merchandiser" Text="@WtmBlazor.Localizer["Page.业务员"]" />
        <TableColumn @bind-Field="@context.PurchaseOrder_OrderType" Text="@WtmBlazor.Localizer["Page.订单类型"]" />
        <TableColumn @bind-Field="@context.PurchaseOrder_TotalYards" Text="@WtmBlazor.Localizer["Page.总码数"]" Align="Alignment.Right" FormatString="0" /> *@
        <TableColumn @bind-Field="@context.AuditStatus" >
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>

    </TableColumns>
    <DetailRowTemplate>
        <OrderDetailList id="@context.ID.ToString()"></OrderDetailList>
    </DetailRowTemplate>
    <RowButtonTemplate>
        <div>
        @if (IsAccessable("/api/Models/PurchaseOrder/{id}"))
        {
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="@WtmBlazor.Localizer["Page.详情"]" OnClick="()=>OnDetailsClick(context)" />
        }
        </div>
        <div>
        @if (IsAccessable("/api/Models/PurchaseOrder/Edit") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
        {
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-pencil-square" Color="Color.Success" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="()=>OnEditClick(context)" />
        }
        </div>
        @if (IsAccessable("/api/Models/PurchaseOrder/UpdateAuditField/{id}"))
        {
            <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                              ConfirmButtonColor="Color.Warning" />
        }
        @if (IsAccessable("/api/Models/PurchaseOrder/BatchDelete") && context.AuditStatus != AuditStatusEnum.AuditedApproved)
        {
            <PopConfirmButton OnConfirm="() => OnDeleteClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]"
                              ConfirmButtonColor="Color.Danger" />
        }
    </RowButtonTemplate>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/Models/PurchaseOrder/Create"))
        {
            <TableToolbarButton TItem="PurchaseOrder_View" Icon="fa fa-plus" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="(x)=>OnCreateClick()" />
        }
        @* @if (IsAccessable("/api/Models/PurchaseOrder/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="PurchaseOrder_View"
                                          Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" ConfirmButtonColor="Color.Danger" />
        } 
        @if (IsAccessable("/api/Models/PurchaseOrder/BatchEdit"))
        {
            <TableToolbarButton TItem="PurchaseOrder_View" Icon="fa fa-pencil-square" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.BatchEdit"]" OnClickCallback="(x)=>OnBatchEditClick()" />
        }*@
        @if (IsAccessable("/api/Models/PurchaseOrder/Import"))
        {
            <TableToolbarButton TItem="PurchaseOrder_View" Icon="fa fa-tasks" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="(x)=>OnImportClick()" />
        }
        @if (IsAccessable("/api/Models/PurchaseOrder/PurchaseOrderExportExcel"))
        {
            <TableToolbarButton TItem="PurchaseOrder_View" Icon="fa fa-arrow-circle-down" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="(x)=>OnExportClick()" IsAsync="true" />
        }
    </TableToolbarTemplate>

</Table>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private PurchaseOrderListVM Model = new PurchaseOrderListVM();
    private Table<PurchaseOrder_View> PurchaseOrderListVMdataTable;
    private async Task PurchaseOrderListVMDoSearch()
    {
        await PurchaseOrderListVMdataTable.QueryAsync();
    }
    private async Task<QueryData<PurchaseOrder_View>> PurchaseOrderListVMOnSearchPurchaseOrder(QueryPageOptions opts)
    {

        return await StartSearch<PurchaseOrder_View>("/api/Models/PurchaseOrder/SearchPurchaseOrder", Model.Searcher, opts);
    }
    private async Task OnCreateClick()
    {
        var id = PurchaseOrderListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Create>(@WtmBlazor.Localizer["Sys.Create"], x => x.id == (id ?? ""), isMax: false) == DialogResult.Yes)
        {
            await PurchaseOrderListVMdataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(PurchaseOrder_View item)
    {
        if (await OpenDialog<Edit>(@WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString(), isMax: false) == DialogResult.Yes)
        {
            await PurchaseOrderListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(PurchaseOrder_View item)
    {
        if (await OpenDialog<Details>(@WtmBlazor.Localizer["Page.详情"], x => x.id == item.ID.ToString(), isMax: false) == DialogResult.Yes)
        {
            await PurchaseOrderListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDeleteClick(PurchaseOrder_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/Models/PurchaseOrder/batchdelete", (s) => "Sys.OprationSuccess");
        await PurchaseOrderListVMdataTable.QueryAsync();
    }

    //加入审核功能
    private async Task OnAuditClick(PurchaseOrder_View item)
    {
        //Controller参数前不加[Frombody]会报错InternalError,调试到API上,传过去的ID为null,在httpclient.PostAsync(url,content)时,id还是有值的
        await PostsData(item.ID, $"/api/Models/PurchaseOrder/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await PurchaseOrderListVMdataTable.QueryAsync();
    }
    private async Task OnBatchDeleteClick()
    {
        if (PurchaseOrderListVMdataTable.SelectedRows?.Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved).Any() == true)
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["已审核项不能修改!!"]);
        }
        else
        {
            if (PurchaseOrderListVMdataTable.SelectedRows?.Any() == true)
            {
                await PostsData(PurchaseOrderListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList(), $"/api/Models/PurchaseOrder/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
                await PurchaseOrderListVMdataTable.QueryAsync();
            }
            else
            {
                await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
            }
        }
    }

    private async Task OnBatchEditClick()
    {
        if (PurchaseOrderListVMdataTable.SelectedRows?.Where(x => x.AuditStatus == AuditStatusEnum.AuditedApproved).Any() == true)
        {
            await WtmBlazor.Toast.Warning(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["选择行中有已审核项,已审核项不能修改!!"]);
        }
        else
        {
            if (PurchaseOrderListVMdataTable.SelectedRows?.Any() == true)
            {
                if (await OpenDialog<BatchEdit>(WtmBlazor.Localizer["Sys.BatchEdit"], x => x.ids == PurchaseOrderListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToArray(), isMax: false) == DialogResult.Yes)
                {
                    await PurchaseOrderListVMdataTable.QueryAsync();
                }
            }
            else
            {
                await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
            }
        }
    }

    private async Task OnImportClick()
    {
        var id = PurchaseOrderListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Import>(@WtmBlazor.Localizer["Sys.Import"], x => x.id == (id ?? ""), isMax: false) == DialogResult.Yes)
        {
            await PurchaseOrderListVMdataTable.QueryAsync();
        }
    }

    private async Task OnExportClick()
    {
        if (PurchaseOrderListVMdataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/Models/PurchaseOrder/PurchaseOrderExportExcelByIds", PurchaseOrderListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/Models/PurchaseOrder/PurchaseOrderExportExcel", Model.Searcher);
        }
    }


    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetPurchaseOrders", placeholder: WtmBlazor.Localizer["Sys.All"]);
        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys", placeholder: WtmBlazor.Localizer["Sys.All"]);
        //AllCustomers.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.All"], Value = "" });
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts", placeholder: WtmBlazor.Localizer["Sys.All"]);
        //AllProducts.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.All"], Value = "" });

        await base.OnInitializedAsync();
    }
}
