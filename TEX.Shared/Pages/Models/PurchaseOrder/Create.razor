@page "/Models/PurchaseOrder/Create"
@using TEX.ViewModel.Models.ProductVMs
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.Model.Models
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row >
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
                <Select @bind-Value="@Model.Entity.CustomerId" Items="AllCustomers" ShowSearch="true" OnSelectedItemChanged="OnCustomerSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g0e964d44122840db92c1afbe4d3002d5" />
                <Select @bind-Value="@Model.Entity.MerchandiserId" Items="Merchandiser" />
                <DateTimePicker @bind-Value="@Model.Entity.CreateDate" />
                <DateTimePicker @bind-Value="@Model.Entity.DeliveryDate" />

                <BootstrapInput @bind-Value="@Model.Entity.OrderNo" IsTrim="true" />
                <BootstrapInput @bind-Value="@Model.Entity.CustomerOrderNo" IsTrim="true" />

                <Select @bind-Value="@Model.Entity.Light" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g18cabd9ddac34d23ba5008f29ed66267" />
                <Select @bind-Value="@Model.Entity.Light2" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g502d074f1766404e9506721a10eac8d4" />


                <Select @bind-Value="@Model.Entity.ProductId" Items="AllProducts" ShowSearch="true" OnSelectedItemChanged="OnProductSelect" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
                <BootstrapInput @bind-Value="@Model.Entity.DyeingProductName" IsTrim="true" />
                <BootstrapInput @bind-Value="@spec" DisplayText="@WtmBlazor.Localizer["_Spec"]" IsDisabled="true" />
                <Select @bind-Value="@Model.Entity.OrderType" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g204736cc55cc4d4895014e2c9647b557" />
            </Row>
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
                <Select @bind-Value="@Model.Entity.AccountUnit" />
                <Select @bind-Value="@Model.Entity.PriceUnit" />

                <Row ColSpan="2" RowType="RowType.Inline">
                    <BootstrapInput @bind-Value="@Model.Entity.Remark" />
                </Row>
            </Row>
        </Row>
        <div style="height:300px;margin:20px 0;">
            <Table TItem="OrderDetail" @bind-Items="@OrderDetailList" ShowRefresh="false"
            IsPagination="false" TableSize="TableSize.Compact" EditDialogSize=Size.Large
            IsStriped="true" IsBordered="true" IsMultipleSelect="true" ShowEditButton="false"
            ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true" EditMode="EditMode.InCell"
                   OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync"
            ShowToastAfterSaveOrDeleteModel="false">
                <TableColumns>
                    <TableColumn @bind-Field="@context.Color" Align="Alignment.Center" />
                    <TableColumn @bind-Field="@context.EngColor" Align="Alignment.Center" />
                    <TableColumn @bind-Field="@context.ColorCode" Align="Alignment.Center" />
                    <TableColumn @bind-Field="@context.Meters" FormatString="0" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.KG" FormatString="0" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.Yards" FormatString="0" Align="Alignment.Right" />
                    <TableColumn @bind-Field="@context.Price" FormatString="0.00" Align="Alignment.Right" />
                    @* <TableColumn @bind-Field="@context.Amount" FormatString="0.00" Align="Alignment.Right" /> *@
                    <TableColumn @bind-Field="@context.Remark" />
                </TableColumns>
                <EditTemplate>
                    <OrderDetailEditTemplate @bind-OrderDetail="context" accountUnit="@Model.Entity.AccountUnit"></OrderDetailEditTemplate>
                </EditTemplate>
            </Table>
        </div>
        @* <br /> 8.8.2版本修复了弹窗高度计算问题,此处可以去掉
        <br /> *@
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@Model.Entity.TotalMeters" IsDisabled="true" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.TotalYards" IsDisabled="true" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.TotalWeight" IsDisabled="true" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.TotalAmount" IsDisabled="true" FormatString="0.00" />
        </Row>
        <div class="modal-footer table-modal-footer">
            <Button Color="Color.Secondary" OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
            <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]" />
        </div>
    </ValidateForm>
</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private PurchaseOrderVM Model = new PurchaseOrderVM();
    private ValidateForm vform { get; set; }


    private string spec { get; set; }
    private async Task Submit(EditContext context)
    {
        //产品必填验证莫名奇妙失效了,只能自定义验证了
        if (Model.Entity.ProductId == Guid.Empty)
        {
            vform.SetError<PurchaseOrder>(f => f.ProductId, "产品为必填项");
        }
        else
        {
            Model.Entity.OrderDetailList = OrderDetailList.ToList();
            await PostsForm(vform, "/api/Models/PurchaseOrder/Create", (s) => "Sys.OprationSuccess");
        }
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/Company/GetCustomerCompanys");
        AllCustomers.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts");
        AllProducts.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });

        await base.OnInitializedAsync();
    }

    private List<OrderDetail> _orderDetailList = new List<OrderDetail>();
    public IEnumerable<OrderDetail> OrderDetailList
    {
        get { return _orderDetailList; }
        set
        {
            _orderDetailList = value.ToList();
            Model.Entity.TotalMeters = _orderDetailList.Sum(x => x.Meters);
            Model.Entity.TotalYards = _orderDetailList.Sum(x => x.Yards);
            Model.Entity.TotalWeight = _orderDetailList.Sum(x => x.KG);
            Model.Entity.TotalAmount = _orderDetailList.Sum(x => x.Amount);
        }
    }

    private async Task OnProductSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<ProductVM>($"/api/Models/Product/{item.Value}");
            var p = rv.Data;
            spec = p.Entity.Spec;
            if (p.Entity.GSM != null && p.Entity.GSM != 0)
            {
                spec += " - " + p.Entity.GSM + "Gsm";
            }
            if (p.Entity.Width != null && p.Entity.Width != 0)
            {
                spec += " - " + p.Entity.Width + "CM";
            }
            StateHasChanged();
        }
    }

    private List<SelectedItem> Merchandiser = new List<SelectedItem>();
    private async Task OnCustomerSelect(SelectedItem item)
    {
        if (item.Value != "")
        {
            Merchandiser = await WtmBlazor.Api.CallItemsApi($"/api/Contact/GetContactByCompanyId/{item.Value}");
            Merchandiser.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
            StateHasChanged();
        }
    }

    private Task<OrderDetail> OnAddAsync()
    {
        // 此处代码为示例代码
        OrderDetail orderDetail = new()
        {
            ID=Guid.NewGuid(),
        };
        _orderDetailList.Add( orderDetail);

        return Task.FromResult(orderDetail);
    }

    private Task<bool> OnSaveAsync(OrderDetail item, ItemChangedType changedType)
    {
        if (item.Price is not null && Model.Entity.AccountUnit is not null)
         {
            var isM = Model.Entity.AccountUnit == AccountingUnitEnum.M;
            var isKg = Model.Entity.AccountUnit == AccountingUnitEnum.KG;

            item.Amount =isM? item.Price * item.Meters:(isKg? item.Price * item.KG: item.Price * item.Yards);
        }
 
        return Task.FromResult(true);
    }

}
