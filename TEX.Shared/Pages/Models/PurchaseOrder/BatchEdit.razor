
@page "/Models/PurchaseOrder/BatchEdit"
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
  <div> @WtmBlazor.Localizer["Sys.BatchEditConfirm"]</div>
  <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
    <DateTimePicker @bind-Value="@Model.LinkedVM.CreateDate"/>
    <DateTimePicker @bind-Value="@Model.LinkedVM.DeliveryDate"/>
    <Select @bind-Value="@Model.LinkedVM.CustomerId" Items="AllCustomers" Id="g6a1bf53fbb504f08a83468a2b17557d3"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.CustomerOrderNo"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Merchandiser"/>
    <Select @bind-Value="@Model.LinkedVM.OrderType" Id="gc97c1dfb3a60464996cedf35c33a4e13"/>
    <Select @bind-Value="@Model.LinkedVM.ProductId" Items="AllProducts"/>
    <Select @bind-Value="@Model.LinkedVM.Light" Id="g4b966b86aeab40fcb15bedeb21fbfdd2"/>
    <Select @bind-Value="@Model.LinkedVM.Light2" Id="g386c7acadc284f84bcd52ad929298501"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.TotalMeters"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.TotalYards"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.TotalWeight"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.TotalAmount"/>
  </Row>

  <div class="modal-footer table-modal-footer">
    <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]"/>
    <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]"/>
  </div>

</ValidateForm>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private PurchaseOrderBatchVM Model = new PurchaseOrderBatchVM();
    private ValidateForm vform { get; set; }
    
    private async Task Submit(EditContext context)
    {
        Model.Ids = ids;
        await PostsForm(vform, "/api/Models/PurchaseOrder/BatchEdit", (s) => WtmBlazor.Localizer["Sys.BatchEditSuccess", s], method: HttpMethodEnum.POST);
    }
            

    public void OnClose()
    {
        CloseDialog();
    }
    
    private List<SelectedItem> AllCustomers = new List<SelectedItem>();
    private List<SelectedItem> AllProducts = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        
        
        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetCompanys");
        AllCustomers.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });
        AllProducts = await WtmBlazor.Api.CallItemsApi("/api/Models/PurchaseOrder/GetProducts");
        AllProducts.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });

        await base.OnInitializedAsync();
    }
}
