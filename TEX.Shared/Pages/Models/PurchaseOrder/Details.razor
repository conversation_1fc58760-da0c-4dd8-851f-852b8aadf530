@page "/Models/PurchaseOrder/Details"
@using TEX.ViewModel.Models.PurchaseOrderVMs
@using TEX.Model.Models
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row>
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
                <Display @bind-Value="@Model.Entity.CreateDate" ShowLabel="true" FormatString="yyyy-MM-dd" />
                <Display @bind-Value="@Model.Entity.DeliveryDate" ShowLabel="true" FormatString="yyyy-MM-dd" />
                <Display @bind-Value="@customer" ShowLabel="true" DisplayText="客户" />
                <Display @bind-Value="@Model.Entity.OrderNo" ShowLabel="true" />
                <Display @bind-Value="@Model.Entity.CustomerOrderNo" ShowLabel="true" DisplayText="客户款号" />
                <Display @bind-Value="@merchandiser" ShowLabel="true" DisplayText="业务员" />
                <Display @bind-Value="@Model.Entity.OrderType" ShowLabel="true" />
                <Display @bind-Value="@product" ShowLabel="true" DisplayText="品名" />
                <Display @bind-Value="@Model.Entity.Light" ShowLabel="true" />
                <Display @bind-Value="@Model.Entity.Light2" ShowLabel="true" />
                <Display @bind-Value="@Model.Entity.AccountUnit" ShowLabel="true" />
                <Display @bind-Value="@Model.Entity.PriceUnit" ShowLabel="true" />
                <Display @bind-Value="@Model.Entity.TotalMeters" ShowLabel="true" FormatString="0" Align="Alignment.Right" />
                <Display @bind-Value="@Model.Entity.TotalYards" ShowLabel="true" FormatString="0" Align="Alignment.Right" />
                <Display @bind-Value="@Model.Entity.TotalWeight" ShowLabel="true" FormatString="0.0" Align="Alignment.Right" />
                @if (@isShowPrice)
                {
                    <BootstrapInput @bind-Value="@Model.Entity.TotalAmount" ShowLabel="true" FormatString="0.00" Align="Alignment.Right" IsDisabled="true" />
                }
                @* <Display @bind-Value="@Model.Entity.CreateBy" ShowLabel="true" />
                <Display @bind-Value="@Model.Entity.UpdateBy" ShowLabel="true" /> *@
            </Row>
            <Row RowType="RowType.Inline">
                <Display @bind-Value="@Model.Entity.Remark" />
            </Row>
        </Row>
        <div style="height:300px;margin:20px 0;">
            <Tab IsBorderCard="true" IsLazyLoadTabItem="true" Height="30">
                <TabItem Text="@WtmBlazor.Localizer["Page.订单明细"]">

                    <Table TItem="OrderDetail"
                           IsPagination="false" TableSize="TableSize.Compact"
                           IsStriped="true" IsBordered="true" IsMultipleSelect="false" @bind-Items="@OrderDetails"
                           ShowToolbar="false" ShowExtendButtons="false" ShowSkeleton="true" ShowToastAfterSaveOrDeleteModel="false">
                        <TableColumns>
                            <TableColumn @bind-Field="@context.Color" Align="Alignment.Center" />
                            <TableColumn @bind-Field="@context.ColorCode" Align="Alignment.Center" />
                            <TableColumn @bind-Field="@context.Meters" FormatString="0" Align="Alignment.Right" Visible="@isShowMeters" />
                            <TableColumn @bind-Field="@context.KG" FormatString="0" Align="Alignment.Right" />
                            <TableColumn @bind-Field="@context.Yards" FormatString="0" Align="Alignment.Right" Visible="@isShowYards" />
                            <TableColumn @bind-Field="@context.Price" FormatString="0.00" Align="Alignment.Right" Visible="@isShowPrice" />
                            <TableColumn @bind-Field="@context.Amount" FormatString="0.00" Align="Alignment.Right" Visible="@isShowPrice" />
                            <TableColumn @bind-Field="@context.Remark" />
                        </TableColumns>
                    </Table>

                </TabItem>

                <TabItem Text="@WtmBlazor.Localizer["Page.坯布采购"]">
                </TabItem>

                <TabItem Text="@WtmBlazor.Localizer["Page.染色计划"]">
                    <TEX.Shared.Pages.Producttion.DyeingPlan.DyeingPlanList Id="@Model.Entity.ID"></TEX.Shared.Pages.Producttion.DyeingPlan.DyeingPlanList>
                </TabItem>

                <TabItem Text="@WtmBlazor.Localizer["Page.成品入库"]">
                </TabItem>

                <TabItem Text="@WtmBlazor.Localizer["Page.成品出库"]">
                </TabItem>

                <TabItem Text="@WtmBlazor.Localizer["Page.成品库存"]">
                </TabItem>

            </Tab>
        </div>

        <div class="modal-footer table-modal-footer">
            <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }

    private bool isShowMeters;
    private bool isShowYards;
    private bool isShowPrice = false;

    //当VM包含外键时,默认值必须为null,不能new(),否则报错NO INSTANCE
    // new()不能把外键完整属性new出来,导致<Display @bind-Value="@Model.Entity.Product.ProductName" ShowLabel="true"  />引用报错
    private PurchaseOrderVM Model = null;
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/PurchaseOrder/Edit", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    public string customer { get; set; } = "";
    public string product { get; set; } = "";
    public string merchandiser { get; set; } = "";
    private IEnumerable<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
    protected override async Task OnInitializedAsync()
    {
         if (!string.IsNullOrEmpty(id)) // 确保 id 不为空
        {
            var rv = await WtmBlazor.Api.CallAPI<PurchaseOrderVM>($"/api/Models/PurchaseOrder/{id}");
            if (rv.Data != null) // 确保数据有效
            {
                Model = rv.Data;
                OrderDetails = Model.Entity.OrderDetailList;

                // 只在必要时更新状态
                customer = Model.Entity.Customer.CompanyName ?? "";
                product = Model.Entity.Product.ProductName ?? "";
                isShowMeters = OrderDetails.Any(x => x.Meters != null);
                isShowYards = OrderDetails.Any(x => x.Yards != null);
                merchandiser = Model.Entity.Merchandiser?.ContactName ?? "";
                
                // 只在角色变化时更新 isShowPrice
                var l = UserInfo.Roles.Where(x => x.RoleCode.StartsWith("8") || x.RoleCode.Contains("001")).ToList();
                isShowPrice = l.Count > 0;
            }
        }
        await base.OnInitializedAsync();
    }
}
