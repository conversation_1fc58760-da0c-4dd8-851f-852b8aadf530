@using System.Diagnostics.CodeAnalysis;
@using TEX.Model.Models;
@using TEX.ViewModel.Models.ProductVMs;


<Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">
    <BootstrapInput @bind-Value="ODetail.Color"  IsTrim="true" />
    <BootstrapInput @bind-Value="ODetail.ColorCode" IsTrim="true" />
    <BootstrapInput @bind-Value="ODetail.Meters" FormatString="0.#" />
    <BootstrapInput @bind-Value="ODetail.Yards" FormatString="0.#" OnValueChanged="ConvertYtoM"/>
    <BootstrapInput @bind-Value="ODetail.KG" FormatString="0.#" />
    <BootstrapInput @bind-Value="@ODetail.Price" FormatString="0.00" />
    @* <BootstrapInput @bind-Value="@ODetail.Amount" /> *@
    <BootstrapInput Value="@amount" ShowLabel="true" DisplayText="金额" FormatString="0.00" />
    <BootstrapInput @bind-Value="@ODetail.Remark" IsTrim="true" />
</Row>


@code {
    [Parameter]
    public AccountingUnitEnum? accountUnit { get; set; }

    [Parameter]
    public OrderDetail OrderDetail { get; set; } 
    [Parameter]
    public EventCallback<OrderDetail> OrderDetailChanged { get; set; }

    decimal? amount
    {
        get
        {
            if (ODetail.Price.HasValue && accountUnit != null)
            {
                Decimal? x = ODetail.Price.Value * (
                                accountUnit == AccountingUnitEnum.KG ? ODetail.KG :
                                accountUnit == AccountingUnitEnum.M ? ODetail.Meters : ODetail.Yards);
                OrderDetail.Amount = x;
                return x;
            }
            return 0;
        }
    }
    public OrderDetail ODetail
    {
        get
        {
            return OrderDetail;
        }
        set
        {
            if (OrderDetail != value)
            {
                OrderDetail = value;
                OrderDetailChanged.InvokeAsync(OrderDetail);
            }

        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    //OnValueChanged事件期望的委托类型;System.Func<decimal?, Task>
    private Task ConvertYtoM(decimal? yardsValue)
    {
        if (yardsValue.HasValue)
        {
            ODetail.Meters = yardsValue.Value * 0.9144m;
        }
        return Task.CompletedTask;
    }

}
