@page "/Models/Product/Create"
@using TEX.Shared.Components
@using TEX.ViewModel.Models.ProductVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            <BootstrapInput @bind-Value="@Model.Entity.ProductName" IsTrim="true" />

            <InputWithSuffix DisplayText="平方克重" Unit="GSM">
                <BootstrapInputNumber @bind-Value="@Model.Entity.GSM" ShowLabel="false" />
            </InputWithSuffix>
            <InputWithSuffix DisplayText="有效门幅" Unit="CM">
                <BootstrapInputNumber @bind-Value="@Model.Entity.Width" ShowLabel="false" />
            </InputWithSuffix>
            <InputWithSuffix DisplayText="毛长" Unit="MM" Required="false">
                <BootstrapInputNumber @bind-Value="@Model.Entity.PileLength" ShowLabel="false" />
            </InputWithSuffix>

            @* <InputWithButton  DisplayText="产品代码" @bind-Value="@Model.Entity.ProductCode"
            OnButtonClick="@RuleReNamed" ButtonText="生成"/>  不能显示验证错误*@


            <div class="input-group">
                <BootstrapInputGroupLabel @bind-Value="@Model.Entity.ProductCode" ShowRequiredMark="true"/>
                <div class="input-group">
                    <BootstrapInput @bind-Value="@Model.Entity.ProductCode" ShowLabel="false" />
                    <Button Color="Color.Primary" OnClick="@RuleReNamed" Icon="fa fa-plus" Text="生成" />
                </div>
            </div>

            <Select @bind-Value="@Model.Entity.CategoryId" IsPopover="true"
            Items="Categories" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" required="true" />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingProductName" IsTrim="true" />

            <BootstrapInput @bind-Value="@Model.Entity.Contents" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Spec" />

            <BootstrapInput @bind-Value="@Model.Entity.DyeingProcess" />
            <BootstrapInput @bind-Value="@Model.Entity.KnittingProcess" />
            <BootstrapInput @bind-Value="@Model.Entity.FinishingProcess" />
            <Select @bind-Value="@Model.Entity.ParentId" Items="AllParents" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <WTUploadFile @bind-Value="@Model.Entity.Photo" />
        </Row>

        <div class="modal-footer table-modal-footer">
            <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
            <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
        </div>
    </ValidateForm>
</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private ProductVM Model = new ProductVM();
    private ValidateForm vform { get; set; }

    

    private async Task Submit(EditContext context)
    {
        //产品必填验证莫名奇妙失效了,只能自定义验证了
        if (Model.Entity.CategoryId == 0 )
        {
            vform.SetError<Product>(f => f.CategoryId, "产品分类为必填项");
            return;
        }
        await PostsForm(vform, "/api/Models/Product/Create", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }
    private List<SelectedItem> Categories { get; set; }
    private List<SelectedItem> AllParents = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        AllParents = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts", placeholder: WtmBlazor.Localizer["Sys.All"]);

        Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task RuleReNamed()
    {

        if (Model.Entity.ProductName != null && Model.Entity.Width > 0 && Model.Entity.GSM > 0)
        {
            var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/DictItem/GetDictItemsByDictName/ProductCodeRule");
            var rule = rv.Data[2];//第三条规则为产品代码规则
            Model.Entity.ProductCode = NamedRuleHelper.GenerateProductCode(rule.Text, Model.Entity);
        }
        else
        {
            await WtmBlazor.Toast.Show(new ToastOption()
                {
                    Title = "提示",
                    Content = "请先输入产品名称、门幅、克重，再生成产品代码。"
                });
        }
    }


}
