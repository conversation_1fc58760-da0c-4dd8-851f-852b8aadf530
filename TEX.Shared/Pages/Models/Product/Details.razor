@page "/Models/Product/Details"
@using TEX.ViewModel.Models.ProductVMs
@using System.ComponentModel.DataAnnotations
@using BootstrapBlazor.Components
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            <Display @bind-Value="@Model.Entity.ProductName" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.GSM" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.Width" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.PileLength" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.ProductCode" ShowLabel="true" />
            <Select @bind-Value="@Model.Entity.CategoryId" Items="Categories" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.DyeingProductName" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.Contents" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.Spec" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.DyeingProcess" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.KnittingProcess" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.FinishingProcess" ShowLabel="true" />
            <Display @bind-Value="@Model.Entity.ParentId" ShowLabel="true" Lookup="AllParents" />
            <Button Text="查看图片" OnClick="ShowImagePreviewer"></Button>
        </Row>

        @if (isShow)
        {
            <div class="images-item" style="margin:16px">
                @if (Model.Entity.Photo?.Any() == true)
                {
                    <ImageViewer Url="@(PreviewList.FirstOrDefault() ?? string.Empty)"
                                 PreviewList="@PreviewList" />
                }
                else
                {
                    <Empty Text="暂无图片" />
                }
            </div>
        }

        <div class="modal-footer table-modal-footer">
            <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
        </div>

    </ValidateForm>

</div>


@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }

    private ProductVM Model = new ProductVM();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> Categories { get; set; }

    private List<string> PreviewList { get; } = [];

    private bool isShow = false;

    private Dictionary<string, string> imageCache = new Dictionary<string, string>();

    private bool _isLoading = false;
    private string _errorMessage = string.Empty;

    /// <summary>
    /// 显示图片预览器
    /// </summary>
    private async Task ShowImagePreviewer()
    {
        try
        {
            if (_isLoading) return;

            if (!isShow)
            {
                _isLoading = true;
                await LoadImages();
                _isLoading = false;
            }

            isShow = !isShow;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _errorMessage = $"加载图片失败: {ex.Message}";
            _isLoading = false;
            await WtmBlazor.Toast.Error("错误", _errorMessage);
        }
    }

    /// <summary>
    /// 加载所有图片
    /// </summary>
    private async Task LoadImages()
    {
        // 检查是否有图片需要加载
        if (Model.Entity.Photo == null || !Model.Entity.Photo.Any())
        {
            return;
        }
        // 只在缓存为空时加载图片
        if (!imageCache.Any())
        {
            PreviewList.Clear();
            var loadTasks = Model.Entity.Photo.Select(photo => LoadImage(photo.FileId));
            await Task.WhenAll(loadTasks);
        }
    }

    /// <summary>
    /// 加载单张图片
    /// </summary>
    /// <param name="fileId">图片文件ID</param>
    private async Task LoadImage(Guid fileId)
    {
        try
        {
            if (imageCache.ContainsKey(fileId.ToString()))// 检查缓存中是否已存在
            {
                return;
            }

            var rv = await WtmBlazor.Api.CallAPI<byte[]>($"/api/_file/GetFile/{fileId}",
                HttpMethodEnum.GET,
                new Dictionary<string, string> {
                    {"width", "500"},
                    {"height", "500"}// 限制图片尺寸,优化加载性能
                    });

            if (rv.StatusCode == System.Net.HttpStatusCode.OK && rv.Data != null)
            {
                var base64 = Convert.ToBase64String(rv.Data);// 转换为base64图片格式
                var imageUrl = $"data:image/jpeg;base64,{base64}";
                imageCache[fileId.ToString()] = imageUrl; // 存入缓存
                PreviewList.Add(imageUrl);// 添加到预览列表
            }
        }
        catch (Exception ex)
        {
            _errorMessage = $"加载图片 {fileId} 失败: {ex.Message}";
            await WtmBlazor.Toast.Error("错误", _errorMessage);
        }
    }

    private async Task Submit(EditContext context)
    {

        await PostsForm(vform, "/api/Models/Product/Edit", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllParents = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {
        if (id != "")
        {
            AllParents = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts");
            AllParents.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
            var rv = await WtmBlazor.Api.CallAPI<ProductVM>($"/api/Models/Product/{id}");
            Model = rv.Data;

            Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);
        }
        await base.OnInitializedAsync();
    }

    /// <summary>
    /// 资源清理
    /// </summary>
    public void Dispose()
    {
        // 清理资源
        PreviewList.Clear();
        imageCache.Clear();
    }

}