@page "/Models/Product/Index"
@using TEX.Model.Models
@using TEX.ViewModel.Models.ProductVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage
@attribute [ActionDescription("_Page.Models.Product.Index", "TEX.Models.Controllers,Product")]

<WTSearchPanel OnSearch="@ProductListVMDoSearch">
    <ValidateForm Model="@Model">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@Model.Searcher.ProductCode" />
            <BootstrapInput @bind-Value="@Model.Searcher.ProductName" />
            <Select @bind-Value="@Model.Searcher.CategoryId" Items="Categories" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g4a95d11ba81442dbb1319a750134c4e6" />
            <BootstrapInput @bind-Value="@Model.Searcher.Contents" />
            <BootstrapInput @bind-Value="@Model.Searcher.Spec" />
            <BootstrapInput @bind-Value="@Model.Searcher.GSM" />
            <BootstrapInput @bind-Value="@Model.Searcher.Width" />
            <Select @bind-Value="@Model.Searcher.AuditStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
        </Row>

    </ValidateForm>
</WTSearchPanel>


<Table @ref="ProductListVMdataTable" TItem="TEX.ViewModel.Models.ProductVMs.Product_View" OnQueryAsync="ProductListVMOnSearchProduct"
       IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
       ShowDeleteButton="false"
       ShowDefaultButtons="false" IsPagination="true" style="margin-top:16px">
    <TableColumns>
        <TableColumn @bind-Field="@context.Product_ProductCode" Text="@WtmBlazor.Localizer["Page.产品编码"]" />
        <TableColumn @bind-Field="@context.Product_ProductName" Text="@WtmBlazor.Localizer["Page.产品名称"]" />
        <TableColumn @bind-Field="@context.Product_Category" Lookup="Categories" Text="@WtmBlazor.Localizer["Page.产品分类"]" />
        <TableColumn @bind-Field="@context.Product_Contents" Text="@WtmBlazor.Localizer["Page.成份"]" />
        <TableColumn @bind-Field="@context.Product_Spec" Text="@WtmBlazor.Localizer["Page.规格"]" />
        <TableColumn @bind-Field="@context.Product_GSM" Text="@WtmBlazor.Localizer["Page.平方克重"]" />
        <TableColumn @bind-Field="@context.Product_Width" Text="@WtmBlazor.Localizer["Page.有效门幅"]" />
        @* <TableColumn @bind-Field="@context.Product_PileLength" Text="@WtmBlazor.Localizer["Page.毛长"]" />
        <TableColumn @bind-Field="@context.Product_DyeingProcess" Text="@WtmBlazor.Localizer["Page.染色工艺"]" />
        <TableColumn @bind-Field="@context.Product_KnittingProcess" Text="@WtmBlazor.Localizer["Page.织造工艺"]" />
        <TableColumn @bind-Field="@context.Product_FinishingProcess" Text="@WtmBlazor.Localizer["Page.后整工艺"]" /> *@
        <TableColumn @bind-Field="@context.Product_AuditStatus" Text="@WtmBlazor.Localizer["_AuditStatus"]">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.Product_AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>
    </TableColumns>

    <RowButtonTemplate>

        @if (IsAccessable("/api/Models/Product/Edit") && context.Product_AuditStatus != AuditStatusEnum.AuditedApproved)
        {
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-pencil-square" Color="Color.Success" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="()=>OnEditClick(context)" />
        }
        @if (IsAccessable("/api/Models/Product/{id}"))
        {
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="@WtmBlazor.Localizer["Page.详情"]" OnClick="()=>OnDetailsClick(context)" />
        }
        @if (IsAccessable("/api/Models/Product/BatchDelete") && context.Product_AuditStatus != AuditStatusEnum.AuditedApproved)
        {
            <PopConfirmButton OnConfirm="() => OnDeleteClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]"
                              ConfirmButtonColor="Color.Danger" />
        }
        @if (IsAccessable("/api/Models/Product/UpdateAuditField/{id}"))
        {
            <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                              ConfirmButtonColor="Color.Warning" />
        }
    </RowButtonTemplate>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/Models/Product/Create"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.ProductVMs.Product_View" Icon="fa fa-plus" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="(x)=>OnCreateClick()" />
        }
        @* @if (IsAccessable("/api/Models/Product/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="TEX.ViewModel.Models.ProductVMs.Product_View"
                                          Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" ConfirmButtonColor="Color.Danger" />
        } *@
        @if (IsAccessable("/api/Models/Product/BatchEdit"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.ProductVMs.Product_View" Icon="fa fa-pencil-square" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.BatchEdit"]" OnClickCallback="(x)=>OnBatchEditClick()" />
        }
        @if (IsAccessable("/api/Models/Product/Import"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.ProductVMs.Product_View" Icon="fa fa-tasks" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="(x)=>OnImportClick()" />
        }
        @if (IsAccessable("/api/Models/Product/ProductExportExcel"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.ProductVMs.Product_View" Icon="fa fa-arrow-circle-down" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="(x)=>OnExportClick()" IsAsync="true" />
        }
    </TableToolbarTemplate>

</Table>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private ProductListVM Model = new ProductListVM();
    private Table<TEX.ViewModel.Models.ProductVMs.Product_View> ProductListVMdataTable;
    private List<SelectedItem> Categories { get; set; }

    private async Task ProductListVMDoSearch()
    {
        await ProductListVMdataTable.QueryAsync();
    }

    private async Task<QueryData<TEX.ViewModel.Models.ProductVMs.Product_View>> ProductListVMOnSearchProduct(QueryPageOptions opts)
    {

        return await StartSearch<TEX.ViewModel.Models.ProductVMs.Product_View>("/api/Models/Product/SearchProduct", Model.Searcher, opts);
    }
    private async Task OnCreateClick()
    {
        var id = ProductListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Create>(@WtmBlazor.Localizer["Sys.Create"], x => x.id == (id ?? ""), isMax: false) == DialogResult.Yes)
        {
            await ProductListVMdataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(TEX.ViewModel.Models.ProductVMs.Product_View item)
    {
        if (await OpenDialog<Edit>(@WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString(), isMax: false) == DialogResult.Yes)
        {
            await ProductListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(TEX.ViewModel.Models.ProductVMs.Product_View item)
    {
        if (await OpenDialog<Details>(@WtmBlazor.Localizer["Page.详情"], x => x.id == item.ID.ToString(), isMax: false) == DialogResult.Yes)
        {
            await ProductListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDeleteClick(TEX.ViewModel.Models.ProductVMs.Product_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/Models/Product/batchdelete", (s) => "Sys.OprationSuccess");
        await ProductListVMdataTable.QueryAsync();
    }

    private async Task OnBatchDeleteClick()
    {
        if (ProductListVMdataTable.SelectedRows?.Any() == true)
        {
            await PostsData(ProductListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList(), $"/api/Models/Product/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await ProductListVMdataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnBatchEditClick()
    {
        if (ProductListVMdataTable.SelectedRows?.Any() == true)
        {
            if (await OpenDialog<BatchEdit>(WtmBlazor.Localizer["Sys.BatchEdit"], x => x.ids == ProductListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToArray(), isMax: false) == DialogResult.Yes)
            {
                await ProductListVMdataTable.QueryAsync();
            }
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnImportClick()
    {
        var id = ProductListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Import>(@WtmBlazor.Localizer["Sys.Import"], x => x.id == (id ?? ""), isMax: false) == DialogResult.Yes)
        {
            await ProductListVMdataTable.QueryAsync();
        }
    }

    private async Task OnExportClick()
    {
        if (ProductListVMdataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/Models/Product/ProductExportExcelByIds", ProductListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/Models/Product/ProductExportExcel", Model.Searcher);
        }
    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);
    }

    //加入审核功能
    private async Task OnAuditClick(Product_View item)
    {
        //Controller参数前不加[Frombody]会报错InternalError,调试到API上,传过去的ID为null,在httpclient.PostAsync(url,content)时,id还是有值的
        await PostsData(item.ID, $"/api/Models/Product/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await ProductListVMdataTable.QueryAsync();
    }
}
