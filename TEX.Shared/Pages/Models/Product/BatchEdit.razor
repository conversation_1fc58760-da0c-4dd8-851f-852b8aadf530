
@page "/Models/Product/BatchEdit"
@using TEX.ViewModel.Models.ProductVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
  <div style="margin-bottom:10px;"> @WtmBlazor.Localizer["Sys.BatchEditConfirm"]</div>
  <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
    <BootstrapInput @bind-Value="@Model.LinkedVM.ProductName"/>
    <Select @bind-Value="@Model.LinkedVM.Category" Id="gc90fab5641434790adbe43bf45045be4"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Contents"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Spec"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.GSM"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Width"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.PileLength"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.DyeingProcess"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.KnittingProcess"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.FinishingProcess"/>
  </Row>

  <div class="modal-footer table-modal-footer">
    <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]"/>
    <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]"/>
  </div>

</ValidateForm>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private ProductBatchVM Model = new ProductBatchVM();
    private ValidateForm vform { get; set; }
    
    private async Task Submit(EditContext context)
    {
        Model.Ids = ids;
        await PostsForm(vform, "/api/Models/Product/BatchEdit", (s) => WtmBlazor.Localizer["Sys.BatchEditSuccess", s], method: HttpMethodEnum.POST);
    }
            

    public void OnClose()
    {
        CloseDialog();
    }
    
    protected override async Task OnInitializedAsync()
    {
        
        

        await base.OnInitializedAsync();
    }
}
