@page "/Models/Product/Edit"
@using TEX.ViewModel.Models.ProductVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            
            <BootstrapInput @bind-Value="@Model.Entity.ProductName" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.GSM" />
            <BootstrapInput @bind-Value="@Model.Entity.Width" />
            <BootstrapInput @bind-Value="@Model.Entity.PileLength" />
            <BootstrapInput @bind-Value="@Model.Entity.ProductCode" IsTrim="true" />
            <Select @bind-Value="@Model.Entity.CategoryId" Items="Categories" IsPopover="true" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g37cc9c85f9b2405ebb716015b9aad5f1" />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingProductName" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Contents" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Spec" />
            <BootstrapInput @bind-Value="@Model.Entity.DyeingProcess" />
            <BootstrapInput @bind-Value="@Model.Entity.KnittingProcess" />
            <BootstrapInput @bind-Value="@Model.Entity.FinishingProcess" />
            <Select @bind-Value="@Model.Entity.ParentId" Items="AllParents" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" />
            <WTUploadFile @bind-Value="@Model.Entity.Photo" />
        </Row>

        <div class="modal-footer table-modal-footer">
            <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
            <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private ProductVM Model = new ProductVM();
    private ValidateForm vform { get; set; }
    private List<SelectedItem> Categories { get; set; }
    private async Task Submit(EditContext context)
    {
        if (Model.Entity.CategoryId == 0)
        {
            vform.SetError<Product>(f => f.CategoryId, "产品分类为必填项");
            return;
        }

        await PostsForm(vform, "/api/Models/Product/Edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllParents = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {


        AllParents = await WtmBlazor.Api.CallItemsApi("/api/Models/Product/GetProducts", placeholder: WtmBlazor.Localizer["Sys.All"]);

        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<ProductVM>($"/api/Models/Product/{id}");
            Model = rv.Data;
        }
        Categories = await WtmBlazor.Api.CallItemsApi($"/api/dictitem/getdictitemsbydictname/Category", placeholder: WtmBlazor.Localizer["Sys.All"]);
        await base.OnInitializedAsync();
    }
}
