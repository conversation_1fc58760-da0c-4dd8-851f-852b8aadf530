@page "/Models/DictType/Details/{id}"
@using TEX.ViewModel.Models.DictTypeVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">
        <Display @bind-Value="@Model.Entity.Name" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.Description" ShowLabel="true" />
        <Display @bind-Value="@Model.Entity.DictOrder" ShowLabel="true" />
    </Row>
    <Table TItem="DictItem" @bind-Items="@DictItems" IsStriped="true" IsBordered="true" ShowRefresh="false" ShowToolbar="false"
           ShowDefaultButtons="false" ShowExtendEditButton="false"
           TableSize="TableSize.Compact" IsFixedHeader="true" Height="300"
           ShowExtendDeleteButton="true" ShowToastAfterSaveOrDeleteModel="false" style="margin-top:15px">
        <TableColumns>
            <TableColumn @bind-Field="@context.ItemName" />
            <TableColumn @bind-Field="@context.Description" />
            <TableColumn @bind-Field="@context.DictOrder" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private DictTypeVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    public IEnumerable<DictItem> DictItems = new List<DictItem>();

    protected override async Task OnInitializedAsync()
    {

        var rv = await WtmBlazor.Api.CallAPI<DictTypeVM>($"/api/DictType/{id}");
        Model = rv.Data;
        DictItems = Model.Entity.DictItems;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
