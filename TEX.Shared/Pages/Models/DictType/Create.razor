@page "/Models/DictType/Create"
@using TEX.ViewModel.Models.DictTypeVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.Name"  />
            <BootstrapInput @bind-Value="@Model.Entity.Description"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.DictOrder"  />
    </Row>
    <Table TItem="DictItem" @bind-Items="@DictItems" IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
           ShowDefaultButtons="true" ShowExtendEditButton="true"
           EditMode="EditMode.InCell" TableSize="TableSize.Compact" IsFixedHeader="true" Height="300"
           ShowExtendDeleteButton="true" ShowToastAfterSaveOrDeleteModel="false" style="margin-top:15px">
        <TableColumns>
            <TableColumn @bind-Field="@context.ItemName" />
            <TableColumn @bind-Field="@context.Description" />
            <TableColumn @bind-Field="@context.DictOrder" />
            <TableColumn @bind-Field="@context.Remark" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private DictTypeVM Model = new DictTypeVM();
    private ValidateForm vform { get; set; }
    public IEnumerable<DictItem> DictItems = new List<DictItem>();

    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        Model.Entity.DictItems = DictItems.ToList();
        await PostsForm(vform, "/api/DictType/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
