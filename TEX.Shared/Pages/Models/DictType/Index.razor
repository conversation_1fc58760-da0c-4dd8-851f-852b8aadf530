@page "/Models/DictType"
@using TEX.ViewModel.Models.DictTypeVMs;
@inherits BasePage
@attribute [ActionDescription("字典管理", "TEX.Controllers,DictType")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.Name"  />
            <BootstrapInput @bind-Value="@SearchModel.Description"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="DictType_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.Name"  />
        <TableColumn @bind-Field="@context.Description"  />
        <TableColumn @bind-Field="@context.DictOrder"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/DictType/Add"))
        {
            <TableToolbarButton TItem="DictType_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/DictType/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="DictType_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/DictType/Import"))
        {
            <TableToolbarButton TItem="DictType_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/DictType/ExportExcel"))
        {
            <TableToolbarButton TItem="DictType_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/DictType/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/DictType/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/DictType/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private DictTypeSearcher SearchModel = new DictTypeSearcher();
    private Table<DictType_View> dataTable;


    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<DictType_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<DictType_View>("/api/DictType/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<DictType_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(DictType_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(DictType_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/DictType/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(DictType_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/DictType/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<DictType_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/DictType/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/DictType/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<DictType_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
