@page "/Models/DictType/Edit/{id}"
@using TEX.ViewModel.Models.DictTypeVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Normal">

        <Display @bind-Value="@Model.Entity.Name" />
        <Display @bind-Value="@Model.Entity.Description" />
        <BootstrapInputNumber @bind-Value="@Model.Entity.DictOrder" />
    </Row>

    <Table TItem="DictItem" @bind-Items="@DictItems" IsStriped="true" IsBordered="true" ShowRefresh="false"
           ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
           ShowDefaultButtons="true" ShowExtendEditButton="true"
           EditMode="EditMode.InCell" TableSize="TableSize.Compact" IsFixedHeader="true" Height="300"
           ShowExtendDeleteButton="true" ShowToastAfterSaveOrDeleteModel="false" style="margin-top:15px">
        <TableColumns>
            <TableColumn @bind-Field="@context.ItemName" Align="Alignment.Left" ShowTips="true" TextEllipsis="true"/>
            <TableColumn @bind-Field="@context.Description" Align="Alignment.Left" ShowTips="true" TextEllipsis="true" />
            <TableColumn @bind-Field="@context.DictOrder" />
            <TableColumn @bind-Field="@context.Remark" Align="Alignment.Left" ShowTips="true" TextEllipsis="true" />
        </TableColumns>
    </Table>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private DictTypeVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }
    public IEnumerable<DictItem> DictItems = new List<DictItem>();

    protected override async Task OnInitializedAsync()
    {

        var rv = await WtmBlazor.Api.CallAPI<DictTypeVM>($"/api/DictType/{id}");
        Model = rv.Data;
        DictItems = Model.Entity.DictItems;
        await base.OnInitializedAsync();
    }

    private async Task Submit(EditContext context)
    {
        Model.Entity.DictItems = DictItems.ToList();
        await PostsForm(vform, $"/api/DictType/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
