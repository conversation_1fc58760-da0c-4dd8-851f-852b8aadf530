@page "/Models/OrderDetail/Details"
@using TEX.ViewModel.Models.OrderDetailVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            <Display @bind-Value="@Model.Entity.PurchaseOrderId" ShowLabel="true" Lookup="AllPurchaseOrders" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.Color" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.EngColor" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.ColorCode" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.Meters" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.KG" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.Yards" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.Price" ShowLabel="true" IsDisabled="true" />
            @* <Display @bind-Value="@Model.Entity.Amount"  ShowLabel="true" IsDisabled="true"/> *@
            <Display @bind-Value="@Model.Entity.CreateTime" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.UpdateTime" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.CreateBy" ShowLabel="true" IsDisabled="true" />
            <Display @bind-Value="@Model.Entity.UpdateBy" ShowLabel="true" IsDisabled="true" />
        </Row>

        <div class="modal-footer table-modal-footer">
            <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private OrderDetailVM Model = new OrderDetailVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/OrderDetail/Edit", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {


        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders");
        AllPurchaseOrders.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });
        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<OrderDetailVM>($"/api/Models/OrderDetail/{id}");
            Model = rv.Data;
        }

        await base.OnInitializedAsync();
    }
}
