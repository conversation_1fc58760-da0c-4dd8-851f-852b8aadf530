@page "/Models/OrderDetail/Create"
@using TEX.ViewModel.Models.OrderDetailVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            <Select @bind-Value="@Model.Entity.PurchaseOrderId" Items="AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="gea8f84a32e6c42d6b1185d36ddabea51" />
            <BootstrapInput @bind-Value="@Model.Entity.Color" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.EngColor" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.ColorCode" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Meters" />
            <BootstrapInput @bind-Value="@Model.Entity.KG" />
            <BootstrapInput @bind-Value="@Model.Entity.Yards" />
            <BootstrapInput @bind-Value="@Model.Entity.Price" />
            <BootstrapInput @bind-Value="@Model.Entity.Amount" />
        </Row>

        <div class="modal-footer table-modal-footer">
            <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]" />
            <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private OrderDetailVM Model = new OrderDetailVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/OrderDetail/Create", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {


        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders");
        AllPurchaseOrders.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"], Value = "" });

        await base.OnInitializedAsync();
    }
}
