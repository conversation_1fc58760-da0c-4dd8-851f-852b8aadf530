
@page "/Models/OrderDetail/Index"
@using TEX.ViewModel.Models.OrderDetailVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage
@attribute [ActionDescription("_Page.Models.OrderDetail.Index", "TEX.Models.Controllers,OrderDetail")]

@if (id is null)
{
    <WTSearchPanel OnSearch="@OrderDetailListVMDoSearch">
  <ValidateForm Model="@Model">
    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
      <Select @bind-Value="@Model.Searcher.PurchaseOrderId" Items="AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="gad76cdd6f53a42f68dbabea28eabd7b8"/>
      <BootstrapInput @bind-Value="@Model.Searcher.Color"/>
      <BootstrapInput @bind-Value="@Model.Searcher.ColorCode"/>
    </Row>

  </ValidateForm>
</WTSearchPanel>
}

<Table @ref="OrderDetailListVMdataTable" TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View" OnQueryAsync="OrderDetailListVMOnSearchOrderDetail" 
                IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false"
       ShowToolbar="@isDetail" IsMultipleSelect="@isDetail" ShowExtendButtons="@isDetail"
       ShowDefaultButtons="false" IsPagination="@isDetail" >
  <TableColumns>
    <TableColumn @bind-Field="@context.PurchaseOrder" Text="@WtmBlazor.Localizer["Page.订单号"]" />
    <TableColumn @bind-Field="@context.OrderDetail_ProductSpec" Text="@WtmBlazor.Localizer["Page.规格"]" />
    <TableColumn @bind-Field="@context.Color" Text="@WtmBlazor.Localizer["Page.颜色"]" />
    <TableColumn @bind-Field="@context.EngColor" Text="颜色(英)" />
    <TableColumn @bind-Field="@context.ColorCode" Text="@WtmBlazor.Localizer["Page.色号"]" />
    <TableColumn @bind-Field="@context.Meters" Text="@WtmBlazor.Localizer["Page.米数"]" />
    <TableColumn @bind-Field="@context.KG" Text="@WtmBlazor.Localizer["Page.重量"]" />
    <TableColumn @bind-Field="@context.Yards" Text="@WtmBlazor.Localizer["Page.码数"]" />
    <TableColumn @bind-Field="@context.Price" Text="@WtmBlazor.Localizer["Page.单价"]" />
    <TableColumn @bind-Field="@context.Amount" Text="@WtmBlazor.Localizer["Page.金额"]" />
  </TableColumns>

  <RowButtonTemplate>
    @if (IsAccessable("/api/Models/OrderDetail/Edit"))
    {
        <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-pencil-square" Color="Color.Warning" Text="@WtmBlazor.Localizer["Sys.Edit"]"  OnClick="()=>OnEditClick(context)" />
    }
    @if (IsAccessable("/api/Models/OrderDetail/{id}"))
    {
        <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="@WtmBlazor.Localizer["Page.详情"]"  OnClick="()=>OnDetailsClick(context)" />
    }
    @if (IsAccessable("/api/Models/OrderDetail/BatchDelete"))
    {
        <PopConfirmButton OnConfirm="() => OnDeleteClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" 
            ConfirmButtonColor="Color.Danger" />
    }
  </RowButtonTemplate>
  <TableToolbarTemplate>
    @if (IsAccessable("/api/Models/OrderDetail/Create"))
    {
        <TableToolbarButton TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View" Icon="fa fa-plus" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Create"]"  OnClickCallback="(x)=>OnCreateClick()" />
    }
    @if (IsAccessable("/api/Models/OrderDetail/BatchDelete"))
    {
        <TableToolbarPopConfirmButton TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View"
             Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" 
            OnConfirm="@OnBatchDeleteClick" ConfirmButtonColor="Color.Danger"/>
    }
    @if (IsAccessable("/api/Models/OrderDetail/BatchEdit"))
    {
        <TableToolbarButton TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View" Icon="fa fa-pencil-square" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.BatchEdit"]"  OnClickCallback="(x)=>OnBatchEditClick()" />
    }
    @if (IsAccessable("/api/Models/OrderDetail/Import"))
    {
        <TableToolbarButton TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View" Icon="fa fa-tasks" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Import"]"  OnClickCallback="(x)=>OnImportClick()" />
    }
    @if (IsAccessable("/api/Models/OrderDetail/OrderDetailExportExcel"))
    {
        <TableToolbarButton TItem="TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View" Icon="fa fa-arrow-circle-down" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="(x)=>OnExportClick()" IsAsync="true" />
    }
  </TableToolbarTemplate>

</Table>



@code {
    [Parameter]
    public string id { get; set; }

    private Boolean isDetail
    {
        get
        {
            return id == null ? true : false;
        }
    }
    [Parameter]
    public string[] ids { get; set; }
    private OrderDetailListVM Model = new OrderDetailListVM();
    private Table<TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View> OrderDetailListVMdataTable;
    private async Task OrderDetailListVMDoSearch()
    {
        await OrderDetailListVMdataTable.QueryAsync();
    }
    private async Task<QueryData<TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View>> OrderDetailListVMOnSearchOrderDetail(QueryPageOptions opts)
    {

        return await StartSearch<TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View>("/api/Models/OrderDetail/SearchOrderDetail", Model.Searcher, opts);
    }
    private async Task OnCreateClick()
    {
        var id = OrderDetailListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Create>(@WtmBlazor.Localizer["Sys.Create"], x => x.id == (id ?? ""), isMax:false) == DialogResult.Yes)
        {
            await OrderDetailListVMdataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View item)
    {
        if (await OpenDialog<Edit>(@WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString(), isMax:false) == DialogResult.Yes)
        {
            await OrderDetailListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View item)
    {
        if (await OpenDialog<Details>(@WtmBlazor.Localizer["Page.详情"], x => x.id == item.ID.ToString(), isMax:false) == DialogResult.Yes)
        {
            await OrderDetailListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDeleteClick(TEX.ViewModel.Models.OrderDetailVMs.OrderDetail_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/Models/OrderDetail/batchdelete", (s) => "Sys.OprationSuccess");
        await OrderDetailListVMdataTable.QueryAsync();
    }

    private async Task OnBatchDeleteClick()
    {
        if (OrderDetailListVMdataTable.SelectedRows?.Any() == true)
        {
            await PostsData(OrderDetailListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList(), $"/api/Models/OrderDetail/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await OrderDetailListVMdataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnBatchEditClick()
    {
        if (OrderDetailListVMdataTable.SelectedRows?.Any() == true)
        {
            if (await OpenDialog<BatchEdit>(WtmBlazor.Localizer["Sys.BatchEdit"], x => x.ids == OrderDetailListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToArray(), isMax:false) == DialogResult.Yes)
            {
                await OrderDetailListVMdataTable.QueryAsync();
            }
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnImportClick()
    {
        var id = OrderDetailListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Import>(@WtmBlazor.Localizer["Sys.Import"], x => x.id == (id ?? ""), isMax:false) == DialogResult.Yes)
        {
            await OrderDetailListVMdataTable.QueryAsync();
        }
    }

    private async Task OnExportClick()
    {
        if (OrderDetailListVMdataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/Models/OrderDetail/OrderDetailExportExcelByIds", OrderDetailListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/Models/OrderDetail/OrderDetailExportExcel", Model.Searcher);
        }
    }


    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        if (id is not null) Model.Searcher.PurchaseOrderId = Guid.Parse(id);

        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders");
        AllPurchaseOrders.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });

        await base.OnInitializedAsync();
    }
}
