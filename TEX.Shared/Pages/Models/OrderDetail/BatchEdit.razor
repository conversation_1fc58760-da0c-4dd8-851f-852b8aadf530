
@page "/Models/OrderDetail/BatchEdit"
@using TEX.ViewModel.Models.OrderDetailVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
  <div style="margin-bottom:10px;"> @WtmBlazor.Localizer["Sys.BatchEditConfirm"]</div>
  <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
    <Select @bind-Value="@Model.LinkedVM.PurchaseOrderId" Items="AllPurchaseOrders" Id="g1d1929fca58e444ebabdf9d0e2c4c891"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Color"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.ColorCode"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Meters"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.KG"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Yards"/>
    <Select @bind-Value="@Model.LinkedVM.AccountUnit" Id="ga6d59a0d4cc84fba81c9cd8c81232b92"/>
    <Select @bind-Value="@Model.LinkedVM.PriceUnit" Id="gdb84a150444d48b798a6ddd97a6590a6"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Price"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Amount"/>
  </Row>

  <div class="modal-footer table-modal-footer">
    <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]"/>
    <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]"/>
  </div>

</ValidateForm>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private OrderDetailBatchVM Model = new OrderDetailBatchVM();
    private ValidateForm vform { get; set; }
    
    private async Task Submit(EditContext context)
    {
        Model.Ids = ids;
        await PostsForm(vform, "/api/Models/OrderDetail/BatchEdit", (s) => WtmBlazor.Localizer["Sys.BatchEditSuccess", s], method: HttpMethodEnum.POST);
    }
            

    public void OnClose()
    {
        CloseDialog();
    }
    
    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {
        
        
        AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders");
        AllPurchaseOrders.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });

        await base.OnInitializedAsync();
    }
}
