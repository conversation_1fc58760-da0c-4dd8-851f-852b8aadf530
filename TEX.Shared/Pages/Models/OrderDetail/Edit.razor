@page "/Models/OrderDetail/Edit"
@using TEX.ViewModel.Models.OrderDetailVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            @* <Select @bind-Value="@Model.Entity.PurchaseOrderId" Items="AllPurchaseOrders" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g8c53b812e0d44c8a932190cdcdbb7559"/> *@
            <BootstrapInput @bind-Value="@Model.Entity.Color" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.EngColor" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.ColorCode" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Meters" FormatString="0" />
            <BootstrapInput @bind-Value="@Model.Entity.KG" FormatString="0.0" />
            <BootstrapInput @bind-Value="@Model.Entity.Yards" FormatString="0" OnValueChanged="ConvertYtoM" />
            <BootstrapInput @bind-Value="@Model.Entity.Price" FormatString="0.00" />
            @* <BootstrapInput @bind-Value="@Model.Entity.Amount"/> *@
        </Row>

        <div class="modal-footer table-modal-footer">
            <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]" />
            <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private OrderDetailVM Model = new OrderDetailVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/OrderDetail/Edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    private List<SelectedItem> AllPurchaseOrders = new List<SelectedItem>();
    protected override async Task OnInitializedAsync()
    {


        // AllPurchaseOrders = await WtmBlazor.Api.CallItemsApi("/api/Models/OrderDetail/GetPurchaseOrders");
        // AllPurchaseOrders.Insert(0, new SelectedItem { Text = WtmBlazor.Localizer["Sys.PleaseSelect"],Value = "" });
        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<OrderDetailVM>($"/api/Models/OrderDetail/{id}");
            Model = rv.Data;
        }

        await base.OnInitializedAsync();
    }

    //OnValueChanged事件期望的委托类型;System.Func<decimal?, Task>
    private Task ConvertYtoM(decimal? yardsValue)
    {
        if (yardsValue.HasValue)
        {
            Model.Entity.Meters = yardsValue.Value * 0.9144m;
        }
        return Task.CompletedTask;
    }
}
