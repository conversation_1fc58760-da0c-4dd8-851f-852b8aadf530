@page "/Models/Company/Edit"
@using TEX.ViewModel.Models.CompanyVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            <BootstrapInput @bind-Value="@Model.Entity.CompanyCode" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.CompanyName" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.CompanyFullName" IsTrim="true" />
            <Select @bind-Value="@Model.Entity.CompanyType" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g453e923309284ae1886e36388d4dd42e" />
            <Select @bind-Value="@Model.Entity.Relationship" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g06f70576e9b24ff491b4a4e9bd87c42a" />
            <BootstrapInput @bind-Value="@Model.Entity.ContactPhone" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Adress" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.TaxNO" />
            
        </Row>
        <Row>
            <Textarea @bind-Value="@Model.Entity.InvoiceInfo" rows="6" ShowLabel="true" />
        </Row>
        <div class="modal-footer table-modal-footer">
            <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
            <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private CompanyVM Model = new CompanyVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/Company/Edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

    protected override async Task OnInitializedAsync()
    {


        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<CompanyVM>($"/api/Models/Company/{id}");
            Model = rv.Data;
        }

        await base.OnInitializedAsync();
    }
}
