@page "/Models/Company/Details"
@using TEX.ViewModel.Models.CompanyVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row>
            <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
                <Display @bind-Value="@Model.Entity.CompanyCode" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.CompanyName" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.CompanyFullName" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.CompanyType" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.Relationship" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.ContactPhone" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.Adress" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.TaxNO" ShowLabel="true" IsDisabled="true" />
                @* <Display @bind-Value="@Model.Entity.CreateTime" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.UpdateTime" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.CreateBy" ShowLabel="true" IsDisabled="true" />
                <Display @bind-Value="@Model.Entity.UpdateBy" ShowLabel="true" IsDisabled="true" /> *@
            </Row>
            
        </Row>
        <Row>
            <Textarea @bind-Value="@Model.Entity.InvoiceInfo" rows="4" ShowLabel="true" IsDisabled="true" />
        </Row>
        <div style="height:300px;margin:16px 0;">
            <Tab IsBorderCard="true" IsLazyLoadTabItem="true" Height="30">
                <TabItem Text="货运地址">
                    <TEX.Shared.Pages.BasicInfo.DeliveryAddress.AddressManage Company="@Model.Entity" />
                    @* <TEX.Shared.Pages.BasicInfo.DeliveryAddress.Index Company="@Model.Entity" /> *@
                </TabItem>

                <TabItem Text="联系人">
                    <TEX.Shared.Pages.BasicInfo.Contact.ContactList Company="@Model.Entity" />

                </TabItem>

            </Tab>
        </div>
        
        <div class="modal-footer table-modal-footer">
            <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private CompanyVM Model = new CompanyVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/Company/Edit", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    protected override async Task OnInitializedAsync()
    {


        if (id != "")
        {
            var rv = await WtmBlazor.Api.CallAPI<CompanyVM>($"/api/Models/Company/{id}");
            Model = rv.Data;
        }

        await base.OnInitializedAsync();
    }
}
