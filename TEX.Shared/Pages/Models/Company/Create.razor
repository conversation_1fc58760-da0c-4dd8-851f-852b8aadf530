@page "/Models/Company/Create"
@using TEX.ViewModel.Models.CompanyVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<div>
    <ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Normal">
            <BootstrapInput @bind-Value="@Model.Entity.CompanyCode" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.CompanyName" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.CompanyFullName" IsTrim="true" />
            <Select @bind-Value="@Model.Entity.CompanyType" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g8cdcd1ccd2154ae5b7a9e44273762329" />
            <Select @bind-Value="@Model.Entity.Relationship" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]" Id="g47225409948449cfac0d0a3c9af34fb7" />
            <BootstrapInput @bind-Value="@Model.Entity.ContactPhone" IsTrim="true" />
            <BootstrapInput @bind-Value="@Model.Entity.Adress" />
            <BootstrapInput @bind-Value="@Model.Entity.TaxNO" />
            
        </Row>
        <Row>
            <Textarea @bind-Value="@Model.Entity.InvoiceInfo" rows="6" ShowLabel="true" />
        </Row>

        <div class="modal-footer table-modal-footer">
            <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
            <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Submit"]" IsAsync="true" />
        </div>

    </ValidateForm>

</div>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private CompanyVM Model = new CompanyVM();
    private ValidateForm vform { get; set; }
    private async Task Submit(EditContext context)
    {


        await PostsForm(vform, "/api/Models/Company/Create", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

    protected override async Task OnInitializedAsync()
    {

        await base.OnInitializedAsync();
    }
}
