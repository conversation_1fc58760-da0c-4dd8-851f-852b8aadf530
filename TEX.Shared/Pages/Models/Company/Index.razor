@page "/Models/Company/Index"
@using TEX.Model.Models
@using TEX.ViewModel.Models.CompanyVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage
@attribute [ActionDescription("_Page.Models.Company.Index", "TEX.Models.Controllers,Company")]

<WTSearchPanel OnSearch="@CompanyListVMDoSearch">
    <ValidateForm Model="@Model">
        <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
            <BootstrapInput @bind-Value="@Model.Searcher.CompanyCode" />
            <BootstrapInput @bind-Value="@Model.Searcher.CompanyName" />
            <Select @bind-Value="@Model.Searcher.CompanyType" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g9e035adf57514f848f43bc4e6c83183e" />
            <Select @bind-Value="@Model.Searcher.Relationship" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" Id="g39cb155d9b4b4a14b14db52c75f88022" />
            <Select @bind-Value="@Model.Searcher.AuditStatus" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]" />
        </Row>

    </ValidateForm>
</WTSearchPanel>


<Table @ref="CompanyListVMdataTable" TItem="TEX.ViewModel.Models.CompanyVMs.Company_View"
       OnQueryAsync="CompanyListVMOnSearchCompany" ShowExtendDeleteButton="false"
       IsStriped="true" IsBordered="true" ShowRefresh="false" ShowExtendEditButton="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
       ShowDeleteButton="false"
       ShowDefaultButtons="false" IsPagination="true" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.Company_CompanyCode" Text="@WtmBlazor.Localizer["Page.公司代码"]" />
        <TableColumn @bind-Field="@context.Company_CompanyName" Text="@WtmBlazor.Localizer["Page.公司名称"]" />
        <TableColumn @bind-Field="@context.Company_CompanyFullName" Text="@WtmBlazor.Localizer["Page.公司全称"]" />
        <TableColumn @bind-Field="@context.Company_CompanyType" Text="@WtmBlazor.Localizer["Page.公司类型"]" />
        <TableColumn @bind-Field="@context.Company_Relationship" Text="@WtmBlazor.Localizer["Page.往来关系"]" />
        <TableColumn @bind-Field="@context.Company_ContactPhone" Text="@WtmBlazor.Localizer["Page.电话"]" />
        @* <TableColumn @bind-Field="@context.Company_Adress" Text="@WtmBlazor.Localizer["_Admin.Address"]" /> *@
        <TableColumn @bind-Field="@context.Company_TaxNO" Text="@WtmBlazor.Localizer["Page.税号"]" ShowTips="true" TextEllipsis="true"/>
        @* <TableColumn @bind-Field="@context.Company_InvoiceInfo" Text="@WtmBlazor.Localizer["Page.开票资料"]" ShowTips="true" TextEllipsis="true" /> *@
        <TableColumn @bind-Field="@context.Company_AuditStatus" Text="@WtmBlazor.Localizer["_AuditStatus"]">
            <Template Context="v">
                <AuditStatusDisplay statusEnum="@v.Row.Company_AuditStatus"></AuditStatusDisplay>
            </Template>
        </TableColumn>
    </TableColumns>

    <RowButtonTemplate>
        @if (IsAccessable("/api/Models/Company/Edit") && context.Company_AuditStatus != AuditStatusEnum.AuditedApproved)
        {
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-pencil-square" Color="Color.Success" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="()=>OnEditClick(context)" />
        }
        @if (IsAccessable("/api/Models/Company/{id}"))
        {
            <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="@WtmBlazor.Localizer["Page.详情"]" OnClick="()=>OnDetailsClick(context)" />
        }
        @if (IsAccessable("/api/Models/Company/BatchDelete") && context.Company_AuditStatus != AuditStatusEnum.AuditedApproved)
        {
            <PopConfirmButton OnConfirm="() => OnDeleteClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]"
                              ConfirmButtonColor="Color.Danger" />
        }
        @if (IsAccessable("/api/Models/Company/UpdateAuditField/{id}"))
        {
            <PopConfirmButton OnConfirm="() => OnAuditClick(context)" Size="Size.ExtraSmall" Icon="fa fa-trash" Color="Color.Warning" Text="@WtmBlazor.Localizer["审核"]" Content="@WtmBlazor.Localizer["_AuditConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["_AuditOrUnAudit"]"
                              ConfirmButtonColor="Color.Warning" />
        }
    </RowButtonTemplate>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/Models/Company/Create"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.CompanyVMs.Company_View" Icon="fa fa-plus" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="(x)=>OnCreateClick()" />
        }
        @* @if (IsAccessable("/api/Models/Company/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="TEX.ViewModel.Models.CompanyVMs.Company_View"
                                          Icon="fa fa-trash" Color="Color.Danger" Text="@WtmBlazor.Localizer["Sys.Delete"]" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" ConfirmButtonColor="Color.Danger" />
        } *@
        @if (IsAccessable("/api/Models/Company/BatchEdit"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.CompanyVMs.Company_View" Icon="fa fa-pencil-square" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.BatchEdit"]" OnClickCallback="(x)=>OnBatchEditClick()" />
        }
        @if (IsAccessable("/api/Models/Company/Import"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.CompanyVMs.Company_View" Icon="fa fa-tasks" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="(x)=>OnImportClick()" />
        }
        @if (IsAccessable("/api/Models/Company/CompanyExportExcel"))
        {
            <TableToolbarButton TItem="TEX.ViewModel.Models.CompanyVMs.Company_View" Icon="fa fa-arrow-circle-down" Color="Color.Primary" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="(x)=>OnExportClick()" IsAsync="true" />
        }
    </TableToolbarTemplate>

</Table>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private CompanyListVM Model = new CompanyListVM();
    private Table<TEX.ViewModel.Models.CompanyVMs.Company_View> CompanyListVMdataTable;
    private async Task CompanyListVMDoSearch()
    {
        await CompanyListVMdataTable.QueryAsync();
    }
    private async Task<QueryData<TEX.ViewModel.Models.CompanyVMs.Company_View>> CompanyListVMOnSearchCompany(QueryPageOptions opts)
    {

        return await StartSearch<TEX.ViewModel.Models.CompanyVMs.Company_View>("/api/Models/Company/SearchCompany", Model.Searcher, opts);
    }
    private async Task OnCreateClick()
    {
        var id = CompanyListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Create>(@WtmBlazor.Localizer["Sys.Create"], x => x.id == (id ?? ""), isMax: false) == DialogResult.Yes)
        {
            await CompanyListVMdataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(TEX.ViewModel.Models.CompanyVMs.Company_View item)
    {
        if (await OpenDialog<Edit>(@WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString(), isMax: false) == DialogResult.Yes)
        {
            await CompanyListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(TEX.ViewModel.Models.CompanyVMs.Company_View item)
    {
        if (await OpenDialog<Details>(@WtmBlazor.Localizer["Page.详情"], x => x.id == item.ID.ToString(), isMax: false) == DialogResult.Yes)
        {
            await CompanyListVMdataTable.QueryAsync();
        }
    }

    private async Task OnDeleteClick(TEX.ViewModel.Models.CompanyVMs.Company_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/Models/Company/batchdelete", (s) => "Sys.OprationSuccess");
        await CompanyListVMdataTable.QueryAsync();
    }

    private async Task OnBatchDeleteClick()
    {
        if (CompanyListVMdataTable.SelectedRows?.Any() == true)
        {
            await PostsData(CompanyListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList(), $"/api/Models/Company/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await CompanyListVMdataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnBatchEditClick()
    {
        if (CompanyListVMdataTable.SelectedRows?.Any() == true)
        {
            if (await OpenDialog<BatchEdit>(WtmBlazor.Localizer["Sys.BatchEdit"], x => x.ids == CompanyListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToArray(), isMax: false) == DialogResult.Yes)
            {
                await CompanyListVMdataTable.QueryAsync();
            }
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnImportClick()
    {
        var id = CompanyListVMdataTable.SelectedRows?.Select(x => x.ID.ToString())?.FirstOrDefault();
        if (await OpenDialog<Import>(@WtmBlazor.Localizer["Sys.Import"], x => x.id == (id ?? ""), isMax: false) == DialogResult.Yes)
        {
            await CompanyListVMdataTable.QueryAsync();
        }
    }

    private async Task OnExportClick()
    {
        if (CompanyListVMdataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/Models/Company/CompanyExportExcelByIds", CompanyListVMdataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/Models/Company/CompanyExportExcel", Model.Searcher);
        }
    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }
    //加入审核功能
    private async Task OnAuditClick(Company_View item)
    {
        //Controller参数前不加[Frombody]会报错InternalError,调试到API上,传过去的ID为null,在httpclient.PostAsync(url,content)时,id还是有值的
        await PostsData(item.ID, $"/api/Models/Company/UpdateAuditField", (s) => "Sys.OprationSuccess");
        await CompanyListVMdataTable.QueryAsync();
    }
}
