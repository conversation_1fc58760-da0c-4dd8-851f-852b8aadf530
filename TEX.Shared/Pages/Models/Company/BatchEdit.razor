
@page "/Models/Company/BatchEdit"
@using TEX.ViewModel.Models.CompanyVMs
@using System.ComponentModel.DataAnnotations
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
  <div style="margin-bottom:10px;"> @WtmBlazor.Localizer["Sys.BatchEditConfirm"]</div>
  <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
    <BootstrapInput @bind-Value="@Model.LinkedVM.CompanyFullName"/>
    <Select @bind-Value="@Model.LinkedVM.CompanyType" Id="gaae974631def4c0a8b578ff134a25519"/>
    <Select @bind-Value="@Model.LinkedVM.Relationship" Id="g9bd09fed86364c8ba6a2bb95d5a06fd9"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.ContactPhone"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.Adress"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.TaxNO"/>
    <BootstrapInput @bind-Value="@Model.LinkedVM.InvoiceInfo"/>
  </Row>

  <div class="modal-footer table-modal-footer">
    <Button ButtonType="ButtonType.Submit" IsAsync="true" Text="@WtmBlazor.Localizer["Sys.Submit"]"/>
    <Button OnClick="OnClose" Text="@WtmBlazor.Localizer["Sys.Close"]"/>
  </div>

</ValidateForm>



@code {
    [Parameter]
    public string id { get; set; }
    [Parameter]
    public string[] ids { get; set; }
    private CompanyBatchVM Model = new CompanyBatchVM();
    private ValidateForm vform { get; set; }
    
    private async Task Submit(EditContext context)
    {
        Model.Ids = ids;
        await PostsForm(vform, "/api/Models/Company/BatchEdit", (s) => WtmBlazor.Localizer["Sys.BatchEditSuccess", s], method: HttpMethodEnum.POST);
    }
            

    public void OnClose()
    {
        CloseDialog();
    }
    
    protected override async Task OnInitializedAsync()
    {
        
        

        await base.OnInitializedAsync();
    }
}
