@page "/Dashboard/PurchaseOrder"
@using System.Diagnostics.CodeAnalysis
@using TEX.ViewModel.Models;
@using TEX.ViewModel.Models.PurchaseOrderVMs
@inherits BasePage
@attribute [ActionDescription("订单看板", "TEX.Controllers,Dashboard")]

@if (DashData != null)
{
    <div class="row g-3">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <div class="flex-fill text-dark fw-bold me-3">订单总览</div>
                <Select Items="YearItems" OnSelectedItemChanged="@OnItemChanged" @bind-Value="@SelectedYear"></Select>
                @* <DateTimePicker Value="DateTimePickerValue" OnValueChanged="OnDateTimePickerValueChanged" /> *@
            </div>
        </div>
        <div class="col-md-6 col-xl-3">
            <Card IsShadow="true">
                <BodyTemplate>
                    <div class="row align-items-center no-gutters">
                        <div class="col me-2">
                            <div class=" text-primary fw-bold mb-1"><span>本周下单数量</span></div>
                            <div class="text-dark fw-bold h5">
                                <CountUp Value="@DashData.POWeekTotalWeight"></CountUp>
                            </div>
                            <div class=" text-success fw-bold mb-1"><span>本月下单数量</span></div>
                            <div class="row g-0 align-items-center">
                                <div class="col-auto">
                                    <div class="text-dark fw-bold h5 me-3 mb-0">
                                        <CountUp Value="@DashData.POMonthTotalWeight"></CountUp>
                                    </div>
                                </div>
                                <div class="col"><div class="db-progress"><Progress Value="@progressValue" IsShowValue="true" IsAnimated=true Color="Color.Info" IsStriped=true IsShowPercent=true Round="1"></Progress></div></div>
                            </div>
                        </div>
                        <div class="col-auto"><i class="fa-regular fa-calendar-check fa-2x"></i></div>
                    </div>
                </BodyTemplate>
            </Card>
        </div>
        <div class="col-md-6 col-xl-3">
            <Card IsShadow="true">
                <BodyTemplate>
                    <div class="row align-items-center no-gutters">
                        <div class="col me-2">
                            <div class=" text-primary fw-bold mb-1"><span>本月下单数量</span></div>
                            <div class="text-dark fw-bold h5">
                                <CountUp Value="@DashData.POMonthTotalWeight"></CountUp>
                            </div>
                            <div class="fw-bold mb-1" style="color: #6522cc;"><span>本年下单数量</span></div>
                            <div class="row g-0 align-items-center">
                                <div class="col-auto">
                                    <div class="text-dark fw-bold h5 me-3 mb-0">
                                        <CountUp Value="@DashData.POYearTotalWeight"></CountUp>
                                    </div>
                                </div>
                                <div class="col"><div class="db-progress"><Progress Value="@yearProgressValue" IsShowValue="true" IsAnimated=true Color="Color.Info" Round="1"></Progress></div></div>
                            </div>
                        </div>
                        <div class="col-auto"><i class="fa-regular fa-calendar-check fa-2x"></i></div>
                    </div>
                </BodyTemplate>
            </Card>
        </div>
        <div class="col-md-6 col-xl-3">
            <Card IsShadow="true">
                <BodyTemplate>
                    <div class="row align-items-center no-gutters">
                        <div class="col me-2">
                            <div class=" text-primary fw-bold mb-1"><span>本周下单金额</span></div>
                            <div class="text-dark fw-bold h5">
                                <CountUp Value="@DashData.POWeekTotalAmount"></CountUp>
                            </div>
                            <div class=" text-info fw-bold mb-1"><span>本月下单金额</span></div>
                            <div class="row g-0 align-items-center">
                                <div class="col-auto">
                                    <div class="text-dark fw-bold h5 me-3 mb-0">
                                        <CountUp Value="@DashData.POMonthTotalAmount"></CountUp>
                                    </div>
                                </div>
                                <div class="col"><div class="db-progress"><Progress Value="@progressAmountValue" IsShowValue="true" Round="1"></Progress></div></div>
                            </div>
                        </div>
                        <div class="col-auto"><i class="fa-regular fa-calendar-check fa-2x"></i></div>
                    </div>
                </BodyTemplate>
            </Card>
        </div>
        <div class="col-md-6 col-xl-3">
            <Card IsShadow="true">
                <BodyTemplate>
                    <div class="row align-items-center no-gutters">
                        <div class="col me-2">
                            <div class=" text-primary fw-bold mb-1"><span>本月下单金额</span></div>
                            <div class="text-dark fw-bold h5">
                                <CountUp Value="@DashData.POMonthTotalAmount"></CountUp>
                            </div>
                            <div class=" text-info fw-bold mb-1"><span>本年下单金额</span></div>
                            <div class="row g-0 align-items-center">
                                <div class="col-auto">
                                    <div class="text-dark fw-bold h5 me-3 mb-0">
                                        <CountUp Value="@DashData.POYearTotalAmount"></CountUp>
                                    </div>
                                </div>
                                <div class="col"><div class="db-progress"><Progress Value="@yearProgressAmountValue" IsShowValue="true" Round="1"></Progress></div></div>
                            </div>
                        </div>
                        <div class="col-auto"><i class="fa-regular fa-calendar-check fa-2x"></i></div>
                    </div>
                </BodyTemplate>
            </Card>
        </div>

        @if (currentWeight != null && currentWeight.Any() && DashData != null && DashData.MonthlyTotalWeight != null && DashData.MonthlyTotalWeight.Any())
        {
            <div class="col-md-6">
                <Card IsShadow="true" HeaderText="下单数量">
                <BodyTemplate>
                    <div class="chart-area">
                        <Chart @ref="BarChart" Height="320px" Width="auto" style="display: block; width: auto; height: 320px;"
                               ChartType="ChartType.Bar" OnClickDataAsync="OnClickDateAsync" OnInitAsync="OnInitBarChartAsync" />
                    </div>
                </BodyTemplate>
            </Card>
        </div>

        <div class="col-md-6">
            <Card IsShadow="true" HeaderText="下单金额">
                <BodyTemplate>
                    <div class="chart-area">
                        <Chart @ref="AmountBarChart" Height="320px" Width="auto" style="display: block; width: auto;"
                               ChartType="ChartType.Bar" OnInitAsync="OnInitAmountBarChartAsync" />
                    </div>
                </BodyTemplate>
            </Card>
        </div>
        }

        <div class="col-md-12">
            <Card IsShadow="true" HeaderText=@label>
                <BodyTemplate>
                    <TEX.Shared.CustomeComponents.OrderList CreateDate="@SelectedData" />
                    @* <Empty Image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" Text="暂无数据"></Empty> *@
                </BodyTemplate>
            </Card>
        </div>
        <div class="col-md-12">
            <Card IsShadow="true" HeaderText="暂无数据">
                <BodyTemplate>
                    <Empty Image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" Text="暂无数据"></Empty>
                </BodyTemplate>
            </Card>
        </div>
    </div>
}
@code {

    private Chart BarChart { get; set; }
    private Chart AmountBarChart { get; set; }

    private Chart PieChart { get; set; }
    private DateTime DateTimePickerValue { get; set; } = DateTime.Today;

    private DateTime SelectedData { get; set; }

    //
    private PODashboardData DashData { get; set; } = new();

    //TODO 月份选择后刷新数据,暂时不做,后续再优化
    private IEnumerable<SelectedItem> YearItems { get; set; }
    private string SelectedYear { get; set; } = DateTime.Now.Year.ToString();
    private List<MonthlyTotalAmount> currentAmount { get; set; } = new();
    private List<MonthlyTotalWeight> currentWeight { get; set; } = new();

    private double progressValue;
    private double yearProgressValue;

    private double progressAmountValue;
    private double yearProgressAmountValue;

    private bool IsWoven { get; set; } = false;
    private string label { get; set; } = "订单记录";


    protected override async Task OnInitializedAsync()
    {
        await GetData();//异步获取数据可能会导致空引用
        await base.OnInitializedAsync();
        //图表在异步数据初始化时加载数据为空,需要重新加载数据
        //await BarChart.Reload();
    }

    private async Task GetData()
    {
        await Task.Delay(1000);
        //var rv = await WtmBlazor.Api.CallAPI<PODashboardData>("/api/DashboardOrder/GetOrderData");
        var rv = await WtmBlazor.Api.CallAPI<PODashboardData>("/api/Models/PurchaseOrder/GetOrderData");
        DashData = rv.Data;
        //DashData = service.GetData();
        progressValue = DashData.POMonthTotalWeight == 0m ? 0 : (double)(DashData.POWeekTotalWeight / DashData.POMonthTotalWeight) * 100;
        yearProgressValue = DashData.POYearTotalWeight == 0m ? 0 : (double)(DashData.POMonthTotalWeight / DashData.POYearTotalWeight) * 100;
        progressAmountValue = DashData.POMonthTotalAmount == 0m ? 0 : (double)(DashData.POWeekTotalAmount / DashData.POMonthTotalAmount) * 100;
        yearProgressAmountValue = DashData.POYearTotalAmount == 0m ? 0 : (double)(DashData.POMonthTotalAmount / DashData.POYearTotalAmount) * 100;

        YearItems = DashData.MonthlyTotalAmount
            .Select(x => x.YearMonth.Year)
            .Distinct()
            .OrderByDescending(year => year)
            .Select(year => new SelectedItem
                {
                    Text = year + "年",
                    Value = year + "-01"
                })
            .ToList();
        currentAmount = DashData.MonthlyTotalAmount.Where(x => x.YearMonth.Year == DateTime.Now.Year).ToList();
        currentWeight = DashData.MonthlyTotalWeight.Where(x => x.YearMonth.Year == DateTime.Now.Year).ToList();
        StateHasChanged();
    }

    /// <summary>
    /// 日期切换
    /// </summary>
    /// <returns></returns>
    // private async Task OnDateTimePickerValueChanged(DateTime dt)
    // {
    //     Data = await DashboardService.GetDashboardDataAsync();
    //     await BarChart.Reload();
    //     await PieChart.Reload();
    //     StateHasChanged();
    // }
    private async Task OnItemChanged(SelectedItem item)
    {
        //SelectedYear = item.Value;
        //Logger.Log($"SelectedItem Text: {item.Text} Value: {item.Value} Selected");
        currentAmount = DashData.MonthlyTotalAmount.Where(x => x.YearMonth.Year == DateTime.Parse(SelectedYear).Year).ToList();
        currentWeight = DashData.MonthlyTotalWeight.Where(x => x.YearMonth.Year == DateTime.Parse(SelectedYear).Year).ToList();
        //await BarChart.Reload();
        await BarChart.Reload();
        await AmountBarChart.Reload();
        StateHasChanged();
        //await Task.CompletedTask;
    }


    private async Task OnClickDateAsync((int DatasetIndex, int index) v)
    {
        var r = currentWeight;
        var c = r[v.index];
        var date = c.YearMonth.Date;
        label = date.ToString("yyyy-MM") + " 订单明细";
        SelectedData = new DateTime(date.Year, date.Month, 1);
        StateHasChanged();
        await Task.CompletedTask;
    }

    /// <summary>
    /// 初始化柱形图
    /// </summary>
    /// <returns></returns>

    private Task<ChartDataSource> OnInitBarChartAsync()
    {
        var ds = new ChartDataSource();
        //循环加载几十次,报错空引用几十次
        //Data.MonthlyTotalWeights初始化为空,导致报错,在Model上new一个解决
        //if (!currentWeight.Any()) return Task.FromResult(ds);

        if (DashData.MonthlyTotalWeight.Any())
        {
            var set = new ChartDataset
                {
                    Label = "下单数量",
                    Data = currentWeight.Select(x => x.TotalWeight).Cast<object>()
                };
            ds.Options.Title = $"{SelectedYear.Substring(0, 4)}年 月度下单重量";
            ds.Options.X.Title = $"月份";
            ds.Options.Y.Title = "下单重量";
            ds.Options.ShowLegend = false;
            ds.Labels = currentWeight.Select(x => x.YearMonth.ToString(format: "yy-MM"));
            ds.Data.Add(set);
        }
        return Task.FromResult(ds);
    }
    private Task<ChartDataSource> OnInitAmountBarChartAsync()
    {
        var ds = new ChartDataSource();
        ds.Options.BarColorSeparately = true;
        if (DashData.MonthlyTotalAmount.Any())
        {
            var set = new ChartDataset
                {
                    Label = "下单金额",
                    BackgroundColor = new string[]
                                            {
                                        "rgb(75, 192, 192, 0.5)",
                                        "rgb(255, 99, 132, 0.5)",
                                        "rgb(255, 159, 64, 0.5)",
                                        "rgb(255, 205, 86, 0.5)",
                                        "rgb(255, 99, 71, 0.5)",
                                        "rgb(255, 192, 203, 0.5)"
                                            },

                    //BackgroundColor = ["rgb(75, 192, 192, 0.5)", "rgb(255, 99, 132, 0.5)", "rgb(255, 159, 64, 0.5)", "rgb(255, 205, 86, 0.5)", "rgb(255, 99, 71, 0.5)", "rgb(255, 192, 203, 0.5)"],
                    Data = currentAmount.Select(x => x.TotalAmount).Cast<object>()
                };
            ds.Options.Title = $"{SelectedYear.Substring(0, 4)}年 月度下单金额";
            ds.Options.X.Title = $"月份";
            ds.Options.Y.Title = "下单金额";
            ds.Options.ShowLegend = false;

            ds.Labels = currentAmount.Select(x => x.YearMonth.ToString(format: "yy-MM"));
            ds.Data.Add(set);
        }
        return Task.FromResult(ds);
    }

    /*
    private Task<ChartDataSource> OnInitPieChartAsync()
    {
        var ds = new ChartDataSource();
        if (Data.TestKKSGroupList.Any())
        {
            var set = new ChartDataset
                {
                    Label = $"{DateTimePickerValue.Month} 月数量",
                    Data = Data.TestKKSGroupList.Select(x => x.Count).Cast<object>()
                };
            ds.Labels = Data.TestKKSGroupList.Select(x => $"{x.KKS} {x.NAM}");
            ds.Options.ShowLegend = false;
            ds.Options.ShowXScales = false;
            ds.Options.ShowYScales = false;
            ds.Data.Add(set);
        }

        return Task.FromResult(ds);
    }
    */

    /// <summary>
    /// 根据数值大小获取颜色
    /// </summary>
    /// <param name = "value"></param>
    private static Color GetColor(double value) => value switch
    {
        100 => Color.Success,
        >= 50 => Color.Info,
        >= 25 => Color.Danger,
        _ => Color.Warning
    };
}
