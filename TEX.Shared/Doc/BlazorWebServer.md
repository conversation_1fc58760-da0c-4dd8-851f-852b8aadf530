# TEX项目 Blazor Web Server 开发指南

## 概述

本文档基于TEX纺织企业管理系统，详细介绍如何在WTM框架基础上进行Blazor Web Server开发。WTM框架提供了强大的代码生成功能，可以快速创建基础的CRUD页面，但在复杂业务场景下需要进行扩展开发。

## 项目架构

### 分层结构

```
TEX.Shared/          # Blazor前端项目
├── Pages/           # 业务页面
├── Components/      # 可复用组件
├── CustomeComponents/ # 自定义业务组件
├── Shared/          # 布局和共享组件
├── WtmBlazorUtils/  # WTM框架工具类
└── wwwroot/         # 静态资源

TEX.ViewModel/       # 视图模型层
├── [Module]VMs/     # 各模块ViewModel
│   ├── [Entity]VM.cs      # 编辑ViewModel
│   ├── [Entity]ListVM.cs  # 列表ViewModel
│   └── [Entity]Searcher.cs # 搜索ViewModel

TEX.Model/           # 数据模型层
├── Models/          # 实体模型
├── Enums.cs         # 枚举定义
└── AuditBase.cs     # 基础类和接口
```

### 数据流转

```
Blazor Page → ViewModel → Model → Database
     ↑                              ↓
   UI组件 ← API Response ← Controller ← DataContext
```

## WTM框架基础功能

### 1. 自动生成的CRUD页面

WTM框架为每个实体自动生成以下页面：

- **Index.razor** - 列表页面，包含搜索、分页、批量操作
- **Create.razor** - 新增页面
- **Edit.razor** - 编辑页面
- **Details.razor** - 详情页面
- **Import.razor** - 导入页面

#### 典型的Index页面结构

```razor
@page "/Greige/Pattern"
@using TEX.ViewModel.Greige.PatternVMs;
@inherits BasePage
@attribute [ActionDescription("提花花型", "TEX.Controllers,Pattern")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
            <WTDateRange @bind-Value="@SearchModel.CreateDate" />
            <BootstrapInput @bind-Value="@SearchModel.CodeNo" />
            <BootstrapInput @bind-Value="@SearchModel.PatternName" />
        </Row>
    </ValidateForm>
</WTSearchPanel>

<Table @ref="dataTable" TItem="Pattern_View" OnQueryAsync="OnSearch" 
       IsPagination="true" IsStriped="true" IsBordered="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.CodeNo" />
        <TableColumn @bind-Field="@context.PatternName" />
        <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
    </TableColumns>
</Table>
```

#### 对应的代码后置文件

```csharp
public partial class Index : BasePage
{
    private PatternSearcher SearchModel = new PatternSearcher();
    private Table<Pattern_View> dataTable;

    private async Task<QueryData<Pattern_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<Pattern_View>("/api/Greige/Pattern/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }
}
```

### 2. ViewModel层设计模式

#### 搜索ViewModel (Searcher)

```csharp
public partial class PatternSearcher : BaseSearcher
{
    [Display(Name = "_CreateDate")]
    public DateRange CreateDate { get; set; }
    
    [Display(Name = "_CodeNo")]
    public int? CodeNo { get; set; }
    
    [Display(Name = "_PatternName")]
    public String PatternName { get; set; }
}
```

#### 编辑ViewModel (VM)

```csharp
public partial class PatternVM : BaseCRUDVM<Pattern>
{
    public PatternVM()
    {
        SetInclude(x => x.DetailList);
    }

    public override DuplicatedInfo<Pattern> SetDuplicatedCheck()
    {
        return CreateFieldsInfo(SimpleField(x => x.CodeNo));
    }

    public override async Task DoAddAsync()
    {
        // 自定义新增逻辑
        await base.DoAddAsync();
    }
}
```

#### 列表ViewModel (ListVM)

```csharp
public partial class PatternListVM : BasePagedListVM<Pattern_View, PatternSearcher>
{
    public override IOrderedQueryable<Pattern_View> GetSearchQuery()
    {
        var query = DC.Set<Pattern>()
            .CheckBetween(Searcher.CreateDate?.GetStartTime(), 
                         Searcher.CreateDate?.GetEndTime(), x => x.CreateDate)
            .CheckEqual(Searcher.CodeNo, x => x.CodeNo)
            .CheckContain(Searcher.PatternName, x => x.PatternName)
            .Select(x => new Pattern_View
            {
                ID = x.ID,
                CodeNo = x.CodeNo,
                PatternName = x.PatternName,
                CreateDate = x.CreateDate
            })
            .OrderBy(x => x.ID);
        return query;
    }
}
```

## 扩展开发指南

### 1. 突破CRUD限制的方法

#### 方法一：自定义API端点

在Controller中添加自定义方法：

```csharp
[HttpPost("CustomAction")]
public async Task<IActionResult> CustomAction([FromBody] CustomRequest request)
{
    // 自定义业务逻辑
    var result = await ProcessCustomLogic(request);
    return Ok(result);
}
```

在Blazor页面中调用：

```csharp
private async Task HandleCustomAction()
{
    var request = new CustomRequest { /* 设置参数 */ };
    var response = await WtmBlazor.Api.CallAPI("/api/Pattern/CustomAction", 
                                              HttpMethodEnum.POST, request);
    if (response.StatusCode == HttpStatusCode.OK)
    {
        // 处理成功响应
    }
}
```

#### 方法二：重写ViewModel方法

```csharp
public override async Task DoAddAsync()
{
    // 添加前的自定义验证
    if (!ValidateCustomRules())
    {
        MSD.AddModelError("", "自定义验证失败");
        return;
    }

    // 执行自定义业务逻辑
    await ProcessCustomBusinessLogic();

    // 调用基类方法
    await base.DoAddAsync();

    // 添加后的后续处理
    await PostProcessing();
}
```

#### 方法三：自定义页面组件

创建完全自定义的页面，不依赖框架生成：

```razor
@page "/Custom/ComplexPage"
@inherits BasePage

<div class="complex-page">
    <Card>
        <HeaderTemplate>
            <h4>复杂业务页面</h4>
        </HeaderTemplate>
        <BodyTemplate>
            <!-- 自定义复杂UI -->
            <CustomBusinessComponent @bind-Data="@businessData" 
                                   OnDataChanged="@HandleDataChanged" />
        </BodyTemplate>
    </Card>
</div>

@code {
    private BusinessData businessData = new();

    protected override async Task OnInitializedAsync()
    {
        businessData = await LoadComplexData();
    }

    private async Task HandleDataChanged(BusinessData data)
    {
        await SaveComplexData(data);
        await InvokeAsync(StateHasChanged);
    }
}
```

### 2. 自定义组件开发

#### 创建可复用的业务组件

```razor
@* CustomeComponents/ProductSelector.razor *@
@inherits ComponentBase

<Select @bind-Value="@SelectedProductId" 
        Items="@ProductItems" 
        OnSelectedItemChanged="@OnProductChanged"
        PlaceHolder="请选择产品">
</Select>

@code {
    [Parameter] public Guid? SelectedProductId { get; set; }
    [Parameter] public EventCallback<Guid?> SelectedProductIdChanged { get; set; }
    [Parameter] public EventCallback<Product> OnProductSelected { get; set; }

    private List<SelectedItem> ProductItems = new();

    protected override async Task OnInitializedAsync()
    {
        ProductItems = await LoadProducts();
    }

    private async Task OnProductChanged(SelectedItem item)
    {
        SelectedProductId = Guid.Parse(item.Value);
        await SelectedProductIdChanged.InvokeAsync(SelectedProductId);
        
        var product = await GetProductById(SelectedProductId.Value);
        await OnProductSelected.InvokeAsync(product);
    }
}
```

#### 使用自定义组件

```razor
<ProductSelector @bind-SelectedProductId="@Model.ProductId"
                OnProductSelected="@HandleProductSelected" />

@code {
    private async Task HandleProductSelected(Product product)
    {
        // 处理产品选择事件
        Model.ProductName = product.ProductName;
        Model.ProductCode = product.ProductCode;
        await InvokeAsync(StateHasChanged);
    }
}
```

### 3. 复杂数据绑定

#### 主从表编辑

```razor
<ValidateForm Model="@Model">
    <!-- 主表信息 -->
    <Row>
        <BootstrapInput @bind-Value="@Model.Entity.OrderNo" />
        <WTDateRange @bind-Value="@Model.Entity.CreateDate" />
    </Row>

    <!-- 从表信息 -->
    <Card>
        <HeaderTemplate>订单明细</HeaderTemplate>
        <BodyTemplate>
            <Table TItem="OrderDetail" Items="@Model.Entity.OrderDetailList"
                   IsStriped="true" IsBordered="true">
                <TableColumns>
                    <TableColumn @bind-Field="@context.ProductName" />
                    <TableColumn @bind-Field="@context.Quantity" />
                    <TableColumn @bind-Field="@context.Price" />
                    <TableColumn>
                        <Template Context="detail">
                            <Button Color="Color.Danger" Size="Size.Small"
                                    OnClick="@(() => RemoveDetail(detail))">
                                删除
                            </Button>
                        </Template>
                    </TableColumn>
                </TableColumns>
            </Table>
            <Button Color="Color.Primary" OnClick="@AddDetail">添加明细</Button>
        </BodyTemplate>
    </Card>
</ValidateForm>

@code {
    private void AddDetail()
    {
        Model.Entity.OrderDetailList.Add(new OrderDetail());
    }

    private void RemoveDetail(OrderDetail detail)
    {
        Model.Entity.OrderDetailList.Remove(detail);
    }
}
```

### 4. 事件处理和状态管理

#### 组件间通信

```csharp
// 使用CascadingValue传递数据
<CascadingValue Value="@CurrentUser">
    <ChildComponent />
</CascadingValue>

// 子组件接收
[CascadingParameter] public LoginUserInfo CurrentUser { get; set; }
```

#### 状态更新

```csharp
private async Task RefreshData()
{
    // 重新加载数据
    await LoadData();
    
    // 通知UI更新
    await InvokeAsync(StateHasChanged);
}
```

## 性能优化建议

### 1. 减少不必要的渲染

```csharp
protected override bool ShouldRender()
{
    // 只在必要时重新渲染
    return dataChanged;
}
```

### 2. 使用虚拟化

```razor
<Virtualize Items="@largeDataSet" Context="item">
    <div>@item.Name</div>
</Virtualize>
```

### 3. 异步加载

```csharp
protected override async Task OnInitializedAsync()
{
    // 先显示页面框架
    await InvokeAsync(StateHasChanged);
    
    // 异步加载数据
    await LoadDataAsync();
}
```

## 调试技巧

### 1. 浏览器开发者工具

- 使用F12查看SignalR连接状态
- 监控网络请求和响应
- 检查JavaScript错误

### 2. 服务端调试

- 在ViewModel和Controller中设置断点
- 使用日志记录关键信息
- 监控数据库查询性能

### 3. 常见问题排查

#### 页面不更新
```csharp
// 确保调用StateHasChanged
await InvokeAsync(StateHasChanged);
```

#### 数据绑定失效
```csharp
// 检查双向绑定语法
@bind-Value="@Model.Property"
// 而不是
Value="@Model.Property"
```

## 最佳实践

1. **遵循WTM约定** - 充分利用框架的自动化功能
2. **合理使用组件** - 将复杂UI拆分为可复用组件
3. **性能优先** - 避免在渲染方法中进行复杂计算
4. **错误处理** - 实现完善的异常处理机制
5. **用户体验** - 提供加载状态和错误提示

## 高级开发技巧

### 1. 动态表单生成

基于配置动态生成表单：

```csharp
public class FormFieldConfig
{
    public string FieldName { get; set; }
    public string DisplayName { get; set; }
    public string FieldType { get; set; }
    public bool IsRequired { get; set; }
    public List<SelectedItem> Options { get; set; }
}

@code {
    private List<FormFieldConfig> formConfig = new();
    private Dictionary<string, object> formData = new();

    private RenderFragment GenerateFormField(FormFieldConfig config) => builder =>
    {
        var sequence = 0;
        switch (config.FieldType)
        {
            case "text":
                builder.OpenComponent<BootstrapInput<string>>(sequence++);
                builder.AddAttribute(sequence++, "Value", formData.GetValueOrDefault(config.FieldName, ""));
                builder.AddAttribute(sequence++, "ValueChanged",
                    EventCallback.Factory.Create<string>(this, value => formData[config.FieldName] = value));
                builder.AddAttribute(sequence++, "DisplayText", config.DisplayName);
                builder.CloseComponent();
                break;
            case "select":
                builder.OpenComponent<Select<string>>(sequence++);
                builder.AddAttribute(sequence++, "Value", formData.GetValueOrDefault(config.FieldName, ""));
                builder.AddAttribute(sequence++, "ValueChanged",
                    EventCallback.Factory.Create<string>(this, value => formData[config.FieldName] = value));
                builder.AddAttribute(sequence++, "Items", config.Options);
                builder.CloseComponent();
                break;
        }
    };
}
```

### 2. 实时数据更新

使用SignalR实现实时数据推送：

```csharp
@implements IAsyncDisposable
@inject IJSRuntime JSRuntime

<div class="real-time-data">
    <h4>实时生产数据</h4>
    <div class="data-grid">
        @foreach (var item in realTimeData)
        {
            <div class="data-item @(item.IsUpdated ? "updated" : "")">
                <span>@item.Name</span>
                <span>@item.Value</span>
            </div>
        }
    </div>
</div>

@code {
    private List<RealTimeDataItem> realTimeData = new();
    private HubConnection? hubConnection;

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl("/productionHub")
            .Build();

        hubConnection.On<RealTimeDataItem>("UpdateData", (data) =>
        {
            var existingItem = realTimeData.FirstOrDefault(x => x.Id == data.Id);
            if (existingItem != null)
            {
                existingItem.Value = data.Value;
                existingItem.IsUpdated = true;
            }
            else
            {
                realTimeData.Add(data);
            }

            InvokeAsync(StateHasChanged);

            // 3秒后移除更新标记
            _ = Task.Delay(3000).ContinueWith(_ =>
            {
                if (existingItem != null) existingItem.IsUpdated = false;
                InvokeAsync(StateHasChanged);
            });
        });

        await hubConnection.StartAsync();
    }

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }
}
```

### 3. 复杂业务流程处理

实现多步骤业务流程：

```csharp
public enum ProcessStep
{
    OrderInfo,
    ProductSelection,
    ProcessConfig,
    Confirmation
}

<div class="process-wizard">
    <Steps>
        <Step Title="订单信息" Status="@GetStepStatus(ProcessStep.OrderInfo)" />
        <Step Title="产品选择" Status="@GetStepStatus(ProcessStep.ProductSelection)" />
        <Step Title="工艺配置" Status="@GetStepStatus(ProcessStep.ProcessConfig)" />
        <Step Title="确认提交" Status="@GetStepStatus(ProcessStep.Confirmation)" />
    </Steps>

    <div class="step-content">
        @switch (currentStep)
        {
            case ProcessStep.OrderInfo:
                <OrderInfoStep @bind-Data="@processData.OrderInfo"
                              OnNext="@(() => NextStep())" />
                break;
            case ProcessStep.ProductSelection:
                <ProductSelectionStep @bind-Data="@processData.Products"
                                    OnNext="@(() => NextStep())"
                                    OnPrevious="@(() => PreviousStep())" />
                break;
            case ProcessStep.ProcessConfig:
                <ProcessConfigStep @bind-Data="@processData.ProcessConfig"
                                 OnNext="@(() => NextStep())"
                                 OnPrevious="@(() => PreviousStep())" />
                break;
            case ProcessStep.Confirmation:
                <ConfirmationStep Data="@processData"
                                OnSubmit="@SubmitProcess"
                                OnPrevious="@(() => PreviousStep())" />
                break;
        }
    </div>
</div>

@code {
    private ProcessStep currentStep = ProcessStep.OrderInfo;
    private ProcessData processData = new();

    private StepStatus GetStepStatus(ProcessStep step)
    {
        if (step < currentStep) return StepStatus.Finish;
        if (step == currentStep) return StepStatus.Process;
        return StepStatus.Wait;
    }

    private void NextStep()
    {
        if (currentStep < ProcessStep.Confirmation)
        {
            currentStep++;
        }
    }

    private void PreviousStep()
    {
        if (currentStep > ProcessStep.OrderInfo)
        {
            currentStep--;
        }
    }

    private async Task SubmitProcess()
    {
        var result = await WtmBlazor.Api.CallAPI("/api/Process/Submit",
                                               HttpMethodEnum.POST, processData);
        if (result.StatusCode == HttpStatusCode.OK)
        {
            await WtmBlazor.Toast.Success("提交成功", "业务流程已成功提交");
            // 重定向到结果页面
        }
    }
}
```

### 4. 数据导入导出

实现Excel数据导入导出功能：

```csharp
<div class="import-export-panel">
    <Card>
        <HeaderTemplate>数据导入导出</HeaderTemplate>
        <BodyTemplate>
            <Row>
                <div class="col-md-6">
                    <h5>数据导入</h5>
                    <InputFile OnChange="@HandleFileSelected" accept=".xlsx,.xls" />
                    <Button Color="Color.Primary" OnClick="@ImportData"
                            IsDisabled="@(selectedFile == null)">
                        导入数据
                    </Button>
                </div>
                <div class="col-md-6">
                    <h5>数据导出</h5>
                    <Button Color="Color.Success" OnClick="@ExportData">
                        导出Excel
                    </Button>
                    <Button Color="Color.Info" OnClick="@DownloadTemplate">
                        下载模板
                    </Button>
                </div>
            </Row>
        </BodyTemplate>
    </Card>
</div>

@code {
    private IBrowserFile? selectedFile;

    private void HandleFileSelected(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
    }

    private async Task ImportData()
    {
        if (selectedFile == null) return;

        var content = new MultipartFormDataContent();
        var fileContent = new StreamContent(selectedFile.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024));
        fileContent.Headers.ContentType = new MediaTypeHeaderValue(selectedFile.ContentType);
        content.Add(fileContent, "file", selectedFile.Name);

        var response = await WtmBlazor.Api.CallAPI("/api/Data/Import",
                                                 HttpMethodEnum.POST, content);

        if (response.StatusCode == HttpStatusCode.OK)
        {
            await WtmBlazor.Toast.Success("导入成功", $"成功导入 {response.Data} 条记录");
            // 刷新页面数据
            await RefreshData();
        }
        else
        {
            await WtmBlazor.Toast.Error("导入失败", response.Msg);
        }
    }

    private async Task ExportData()
    {
        var response = await WtmBlazor.Api.CallAPI("/api/Data/Export", HttpMethodEnum.GET);
        if (response.StatusCode == HttpStatusCode.OK)
        {
            // 下载文件
            await JSRuntime.InvokeVoidAsync("downloadFile", response.Data, "导出数据.xlsx");
        }
    }

    private async Task DownloadTemplate()
    {
        await JSRuntime.InvokeVoidAsync("downloadFile", "/templates/import-template.xlsx", "导入模板.xlsx");
    }
}
```

## 样式和主题定制

### 1. 自定义CSS样式

```css
/* TEX.Shared/wwwroot/css/custom.css */

/* 业务特定样式 */
.production-status {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

.production-status.in-progress {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.production-status.completed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.production-status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 数据表格增强 */
.data-table-enhanced {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.data-table-enhanced .table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.data-table-enhanced .table-row:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* 表单增强 */
.form-enhanced .form-group {
    margin-bottom: 1.5rem;
}

.form-enhanced .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-enhanced .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
```

### 2. 响应式设计

```razor
<div class="responsive-layout">
    <Row>
        <div class="col-12 col-md-8 col-lg-9">
            <!-- 主要内容区域 -->
            <div class="main-content">
                @MainContent
            </div>
        </div>
        <div class="col-12 col-md-4 col-lg-3">
            <!-- 侧边栏 -->
            <div class="sidebar d-none d-md-block">
                @SidebarContent
            </div>
            <!-- 移动端底部操作栏 -->
            <div class="mobile-actions d-md-none">
                @MobileActions
            </div>
        </div>
    </Row>
</div>

<style>
    @media (max-width: 768px) {
        .main-content {
            padding: 0.5rem;
        }

        .mobile-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 1rem;
            z-index: 1000;
        }
    }
</style>
```

## 错误处理和用户体验

### 1. 全局错误处理

```csharp
public class GlobalErrorHandler : ComponentBase, IErrorBoundary
{
    [Inject] private ILogger<GlobalErrorHandler> Logger { get; set; }
    [Inject] private WtmBlazorContext WtmBlazor { get; set; }

    public Exception? CurrentException { get; set; }

    public async Task HandleErrorAsync(Exception exception)
    {
        CurrentException = exception;
        Logger.LogError(exception, "Blazor组件发生未处理异常");

        await WtmBlazor.Toast.Error("系统错误", "页面发生错误，请刷新后重试");
        StateHasChanged();
    }

    public void Recover()
    {
        CurrentException = null;
        StateHasChanged();
    }
}
```

### 2. 加载状态管理

```csharp
<div class="loading-container">
    @if (isLoading)
    {
        <div class="loading-overlay">
            <div class="loading-spinner">
                <Spinner Color="Color.Primary" />
                <p>@loadingMessage</p>
            </div>
        </div>
    }

    <div class="content @(isLoading ? "loading" : "")">
        @ChildContent
    </div>
</div>

@code {
    [Parameter] public RenderFragment ChildContent { get; set; }
    [Parameter] public bool IsLoading { get; set; }
    [Parameter] public string LoadingMessage { get; set; } = "加载中...";

    private bool isLoading => IsLoading;
    private string loadingMessage => LoadingMessage;
}

<style>
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .content.loading {
        pointer-events: none;
        opacity: 0.6;
    }
</style>
```

### 3. 表单验证增强

```csharp
<ValidateForm Model="@Model" OnValidSubmit="@OnSubmit" OnInvalidSubmit="@OnInvalidSubmit">
    <div class="form-validation-summary">
        @if (validationErrors.Any())
        {
            <Alert Color="Color.Danger">
                <h6>请修正以下错误：</h6>
                <ul class="mb-0">
                    @foreach (var error in validationErrors)
                    {
                        <li>@error</li>
                    }
                </ul>
            </Alert>
        }
    </div>

    <Row>
        <BootstrapInput @bind-Value="@Model.Name"
                       ValidateRules="@nameValidateRules" />
        <BootstrapInput @bind-Value="@Model.Email"
                       ValidateRules="@emailValidateRules" />
    </Row>

    <div class="form-actions">
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit"
                IsAsync="true" IsDisabled="@isSubmitting">
            @if (isSubmitting)
            {
                <Spinner Size="Size.Small" /> <span>提交中...</span>
            }
            else
            {
                <span>提交</span>
            }
        </Button>
    </div>
</ValidateForm>

@code {
    private List<string> validationErrors = new();
    private bool isSubmitting = false;

    private List<IValidator> nameValidateRules = new List<IValidator>
    {
        new RequiredValidator() { ErrorMessage = "姓名不能为空" },
        new StringLengthValidator(2, 50) { ErrorMessage = "姓名长度必须在2-50个字符之间" }
    };

    private List<IValidator> emailValidateRules = new List<IValidator>
    {
        new RequiredValidator() { ErrorMessage = "邮箱不能为空" },
        new RegexValidator(@"^[^@\s]+@[^@\s]+\.[^@\s]+$") { ErrorMessage = "邮箱格式不正确" }
    };

    private async Task OnSubmit(EditContext context)
    {
        isSubmitting = true;
        validationErrors.Clear();

        try
        {
            var result = await SubmitData();
            if (result.Success)
            {
                await WtmBlazor.Toast.Success("提交成功", "数据已保存");
            }
            else
            {
                validationErrors.AddRange(result.Errors);
            }
        }
        catch (Exception ex)
        {
            validationErrors.Add("提交失败：" + ex.Message);
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private void OnInvalidSubmit(EditContext context)
    {
        validationErrors.Clear();
        foreach (var error in context.GetValidationMessages())
        {
            validationErrors.Add(error);
        }
    }
}
```

## 实际项目应用示例

### 1. 纺织订单管理页面

基于TEX项目的实际业务场景：

```razor
@page "/Orders/Management"
@using TEX.ViewModel.Models.PurchaseOrderVMs
@inherits BasePage

<div class="order-management">
    <Card>
        <HeaderTemplate>
            <div class="d-flex justify-content-between align-items-center">
                <h4>订单管理</h4>
                <div>
                    <Button Color="Color.Primary" OnClick="@CreateOrder">
                        <i class="fa fa-plus"></i> 新建订单
                    </Button>
                    <Button Color="Color.Success" OnClick="@ExportOrders">
                        <i class="fa fa-download"></i> 导出
                    </Button>
                </div>
            </div>
        </HeaderTemplate>
        <BodyTemplate>
            <!-- 搜索面板 -->
            <WTSearchPanel OnSearch="@DoSearch">
                <ValidateForm Model="@SearchModel">
                    <Row ItemsPerRow="ItemsPerRow.Four" RowType="RowType.Inline">
                        <BootstrapInput @bind-Value="@SearchModel.OrderNo" />
                        <Select @bind-Value="@SearchModel.CustomerId" Items="@AllCustomers" />
                        <WTDateRange @bind-Value="@SearchModel.CreateDate" />
                        <Select @bind-Value="@SearchModel.CompletedStatus" />
                    </Row>
                </ValidateForm>
            </WTSearchPanel>

            <!-- 数据表格 -->
            <Table @ref="dataTable" TItem="PurchaseOrder_View"
                   OnQueryAsync="OnSearch" IsPagination="true"
                   IsStriped="true" IsBordered="true"
                   ShowToolbar="true" ShowExtendButtons="true">
                <TableColumns>
                    <TableColumn @bind-Field="@context.OrderNo" />
                    <TableColumn @bind-Field="@context.Customer_view" />
                    <TableColumn @bind-Field="@context.CreateDate" FormatString="yyyy-MM-dd" />
                    <TableColumn @bind-Field="@context.DeliveryDate" FormatString="yyyy-MM-dd" />
                    <TableColumn @bind-Field="@context.TotalAmount" FormatString="C" />
                    <TableColumn @bind-Field="@context.CompletedStatus">
                        <Template Context="order">
                            <span class="production-status @GetStatusClass(order.CompletedStatus)">
                                @order.CompletedStatus.GetDisplayName()
                            </span>
                        </Template>
                    </TableColumn>
                    <TableColumn>
                        <Template Context="order">
                            <div class="btn-group">
                                <Button Size="Size.Small" Color="Color.Info"
                                        OnClick="@(() => ViewOrder(order.ID))">
                                    查看
                                </Button>
                                <Button Size="Size.Small" Color="Color.Warning"
                                        OnClick="@(() => EditOrder(order.ID))">
                                    编辑
                                </Button>
                                <Button Size="Size.Small" Color="Color.Success"
                                        OnClick="@(() => CreateProduction(order.ID))">
                                    生产计划
                                </Button>
                            </div>
                        </Template>
                    </TableColumn>
                </TableColumns>
            </Table>
        </BodyTemplate>
    </Card>
</div>

@code {
    private PurchaseOrderSearcher SearchModel = new();
    private Table<PurchaseOrder_View> dataTable;
    private List<SelectedItem> AllCustomers = new();

    protected override async Task OnInitializedAsync()
    {
        AllCustomers = await WtmBlazor.Api.CallItemsApi("/api/PurchaseOrder/GetCustomers");
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<PurchaseOrder_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<PurchaseOrder_View>("/api/PurchaseOrder/Search", SearchModel, opts);
    }

    private void DoSearch() => dataTable.QueryAsync();

    private string GetStatusClass(CompletedStatusEnum status)
    {
        return status switch
        {
            CompletedStatusEnum.InProcess => "in-progress",
            CompletedStatusEnum.Completed => "completed",
            CompletedStatusEnum.Cancelled => "cancelled",
            _ => ""
        };
    }

    private async Task CreateOrder()
    {
        await WtmBlazor.OpenDialog<Create>("新建订单", x => x.id = "");
        await dataTable.QueryAsync();
    }

    private async Task EditOrder(Guid id)
    {
        await WtmBlazor.OpenDialog<Edit>("编辑订单", x => x.id = id.ToString());
        await dataTable.QueryAsync();
    }

    private async Task ViewOrder(Guid id)
    {
        await WtmBlazor.OpenDialog<Details>("订单详情", x => x.id = id.ToString());
    }

    private async Task CreateProduction(Guid id)
    {
        // 跳转到生产计划页面
        await Redirect($"/Production/Plan?orderId={id}");
    }

    private async Task ExportOrders()
    {
        var response = await WtmBlazor.Api.CallAPI("/api/PurchaseOrder/Export", HttpMethodEnum.POST, SearchModel);
        if (response.StatusCode == HttpStatusCode.OK)
        {
            await JSRuntime.InvokeVoidAsync("downloadFile", response.Data, "订单数据.xlsx");
        }
    }
}
```

## 总结

WTM框架的Blazor实现提供了强大的基础功能，通过合理的扩展开发可以满足复杂的业务需求。关键是理解框架的设计模式，在保持框架优势的同时，灵活运用自定义开发技术。

### 开发要点总结

1. **充分利用WTM自动化** - 基础CRUD功能使用框架生成
2. **合理扩展复杂功能** - 通过自定义API和组件实现复杂业务
3. **注重用户体验** - 实现加载状态、错误处理、响应式设计
4. **性能优化** - 避免不必要的渲染，使用虚拟化处理大数据
5. **代码复用** - 创建可复用的业务组件和工具类
6. **测试和调试** - 建立完善的调试和测试机制

通过遵循这些指导原则，可以在WTM框架基础上构建出功能强大、用户体验良好的企业级应用系统。
