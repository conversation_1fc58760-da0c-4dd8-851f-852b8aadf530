## **TEX产品出入库和库存查询统计方案规划**

### **一、现状分析**

**现有功能：**

- ✅ 基础列表查询：ProductInboundBillListVM、ProductOutboundBillListVM、ProductStockListVM
- ✅ 基础搜索功能：按日期、单号、客户、仓库等条件筛选
- ✅ 订单仪表板：OrderDashboard（月度下单统计、图表展示）
- ✅ 三级联动CRUD：完整的Bill-Lot-Roll增删改查功能

**缺失功能：**

- ❌ 出入库统计分析页面
- ❌ 多维度数据透视分析
- ❌ 业务趋势分析报表
- ❌ 统计数据钻取查询功能
- ❌ 按订单维度的库存状态监控

### **二、查询统计方案设计**

#### **方案A：统计优先查询方案**

**适用场景：** 快速实现，满足日常统计查询需求 **实现复杂度：** ⭐⭐ **开发周期：** 1-2周

**核心功能：**

1. **统计数据优先展示**
    - 基于现有搜索条件直接显示汇总统计（总件数、总重量、总米数、总码数）
    - 按时间段、客户、产品、订单等维度分组统计
    - 统计结果支持图表可视化（柱状图、饼图）
2. **钻取查询功能**
    - 点击统计数据可钻取到对应的明细列表
    - 支持多级钻取（汇总→分组→明细）
    - 钻取时保持原有查询条件
3. **按订单维度库存查询**
    - 按订单明细查看库存分布
    - 订单完成度统计
    - 订单库存状态跟踪

#### **方案B：多维度统计分析方案**

**适用场景：** 专业数据分析，支持决策 **实现复杂度：** ⭐⭐⭐⭐ **开发周期：** 3-4周

**核心功能：**

1. **多维度数据透视**
    - 按客户、产品、时间、仓库、订单等维度交叉分析
    - 支持多级钻取查询（汇总→分组→明细→单据）
    - 动态图表展示（柱状图、饼图、趋势图、热力图）
2. **订单导向的业务分析**
    - 订单执行进度分析
    - 客户订单完成情况统计
    - 产品交付周期分析
    - 订单库存匹配度分析
3. **高级统计查询功能**
    - 自定义统计维度组合
    - 保存常用统计模板
    - 统计结果导出和分享
    - 定时统计报表生成

#### **方案C：订单驱动的智能统计平台**

**适用场景：** 企业级数据平台，长期规划 **实现复杂度：** ⭐⭐⭐⭐⭐ **开发周期：** 6-8周

**核心功能：**

1. **订单执行监控大屏**
    - 实时订单执行状态
    - 出入库实时流水
    - 订单交付进度监控
    - 关键业务KPI指标
2. **订单导向的预测分析**
    - 订单交付时间预测
    - 客户需求趋势分析
    - 生产计划优化建议
    - 季节性订单模式分析
3. **移动端统计查询**
    - 移动端统计查询应用
    - 订单状态推送通知
    - 离线统计数据同步
    - 现场数据采集支持

### **三、推荐实施方案**

考虑到TEX项目的实际情况和现有技术架构，我推荐采用**渐进式实施策略**：

**第一阶段：统计优先查询实现（优先级：高）**

- 新建展示统计数据页面
- 实现统计数据的钻取查询功能
- 添加按订单维度的库存查询
- 实现基础统计数据导出功能

**第二阶段：多维度统计分析（优先级：中）**

- 开发专门的多维度统计分析页面
- 实现订单导向的数据透视功能
- 添加动态图表可视化
- 构建统计模板保存和复用机制

**第三阶段：订单驱动智能平台（优先级：低）**

- 构建订单执行监控系统
- 集成订单导向的预测分析功能
- 开发移动端统计查询应用

### **四、具体页面设计建议**

#### **4.1 出入库统计查询页面**

**页面路径：** `/Finished/InOutboundStatistics`

**核心设计理念：** 统计数据优先，支持钻取查询

**功能模块：**
- **统计数据展示区**：基于搜索条件显示汇总统计（件数、重量、米数、码数）
- **多维度分组统计**：按时间、客户、产品、订单等维度分组展示
- **图表可视化**：柱状图、饼图展示统计结果
- **钻取查询功能**：点击统计数据钻取到明细列表
- **搜索条件保持**：钻取时保持原有查询条件

#### **4.2 订单库存统计页面**

**页面路径：** `/Finished/OrderStockStatistics`

**核心设计理念：** 按订单维度统计库存，支持订单执行跟踪

**功能模块：**
- **订单库存总览**：按订单明细统计当前库存
- **订单完成度统计**：订单执行进度可视化
- **库存分布分析**：按仓库、产品维度分析库存分布
- **订单状态跟踪**：订单从下单到交付的全流程跟踪

#### **4.3 多维度统计分析页面**

**页面路径：** `/Finished/MultiDimensionAnalysis`

**核心设计理念：** 支持自定义维度组合的高级统计分析

**功能模块：**
- **维度选择器**：支持客户、产品、时间、仓库、订单等维度组合
- **交叉分析表格**：多维度数据透视表
- **动态图表生成**：根据选择维度自动生成对应图表
- **统计模板管理**：保存和复用常用统计配置
- **高级钻取功能**：支持多级钻取（汇总→分组→明细→单据）

### **五、技术实现要点**

#### **5.1 统计数据优先的查询架构**
```csharp
// 统计查询优先，明细查询按需
public class StatisticsFirstQueryVM : BasePagedListVM<T, TSearcher>
{
    // 优先返回统计数据
    public StatisticsResult GetStatistics();

    // 支持钻取的明细查询
    public IEnumerable<T> GetDetailsByDrillDown(DrillDownParams params);
}
```

#### **5.2 钻取查询的实现模式**
- **保持查询上下文**：钻取时保留原始搜索条件
- **分层数据结构**：汇总→分组→明细→单据的层级关系
- **动态查询构建**：根据钻取层级动态构建查询条件

#### **5.3 订单导向的数据模型**
- **以OrderDetail为核心**：所有统计都基于订单明细维度
- **库存与订单关联**：ProductStock通过OrderDetailId关联订单
- **订单执行状态跟踪**：从下单→入库→库存→出库的全流程

**改进后的方案更加符合纺织外贸行业的业务特点：**
1. ✅ **统计数据优先**：用户首先看到汇总统计，而不是冗长的列表
2. ✅ **钻取查询支持**：从统计数据可以深入到具体明细
3. ✅ **订单导向设计**：完全按照订单生产的业务模式设计
4. ✅ **移除库存预警**：不需要传统制造业的库存预警功能

---

## **六、第一阶段开发计划（TODO）**

### **6.1 开发目标**
实现"统计优先查询方案"，改造现有的出入库和库存查询页面，优先展示统计数据，支持钻取查询功能。

### **6.2 技术架构设计**

#### **6.2.1 统计数据模型设计**
```csharp
// 统计结果基础模型
public class StatisticsResult
{
    public int TotalPcs { get; set; }           // 总件数
    public decimal TotalWeight { get; set; }    // 总重量
    public decimal TotalMeters { get; set; }    // 总米数
    public decimal TotalYards { get; set; }     // 总码数
    public int TotalBills { get; set; }         // 单据数量
    public int TotalLots { get; set; }          // 批次数量
}

// 分组统计结果模型
public class GroupedStatisticsResult : StatisticsResult
{
    public string GroupKey { get; set; }        // 分组键
    public string GroupName { get; set; }       // 分组显示名称
    public DateTime? GroupDate { get; set; }    // 分组日期（时间维度）
}
```

#### **6.2.2 钻取查询参数模型**
```csharp
public class DrillDownParams
{
    public string GroupKey { get; set; }        // 分组键
    public string GroupType { get; set; }       // 分组类型（Customer/Product/Date等）
    public Dictionary<string, object> OriginalSearchParams { get; set; } // 原始搜索条件
}
```

### **6.3 开发任务清单**

#### **阶段1.1：后端API开发**
- [ ] **1.1.1 创建统计查询ViewModel**
  - [ ] `ProductInboundBillStatisticsVM.cs` - 入库统计查询
  - [ ] `ProductOutboundBillStatisticsVM.cs` - 出库统计查询
  - [ ] `ProductStockStatisticsVM.cs` - 库存统计查询

- [ ] **1.1.2 创建统计查询Controller方法**
  - [ ] `ProductInboundBillController.GetStatistics()` - 入库统计API
  - [ ] `ProductInboundBillController.GetGroupedStatistics()` - 入库分组统计API
  - [ ] `ProductOutboundBillController.GetStatistics()` - 出库统计API
  - [ ] `ProductOutboundBillController.GetGroupedStatistics()` - 出库分组统计API
  - [ ] `ProductStockController.GetStatistics()` - 库存统计API
  - [ ] `ProductStockController.GetGroupedStatistics()` - 库存分组统计API

- [ ] **1.1.3 实现钻取查询API**
  - [ ] `ProductInboundBillController.GetDetailsByDrillDown()` - 入库钻取查询
  - [ ] `ProductOutboundBillController.GetDetailsByDrillDown()` - 出库钻取查询
  - [ ] `ProductStockController.GetDetailsByDrillDown()` - 库存钻取查询

#### **阶段1.2：前端页面开发**
- [ ] **1.2.1 创建统计展示组件**
  - [ ] `StatisticsCard.razor` - 统计卡片组件
  - [ ] `GroupedStatisticsTable.razor` - 分组统计表格组件
  - [ ] `DrillDownModal.razor` - 钻取查询弹窗组件

- [ ] **1.2.2 改造现有查询页面**
  - [ ] 改造 `ProductInboundBill/Index.razor` - 入库统计优先页面
  - [ ] 改造 `ProductOutboundBill/Index.razor` - 出库统计优先页面
  - [ ] 改造 `ProductStock/Index.razor` - 库存统计优先页面

- [ ] **1.2.3 实现钻取查询功能**
  - [ ] 统计卡片点击钻取功能
  - [ ] 分组统计行点击钻取功能
  - [ ] 钻取结果弹窗展示

#### **阶段1.3：页面布局设计**
- [ ] **1.3.1 统计优先布局**
  ```
  [搜索面板]
  [统计卡片区域] - 4个统计卡片（件数、重量、米数、码数）
  [分组统计表格] - 按选择维度分组展示
  [图表展示区域] - 可选的图表可视化
  [钻取弹窗] - 点击统计数据时显示明细
  ```

- [ ] **1.3.2 响应式设计**
  - [ ] 移动端适配
  - [ ] 平板端适配
  - [ ] 桌面端优化

### **6.4 开发优先级**

#### **高优先级（第1周）**
1. 创建统计数据模型和ViewModel
2. 实现入库统计API（ProductInboundBillController）
3. 创建StatisticsCard组件
4. 改造ProductInboundBill/Index.razor页面

#### **中优先级（第2周）**
1. 实现出库和库存统计API
2. 创建GroupedStatisticsTable组件
3. 改造ProductOutboundBill和ProductStock页面
4. 实现基础钻取查询功能

#### **低优先级（第3周）**
1. 完善钻取查询弹窗
2. 添加图表可视化
3. 优化响应式设计
4. 性能优化和测试

### **6.5 技术实现要点**

#### **6.5.1 BootstrapBlazor组件使用**
- 使用`Card`组件展示统计数据
- 使用`Table`组件展示分组统计
- 使用`Modal`组件实现钻取弹窗
- 使用`Chart`组件（可选）展示图表

#### **6.5.2 数据查询优化**
- 统计查询使用聚合函数，避免大量数据传输
- 钻取查询支持分页，避免一次性加载过多数据
- 合理使用缓存机制

#### **6.5.3 用户体验设计**
- 统计数据加载时显示骨架屏
- 钻取查询支持Loading状态
- 保持原有搜索条件在钻取时的连续性

这个调整后的方案更加贴合您的实际业务需求和用户使用习惯。