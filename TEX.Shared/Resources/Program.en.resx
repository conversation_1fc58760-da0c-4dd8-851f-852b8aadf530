<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Codegen.ApiOnly" xml:space="preserve">
    <value>Generate api only, no frontend code</value>
  </data>
  <data name="Codegen.Attachment" xml:space="preserve">
    <value>Attachment</value>
  </data>
  <data name="Codegen.AuthMode" xml:space="preserve">
    <value>AuthMode</value>
  </data>
  <data name="Codegen.Confirm" xml:space="preserve">
    <value>This will generate following files, are you sure?</value>
  </data>
  <data name="Codegen.ControllerNs" xml:space="preserve">
    <value>ControllerNs</value>
  </data>
  <data name="Codegen.DataNs" xml:space="preserve">
    <value>DataNs</value>
  </data>
  <data name="Codegen.EnglishOnly" xml:space="preserve">
    <value>The {0} field must start with characters or underscores</value>
  </data>
  <data name="Codegen.FieldDes" xml:space="preserve">
    <value>Field Des</value>
  </data>
  <data name="Codegen.FileName" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="Codegen.Gen" xml:space="preserve">
    <value>Generate Code</value>
  </data>
  <data name="Codegen.GenApi" xml:space="preserve">
    <value>Generate Api</value>
  </data>
  <data name="Codegen.InputModuleName" xml:space="preserve">
    <value>Please input module name, such as xxx management</value>
  </data>
  <data name="Codegen.IsBatchField" xml:space="preserve">
    <value>Batch Field</value>
  </data>
  <data name="Codegen.IsFormField" xml:space="preserve">
    <value>Form Field</value>
  </data>
  <data name="Codegen.IsImportField" xml:space="preserve">
    <value>Import Field</value>
  </data>
  <data name="Codegen.IsListField" xml:space="preserve">
    <value>List Field</value>
  </data>
  <data name="Codegen.IsSearcherField" xml:space="preserve">
    <value>Search Field</value>
  </data>
  <data name="Codegen.LinkedType" xml:space="preserve">
    <value>Link Type</value>
  </data>
  <data name="Codegen.ManyToMany" xml:space="preserve">
    <value>ManyToMany</value>
  </data>
  <data name="Codegen.ModelNS" xml:space="preserve">
    <value>ModelNS</value>
  </data>
  <data name="Codegen.ModuleName" xml:space="preserve">
    <value>Module Name</value>
  </data>
  <data name="Codegen.OneToMany" xml:space="preserve">
    <value>OneToMany</value>
  </data>
  <data name="Codegen.SelectedModelMustBeBasePoco" xml:space="preserve">
    <value>The model selected must inherit from TopBasePoco</value>
  </data>
  <data name="Codegen.SelectModule" xml:space="preserve">
    <value>Please select a module</value>
  </data>
  <data name="Codegen.Setup" xml:space="preserve">
    <value>Please setup fields</value>
  </data>
  <data name="Codegen.Start" xml:space="preserve">
    <value>Start Generation</value>
  </data>
  <data name="Codegen.SubField" xml:space="preserve">
    <value>Display field of linked poco</value>
  </data>
  <data name="Codegen.Success" xml:space="preserve">
    <value>Code has been generated, please close the debug and rebuild the solution.</value>
  </data>
  <data name="Codegen.TestNs" xml:space="preserve">
    <value>TestNs</value>
  </data>
  <data name="Codegen.VMNs" xml:space="preserve">
    <value>VMNs</value>
  </data>
  <data name="Login.ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="Login.ChangePasswordSuccess" xml:space="preserve">
    <value>The password has been modified successfully. Please log in with the new password next time.</value>
  </data>
  <data name="Login.InputPassword" xml:space="preserve">
    <value>Please input password</value>
  </data>
  <data name="Login.InputTenant" xml:space="preserve">
    <value>Please enter the tenant number</value>
  </data>
  <data name="Login.InputUserName" xml:space="preserve">
    <value>Please input user name</value>
  </data>
  <data name="Login.InputValidation" xml:space="preserve">
    <value>Validation Code</value>
  </data>
  <data name="Login.ItcodeDuplicate" xml:space="preserve">
    <value>Account is duplicated</value>
  </data>
  <data name="Login.Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Login.LogOut" xml:space="preserve">
    <value>LogOut</value>
  </data>
  <data name="Login.NewPassword" xml:space="preserve">
    <value>New Passwrod</value>
  </data>
  <data name="Login.NewPasswordComfirm" xml:space="preserve">
    <value>Passwrod Again</value>
  </data>
  <data name="Login.OldPassword" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="Login.OldPasswrodWrong" xml:space="preserve">
    <value>Current password is wrong</value>
  </data>
  <data name="Login.PasswordNotSame" xml:space="preserve">
    <value>Inconsistent Password</value>
  </data>
  <data name="Login.Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Login.RegTitle" xml:space="preserve">
    <value>New user registration</value>
  </data>
  <data name="Login.RememberMe" xml:space="preserve">
    <value>Remember Me</value>
  </data>
  <data name="Login.ValidationFail" xml:space="preserve">
    <value>Incorrect verification code</value>
  </data>
  <data name="MenuKey.ActionLog" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="MenuKey.Api" xml:space="preserve">
    <value>Buildin Api</value>
  </data>
  <data name="MenuKey.DataPrivilege" xml:space="preserve">
    <value>Data Privilege</value>
  </data>
  <data name="MenuKey.FrameworkTenant" xml:space="preserve">
    <value>Tenant Management</value>
  </data>
  <data name="MenuKey.GroupManagement" xml:space="preserve">
    <value>Group Management</value>
  </data>
  <data name="MenuKey.Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="MenuKey.MenuMangement" xml:space="preserve">
    <value>Menu Mangement</value>
  </data>
  <data name="MenuKey.RoleManagement" xml:space="preserve">
    <value>Role Management</value>
  </data>
  <data name="MenuKey.SystemManagement" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="MenuKey.UserManagement" xml:space="preserve">
    <value>User Management</value>
  </data>
  <data name="Reg.Success" xml:space="preserve">
    <value>Registered Successfully</value>
  </data>
  <data name="Register.EditEmail" xml:space="preserve">
    <value>Wrong format of email</value>
  </data>
  <data name="Register.EditMobile" xml:space="preserve">
    <value>Wrong format of mobile phone number</value>
  </data>
  <data name="Register.Validate" xml:space="preserve">
    <value>Please enter the correct mobile phone number or email</value>
  </data>
  <data name="Register.ValidateApi" xml:space="preserve">
    <value>Error in verification code API</value>
  </data>
  <data name="Register.ValidateCode" xml:space="preserve">
    <value>The verification code is wrong</value>
  </data>
  <data name="Register.ValidateEmail" xml:space="preserve">
    <value>Email API sending error</value>
  </data>
  <data name="Register.ValidateEmailContent" xml:space="preserve">
    <value>The verification code of your operation is:</value>
  </data>
  <data name="Register.ValidateEmailSub" xml:space="preserve">
    <value>Registration verification code</value>
  </data>
  <data name="Sys.Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="Sys.Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="Sys.All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Sys.ApiDoc" xml:space="preserve">
    <value>Api Doc</value>
  </data>
  <data name="Sys.AttemptedValueIsInvalidAccessor" xml:space="preserve">
    <value>The value '{0}' is not valid for {1}.</value>
  </data>
  <data name="Sys.BackHome" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Sys.BatchDelete" xml:space="preserve">
    <value>Batch Delete</value>
  </data>
  <data name="Sys.BatchDeleteConfirm" xml:space="preserve">
    <value>Are you sure to delete following data:</value>
  </data>
  <data name="Sys.BatchDeleteSuccess" xml:space="preserve">
    <value>{0} rows have been deleted</value>
  </data>
  <data name="Sys.BatchEdit" xml:space="preserve">
    <value>Batch Edit</value>
  </data>
  <data name="Sys.BatchEditConfirm" xml:space="preserve">
    <value>Batch update following data</value>
  </data>
  <data name="Sys.BatchEditSuccess" xml:space="preserve">
    <value>{0} rows have been edited</value>
  </data>
  <data name="Sys.CannotDelete" xml:space="preserve">
    <value>{0} is used, it cannot be deleted</value>
  </data>
  <data name="Sys.CannotFindUser" xml:space="preserve">
    <value>Can not find user {0}</value>
  </data>
  <data name="Sys.CellIndex" xml:space="preserve">
    <value>CellIndex</value>
  </data>
  <data name="Sys.CheckExport" xml:space="preserve">
    <value>Check Export</value>
  </data>
  <data name="Sys.Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Sys.CloseAllTags" xml:space="preserve">
    <value>Close all tags</value>
  </data>
  <data name="Sys.CloseOtherTags" xml:space="preserve">
    <value>Close other tags</value>
  </data>
  <data name="Sys.CloseThisTag" xml:space="preserve">
    <value>Close this tag</value>
  </data>
  <data name="Sys.CodeGen" xml:space="preserve">
    <value>CodeGen</value>
  </data>
  <data name="Sys.ColumnFilter" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="Sys.ComboBox" xml:space="preserve">
    <value>ComboBox</value>
  </data>
  <data name="Sys.Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="Sys.Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Sys.DataCannotDelete" xml:space="preserve">
    <value>Data is used, it cannot be deleted</value>
  </data>
  <data name="Sys.DataNotExist" xml:space="preserve">
    <value>Data does not exist</value>
  </data>
  <data name="Sys.DataRange" xml:space="preserve">
    <value>between {0} and {1}</value>
  </data>
  <data name="Sys.DebugOnly" xml:space="preserve">
    <value>The address can only be accessed in debug mode</value>
  </data>
  <data name="Sys.DefaultArea" xml:space="preserve">
    <value>Default Area</value>
  </data>
  <data name="Sys.Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Sys.DeleteConfirm" xml:space="preserve">
    <value>Are you sure to delete this data:</value>
  </data>
  <data name="Sys.DeleteFailed" xml:space="preserve">
    <value>Data is in use, delete failed.</value>
  </data>
  <data name="Sys.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Sys.Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="Sys.DownloadTemplate" xml:space="preserve">
    <value>Download Template</value>
  </data>
  <data name="Sys.DuplicateError" xml:space="preserve">
    <value>The {0} field has duplicated data</value>
  </data>
  <data name="Sys.DuplicateGroupError" xml:space="preserve">
    <value>The {0} fields has duplicated data</value>
  </data>
  <data name="Sys.Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Sys.EditFailed" xml:space="preserve">
    <value>Edit Failed</value>
  </data>
  <data name="Sys.Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Sys.Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Sys.ErrorHandle" xml:space="preserve">
    <value>Error Handling</value>
  </data>
  <data name="Sys.ErrorMsg" xml:space="preserve">
    <value>Error Message</value>
  </data>
  <data name="Sys.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Sys.ExportByIds" xml:space="preserve">
    <value>CheckExport</value>
  </data>
  <data name="Sys.ExportExcel" xml:space="preserve">
    <value>Export Excel</value>
  </data>
  <data name="Sys.ExtraInfo" xml:space="preserve">
    <value>Extra</value>
  </data>
  <data name="Sys.FailedLoadData" xml:space="preserve">
    <value>Failed to load data</value>
  </data>
  <data name="Sys.Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="Sys.FileData" xml:space="preserve">
    <value>File Data</value>
  </data>
  <data name="Sys.FileNotFound" xml:space="preserve">
    <value>File is not found</value>
  </data>
  <data name="Sys.ForSelect" xml:space="preserve">
    <value>For Selection</value>
  </data>
  <data name="Sys.FrameworkUserBase" xml:space="preserve">
    <value>UserBase</value>
  </data>
  <data name="Sys.FrameworkUserGroup" xml:space="preserve">
    <value>User and group relation</value>
  </data>
  <data name="Sys.FrameworkUserRole" xml:space="preserve">
    <value>User and role relation</value>
  </data>
  <data name="Sys.Get" xml:space="preserve">
    <value>Get</value>
  </data>
  <data name="Sys.Goto" xml:space="preserve">
    <value>Goto</value>
  </data>
  <data name="Sys.GotoButtonText" xml:space="preserve">
    <value>GO</value>
  </data>
  <data name="Sys.HandleInfo" xml:space="preserve">
    <value>Handle Info</value>
  </data>
  <data name="Sys.Have" xml:space="preserve">
    <value>Have</value>
  </data>
  <data name="Sys.HideShow" xml:space="preserve">
    <value>Hide/Show</value>
  </data>
  <data name="Sys.Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Sys.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="Sys.ImportError" xml:space="preserve">
    <value>Error occurs during import</value>
  </data>
  <data name="Sys.ImportStep1" xml:space="preserve">
    <value>Click to download template</value>
  </data>
  <data name="Sys.ImportStep2" xml:space="preserve">
    <value>Upload your template with data</value>
  </data>
  <data name="Sys.ImportStep3" xml:space="preserve">
    <value>Import failed, please download error file for details</value>
  </data>
  <data name="Sys.ImportSuccess" xml:space="preserve">
    <value>{0} rows have been imported</value>
  </data>
  <data name="Sys.Info" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="Sys.Invalid" xml:space="preserve">
    <value>Invalid</value>
  </data>
  <data name="Sys.Layout" xml:space="preserve">
    <value>Layout</value>
  </data>
  <data name="Sys.LayuiDateLan" xml:space="preserve">
    <value>EN</value>
  </data>
  <data name="Sys.LeftRight" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="Sys.LoadFailed" xml:space="preserve">
    <value>Failed to load</value>
  </data>
  <data name="Sys.LoginFailed" xml:space="preserve">
    <value>Login Failed</value>
  </data>
  <data name="Sys.MainPage" xml:space="preserve">
    <value>MainPage</value>
  </data>
  <data name="Sys.Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="Sys.Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="Sys.MoreSettings" xml:space="preserve">
    <value>More</value>
  </data>
  <data name="Sys.NeedLogin" xml:space="preserve">
    <value>You havn't logged in or your login is expired, please login again.</value>
  </data>
  <data name="Sys.No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Sys.NoData" xml:space="preserve">
    <value>No Data</value>
  </data>
  <data name="Sys.NoMatchingData" xml:space="preserve">
    <value>No matching data</value>
  </data>
  <data name="Sys.None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Sys.NoPrivilege" xml:space="preserve">
    <value>You are not allowed to access this page</value>
  </data>
  <data name="Sys.NotHave" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Sys.OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Sys.Operation" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="Sys.OprationSuccess" xml:space="preserve">
    <value>Opration Success</value>
  </data>
  <data name="Sys.Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="Sys.PageError" xml:space="preserve">
    <value>Error Occurred</value>
  </data>
  <data name="Sys.PleaseInputDecimal" xml:space="preserve">
    <value>Please input decimal</value>
  </data>
  <data name="Sys.PleaseInputDecimalFormat" xml:space="preserve">
    <value>Please input with decimal format</value>
  </data>
  <data name="Sys.PleaseInputExistData" xml:space="preserve">
    <value>Please use data existed in the combobox</value>
  </data>
  <data name="Sys.PleaseInputNumber" xml:space="preserve">
    <value>Please input number</value>
  </data>
  <data name="Sys.PleaseInputNumberFormat" xml:space="preserve">
    <value>Please input with number format</value>
  </data>
  <data name="Sys.PleaseInputText" xml:space="preserve">
    <value>Please input text</value>
  </data>
  <data name="Sys.PleaseSelect" xml:space="preserve">
    <value>Please Select</value>
  </data>
  <data name="Sys.PleaseUploadTemplate" xml:space="preserve">
    <value>Please upload template</value>
  </data>
  <data name="Sys.Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="Sys.Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Sys.Record" xml:space="preserve">
    <value>Record(s)</value>
  </data>
  <data name="Sys.RecordsPerPage" xml:space="preserve">
    <value>RecordsPerPage</value>
  </data>
  <data name="Sys.Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Sys.RequestError" xml:space="preserve">
    <value>Request parameter error</value>
  </data>
  <data name="Sys.Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Sys.Rollback" xml:space="preserve">
    <value>Rollback</value>
  </data>
  <data name="Sys.RowIndex" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="Sys.SaveMode" xml:space="preserve">
    <value>Save Mode</value>
  </data>
  <data name="Sys.Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Sys.SearchCondition" xml:space="preserve">
    <value>Conditions</value>
  </data>
  <data name="Sys.Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Sys.Selected" xml:space="preserve">
    <value>Selected</value>
  </data>
  <data name="Sys.SelectOneRow" xml:space="preserve">
    <value>Please select a row</value>
  </data>
  <data name="Sys.SelectOneRowMax" xml:space="preserve">
    <value>Please select only one row</value>
  </data>
  <data name="Sys.SelectOneRowMin" xml:space="preserve">
    <value>Please select at least one row</value>
  </data>
  <data name="Sys.SinglePage" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="Sys.Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Sys.SubmitFailed" xml:space="preserve">
    <value>Failed to submit</value>
  </data>
  <data name="Sys.Tabs" xml:space="preserve">
    <value>Tabs</value>
  </data>
  <data name="Sys.Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="Sys.Total" xml:space="preserve">
    <value>Total:</value>
  </data>
  <data name="Sys.UpdateDone" xml:space="preserve">
    <value>Update Successfully</value>
  </data>
  <data name="Sys.UpDown" xml:space="preserve">
    <value>Vertical</value>
  </data>
  <data name="Sys.UploadFailed" xml:space="preserve">
    <value>Upload Failed</value>
  </data>
  <data name="Sys.UploadTemplate" xml:space="preserve">
    <value>Template</value>
  </data>
  <data name="Sys.UseCannotAbstract" xml:space="preserve">
    <value>{0} is in use and cannot be set to abstract</value>
  </data>
  <data name="Sys.UseCannotDelete" xml:space="preserve">
    <value>{0}The current model is used and cannot be deleted</value>
  </data>
  <data name="Sys.UseCannotDeleteArea" xml:space="preserve">
    <value>{0}The current area model is used and cannot be deleted</value>
  </data>
  <data name="Sys.UseCannotDeleteCommon" xml:space="preserve">
    <value>{0} It is in use and cannot be deleted</value>
  </data>
  <data name="Sys.Valid" xml:space="preserve">
    <value>Valid</value>
  </data>
  <data name="Sys.ValueIsInvalidAccessor" xml:space="preserve">
    <value>The value '{0}' is invalid.</value>
  </data>
  <data name="Sys.WrongTemplate" xml:space="preserve">
    <value>Wrong Template</value>
  </data>
  <data name="Sys.WrongTextLength" xml:space="preserve">
    <value>The text is too long</value>
  </data>
  <data name="Sys.WtmDoc" xml:space="preserve">
    <value>WTM Doc</value>
  </data>
  <data name="Sys.Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Sys.{0}formaterror" xml:space="preserve">
    <value>The {0} field has an error format</value>
  </data>
  <data name="Sys.{0}ValueNotExist" xml:space="preserve">
    <value>The {0} field's value does not exist</value>
  </data>
  <data name="Sys.{0}ValueTypeNotAllowed" xml:space="preserve">
    <value>The {0} field's type is not supported</value>
  </data>
  <data name="System.Children" xml:space="preserve">
    <value>Children</value>
  </data>
  <data name="Validate.FieldNameBaseError{0}" xml:space="preserve">
    <value>Field name cannot be {0}</value>
  </data>
  <data name="Validate.FieldNameError" xml:space="preserve">
    <value>Field name and model name cannot be the same</value>
  </data>
  <data name="Validate.GeneratePagesModel" xml:space="preserve">
    <value>Abstract models cannot generate pages</value>
  </data>
  <data name="Validate.ManyLinkField{0}" xml:space="preserve">
    <value>{0} Duplicate the first mock exam in multiple to many fields</value>
  </data>
  <data name="Validate.NullEnumId{0}" xml:space="preserve">
    <value>Field {0} need to select a Enum</value>
  </data>
  <data name="Validate.NullLinkId{0}" xml:space="preserve">
    <value>Field {0} need to select a model</value>
  </data>
  <data name="Validate.ParentAbstract" xml:space="preserve">
    <value>Only abstract models can be inherited</value>
  </data>
  <data name="Validate.ParentHasId" xml:space="preserve">
    <value>Inheritance model cannot add ID field</value>
  </data>
  <data name="Validate.ParentModelName" xml:space="preserve">
    <value>You can't inherit yourself</value>
  </data>
  <data name="Validate.ReservedModelName{0}" xml:space="preserve">
    <value>Cannot name with a keyword {0}</value>
  </data>
  <data name="Validate.SameFieldName{0}" xml:space="preserve">
    <value>Duplicated Field {0}</value>
  </data>
  <data name="Validate.SubNs" xml:space="preserve">
    <value>Region name and model name cannot be the same</value>
  </data>
  <data name="Validate.SubNsName" xml:space="preserve">
    <value>Area or model name cannot be equal to project name</value>
  </data>
  <data name="Validate.SubNsNameFirst" xml:space="preserve">
    <value>The area or model name cannot be equal to the first paragraph of the project name</value>
  </data>
  <data name="Validate.{0}formaterror" xml:space="preserve">
    <value>The {0} field has an error format</value>
  </data>
  <data name="Validate.{0}number" xml:space="preserve">
    <value>The {0} field must be digit</value>
  </data>
  <data name="Validate.{0}rangemax{1}" xml:space="preserve">
    <value>{0} must be less than {1}</value>
  </data>
  <data name="Validate.{0}rangemin{1}" xml:space="preserve">
    <value>{0} must be at least {1}</value>
  </data>
  <data name="Validate.{0}range{1}{2}" xml:space="preserve">
    <value>{0} must between {1} and {2}</value>
  </data>
  <data name="Validate.{0}required" xml:space="preserve">
    <value>The {0} field is required</value>
  </data>
  <data name="Validate.{0}stringmax{1}" xml:space="preserve">
    <value>The {0} field is limited {1} characters</value>
  </data>
  <data name="Wuma.CanEdit" xml:space="preserve">
    <value>CanEdit</value>
  </data>
  <data name="Wuma.Card" xml:space="preserve">
    <value>Card</value>
  </data>
  <data name="Wuma.Checkbox" xml:space="preserve">
    <value>Checkbox</value>
  </data>
  <data name="Wuma.ColumnName" xml:space="preserve">
    <value>ColumnName</value>
  </data>
  <data name="Wuma.Combobox" xml:space="preserve">
    <value>Combobox</value>
  </data>
  <data name="Wuma.Container" xml:space="preserve">
    <value>Container</value>
  </data>
  <data name="Wuma.Control" xml:space="preserve">
    <value>Control</value>
  </data>
  <data name="Wuma.ControlProperty" xml:space="preserve">
    <value>ControlProperty</value>
  </data>
  <data name="Wuma.Controls" xml:space="preserve">
    <value>Page Controls</value>
  </data>
  <data name="Wuma.ControlType" xml:space="preserve">
    <value>ControlType</value>
  </data>
  <data name="Wuma.CustomPrimaryName" xml:space="preserve">
    <value>CustomPrimaryName</value>
  </data>
  <data name="Wuma.CustomPrimaryType" xml:space="preserve">
    <value>CustomPrimaryType</value>
  </data>
  <data name="Wuma.CustomRegx" xml:space="preserve">
    <value>CustomRegx</value>
  </data>
  <data name="Wuma.DataPrivilegeName" xml:space="preserve">
    <value>DataPrivilegeName</value>
  </data>
  <data name="Wuma.Datetime" xml:space="preserve">
    <value>Datetime</value>
  </data>
  <data name="Wuma.DicFieldDes" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Wuma.DicFieldName" xml:space="preserve">
    <value>Key</value>
  </data>
  <data name="Wuma.DicName" xml:space="preserve">
    <value>Dictionary</value>
  </data>
  <data name="Wuma.Display" xml:space="preserve">
    <value>Display</value>
  </data>
  <data name="Wuma.Enum" xml:space="preserve">
    <value>Enum</value>
  </data>
  <data name="Wuma.EnumDefs" xml:space="preserve">
    <value>Enumeration Definition</value>
  </data>
  <data name="Wuma.EnumFieldDes" xml:space="preserve">
    <value>EnumFieldDes</value>
  </data>
  <data name="Wuma.EnumFieldName" xml:space="preserve">
    <value>EnumFieldName</value>
  </data>
  <data name="Wuma.EnumFields" xml:space="preserve">
    <value>EnumFields</value>
  </data>
  <data name="Wuma.EnumName" xml:space="preserve">
    <value>EnumName</value>
  </data>
  <data name="Wuma.FieldDes" xml:space="preserve">
    <value>FieldDes</value>
  </data>
  <data name="Wuma.FieldName" xml:space="preserve">
    <value>FieldName</value>
  </data>
  <data name="Wuma.Fields" xml:space="preserve">
    <value>Fields</value>
  </data>
  <data name="Wuma.FieldType" xml:space="preserve">
    <value>FieldType</value>
  </data>
  <data name="Wuma.FORM" xml:space="preserve">
    <value>FORM</value>
  </data>
  <data name="Wuma.Grid" xml:space="preserve">
    <value>Grid</value>
  </data>
  <data name="Wuma.GroupName" xml:space="preserve">
    <value>GroupName</value>
  </data>
  <data name="Wuma.IsAbstract" xml:space="preserve">
    <value>IsAbstract</value>
  </data>
  <data name="Wuma.IsAutoGenerated" xml:space="preserve">
    <value>IsAutoGenerated</value>
  </data>
  <data name="Wuma.IsBasePoco" xml:space="preserve">
    <value>IsBasePoco</value>
  </data>
  <data name="Wuma.IsID" xml:space="preserve">
    <value>IsID</value>
  </data>
  <data name="Wuma.IsIndex" xml:space="preserve">
    <value>IsIndex</value>
  </data>
  <data name="Wuma.IsInterfaceField" xml:space="preserve">
    <value>IsInterfaceField</value>
  </data>
  <data name="Wuma.IsOverwrite" xml:space="preserve">
    <value>IsOverwrite</value>
  </data>
  <data name="Wuma.IsPersistPoco" xml:space="preserve">
    <value>IsPersistPoco</value>
  </data>
  <data name="Wuma.IsRequired" xml:space="preserve">
    <value>IsRequired</value>
  </data>
  <data name="Wuma.IsTree" xml:space="preserve">
    <value>IsTree</value>
  </data>
  <data name="Wuma.Length" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="Wuma.LinkProject" xml:space="preserve">
    <value>Link Project</value>
  </data>
  <data name="Wuma.Links" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="Wuma.MaxValue" xml:space="preserve">
    <value>MaxValue</value>
  </data>
  <data name="Wuma.MinValue" xml:space="preserve">
    <value>MinValue</value>
  </data>
  <data name="Wuma.Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="Wuma.ModelDes" xml:space="preserve">
    <value>ModelDes</value>
  </data>
  <data name="Wuma.ModelName" xml:space="preserve">
    <value>ModelName</value>
  </data>
  <data name="Wuma.Models" xml:space="preserve">
    <value>Model Definition</value>
  </data>
  <data name="Wuma.NotMapped" xml:space="preserve">
    <value>NotMapped</value>
  </data>
  <data name="Wuma.Number" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="Wuma.Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="Wuma.PageDes" xml:space="preserve">
    <value>PageDes</value>
  </data>
  <data name="Wuma.PageName" xml:space="preserve">
    <value>PageName</value>
  </data>
  <data name="Wuma.Precision" xml:space="preserve">
    <value>Precision</value>
  </data>
  <data name="Wuma.Project" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="Wuma.ProjectDes" xml:space="preserve">
    <value>Project Description</value>
  </data>
  <data name="Wuma.ProjectIcon" xml:space="preserve">
    <value>Project Icon</value>
  </data>
  <data name="Wuma.ProjectName" xml:space="preserve">
    <value>Entry Name</value>
  </data>
  <data name="Wuma.ProjectNs" xml:space="preserve">
    <value>Namespace</value>
  </data>
  <data name="Wuma.PropertyName" xml:space="preserve">
    <value>PropertyName</value>
  </data>
  <data name="Wuma.PropertyValue" xml:space="preserve">
    <value>PropertyValue</value>
  </data>
  <data name="Wuma.Radio" xml:space="preserve">
    <value>Radio</value>
  </data>
  <data name="Wuma.RichTextbox" xml:space="preserve">
    <value>RichTextbox</value>
  </data>
  <data name="Wuma.Row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="Wuma.Slider" xml:space="preserve">
    <value>Slider</value>
  </data>
  <data name="Wuma.SubNs" xml:space="preserve">
    <value>SubNs</value>
  </data>
  <data name="Wuma.Switch" xml:space="preserve">
    <value>Switch</value>
  </data>
  <data name="Wuma.TableName" xml:space="preserve">
    <value>TableName</value>
  </data>
  <data name="Wuma.Textarea" xml:space="preserve">
    <value>Textarea</value>
  </data>
  <data name="Wuma.Textbox" xml:space="preserve">
    <value>Textbox</value>
  </data>
  <data name="Wuma.Transfer" xml:space="preserve">
    <value>Transfer</value>
  </data>
  <data name="Wuma.Tree" xml:space="preserve">
    <value>Tree</value>
  </data>
  <data name="Wuma.TreeContainer" xml:space="preserve">
    <value>TreeContainer</value>
  </data>
  <data name="Wuma.UniqueFields" xml:space="preserve">
    <value>UniqueFields</value>
  </data>
  <data name="Wuma.Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="Wuma.User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="_Account.CreatePayLinkError" xml:space="preserve">
    <value>Call failed, network exception</value>
  </data>
  <data name="_Account.CreatePayLinkEx" xml:space="preserve">
    <value>Call encountered exception, reason:</value>
  </data>
  <data name="_Account.PayStatus0" xml:space="preserve">
    <value>In Payment</value>
  </data>
  <data name="_Account.PayStatus1" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="_Account.PayStatusError" xml:space="preserve">
    <value>Payment Error</value>
  </data>
  <data name="_Account.RegisterSuccess" xml:space="preserve">
    <value>Registered Successfully</value>
  </data>
  <data name="_Admin.Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="_Admin.Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="_Admin.ActionLogApi" xml:space="preserve">
    <value>Log management Api</value>
  </data>
  <data name="_Admin.ActionName" xml:space="preserve">
    <value>Action Name</value>
  </data>
  <data name="_Admin.ActionTime" xml:space="preserve">
    <value>Action Time</value>
  </data>
  <data name="_Admin.AdditionInfo" xml:space="preserve">
    <value>Additional</value>
  </data>
  <data name="_Admin.Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="_Admin.AllDp" xml:space="preserve">
    <value>All Privilege</value>
  </data>
  <data name="_Admin.Allowed" xml:space="preserve">
    <value>Allowed</value>
  </data>
  <data name="_Admin.AllowedDp" xml:space="preserve">
    <value>Allowed</value>
  </data>
  <data name="_Admin.AllowedRole" xml:space="preserve">
    <value>Allowed Roles</value>
  </data>
  <data name="_Admin.AllPris" xml:space="preserve">
    <value>All Privilege</value>
  </data>
  <data name="_Admin.BasicInfo" xml:space="preserve">
    <value>Basic</value>
  </data>
  <data name="_Admin.CellPhone" xml:space="preserve">
    <value>CellPhone</value>
  </data>
  <data name="_Admin.CheckPage" xml:space="preserve">
    <value>Check Page</value>
  </data>
  <data name="_Admin.Children" xml:space="preserve">
    <value>Children</value>
  </data>
  <data name="_Admin.ClassName" xml:space="preserve">
    <value>ClassName</value>
  </data>
  <data name="_Admin.CreateBy" xml:space="preserve">
    <value>CreateBy</value>
  </data>
  <data name="_Admin.CreateTime" xml:space="preserve">
    <value>CreateTime</value>
  </data>
  <data name="_Admin.DataPrivilege" xml:space="preserve">
    <value>Data Privilege</value>
  </data>
  <data name="_Admin.DataPrivilegeApi" xml:space="preserve">
    <value>Data privilege Api</value>
  </data>
  <data name="_Admin.DataPrivilegeCount" xml:space="preserve">
    <value>Dp Count</value>
  </data>
  <data name="_Admin.DataPrivilegeName" xml:space="preserve">
    <value>Dp Name</value>
  </data>
  <data name="_Admin.Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="_Admin.DisplayOrder" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="_Admin.Domain" xml:space="preserve">
    <value>Domain</value>
  </data>
  <data name="_Admin.DpTargetName" xml:space="preserve">
    <value>Target</value>
  </data>
  <data name="_Admin.DpType" xml:space="preserve">
    <value>Dp Type</value>
  </data>
  <data name="_Admin.Duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="_Admin.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="_Admin.Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="_Admin.FieldName" xml:space="preserve">
    <value>Field Name</value>
  </data>
  <data name="_Admin.FileApi" xml:space="preserve">
    <value>File Api</value>
  </data>
  <data name="_Admin.FileExt" xml:space="preserve">
    <value>File Ext</value>
  </data>
  <data name="_Admin.FolderOnly" xml:space="preserve">
    <value>Folder</value>
  </data>
  <data name="_Admin.Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="_Admin.Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="_Admin.GroupApi" xml:space="preserve">
    <value>Group management Api</value>
  </data>
  <data name="_Admin.GroupCode" xml:space="preserve">
    <value>Group Code</value>
  </data>
  <data name="_Admin.GroupDp" xml:space="preserve">
    <value>Group Dp</value>
  </data>
  <data name="_Admin.GroupManager" xml:space="preserve">
    <value>Group Manager</value>
  </data>
  <data name="_Admin.GroupName" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="_Admin.HasMainHost" xml:space="preserve">
    <value>This operation cannot be performed. Please go to the main site for corresponding operation</value>
  </data>
  <data name="_Admin.HomePhone" xml:space="preserve">
    <value>HomePhone</value>
  </data>
  <data name="_Admin.Icon" xml:space="preserve">
    <value>Icon</value>
  </data>
  <data name="_Admin.IconFont" xml:space="preserve">
    <value>IconFont</value>
  </data>
  <data name="_Admin.Inside" xml:space="preserve">
    <value>Inside</value>
  </data>
  <data name="_Admin.IsInherit" xml:space="preserve">
    <value>Inherit</value>
  </data>
  <data name="_Admin.IsInside" xml:space="preserve">
    <value>Domain</value>
  </data>
  <data name="_Admin.IsPublic" xml:space="preserve">
    <value>Public</value>
  </data>
  <data name="_Admin.IsValid" xml:space="preserve">
    <value>IsValid</value>
  </data>
  <data name="_Admin.Job" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="_Admin.Length" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="_Admin.LoginApi" xml:space="preserve">
    <value>Login Api</value>
  </data>
  <data name="_Admin.LogType" xml:space="preserve">
    <value>Log Type</value>
  </data>
  <data name="_Admin.MenuApi" xml:space="preserve">
    <value>Menu management Api</value>
  </data>
  <data name="_Admin.MenuItem" xml:space="preserve">
    <value>MenuItem</value>
  </data>
  <data name="_Admin.MethodName" xml:space="preserve">
    <value>Method Name</value>
  </data>
  <data name="_Admin.Module" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="_Admin.ModuleHasSet" xml:space="preserve">
    <value>This module has already beed added</value>
  </data>
  <data name="_Admin.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="_Admin.NoIndexInModule" xml:space="preserve">
    <value>There is no View in this Module</value>
  </data>
  <data name="_Admin.NoPris" xml:space="preserve">
    <value>No Privilege</value>
  </data>
  <data name="_Admin.Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="_Admin.Outside" xml:space="preserve">
    <value>Outside</value>
  </data>
  <data name="_Admin.PageFunction" xml:space="preserve">
    <value>Page Privileges</value>
  </data>
  <data name="_Admin.PageName" xml:space="preserve">
    <value>Page Name</value>
  </data>
  <data name="_Admin.Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="_Admin.ParentFolder" xml:space="preserve">
    <value>Parent Folder</value>
  </data>
  <data name="_Admin.Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="_Admin.Path" xml:space="preserve">
    <value>Path</value>
  </data>
  <data name="_Admin.Photo" xml:space="preserve">
    <value>Photo</value>
  </data>
  <data name="_Admin.Privileges" xml:space="preserve">
    <value>Privileges</value>
  </data>
  <data name="_Admin.RefreshMenu" xml:space="preserve">
    <value>Refresh Menu</value>
  </data>
  <data name="_Admin.RelatedId" xml:space="preserve">
    <value>Related ID</value>
  </data>
  <data name="_Admin.Remark" xml:space="preserve">
    <value>Remark</value>
  </data>
  <data name="_Admin.Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="_Admin.RoleApi" xml:space="preserve">
    <value>Role management Api</value>
  </data>
  <data name="_Admin.RoleCode" xml:space="preserve">
    <value>Role Code</value>
  </data>
  <data name="_Admin.RoleName" xml:space="preserve">
    <value>Role Name</value>
  </data>
  <data name="_Admin.SameRole" xml:space="preserve">
    <value>Except for administrators, users cannot modify the page permissions of their roles</value>
  </data>
  <data name="_Admin.SelectedModel" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="_Admin.SelectPris" xml:space="preserve">
    <value>Select Privileges</value>
  </data>
  <data name="_Admin.ShowOnMenu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="_Admin.TableName" xml:space="preserve">
    <value>Privilege Name</value>
  </data>
  <data name="_Admin.Tenant" xml:space="preserve">
    <value>Tenant</value>
  </data>
  <data name="_Admin.TenantAllowed" xml:space="preserve">
    <value>Tenant Allowed</value>
  </data>
  <data name="_Admin.TenantChoose" xml:space="preserve">
    <value>Select Tenant</value>
  </data>
  <data name="_Admin.TenantCode" xml:space="preserve">
    <value>Tenant Code</value>
  </data>
  <data name="_Admin.TenantDb" xml:space="preserve">
    <value>Tenant Database</value>
  </data>
  <data name="_Admin.TenantDbContext" xml:space="preserve">
    <value>Tenant Datacontext</value>
  </data>
  <data name="_Admin.TenantDbError" xml:space="preserve">
    <value>Failed to create database</value>
  </data>
  <data name="_Admin.TenantDbType" xml:space="preserve">
    <value>Tenant Dbtype</value>
  </data>
  <data name="_Admin.TenantDomain" xml:space="preserve">
    <value>Tenant host url</value>
  </data>
  <data name="_Admin.TenantEnableSub" xml:space="preserve">
    <value>Sub tenant allowed</value>
  </data>
  <data name="_Admin.TenantHost" xml:space="preserve">
    <value>Main Host</value>
  </data>
  <data name="_Admin.TenantName" xml:space="preserve">
    <value>Tenant Name</value>
  </data>
  <data name="_Admin.TenantNotAllowed" xml:space="preserve">
    <value>The current tenant cannot use this feature</value>
  </data>
  <data name="_Admin.TenantRole" xml:space="preserve">
    <value>Use the permissions of the selected role to establish the administrator of the tenant, account admin and password 000000</value>
  </data>
  <data name="_Admin.UnsetPages" xml:space="preserve">
    <value>Unset Pages</value>
  </data>
  <data name="_Admin.UpdateBy" xml:space="preserve">
    <value>UpdateBy</value>
  </data>
  <data name="_Admin.UpdateTime" xml:space="preserve">
    <value>Update Time</value>
  </data>
  <data name="_Admin.User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="_Admin.UserApi" xml:space="preserve">
    <value>User management Api</value>
  </data>
  <data name="_Admin.UserDp" xml:space="preserve">
    <value>User Dp</value>
  </data>
  <data name="_Admin.UsersCount" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="_Admin.ValidDate" xml:space="preserve">
    <value>term of validity</value>
  </data>
  <data name="_Admin.VipTag" xml:space="preserve">
    <value>VIP Tag</value>
  </data>
  <data name="_Admin.ZipCode" xml:space="preserve">
    <value>Zip</value>
  </data>
  <data name="_Model.FrameworkRole" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="_Model.FrameworkGroup" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="_Model.FrameworkUser" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="MenuKey.Workflow" xml:space="preserve">
    <value>WorkFlow</value>
  </data>
  <data name="Sys.NoWorkflow" xml:space="preserve">
    <value>No related work flow is found</value>
  </data>
  <data name="_Workflow.Canceled" xml:space="preserve">
    <value>Canceled</value>
  </data>
  <data name="_Workflow.Finished" xml:space="preserve">
    <value>Finished</value>
  </data>
  <data name="_Workflow.NotStarted" xml:space="preserve">
    <value>Not Started</value>
  </data>
  <data name="_Workflow.Processing" xml:space="preserve">
    <value>In Process</value>
  </data>
  <data name="Sys.Aggree" xml:space="preserve">
    <value>Agree</value>
  </data>
  <data name="Sys.Approve" xml:space="preserve">
    <value>Approve</value>
  </data>
  <data name="Sys.Refuse" xml:space="preserve">
    <value>Refuse</value>
  </data>
  <data name="_Admin.WorkflowApi" xml:space="preserve">
    <value>Workflow Api</value>
  </data>
  <data name="_Model._FrameworkUser._Email" xml:space="preserve">
    <value>Mailbox</value>
  </data>
  <data name="_Model._FrameworkUser._Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="_Model._FrameworkUser._CellPhone" xml:space="preserve">
    <value>Mobile Phone</value>
  </data>
  <data name="_Model._FrameworkUser._HomePhone" xml:space="preserve">
    <value>Landline</value>
  </data>
  <data name="_Model._FrameworkUser._Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="_Model._FrameworkUser._ZipCode" xml:space="preserve">
    <value>Postal Code</value>
  </data>
  <data name="_Model._FrameworkUser._Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="_Model._FrameworkUser._Group" xml:space="preserve">
    <value>User Group</value>
  </data>
  <data name="_Model._FrameworkUser._ITCode" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="_Model._FrameworkUser._Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="_Model._FrameworkUser._Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="_Model._FrameworkUser._IsValid" xml:space="preserve">
    <value>Is it valid</value>
  </data>
  <data name="_Model._FrameworkUser._Photo" xml:space="preserve">
    <value>Photo</value>
  </data>
  <data name="_Model._FrameworkUser._ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="_Model._FrameworkUser._CreateTime" xml:space="preserve">
    <value>Creation Time</value>
  </data>
  <data name="_Model._FrameworkUser._UpdateTime" xml:space="preserve">
    <value>Modification Time</value>
  </data>
  <data name="_Model._FrameworkUser._CreateBy" xml:space="preserve">
    <value>Creator</value>
  </data>
  <data name="_Model._FrameworkUser._UpdateBy" xml:space="preserve">
    <value>Modified By</value>
  </data>
  <data name="_Model.Company" xml:space="preserve">
    <value>Correspondence Company</value>
  </data>
  <data name="_Model.Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="_ProductCode" xml:space="preserve">
    <value>Product Code</value>
  </data>
  <data name="_ProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="_Category" xml:space="preserve">
    <value>Product Classification</value>
  </data>
  <data name="_Contents" xml:space="preserve">
    <value>Ingredients</value>
  </data>
  <data name="_Spec" xml:space="preserve">
    <value>Specifications</value>
  </data>
  <data name="_GSM" xml:space="preserve">
    <value>GSM</value>
  </data>
  <data name="_Width" xml:space="preserve">
    <value>Effective gate width</value>
  </data>
  <data name="_PileLength" xml:space="preserve">
    <value>Hair Length</value>
  </data>
  <data name="_DyeingProcess" xml:space="preserve">
    <value>Dyeing Process</value>
  </data>
  <data name="_KnittingProcess" xml:space="preserve">
    <value>Weaving Process</value>
  </data>
  <data name="_FinishingProcess" xml:space="preserve">
    <value>Post finishing process</value>
  </data>
  <data name="_ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="_Parent" xml:space="preserve">
    <value>Parent Level</value>
  </data>
  <data name="_Children" xml:space="preserve">
    <value>Subitem</value>
  </data>
  <data name="_CreateTime" xml:space="preserve">
    <value>Creation Time</value>
  </data>
  <data name="_UpdateTime" xml:space="preserve">
    <value>Modification Time</value>
  </data>
  <data name="_CreateBy" xml:space="preserve">
    <value>Creator</value>
  </data>
  <data name="_UpdateBy" xml:space="preserve">
    <value>Modified By</value>
  </data>
  <data name="_IsValid" xml:space="preserve">
    <value>Is it valid</value>
  </data>
  <data name="_TenantCode" xml:space="preserve">
    <value>Tenant ID</value>
  </data>
  <data name="_PurchaseOrder_Product" xml:space="preserve">
    <value>PurchaseOrder</value>
  </data>
  <data name="_Model.PurchaseOrder" xml:space="preserve">
    <value>PurchaseOrder</value>
  </data>
  <data name="_CustomerOrderNo" xml:space="preserve">
    <value>CustomerOrderNo</value>
  </data>
  <data name="_TotalMeters" xml:space="preserve">
    <value>TotalMeters</value>
  </data>
  <data name="_TotalWeight" xml:space="preserve">
    <value>TotalWeight</value>
  </data>
  <data name="_Enum._ActionLogTypesEnum._Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="_Enum._ActionLogTypesEnum._Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="_Enum._ActionLogTypesEnum._Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="_Enum._GenderEnum._Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="_Enum._GenderEnum._Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Taffeta" xml:space="preserve">
    <value>Taffeta</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._NylonTaffeta" xml:space="preserve">
    <value>NylonTaffeta</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Pongee" xml:space="preserve">
    <value>Pongee</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._TwoWayStretch" xml:space="preserve">
    <value>TwoWayStretch</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Oxford" xml:space="preserve">
    <value>Oxford</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Taslan" xml:space="preserve">
    <value>Taslan</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Fakecotton" xml:space="preserve">
    <value>Fakecotton</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._MicroFiber" xml:space="preserve">
    <value>MicroFiber</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._ImitationSilk" xml:space="preserve">
    <value>ImitationSilk</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._NPTaffeta" xml:space="preserve">
    <value>NPTaffeta</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Faille" xml:space="preserve">
    <value>Faille</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._MicroSuede" xml:space="preserve">
    <value>MicroSuede</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Satin" xml:space="preserve">
    <value>Satin</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Gabardine" xml:space="preserve">
    <value>Gabardine</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._CalvaryTwill" xml:space="preserve">
    <value>CalvaryTwill</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._Others" xml:space="preserve">
    <value>Others</value>
  </data>
  <data name="_Enum._RelationshipEnum._Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="_Enum._RelationshipEnum._Vender" xml:space="preserve">
    <value>Vender</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._TradingCompany" xml:space="preserve">
    <value>TradingCompany</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._GarmentFactory" xml:space="preserve">
    <value>GarmentFactory</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._DyeingFactory" xml:space="preserve">
    <value>DyeingFactory</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._FinishingFactory" xml:space="preserve">
    <value>FinishingFactory</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._WovenFactory" xml:space="preserve">
    <value>WovenFactory</value>
  </data>
  <data name="_KnittingFactory" xml:space="preserve">
    <value>KnittingFactory</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._GreigeVender" xml:space="preserve">
    <value>GreigeVender</value>
  </data>
  <data name="_Enum._CompanyTypeEnum._FabricVender" xml:space="preserve">
    <value>FabricVender</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.Create" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.Edit" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.Index" xml:space="preserve">
    <value>User Management</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.Password" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="_Page._Admin.FrameworkUser.BatchEdit" xml:space="preserve">
    <value>Batch Operations</value>
  </data>
  <data name="_Page.Models.OrderDetail.Create" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="_Page.Models.OrderDetail.Edit" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="_Page.Models.OrderDetail.Index" xml:space="preserve">
    <value>Orderdetail List</value>
  </data>
  <data name="_Page.Models.OrderDetail.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="_Page.Models.OrderDetail.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="_Page.Models.OrderDetail.BatchEdit" xml:space="preserve">
    <value>Batch Operations</value>
  </data>
  <data name="_Page.Models.Company.Create" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="_Page.Models.Company.Edit" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="_Page.Models.Company.Index" xml:space="preserve">
    <value>CompanyManagement</value>
  </data>
  <data name="_Page.Models.Company.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="_Page.Models.Company.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="_Page.Models.Company.BatchEdit" xml:space="preserve">
    <value>Batch Operations</value>
  </data>
  <data name="_Page.Models.Product.Create" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="_Page.Models.Product.Edit" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="_Page.Models.Product.Index" xml:space="preserve">
    <value>Product List</value>
  </data>
  <data name="_Page.Models.Product.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="_Page.Models.Product.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="_Page.Models.Product.BatchEdit" xml:space="preserve">
    <value>Batch Operations</value>
  </data>
  <data name="_Page.Models.PurchaseOrder.Create" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="_Page.Models.PurchaseOrder.Edit" xml:space="preserve">
    <value>Order Modification</value>
  </data>
  <data name="_Page.Models.PurchaseOrder.Index" xml:space="preserve">
    <value>Order List</value>
  </data>
  <data name="_Page.Models.PurchaseOrder.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="_Page.Models.PurchaseOrder.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="_Page.Models.PurchaseOrder.BatchEdit" xml:space="preserve">
    <value>Batch Operations</value>
  </data>
  <data name="Page.详情" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Page.订单号" xml:space="preserve">
    <value>Order Number</value>
  </data>
  <data name="Page.颜色" xml:space="preserve">
    <value>Colour</value>
  </data>
  <data name="Page.色号" xml:space="preserve">
    <value>Color Code</value>
  </data>
  <data name="Page.米数" xml:space="preserve">
    <value>Meters</value>
  </data>
  <data name="Page.重量" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="Page.码数" xml:space="preserve">
    <value>Number of yards</value>
  </data>
  <data name="Page.计量单位" xml:space="preserve">
    <value>Unit of measurement</value>
  </data>
  <data name="Page.计价单位" xml:space="preserve">
    <value>Pricing Unit</value>
  </data>
  <data name="Page.单价" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="Page.金额" xml:space="preserve">
    <value>Money</value>
  </data>
  <data name="Page.公司代码" xml:space="preserve">
    <value>Company Code</value>
  </data>
  <data name="Page.公司名称" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="Page.公司全称" xml:space="preserve">
    <value>Full name of the company</value>
  </data>
  <data name="Page.公司类型" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="Page.往来关系" xml:space="preserve">
    <value>Correspondence</value>
  </data>
  <data name="Page.电话" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="Page.税号" xml:space="preserve">
    <value>Tax ID</value>
  </data>
  <data name="Page.开票资料" xml:space="preserve">
    <value>Invoicing Information</value>
  </data>
  <data name="Page.产品编码" xml:space="preserve">
    <value>Product Code</value>
  </data>
  <data name="Page.产品名称" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="Page.产品分类" xml:space="preserve">
    <value>Product Classification</value>
  </data>
  <data name="Page.成份" xml:space="preserve">
    <value>Ingredients</value>
  </data>
  <data name="Page.规格" xml:space="preserve">
    <value>Specifications</value>
  </data>
  <data name="Page.平方克重" xml:space="preserve">
    <value>Square gram weight</value>
  </data>
  <data name="Page.有效门幅" xml:space="preserve">
    <value>Effective gate width</value>
  </data>
  <data name="Page.毛长" xml:space="preserve">
    <value>Hair Length</value>
  </data>
  <data name="Page.染色工艺" xml:space="preserve">
    <value>Dyeing Process</value>
  </data>
  <data name="Page.织造工艺" xml:space="preserve">
    <value>Weaving Process</value>
  </data>
  <data name="Page.后整工艺" xml:space="preserve">
    <value>Post finishing process</value>
  </data>
  <data name="Page.订单明细" xml:space="preserve">
    <value>Order Details</value>
  </data>
  <data name="Page.坯布采购" xml:space="preserve">
    <value>Grey fabric procurement</value>
  </data>
  <data name="Page.染色计划" xml:space="preserve">
    <value>Dyeing Plan</value>
  </data>
  <data name="Page.成品入库" xml:space="preserve">
    <value>Product Inbound</value>
  </data>
  <data name="Page.成品出库" xml:space="preserve">
    <value>Product Outbound</value>
  </data>
  <data name="Page.成品库存" xml:space="preserve">
    <value>Product Inventory</value>
  </data>
  <data name="Page.下单日期" xml:space="preserve">
    <value>Order Date</value>
  </data>
  <data name="Page.客户" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="Page.客户订单号" xml:space="preserve">
    <value>Customer OrderNO</value>
  </data>
  <data name="Page.业务员" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="Page.订单类型" xml:space="preserve">
    <value>Order Type</value>
  </data>
  <data name="Page.主光源" xml:space="preserve">
    <value>Main light source</value>
  </data>
  <data name="Page.总米数" xml:space="preserve">
    <value>Total Meters</value>
  </data>
  <data name="Page.总码数" xml:space="preserve">
    <value>Total Size</value>
  </data>
  <data name="Page.总重量" xml:space="preserve">
    <value>Total Weight</value>
  </data>
  <data name="_Model.OrderDetail" xml:space="preserve">
    <value>OrderDetail</value>
  </data>
  <data name="_Photo" xml:space="preserve">
    <value>Photo</value>
  </data>
  <data name="_Enum._OrderTypeEnum._Greige" xml:space="preserve">
    <value>Greige</value>
  </data>
  <data name="_Enum._OrderTypeEnum._Knitting" xml:space="preserve">
    <value>Knitting</value>
  </data>
  <data name="_Enum._OrderTypeEnum._Fabric" xml:space="preserve">
    <value>Fabric</value>
  </data>
  <data name="_Enum._LightEnum._D65" xml:space="preserve">
    <value>D65</value>
  </data>
  <data name="_Enum._LightEnum._LED" xml:space="preserve">
    <value>LED</value>
  </data>
  <data name="_Enum._LightEnum._TL84" xml:space="preserve">
    <value>TL84</value>
  </data>
  <data name="_Enum._LightEnum._TL83" xml:space="preserve">
    <value>TL83</value>
  </data>
  <data name="_Enum._LightEnum._U3000" xml:space="preserve">
    <value>U3000</value>
  </data>
  <data name="_Enum._LightEnum._A" xml:space="preserve">
    <value>A</value>
  </data>
  <data name="_Enum._LightEnum._CWF" xml:space="preserve">
    <value>CWF</value>
  </data>
  <data name="_Enum._LightEnum._D65_LED" xml:space="preserve">
    <value>D65_LED</value>
  </data>
  <data name="_Enum._LightEnum._DayLight" xml:space="preserve">
    <value>DayLight</value>
  </data>
  <data name="_Enum._AccountingUnitEnum._M" xml:space="preserve">
    <value>M</value>
  </data>
  <data name="_Enum._AccountingUnitEnum._KG" xml:space="preserve">
    <value>KG</value>
  </data>
  <data name="_Enum._AccountingUnitEnum._Y" xml:space="preserve">
    <value>Y</value>
  </data>
  <data name="_Enum._CurrencyEnum._CNY" xml:space="preserve">
    <value>CNY</value>
  </data>
  <data name="_Enum._CurrencyEnum._USD" xml:space="preserve">
    <value>USD</value>
  </data>
  <data name="_Enum._CurrencyEnum._GBP" xml:space="preserve">
    <value>GBP</value>
  </data>
  <data name="_Enum._CurrencyEnum._HKD" xml:space="preserve">
    <value>HKD</value>
  </data>
  <data name="_Enum._AuditStatusEnum._NotAudited" xml:space="preserve">
    <value>NotAudited</value>
  </data>
  <data name="_Enum._AuditStatusEnum._AuditedApproved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="_Enum._AuditStatusEnum._AuditedFailed" xml:space="preserve">
    <value>Failed</value>
  </data>
  <data name="_Group.FrameworkUser" xml:space="preserve">
    <value>FrameworkUser</value>
  </data>
  <data name="_Group.OrderDetail" xml:space="preserve">
    <value>OrderDetail</value>
  </data>
  <data name="_Group.Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="_Group.Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="_Group.PurchaseOrder" xml:space="preserve">
    <value>PurchaseOrder</value>
  </data>
  <data name="MenuMangement" xml:space="preserve">
    <value>MenuMangement</value>
  </data>
  <data name="Validate.{0}MustBeEnglish" xml:space="preserve">
    <value>{0}MustBeEnglish</value>
  </data>
  <data name="Validate.{0}range{1}" xml:space="preserve">
    <value>{0}range{1}</value>
  </data>
  <data name="_AuditConfirm" xml:space="preserve">
    <value>Confirm Audit?</value>
  </data>
  <data name="_Audit" xml:space="preserve">
    <value>Audit</value>
  </data>
  <data name="_AuditStatus" xml:space="preserve">
    <value>AuditStatus</value>
  </data>
  <data name="_AuditedBy" xml:space="preserve">
    <value>AuditedBy</value>
  </data>
  <data name="_AuditOrUnAudit" xml:space="preserve">
    <value>AuditOrUnAudit</value>
  </data>
  <data name="_Lot" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="_RollNo" xml:space="preserve">
    <value>RollNo</value>
  </data>
  <data name="_Weight" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="_Meters" xml:space="preserve">
    <value>Meters</value>
  </data>
  <data name="_Yards" xml:space="preserve">
    <value>Yards</value>
  </data>
  <data name="_Grade" xml:space="preserve">
    <value>Grade</value>
  </data>
  <data name="_InboundBillNo" xml:space="preserve">
    <value>InboundBillNo</value>
  </data>
  <data name="_PurchaseOrderId" xml:space="preserve">
    <value>PurchaseOrderId</value>
  </data>
  <data name="_FinishingFactory" xml:space="preserve">
    <value>FinishingFactory</value>
  </data>
  <data name="_Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="_Color" xml:space="preserve">
    <value>Colour</value>
  </data>
  <data name="_ColorCode" xml:space="preserve">
    <value>Color Code</value>
  </data>
  <data name="_LotNo" xml:space="preserve">
    <value>LotNo</value>
  </data>
  <data name="_RollsCount" xml:space="preserve">
    <value>RollsCount</value>
  </data>
  <data name="_CreateDate" xml:space="preserve">
    <value>OrderDate</value>
  </data>
  <data name="_BillNo" xml:space="preserve">
    <value>BillNo</value>
  </data>
  <data name="_Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="_POrder" xml:space="preserve">
    <value>POrder</value>
  </data>
  <data name="_Pcs" xml:space="preserve">
    <value>Pcs</value>
  </data>
  <data name="_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="_Wearhouse" xml:space="preserve">
    <value>Wearhouse</value>
  </data>
  <data name="_ContactName" xml:space="preserve">
    <value>ContactName</value>
  </data>
  <data name="_AffiliationCompany" xml:space="preserve">
    <value>AffiliationCompany</value>
  </data>
  <data name="_PositionTitle" xml:space="preserve">
    <value>PositionTitle</value>
  </data>
  <data name="_MobilePhone" xml:space="preserve">
    <value>MobilePhone</value>
  </data>
  <data name="_Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="_Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="_WeChat" xml:space="preserve">
    <value>Wechat</value>
  </data>
  <data name="_QQ" xml:space="preserve">
    <value>Qq</value>
  </data>
  <data name="_Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="_CompanyName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="_InboundBill" xml:space="preserve">
    <value>InboundBill</value>
  </data>
  <data name="_AuditedComment" xml:space="preserve">
    <value>AuditedComment</value>
  </data>
  <data name="_Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="_POrderId" xml:space="preserve">
    <value>POrderId</value>
  </data>
  <data name="_PlanBatch" xml:space="preserve">
    <value>PlanBatch</value>
  </data>
  <data name="_Fabric" xml:space="preserve">
    <value>Fabric</value>
  </data>
  <data name="_FabricId" xml:space="preserve">
    <value>FabricId</value>
  </data>
  <data name="_Light" xml:space="preserve">
    <value>Light</value>
  </data>
  <data name="_Light2" xml:space="preserve">
    <value>Light2</value>
  </data>
  <data name="_GreigeBatch" xml:space="preserve">
    <value>GreigeBatch</value>
  </data>
  <data name="_GreigeVender" xml:space="preserve">
    <value>GreigeVender</value>
  </data>
  <data name="_DyingDemand" xml:space="preserve">
    <value>DyingDemand</value>
  </data>
  <data name="_PackDemand" xml:space="preserve">
    <value>PackDemand</value>
  </data>
  <data name="_AdditionalDemend" xml:space="preserve">
    <value>AdditionalDemend</value>
  </data>
  <data name="_SingleQty" xml:space="preserve">
    <value>SingleQty</value>
  </data>
  <data name="_Qty" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="_QtyUnit" xml:space="preserve">
    <value>QtyUnit</value>
  </data>
  <data name="_DeliveryDate" xml:space="preserve">
    <value>DeliveryDate</value>
  </data>
  <data name="_FinishingPrice" xml:space="preserve">
    <value>FinishingPrice</value>
  </data>
  <data name="_Procedure" xml:space="preserve">
    <value>Procedure</value>
  </data>
  <data name="_Remark" xml:space="preserve">
    <value>Remark</value>
  </data>
  <data name="_Shipper" xml:space="preserve">
    <value>Shipper</value>
  </data>
  <data name="_Receiver" xml:space="preserve">
    <value>Receiver</value>
  </data>
  <data name="_OrderNo" xml:space="preserve">
    <value>OrderNo</value>
  </data>
  <data name="_EngColor" xml:space="preserve">
    <value>EngColor</value>
  </data>
  <data name="_DyeingProductName" xml:space="preserve">
    <value>DyeingProductName</value>
  </data>
  <data name="_OneWayStretch" xml:space="preserve">
    <value>OneWayStretch</value>
  </data>
  <data name="_DyeingPlanBillNo" xml:space="preserve">
    <value>DyeingPlanBillNo</value>
  </data>
  <data name="_Enum._FabricCategoryEnum._OneWayStretch" xml:space="preserve">
    <value>OneWayStretch</value>
  </data>
  <data name="_WIPNo" xml:space="preserve">
    <value>WIPNo</value>
  </data>
  <data name="_TotalScore" xml:space="preserve">
    <value>TotalScore</value>
  </data>
  <data name="_Score" xml:space="preserve">
    <value>Score</value>
  </data>
  <data name="_ProcessName" xml:space="preserve">
    <value>ProcessName</value>
  </data>
  <data name="_Merchandiser" xml:space="preserve">
    <value>Merchandiser</value>
  </data>
  <data name="_OrderType" xml:space="preserve">
    <value>OrderType</value>
  </data>
  <data name="_TotalYards" xml:space="preserve">
    <value>TotalYards</value>
  </data>
  <data name="_TotalAmount" xml:space="preserve">
    <value>TotalAmount</value>
  </data>
  <data name="_OrderDetail_PurchaseOrder" xml:space="preserve">
    <value>OrderDetail_PurchaseOrder</value>
  </data>
  <data name="_PurchaseOrder" xml:space="preserve">
    <value>Order Number</value>
  </data>
  <data name="_KG" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="_AccountUnit" xml:space="preserve">
    <value>QTY Unit</value>
  </data>
  <data name="_PriceUnit" xml:space="preserve">
    <value>Pricing Unit</value>
  </data>
  <data name="_Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="_Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="_CompanyCode" xml:space="preserve">
    <value>Company Code</value>
  </data>
  <data name="_CompanyFullName" xml:space="preserve">
    <value>Full name of the company</value>
  </data>
  <data name="_CompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="_Relationship" xml:space="preserve">
    <value>Correspondence</value>
  </data>
  <data name="_ContactPhone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="_Adress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="_TaxNO" xml:space="preserve">
    <value>Tax ID</value>
  </data>
  <data name="_InvoiceInfo" xml:space="preserve">
    <value>Invoicing Information</value>
  </data>
  <data name="_PurchaseOrder_Customer" xml:space="preserve">
    <value>PurchaseOrder</value>
  </data>
  <data name="_DictName" xml:space="preserve">
    <value>DictName</value>
  </data>
  <data name="_DictItemName" xml:space="preserve">
    <value>DictItemName</value>
  </data>
  <data name="_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="_DictItemOrder" xml:space="preserve">
    <value>DictItemOrder</value>
  </data>
  <data name="_DictOrder" xml:space="preserve">
    <value>DictOrder</value>
  </data>
  <data name="_ShipperMeters" xml:space="preserve">
    <value>ShipperMeters</value>
  </data>
  <data name="_ShipperPcs" xml:space="preserve">
    <value>ShipperPcs</value>
  </data>
  <data name="_ReceiverPcs" xml:space="preserve">
    <value>ReceiverPcs</value>
  </data>
  <data name="_ReceiverMeters" xml:space="preserve">
    <value>ReceiverMeters</value>
  </data>
  <data name="_CompletedStatus" xml:space="preserve">
    <value>CompletedStatus</value>
  </data>
  <data name="_StandBy" xml:space="preserve">
    <value>StandBy</value>
  </data>
  <data name="_InProcess" xml:space="preserve">
    <value>InProcess</value>
  </data>
  <data name="_Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="_Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="_Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="AllocatedWeight" xml:space="preserve">
    <value>AllocatedWeight</value>
  </data>
  <data name="AllocatedMeters" xml:space="preserve">
    <value>AllocatedMeters</value>
  </data>
  <data name="AllocatedMetersPercent" xml:space="preserve">
    <value>AllocatedMetersPercent</value>
  </data>
  <data name="AllocatedWeightPercent" xml:space="preserve">
    <value>AllocatedWeightPercent</value>
  </data>
  <data name="_ProductInboundBill" xml:space="preserve">
    <value>ProductInboundBill</value>
  </data>
  <data name="_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Sys.Save" xml:space="preserve">
    <value>Sys.Save</value>
  </data>
  <data name="_OrderColor" xml:space="preserve">
    <value>OrderColor</value>
  </data>
  <data name="_Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="_ProcessCode" xml:space="preserve">
    <value>ProcessCode</value>
  </data>
  <data name="_InspectionStandard" xml:space="preserve">
    <value>InspectionStandard</value>
  </data>
  <data name="_Batch" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="_Unit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="_PlanFinishedDate" xml:space="preserve">
    <value>PlanFinishedDate</value>
  </data>
  <data name="_Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="_PlanNo" xml:space="preserve">
    <value>PlanNo</value>
  </data>
  <data name="_PlanQty" xml:space="preserve">
    <value>PlanQty</value>
  </data>
  <data name="_InspectStatus" xml:space="preserve">
    <value>InspectStatus</value>
  </data>
  <data name="_TotalLot" xml:space="preserve">
    <value>TotalLot</value>
  </data>
  <data name="_TotalPcs" xml:space="preserve">
    <value>TotalPcs</value>
  </data>
  <data name="_Enum._AuditStatusEnum._Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="_Enum._AuditStatusEnum._Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="_PatternCode" xml:space="preserve">
    <value>PatternCode</value>
  </data>
  <data name="_PatternVersion" xml:space="preserve">
    <value>PatternVersion</value>
  </data>
  <data name="_FabricCategory" xml:space="preserve">
    <value>FabricCategory</value>
  </data>
  <data name="_PatternRepeatWidth" xml:space="preserve">
    <value>PatternRepeatWidth</value>
  </data>
  <data name="_PatternRepeatHeight" xml:space="preserve">
    <value>PatternRepeatHeight</value>
  </data>
  <data name="_RequiredFullWidth" xml:space="preserve">
    <value>RequiredFullWidth</value>
  </data>
  <data name="_RequiredGsm" xml:space="preserve">
    <value>RequiredGsm</value>
  </data>
  <data name="_RequiredCuttableWidth" xml:space="preserve">
    <value>RequiredCuttableWidth</value>
  </data>
  <data name="_MachineInch" xml:space="preserve">
    <value>MachineInch</value>
  </data>
  <data name="_MachineTotalNeedles" xml:space="preserve">
    <value>MachineTotalNeedles</value>
  </data>
  <data name="_MachineSpec" xml:space="preserve">
    <value>MachineSpec</value>
  </data>
  <data name="_JacquardFeed" xml:space="preserve">
    <value>JacquardFeed</value>
  </data>
  <data name="_PatternWeftPoint" xml:space="preserve">
    <value>PatternWeftPoint</value>
  </data>
  <data name="_PatternWarpPoint" xml:space="preserve">
    <value>PatternWarpPoint</value>
  </data>
  <data name="_GreigeRepeatWidth" xml:space="preserve">
    <value>GreigeRepeatWidth</value>
  </data>
  <data name="_GreigeRepeatHeight" xml:space="preserve">
    <value>GreigeRepeatHeight</value>
  </data>
  <data name="_GreigeWidth" xml:space="preserve">
    <value>GreigeWidth</value>
  </data>
  <data name="_GreigeGsm" xml:space="preserve">
    <value>GreigeGsm</value>
  </data>
  <data name="_FabricRepeatWidth" xml:space="preserve">
    <value>FabricRepeatWidth</value>
  </data>
  <data name="_FabricRepeatHeight" xml:space="preserve">
    <value>FabricRepeatHeight</value>
  </data>
  <data name="_FabricFullWidth" xml:space="preserve">
    <value>FabricFullWidth</value>
  </data>
  <data name="_FabricGsm" xml:space="preserve">
    <value>FabricGsm</value>
  </data>
  <data name="_VersionModified" xml:space="preserve">
    <value>VersionModified</value>
  </data>
  <data name="_PictureName" xml:space="preserve">
    <value>PictureName</value>
  </data>
  <data name="_Requirements" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="_CodeNo" xml:space="preserve">
    <value>CodeNo</value>
  </data>
  <data name="_Pattern" xml:space="preserve">
    <value>Pattern</value>
  </data>
  <data name="_MachineNo" xml:space="preserve">
    <value>MachineNo</value>
  </data>
  <data name="_FreeYards" xml:space="preserve">
    <value>FreeYards</value>
  </data>
</root>