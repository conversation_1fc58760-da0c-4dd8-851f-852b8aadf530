<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>disable</Nullable>
		<NoWarn>$(NoWarn);NU1902</NoWarn>
		<NoWarn>$(NoWarn);NU1903</NoWarn>
	</PropertyGroup>


	<ItemGroup>
		<PackageReference Include="BootstrapBlazor" Version="8.8.3" />
		<PackageReference Include="BootstrapBlazor.Chart" Version="8.2.3" />
		<PackageReference Include="BootstrapBlazor.FontAwesome" Version="8.0.5" />
		<PackageReference Include="BootstrapBlazor.Markdown" Version="8.0.0" />
		<PackageReference Include="BootstrapBlazor.SummerNote" Version="8.0.4" />
		
		<PackageReference Include="Elsa.Designer.Components.Web" Version="2.14.1" />
		<!--<PackageReference Include="BootstrapBlazor" Version="9.0.0" />
		<PackageReference Include="BootstrapBlazor.Chart" Version="9.0.0" />
		<PackageReference Include="BootstrapBlazor.Markdown" Version="9.0.0" />
		<PackageReference Include="BootstrapBlazor.SummerNote" Version="9.0.0" />-->
		<PackageReference Include="TinyPinyin" Version="1.1.0" />
		<ProjectReference Include="..\TEX.ViewModel\TEX.ViewModel.csproj" />
	</ItemGroup>
</Project>

