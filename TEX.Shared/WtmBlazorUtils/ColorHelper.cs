using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TEX.Shared;

public class ColorHelper
{

    public static (int R, int G, int B) XYZtoRGB(double X, double Y, double Z, IlluminantType illuminant)
    {
        // 首先需要将 XYZ 值归一化
        X = X / 100.0;
        Y = Y / 100.0;
        Z = Z / 100.0;

        // 根据不同光源选择不同的转换矩阵
        double r, g, b;

        switch (illuminant)
        {
            case IlluminantType.D65:
                // D65光源（日光）
                r = 3.2404542 * X - 1.5371385 * Y - 0.4985314 * Z;
                g = -0.9692660 * X + 1.8760108 * Y + 0.0415560 * Z;
                b = 0.0556434 * X - 0.2040259 * Y + 1.0572252 * Z;
                break;

            case IlluminantType.CWF:
                // CWF光源（冷白荧光）
                r = 3.0415701 * X - 1.3876791 * Y - 0.4738899 * Z;
                g = -0.9692660 * X + 1.8760108 * Y + 0.0415560 * Z;
                b = 0.0678677 * X - 0.2288548 * Y + 1.0693490 * Z;
                break;

            case IlluminantType.A:
                // A光源（白炽灯）
                r = 3.0415701 * X - 1.3876791 * Y - 0.4738899 * Z;
                g = -1.0882110 * X + 1.9753731 * Y + 0.0415560 * Z;
                b = 0.0920512 * X - 0.2289914 * Y + 1.0370581 * Z;
                break;

            default:
                throw new ArgumentException("不支持的光源类型");
        }

        // 伽马校正
        r = GammaCorrection(r);
        g = GammaCorrection(g);
        b = GammaCorrection(b);

        // 转换到0-255范围并四舍五入
        int R = (int)Math.Round(Math.Max(0, Math.Min(255, r * 255)));
        int G = (int)Math.Round(Math.Max(0, Math.Min(255, g * 255)));
        int B = (int)Math.Round(Math.Max(0, Math.Min(255, b * 255)));

        return (R, G, B);
    }

    // 伽马校正保持不变
    private static double GammaCorrection(double value)
    {
        if (value <= 0) return 0;
        return value > 0.0031308
            ? 1.055 * Math.Pow(value, 1.0 / 2.4) - 0.055
            : 12.92 * value;
    }
}
public enum IlluminantType
{
    D65, // 日光
    CWF, // 冷白荧光
    A    // 白炽灯
}