using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyPinyin;

namespace TEX.Shared;

public class PinYinHelper
{
    public static string GetPinYinFirstLetters(string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < input.Length; i++)
        {
            char c = input[i];
            // 判断是否为中文字符
            if (IsChinese(c))
            {
                // 获取单个中文字符的拼音首字母
                string pinyin = PinyinHelper.GetPinyinInitials(c.ToString(), "");
                result.Append(pinyin);
            }
            else
            {
                // 如果是非中文字符，直接保留
                result.Append(c);
            }
        }

        return result.ToString();
    }

    private static bool IsChinese(char c)
    {
        return c >= 0x4E00 && c <= 0x9FFF;
    }
}
