using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using TEX.Model.Models;
using TEX.ViewModel.Finished.InspectedRollVMs;
using TEX.ViewModel.Finished.ProductOutboundBillVMs;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using ICell = NPOI.SS.UserModel.ICell;

namespace TEX.Shared;

public class ExcelHelper
{
    /// <summary>
    /// 使用NPOI生成检验报告
    /// </summary>
    public static byte[] GenerateInspectReportNPOI(string filePath, List<InspectedRoll_View> list)
    {
        IWorkbook workbook;
        using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read))
        {
            workbook = new XSSFWorkbook(file);
        }

        // 计算需要创建的Sheet数量
        int totalSheets = (int)Math.Ceiling(list.Count / 13.0);
        for (int i = 1; i < totalSheets; i++)
        {
            ISheet newSheet = workbook.CloneSheet(0);
            workbook.SetSheetName(i, "第" + (i + 1) + "页");
        }

        ISheet worksheet = workbook.GetSheetAt(0);

        int k = 0;
        string oldLotNo = "";
        int currentSheetIndex = 0;

        // 起始行号和起始列号
        int startRowIndex = 5; // 起始行号为5
        int startColIndex = 2; // 起始列号为2

        foreach (InspectedRoll_View item in list)
        {
            var defects = JsonSerializer.Deserialize<List<Defect>>(item.DefectsRecord);

            var defectsReport = defects.GroupBy(y => y.DefectName).Select(y => new DefectReport
            {
                DefectName = y.Key.ToString(),
                DefectScore = string.Join("+", y.Select(z => z.Score))
            }).ToList();

            //if (k == 13 || (oldLotNo != "" && item.InspectedLot != oldLotNo))
            if (k % 13 == 0 && k > 0)
            {
                k = 0;
                currentSheetIndex++;
                worksheet = workbook.GetSheetAt(currentSheetIndex);
                ClearRange(worksheet, startRowIndex + 6, startRowIndex + 29, startColIndex + 2, startColIndex + 14); // 清空 E12:Q35 范围的内容
            }

            if (k == 0)
            {
                SetCellValue(worksheet, startRowIndex, startColIndex + 2, item.OrderNo);
                SetCellValue(worksheet, startRowIndex - 1, startColIndex + 6, item.ProductName);
                SetCellValue(worksheet, startRowIndex, startColIndex + 6, item.Spec);
            }

            //记录旧的缸号
            oldLotNo = item.InspectedLot;

            // 写入每卷信息
            SetCellValue(worksheet, startRowIndex + 6, k + startColIndex + 2, item.InspectedLot + " / " + item.RollNo);
            SetCellValue(worksheet, startRowIndex + 7, k + startColIndex + 2, item.Color_view);
            SetCellValue(worksheet, startRowIndex + 8, k + startColIndex + 2, item.Yards);
            SetCellValue(worksheet, startRowIndex + 27, k + startColIndex + 2, item.TotalScore);
            SetCellValue(worksheet, startRowIndex + 28, k + startColIndex + 2, item.Score);
            SetCellValue(worksheet, startRowIndex + 29, k + startColIndex + 2, item.Grade);

            foreach (var defect in defectsReport)
            {
                //疵点索引号
                int index = Enum.GetNames(typeof(FabricDefectEnum)).ToList().IndexOf(defect.DefectName);

                //行号,将疵点索引超过15的疵点写入31行其他疵点,并记录到备注
                int row = index <= 15 ? index + startRowIndex + 11 : startRowIndex + 25;
                //列号
                int colIndex = k + startColIndex + 2;

                //需要写入疵点的单元格
                AppendToCellValue(worksheet, row, colIndex, defect.DefectScore);

                //记录到备注
                if (row == startRowIndex + 25)
                {
                    AppendToCellValue(worksheet, startRowIndex + 30, startColIndex + 2, item.InspectedLot + "/" + item.RollNo + ":" + defect.DefectName + defect.DefectScore.ToString() + "; ");
                }
            }

            k++;
        }

        using (MemoryStream stream = new MemoryStream())
        {
            workbook.Write(stream);

            byte[] excelBytes = stream.ToArray();

            return excelBytes;
        }
    }

    /// <summary>
    /// 生成出库单Excel,总入口
    /// </summary>
    /// <param name="bill"></param>
    /// <param name="is1Page">默认出库单为1长页,false:分页</param>
    /// <param name="isSubTotal">默认包含小计行</param>
    /// <returns></returns>
    public static byte[] GenOutBillExcel(ProductOutboundBill_View bill, bool is1Page = false, bool isSubTotal = false, string tenantCompanyNmae = "", string customerInfo = "")
    {
        //获取当前程序集目录
        string currentDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        //获取模板文件路径
        string templatePath = Path.Combine(currentDirectory, "Files", tenantCompanyNmae + "发货码单模板.xlsx");

        // 使用 FileStream 打开文件
        using (FileStream fileStream = new FileStream(templatePath, FileMode.Open, FileAccess.Read))
        {
            // 创建一个新的工作簿
            IWorkbook workbook = new XSSFWorkbook(templatePath);
            ISheet sheet = workbook.GetSheet("发货码单");
            ISheet sheetTotal = workbook.CreateSheet("发货单");

            if (is1Page)
            {
                ExcelHelper.WriteOutbillToSheet1Page(sheetTotal, bill, isSubTotal);
            }
            else
            {
                ExcelHelper.WriteOutbillToSheet(sheetTotal, bill);
            }

            //ExcelHelper.WriteOutRollsToExcel(sheet, bill, 10, 10, 1, 1);
            ExcelHelper.WriteOutRollsToExcelNew(sheet, bill, isMeters: false,isRollNo: true);

            // 将工作簿写入内存流
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.Write(stream);

                byte[] excelBytes = stream.ToArray();

                return excelBytes;

                //string outPath = Path.Combine(currentDirectory, "Files", $"output-{DateTime.Now.ToString("yyyy-MM-dd-HHmmss")}.xlsx");
                //// 保存或提供下载
                //File.WriteAllBytes(outPath, excelBytes);

                //// 打开生成的Excel文件
                //Process.Start(new ProcessStartInfo
                //{
                //    FileName = outPath,
                //    UseShellExecute = true // 使用外壳程序打开文件
                //});
            }

        }

    }

    /// <summary>
    /// 在1个长页面中写入出库单
    /// </summary>
    /// <param name="sheet"></param>
    /// <param name="bill"></param>
    /// <param name="isSubTotal">是否根据OrderDetailId生成小计行</param>
    public static void WriteOutbillToSheet1Page(ISheet sheet, ProductOutboundBill_View bill, bool isSubTotal = false)
    {
        int rows = bill.Details.Count;
        if (rows == 0) return;
        int contentStartRow = 6;//表格起始行,表头行
        IWorkbook workbook = sheet.Workbook;
        // 初始化数据
        var data = bill.Details;
        var groupedData = data.GroupBy(d => d.OrderDetailId)
                              .Where(g => g.Count() > 1)
                              .ToList();

        if (isSubTotal && groupedData.Count > 1)//是否需要小计
        {
            ExcelHelper.GenerateOutbillSheet(sheet, rows + groupedData.Count);//生成表格                                                                                
            // 设置合计公式
            var totalRowNo = contentStartRow + groupedData.Count + rows + 1;
            var totalRow = sheet.GetRow(totalRowNo);
            totalRow.GetCell(7).SetCellFormula($"SUMIF($D7:$D{totalRowNo},\"<>\",H7:H{totalRowNo})");
            totalRow.GetCell(8).SetCellFormula($"SUMIF($D7:$D{totalRowNo},\"<>\",I7:I{totalRowNo})");
            totalRow.GetCell(9).SetCellFormula($"SUMIF($D7:$D{totalRowNo},\"<>\",I7:I{totalRowNo})");
            // 手动刷新公式
            sheet.ForceFormulaRecalculation = true;
        }
        else
        {
            ExcelHelper.GenerateOutbillSheet(sheet, rows);//生成表格
        }

        //int sheetIndex = sheet.Workbook.GetSheetIndex(sheet.SheetName);
        //sheet.Workbook.SetPrintArea(sheetIndex, $"B1:J{pages * 27}");

        //int lastrow = sheet.LastRowNum;


        sheet.GetRow(1).GetCell(8).SetCellValue(bill.BillNo);
        sheet.GetRow(3).GetCell(8).SetCellValue(bill.CreateDate);
        sheet.GetRow(3).GetCell(2).SetCellValue(bill.Customer_view);
        sheet.GetRow(3).GetCell(5).SetCellValue(bill.Receiver_view);



        // 获取起始单元格的行和列索引
        int startRowIndex;
        int startColIndex = 2;
        int lotcount = 0;
        // 写入每页明细数据
        for (int i = 0; i < data.Count; i++)
        {
            startRowIndex = contentStartRow + 1;

            IRow row = sheet.GetRow(startRowIndex + lotcount) ?? sheet.CreateRow(startRowIndex + lotcount);
            var lot = data[i];

            row.GetCell(startColIndex + 0).SetCellValue(lot.OrderNo_view.ToString());
            row.GetCell(startColIndex + 1).SetCellValue(lot.Product_view.ToString());
            row.GetCell(startColIndex + 2).SetCellValue(lot.Spec_view != null ? lot.Spec_view.ToString() : "");
            row.GetCell(startColIndex + 3).SetCellValue(lot.Color.ToString());
            row.GetCell(startColIndex + 4).SetCellValue(lot.LotNo.ToString());
            row.GetCell(startColIndex + 5).SetCellValue(lot.Pcs);
            row.GetCell(startColIndex + 6).SetCellValue(Convert.ToDouble(lot.Weight));
            row.GetCell(startColIndex + 7).SetCellValue(Convert.ToDouble(lot.Meters));
            lotcount++;

            // 检查是否需要插入统计行
            if (isSubTotal)//是否需要小计
            {
                if ((i < data.Count - 1 && data[i].OrderDetailId != data[i + 1].OrderDetailId) || i == data.Count - 1)
                {
                    var group = groupedData.FirstOrDefault(g => g.Key == data[i].OrderDetailId);
                    if (group != null && group.Count() > 1)
                    {
                        // 检查是否是分组的最后一行
                        if (i == data.Count - 1 ||
                            data[i].OrderDetailId != data[i + 1].OrderDetailId)
                        {
                            int totalPcs = group.Sum(d => (d.Pcs));
                            double totalWeight = group.Sum(d => Convert.ToDouble(d.Weight));
                            double totalMeters = group.Sum(d => Convert.ToDouble(d.Meters));

                            // 插入统计行
                            IRow summaryRow = sheet.GetRow(startRowIndex + lotcount) ?? sheet.CreateRow(startRowIndex + lotcount);
                            summaryRow.GetCell(startColIndex + 0).SetCellValue("小计");
                            summaryRow.GetCell(startColIndex + 5).SetCellValue(totalPcs);
                            summaryRow.GetCell(startColIndex + 6).SetCellValue(totalWeight);
                            summaryRow.GetCell(startColIndex + 7).SetCellValue(totalMeters);
                            // 获取原有样式
                            ICellStyle originalStyle = summaryRow.GetCell(startColIndex + 0).CellStyle;

                            // 创建新的样式，保留原有样式属性并修改字体
                            ICellStyle italic12Style = workbook.CreateCellStyle();
                            italic12Style.CloneStyleFrom(originalStyle);
                            IFont italic12Font = workbook.CreateFont();
                            italic12Font.IsItalic = true;
                            italic12Font.FontName = "微软雅黑";
                            //italic12Font.FontHeightInPoints = 12;
                            italic12Style.SetFont(italic12Font);

                            // 应用新样式到统计行的单元格
                            summaryRow.GetCell(startColIndex + 0).CellStyle = italic12Style;
                            summaryRow.GetCell(startColIndex + 5).CellStyle = italic12Style;
                            summaryRow.GetCell(startColIndex + 6).CellStyle = italic12Style;
                            summaryRow.GetCell(startColIndex + 7).CellStyle = italic12Style;

                            lotcount++;//行号+1
                        }
                    }
                }
            }
        }

    }


    /// <summary>
    /// 写入分页生成出库单,页面为A4的二联单
    /// </summary>
    /// <param name="sheet"></param>
    /// <param name="bill"></param>
    public static void WriteOutbillToSheet(ISheet sheet, ProductOutboundBill_View bill, bool isSubTotal = false)
    {
        if (bill.Details.Count == 0) return;

        // 初始化数据
        var data = bill.Details;
        var groupedData = data.GroupBy(d => d.OrderDetailId)
                              .Where(g => g.Count() > 1)
                              .ToList();

        // 计算页数
        int totalRows = data.Count + groupedData.Count; // 每组增加一个统计行
        int pages = (int)((totalRows + 15) / 16f);

        ExcelHelper.GenerateOutbillToSheet(sheet); // 生成出库单:包含27行的表格,包含16个明细行
        ExcelHelper.CopyAndAppendRows(sheet, 0, 26, pages - 1); // 复制出需要的份数

        // 获取Sheet的索引
        int sheetIndex = sheet.Workbook.GetSheetIndex(sheet.SheetName);
        sheet.Workbook.SetPrintArea(sheetIndex, $"B:J");

        //分页
        sheet.PrintSetup.FitHeight = (short)pages;
        sheet.PrintSetup.FitWidth = 1;

        int currentPage = 0;
        int lotcount = 0;

        // 循环写入每页页头信息
        for (int j = 0; j < pages; j++)
        {
            sheet.GetRow(j * 27 + 1).GetCell(8).SetCellValue(bill.BillNo);
            sheet.GetRow(j * 27 + 3).GetCell(8).SetCellValue(bill.CreateDate);
            sheet.GetRow(j * 27 + 3).GetCell(2).SetCellValue(bill.Customer_view);
            sheet.GetRow(j * 27 + 3).GetCell(5).SetCellValue(bill.Receiver_view);
        }

        // 写入每页明细数据
        int startRowIndex;
        int startColIndex = 2;

        for (int i = 0; i < data.Count; i++)
        {
            if (lotcount >= 16)
            {
                currentPage++;
                lotcount = 0;
            }

            startRowIndex = currentPage * 27 + 6;

            IRow row = sheet.GetRow(startRowIndex + lotcount) ?? sheet.CreateRow(startRowIndex + lotcount);
            var lot = data[i];

            row.GetCell(startColIndex + 0).SetCellValue(lot.OrderNo_view.ToString());
            row.GetCell(startColIndex + 1).SetCellValue(lot.Product_view.ToString());
            row.GetCell(startColIndex + 2).SetCellValue(lot.Spec_view != null ? lot.Spec_view.ToString() : "");
            row.GetCell(startColIndex + 3).SetCellValue(lot.Color.ToString());
            row.GetCell(startColIndex + 4).SetCellValue(lot.LotNo.ToString());
            row.GetCell(startColIndex + 5).SetCellValue(lot.Pcs);
            row.GetCell(startColIndex + 6).SetCellValue(Convert.ToDouble(lot.Weight));
            row.GetCell(startColIndex + 7).SetCellValue(Convert.ToDouble(lot.Meters));
            lotcount++;

            // 检查是否需要插入按订单颜色分类小计行

            if (!isSubTotal) continue;
            if ((i < data.Count - 1 && data[i].OrderDetailId != data[i + 1].OrderDetailId) || i == data.Count - 1)
            {
                var group = groupedData.FirstOrDefault(g => g.Key == data[i].OrderDetailId);
                if (group != null && group.Count() > 1)
                {
                    // 检查是否需要换页
                    if (lotcount >= 16)
                    {
                        currentPage++;
                        lotcount = 0;
                    }
                    // 检查是否是分组的最后一行
                    if (i == data.Count - 1 ||
                        data[i].OrderDetailId != data[i + 1].OrderDetailId)
                    {
                        int totalPcs = group.Sum(d => d.Pcs);
                        double totalWeight = group.Sum(d => Convert.ToDouble(d.Weight));
                        double totalMeters = group.Sum(d => Convert.ToDouble(d.Meters));

                        // 插入统计行
                        IRow summaryRow = sheet.GetRow(startRowIndex + lotcount) ?? sheet.CreateRow(startRowIndex + lotcount);
                        summaryRow.GetCell(startColIndex + 0).SetCellValue("小计");
                        summaryRow.GetCell(startColIndex + 6).SetCellValue(totalPcs);
                        summaryRow.GetCell(startColIndex + 6).SetCellValue(totalWeight);
                        summaryRow.GetCell(startColIndex + 7).SetCellValue(totalMeters);
                        lotcount++;

                        // 移除已经处理的组
                        //groupedData.Remove(group);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 生成默认16行出库单,大小为A4的二联单
    /// </summary>
    /// <param name="sheet"></param>
    public static void GenerateOutbillToSheet(ISheet sheet)
    {
        var workbook = sheet.Workbook;
        // 设置不显示网格线
        sheet.DisplayGridlines = false;

        // 设置水平对齐方式
        ICellStyle centerborderStyle = CreateStyle(workbook, HorizontalAlignment.Center, isAddBorder: true, isShrinkToFit: true);
        ICellStyle rightborderStyle = CreateStyle(workbook, HorizontalAlignment.Right, isAddBorder: true, isShrinkToFit: true);


        // 设置行高
        for (int i = 0; i <= 26; i++)
        {
            IRow row = sheet.CreateRow(i);
            row.HeightInPoints = 16.5f;
            for (int j = 0; j <= 9; j++)
            {
                ICell cell = row.CreateCell(j);
            }
        }

        sheet.GetRow(1).HeightInPoints = 32;
        sheet.GetRow(2).HeightInPoints = 14;
        sheet.GetRow(22).HeightInPoints = 18;

        // 设置列宽
        sheet.SetColumnWidth(1, 6 * 256); // B列
        sheet.SetColumnWidth(7, 6 * 256); // H列 匹数
        for (int i = 2; i <= 5; i++) // C到E列
        {
            sheet.SetColumnWidth(i, 14 * 256);
        }

        sheet.SetColumnWidth(6, 10 * 256);
        sheet.SetColumnWidth(8, 9 * 256);
        sheet.SetColumnWidth(9, 9 * 256);

        // 设置 B7:B22 的序号
        int[] values = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
        for (int i = 0; i < values.Length; i++)
        {
            IRow row = sheet.GetRow(i + 6);
            var cell = row.GetCell(1);
            cell.SetCellValue(values[i]);
            cell.CellStyle = centerborderStyle;
        }

        IRow titleRow = sheet.GetRow(1);
        IRow title2Row = sheet.GetRow(3);
        IRow totalRow = sheet.GetRow(22);
        IRow remarkRow = sheet.GetRow(23);
        IRow addressRow = sheet.GetRow(4);



        // 设置单元格样式
        for (int i = 5; i <= 22; i++)
        {
            IRow row = sheet.GetRow(i);
            for (int j = 1; j <= 9; j++)
            {
                var cell = row.GetCell(j);
                cell.CellStyle = (j >= 6) ? rightborderStyle : centerborderStyle;

                //    cell.SetCellType(CellType.Numeric);//不用手动设置,以Double类型写入类似手工输入数字,系统默认数字格式
            }
        }



        // 设置表格标题行
        string[] headers = { "序号", "订单号", "品种", "规格", "颜色", "缸号", "匹数", "重量", "米数" };
        IRow headerRow = sheet.GetRow(5);
        for (int i = 0; i < headers.Length; i++)
        {
            ICell cell1 = headerRow.GetCell(i + 1);
            cell1.SetCellValue(headers[i]);
            cell1.CellStyle = CreateStyle(workbook, HorizontalAlignment.Center, isBold: true, fontHeightInPoints: 12, isAddBorder: true, isAddBorderColor: true);
        }

        titleRow.GetCell(1).SetCellValue("面料出库单");
        titleRow.GetCell(1).CellStyle = CreateStyle(workbook, isBold: true, fontHeightInPoints: 22);
        titleRow.GetCell(7).SetCellValue("NO:");
        titleRow.GetCell(7).CellStyle = CreateStyle(workbook, fontHeightInPoints: 12, fontColor: HSSFColor.Red.Index);
        titleRow.GetCell(8).CellStyle = CreateStyle(workbook, isItalic: true, fontHeightInPoints: 12, fontColor: HSSFColor.Red.Index);


        title2Row.GetCell(1).SetCellValue("客户:");
        title2Row.GetCell(4).SetCellValue("收货方:");
        title2Row.GetCell(7).SetCellValue("日期:");
        title2Row.GetCell(2).CellStyle = CreateStyle(workbook, isShrinkToFit: true);
        title2Row.GetCell(5).CellStyle = CreateStyle(workbook, isShrinkToFit: true);
        title2Row.GetCell(8).CellStyle = CreateStyle(workbook, isShrinkToFit: true, dataFormat: "yyyy年MM月dd日");

        addressRow.GetCell(1).SetCellValue("地址:");
        addressRow.GetCell(6).SetCellValue("联系人:");


        // 设置合计公式,设置忽略小计的数据(根据品种列小计行为空)
        totalRow.CreateCell(7).SetCellFormula("SUMIF($D7:$D22,\"<>\",H7:H22)");
        totalRow.CreateCell(8).SetCellFormula("SUMIF($D7:$D22,\"<>\",I7:I22)");
        totalRow.CreateCell(9).SetCellFormula("SUMIF($D7:$D22,\"<>\",J7:J22)");

        totalRow.GetCell(2).SetCellValue("合计");
        remarkRow.GetCell(1).SetCellValue("备注:");
        IRow bottomRow = sheet.GetRow(25);
        bottomRow.GetCell(1).SetCellValue("收货单位签字:");
        bottomRow.GetCell(4).SetCellValue("承运人签字:");
        bottomRow.GetCell(7).SetCellValue("制单:");

        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 2, 3));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 5, 6));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 8, 9));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(4, 4, 2, 5));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(4, 4, 7, 9));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 8, 9));

        ICellStyle bold12RightBorder = CreateStyle(workbook, HorizontalAlignment.Center, isBold: true, fontHeightInPoints: 12, isAddBorder: true, isAddBorderColor: true, isShrinkToFit: true);

        totalRow.GetCell(1).CellStyle = bold12RightBorder;
        totalRow.GetCell(7).CellStyle = bold12RightBorder;
        totalRow.GetCell(8).CellStyle = bold12RightBorder;
        totalRow.GetCell(9).CellStyle = bold12RightBorder;

        ICellStyle bold12 = CreateStyle(workbook, isBold: true, fontHeightInPoints: 12);
        title2Row.GetCell(1).CellStyle = bold12;
        title2Row.GetCell(4).CellStyle = bold12;
        title2Row.GetCell(7).CellStyle = bold12;

        // 设置打印区域
        sheet.PrintSetup.PaperSize = 9;
        // 设置纸张大小为A4 (int)PaperSize.A4;
        //workbook.SetPrintArea(0, 1, 10, 0, 25); // 设置打印区域为 B1:K26
        //根据sheet名称获取Index来设置打印区域
        workbook.SetPrintArea(sheet.Workbook.GetSheetIndex(sheet.SheetName), "B1:J26");

        //分页
        //sheet.PrintSetup.FitHeight = 1;
        sheet.PrintSetup.FitWidth = 1;

        //打印标题:
        //顶端标题行：Sheet.RepeatingRows = New CellRangeAddress(开始行号, 结束行号, 开始列号, 结束列号)
        //左端标题行：Sheet.RepeatingColumns = New CellRangeAddress(开始行号, 结束行号, 开始列号, 结束列号)

        //设置打印边距，数值为打印设置里的边距设置(厘米)/3
        sheet.SetMargin(MarginType.RightMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.TopMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.LeftMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.BottomMargin, (double)0.5 / 3);

        sheet.PrintSetup.HeaderMargin = 0;
        sheet.PrintSetup.FooterMargin = 0;
        sheet.HorizontallyCenter = true; // 水平居中
        sheet.VerticallyCenter = false; // 垂直居中

        sheet.PrintSetup.Landscape = false; // 设置为纵向打印
        sheet.PrintSetup.FitWidth = 1; // 自动调整宽度
    }


    /// <summary>
    /// 根据数据的行数生成出库单
    /// </summary>
    /// <param name="sheet"></param>
    /// <param name="rows"></param>
    public static void GenerateOutbillSheet(ISheet sheet, int rows)
    {
        var workbook = sheet.Workbook;
        // 设置不显示网格线
        sheet.DisplayGridlines = false;

        // 设置水平对齐方式
        ICellStyle centerborderStyle = CreateStyle(workbook, HorizontalAlignment.Center, isAddBorder: true, isShrinkToFit: true);
        ICellStyle rightborderStyle = CreateStyle(workbook, HorizontalAlignment.Right, isAddBorder: true, isShrinkToFit: true);

        int contentStartRow = 6;



        // 创建所有单元格并设置行高
        for (int i = 0; i <= rows + 10; i++)
        {
            IRow row = sheet.CreateRow(i);
            row.HeightInPoints = 16.5f;
            for (int j = 0; j <= 9; j++)
            {
                ICell cell = row.CreateCell(j);
                if (i > contentStartRow && i < contentStartRow + rows + 2 && j >= 1 && j <= 9)
                {
                    cell.CellStyle = (j > 6) ? rightborderStyle : centerborderStyle;
                }
            }
        }

        IRow titleRow = sheet.GetRow(1);
        IRow title2Row = sheet.GetRow(3);
        IRow addressRow = sheet.GetRow(4);
        IRow headerRow = sheet.GetRow(contentStartRow);

        IRow totalRow = sheet.GetRow(contentStartRow + rows + 1);
        IRow remarkRow = sheet.GetRow(contentStartRow + rows + 2);
        IRow bottomRow = sheet.GetRow(contentStartRow + rows + 3); ;

        titleRow.HeightInPoints = 32;
        totalRow.HeightInPoints = 18;
        remarkRow.HeightInPoints = 32;

        // 设置列宽
        sheet.SetColumnWidth(1, 6 * 256); // 序号

        sheet.SetColumnWidth(2, 14 * 256);//订单号
        sheet.SetColumnWidth(3, 14 * 256);//品名
        sheet.SetColumnWidth(4, 12 * 256);//规格
        sheet.SetColumnWidth(5, 12 * 256);//颜色


        sheet.SetColumnWidth(6, 9 * 256);//缸号
        sheet.SetColumnWidth(7, 6 * 256);// 匹数
        sheet.SetColumnWidth(8, 9 * 256);//重量
        sheet.SetColumnWidth(9, 9 * 256);//米数

        // 设置序号
        for (int i = 1; i <= rows; i++)
        {
            var cell = sheet.GetRow(i + contentStartRow).GetCell(1);
            cell.SetCellValue(i);
            cell.CellStyle = centerborderStyle;
        }


        // 设置表格标题行
        string[] headers = { "序号", "订单号", "品种", "规格", "颜色", "缸号", "匹数", "重量", "米数" };

        for (int i = 0; i < headers.Length; i++)
        {
            ICell cell1 = headerRow.GetCell(i + 1);
            cell1.SetCellValue(headers[i]);
            cell1.CellStyle = CreateStyle(workbook, HorizontalAlignment.Center, isBold: true, fontHeightInPoints: 12, isAddBorder: true, isAddBorderColor: true);
        }

        //设置页头
        titleRow.GetCell(1).SetCellValue("面料出库单");
        titleRow.GetCell(1).CellStyle = CreateStyle(workbook, isBold: true, fontHeightInPoints: 22);
        titleRow.GetCell(7).SetCellValue("NO:");
        titleRow.GetCell(7).CellStyle = CreateStyle(workbook, fontHeightInPoints: 12, fontColor: HSSFColor.Red.Index);
        titleRow.GetCell(8).CellStyle = CreateStyle(workbook, isItalic: true, fontHeightInPoints: 12, fontColor: HSSFColor.Red.Index);


        title2Row.GetCell(1).SetCellValue("客户:");
        title2Row.GetCell(4).SetCellValue("收货方:");
        title2Row.GetCell(7).SetCellValue("日期:");
        title2Row.GetCell(2).CellStyle = CreateStyle(workbook, isShrinkToFit: true);
        title2Row.GetCell(5).CellStyle = CreateStyle(workbook, isShrinkToFit: true);
        title2Row.GetCell(8).CellStyle = CreateStyle(workbook, isShrinkToFit: true, dataFormat: "yyyy年MM月dd日");

        addressRow.GetCell(1).SetCellValue("联系人:");
        addressRow.GetCell(4).SetCellValue("地址:");

        //设置页头信息行样式(客户-日期等)
        ICellStyle bold12 = CreateStyle(workbook, isBold: true, fontHeightInPoints: 12, isShrinkToFit: true);
        title2Row.GetCell(1).CellStyle = bold12;
        title2Row.GetCell(4).CellStyle = bold12;
        title2Row.GetCell(7).CellStyle = bold12;
        addressRow.GetCell(1).CellStyle = bold12;
        addressRow.GetCell(4).CellStyle = bold12;

        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 2, 3));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 5, 6));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 8, 9));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(4, 4, 2, 3));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(4, 4, 5, 9));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 8, 9));

        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(remarkRow.RowNum, remarkRow.RowNum, 2, 9));

        ICellStyle wrapText = CreateStyle(workbook, isWrap: true);
        remarkRow.GetCell(2).CellStyle = wrapText;

        // 设置合计公式
        totalRow.CreateCell(7).SetCellFormula($"SUM(H7:H{contentStartRow + rows + 1})");
        totalRow.CreateCell(8).SetCellFormula($"SUM(I7:I{contentStartRow + rows + 1})");
        totalRow.CreateCell(9).SetCellFormula($"SUM(J7:J{contentStartRow + rows + 1})");


        //设置页尾
        totalRow.GetCell(1).SetCellValue("合计");
        remarkRow.GetCell(1).SetCellValue("备注:");
        bottomRow.GetCell(1).SetCellValue("收货单位签字:");
        bottomRow.GetCell(4).SetCellValue("承运人签字:");
        bottomRow.GetCell(7).SetCellValue("制单:");


        //设置统计行样式
        ICellStyle bold12RightBorder = CreateStyle(workbook, HorizontalAlignment.Center, isBold: true, fontHeightInPoints: 12, isAddBorder: true, isAddBorderColor: true, isShrinkToFit: true);
        totalRow.GetCell(1).CellStyle = bold12RightBorder;
        totalRow.GetCell(7).CellStyle = bold12RightBorder;
        totalRow.GetCell(8).CellStyle = bold12RightBorder;
        totalRow.GetCell(9).CellStyle = bold12RightBorder;



        // 设置打印区域
        sheet.PrintSetup.PaperSize = 9; // 设置纸张大小为A4 (int)PaperSize.A4;
                                        //workbook.SetPrintArea(0, 1, 10, 0, 25); // 设置打印区域为 B1:K26
                                        // 获取Sheet的索引
        int sheetIndex = sheet.Workbook.GetSheetIndex(sheet.SheetName);
        sheet.Workbook.SetPrintArea(sheetIndex, $"B1:J{rows + 10}");

        //打印标题:
        //顶端标题行：Sheet.RepeatingRows = New CellRangeAddress(开始行号, 结束行号, 开始列号, 结束列号)
        //左端标题行：Sheet.RepeatingColumns = New CellRangeAddress(开始行号, 结束行号, 开始列号, 结束列号)

        //设置打印边距，数值为打印设置里的边距设置(厘米)/3
        sheet.SetMargin(MarginType.RightMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.TopMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.LeftMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.BottomMargin, (double)0.5 / 3);

        sheet.PrintSetup.HeaderMargin = 0;
        sheet.PrintSetup.FooterMargin = 0;
        sheet.HorizontallyCenter = true; // 水平居中
        sheet.VerticallyCenter = false; // 垂直居中

        sheet.PrintSetup.Landscape = false; // 设置为纵向打印
        sheet.PrintSetup.FitWidth = 1; // 自动调整宽度
    }

    /// <summary>
    /// 生成出库单xlsx文件
    /// </summary>
    /// <param name="bill">出库单对象(带明细)</param>
    /// <returns></returns>
    public static byte[] GenerateStockOutBillFile(ProductOutboundBill_View bill)
    {
        // 创建一个新的工作簿
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("出库单");



        // 设置不显示网格线
        sheet.DisplayGridlines = false;

        // 设置水平对齐方式
        ICellStyle centerborderStyle = CreateStyle(workbook, HorizontalAlignment.Center, isAddBorder: true, isShrinkToFit: true);
        ICellStyle rightborderStyle = CreateStyle(workbook, HorizontalAlignment.Right, isAddBorder: true, isShrinkToFit: true);


        // 设置行高
        for (int i = 0; i <= 26; i++)
        {
            IRow row = sheet.CreateRow(i);
            row.HeightInPoints = 16.5f;
            for (int j = 0; j <= 9; j++)
            {
                ICell cell = row.CreateCell(j);
            }
        }

        sheet.GetRow(1).HeightInPoints = 32;
        sheet.GetRow(2).HeightInPoints = 14;
        sheet.GetRow(22).HeightInPoints = 18;

        // 设置列宽
        sheet.SetColumnWidth(1, 6 * 256); // B列
        sheet.SetColumnWidth(7, 6 * 256); // I列
        for (int i = 2; i <= 5; i++) // C到E列
        {
            sheet.SetColumnWidth(i, 14 * 256);
        }

        sheet.SetColumnWidth(6, 10 * 256);
        sheet.SetColumnWidth(8, 10 * 256);
        sheet.SetColumnWidth(9, 10 * 256);

        // 设置 B7:B22 的序号
        int[] values = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
        for (int i = 0; i < values.Length; i++)
        {
            IRow row = sheet.GetRow(i + 6);
            var cell = row.GetCell(1) ?? row.CreateCell(1);
            cell.SetCellValue(values[i]);
            cell.CellStyle = centerborderStyle;
        }

        IRow titleRow = sheet.GetRow(1);
        IRow title2Row = sheet.GetRow(3);
        IRow totalRow = sheet.GetRow(22);
        IRow remarkRow = sheet.GetRow(23);
        IRow addressRow = sheet.GetRow(4);



        // 设置单元格样式
        for (int i = 5; i <= 22; i++)
        {
            IRow row = sheet.GetRow(i);
            for (int j = 1; j <= 9; j++)
            {
                var cell = row.GetCell(j);
                cell.CellStyle = (j >= 6) ? rightborderStyle : centerborderStyle;
                //if (j >= 6)
                //{
                //    //cell.CellStyle = rightborderStyle;
                //    cell.SetCellType(CellType.Numeric);//不用手动设置,以Double类型写入类似手工输入数字,系统默认数字格式
                //}
            }
        }

        // 设置合计公式
        totalRow.CreateCell(7).SetCellFormula("SUM(H7:H22)");
        totalRow.CreateCell(8).SetCellFormula("SUM(I7:I22)");
        totalRow.CreateCell(9).SetCellFormula("SUM(J7:J22)");

        // 设置表格标题行
        string[] headers = { "序号", "订单号", "品种", "规格", "颜色", "缸号", "匹数", "重量", "米数" };
        IRow headerRow = sheet.GetRow(5);
        for (int i = 0; i < headers.Length; i++)
        {
            ICell cell1 = headerRow.GetCell(i + 1);
            cell1.SetCellValue(headers[i]);
            cell1.CellStyle = CreateStyle(workbook, HorizontalAlignment.Center, isBold: true, fontHeightInPoints: 12, isAddBorder: true, isAddBorderColor: true);
        }

        titleRow.GetCell(1).SetCellValue("面料出库单");
        titleRow.GetCell(1).CellStyle = CreateStyle(workbook, isBold: true, fontHeightInPoints: 22);
        titleRow.GetCell(7).SetCellValue("NO:");
        titleRow.GetCell(7).CellStyle = CreateStyle(workbook, fontHeightInPoints: 12, fontColor: HSSFColor.Red.Index);
        titleRow.GetCell(8).CellStyle = CreateStyle(workbook, isItalic: true, fontHeightInPoints: 12, fontColor: HSSFColor.Red.Index);


        title2Row.GetCell(1).SetCellValue("客户:");
        title2Row.GetCell(4).SetCellValue("收货方:");
        title2Row.GetCell(7).SetCellValue("日期:");
        title2Row.GetCell(2).CellStyle = CreateStyle(workbook, isShrinkToFit: true, dataFormat: "yyyy年MM月dd日");
        title2Row.GetCell(5).CellStyle = CreateStyle(workbook, isShrinkToFit: true, dataFormat: "yyyy年MM月dd日");
        title2Row.GetCell(8).CellStyle = CreateStyle(workbook, isShrinkToFit: true, dataFormat: "yyyy年MM月dd日");
        totalRow.GetCell(1).SetCellValue("合计");
        remarkRow.GetCell(1).SetCellValue("备注:");
        addressRow.GetCell(1).SetCellValue("地址:");
        addressRow.GetCell(6).SetCellValue("联系人:");

        IRow bottomRow = sheet.GetRow(25);
        bottomRow.GetCell(1).SetCellValue("收货单位签字:");
        bottomRow.GetCell(4).SetCellValue("承运人签字:");
        bottomRow.GetCell(7).SetCellValue("制单:");

        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 2, 3));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 5, 6));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(3, 3, 8, 9));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(4, 4, 2, 5));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(4, 4, 7, 9));
        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 8, 9));

        ICellStyle bold12RightBorder = CreateStyle(workbook, HorizontalAlignment.Center, isBold: true, fontHeightInPoints: 12, isAddBorder: true, isAddBorderColor: true, isShrinkToFit: true);

        totalRow.GetCell(1).CellStyle = bold12RightBorder;
        totalRow.GetCell(7).CellStyle = bold12RightBorder;
        totalRow.GetCell(8).CellStyle = bold12RightBorder;
        totalRow.GetCell(9).CellStyle = bold12RightBorder;

        ICellStyle bold12 = CreateStyle(workbook, isBold: true, fontHeightInPoints: 12);
        title2Row.GetCell(1).CellStyle = bold12;
        title2Row.GetCell(4).CellStyle = bold12;
        title2Row.GetCell(7).CellStyle = bold12;

        // 设置打印区域
        sheet.PrintSetup.PaperSize = 9; // 设置纸张大小为A4 (int)PaperSize.A4;
        //workbook.SetPrintArea(0, 1, 10, 0, 25); // 设置打印区域为 B1:K26
        workbook.SetPrintArea(0, "B1:J26");

        //打印标题:
        //顶端标题行：Sheet.RepeatingRows = New CellRangeAddress(开始行号, 结束行号, 开始列号, 结束列号)
        //左端标题行：Sheet.RepeatingColumns = New CellRangeAddress(开始行号, 结束行号, 开始列号, 结束列号)

        //设置打印边距，数值为打印设置里的边距设置(厘米)/3
        sheet.SetMargin(MarginType.RightMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.TopMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.LeftMargin, (double)0.5 / 3);
        sheet.SetMargin(MarginType.BottomMargin, (double)0.5 / 3);

        sheet.PrintSetup.HeaderMargin = 0;
        sheet.PrintSetup.FooterMargin = 0;
        sheet.HorizontallyCenter = true; // 水平居中
        sheet.VerticallyCenter = false; // 垂直居中

        sheet.PrintSetup.Landscape = false; // 设置为纵向打印
        sheet.PrintSetup.FitWidth = 1; // 自动调整宽度

        // 保存工作簿到文件
        //using (FileStream fs = new FileStream("output.xlsx", FileMode.Create, FileAccess.Write))
        //{
        //    workbook.Write(fs);
        //}

        WriteOutbillToExcel(sheet, bill);

        // 将工作簿写入内存流
        using (MemoryStream stream = new MemoryStream())
        {
            workbook.Write(stream);
            byte[] excelBytes = stream.ToArray();

            return excelBytes;
            // 保存或提供下载
            //File.WriteAllBytes("output1.xlsx", excelBytes);
        }
    }

    public static byte[] GenerateOutRollsFile(ProductOutboundBill_View bill)
    {
        //获取当前程序集目录
        string currentDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        //获取模板文件路径
        string templatePath = Path.Combine(currentDirectory, "Files", "梭织发货码单模板.xlsx");

        // 使用 FileStream 打开文件
        using (FileStream fileStream = new FileStream(templatePath, FileMode.Open, FileAccess.Read))
        {
            // 创建一个新的工作簿
            IWorkbook workbook = new XSSFWorkbook(templatePath);
            ISheet sheet = workbook.GetSheet("发货码单");

            WriteOutRollsToExcel(sheet, bill, 10, 10, 1, 1);

            // 将工作簿写入内存流
            using (MemoryStream stream = new MemoryStream())
            {
                workbook.Write(stream);

                byte[] excelBytes = stream.ToArray();

                return excelBytes;
                // 保存或提供下载
                //File.WriteAllBytes("output1.xlsx", excelBytes);
            }

        }
    }


    /// <summary>
    /// 样式设置封装类
    /// </summary>
    /// <param name="workbook"></param>
    /// <param name="hAlignment"></param>
    /// <param name="vAlignment"></param>
    /// <param name="fontHeightInPoints"></param>
    /// <param name="isAddBorder"></param>
    /// <param name="isBold"></param>
    /// <param name="fontName"></param>
    /// <param name="isAddBorderColor"></param>
    /// <param name="isItalic"></param>
    /// <param name="isLineFeed"></param>
    /// <param name="isAddCellBackground"></param>
    /// <param name="fillPattern"></param>
    /// <param name="cellBackgroundColor"></param>
    /// <param name="fontColor"></param>
    /// <param name="underlineStyle"></param>
    /// <param name="typeOffset"></param>
    /// <param name="dataFormat"></param>
    /// <param name="isWrap"></param>
    /// <param name="isStrikeout"></param>
    /// <param name="isShrinkToFit"></param>
    /// <returns></returns>
    public static XSSFCellStyle CreateStyle(IWorkbook workbook, HorizontalAlignment hAlignment = HorizontalAlignment.Left, VerticalAlignment vAlignment = VerticalAlignment.Center, short fontHeightInPoints = 11,
    bool isAddBorder = false, bool isBold = false, string fontName = "微软雅黑",
    bool isAddBorderColor = true, bool isItalic = false, bool isLineFeed = false,
    bool isAddCellBackground = false, FillPattern fillPattern = FillPattern.NoFill,
    short cellBackgroundColor = HSSFColor.Yellow.Index, short fontColor = HSSFColor.Black.Index,
    FontUnderlineType underlineStyle = FontUnderlineType.None,
    FontSuperScript typeOffset = FontSuperScript.None, string dataFormat = null, bool isWrap = false,
    bool isStrikeout = false, bool isShrinkToFit = false)
    {
        XSSFCellStyle cellStyle = (XSSFCellStyle)workbook.CreateCellStyle(); //创建列头单元格实例样式

        cellStyle.WrapText = isWrap;
        cellStyle.Alignment = hAlignment; //水平居中
        cellStyle.VerticalAlignment = vAlignment; //垂直居中
                                                  //cellStyle.WrapText = isLineFeed;//自动换行
        cellStyle.ShrinkToFit = isShrinkToFit;//缩放字体适应单元格大小
                                              //背景颜色，边框颜色，字体颜色都是使用 HSSFColor属性中的对应调色板索引

        //TODO：引用了NPOI后可通过ICellStyle 接口的 FillForegroundColor 属性实现 Excel 单元格的背景色设置，FillPattern 为单元格背景色的填充样式

        //TODO:十分注意，要设置单元格背景色必须是FillForegroundColor和FillPattern两个属性同时设置，否则是不会显示背景颜色
        if (isAddCellBackground)
        {
            cellStyle.FillForegroundColor = cellBackgroundColor;//单元格背景颜色
            cellStyle.FillPattern = fillPattern;//填充图案样式(FineDots 细点，SolidForeground立体前景)
        }


        //是否增加边框
        if (isAddBorder)
        {
            //常用的边框样式 None(没有),Thin(细边框，瘦的),Medium(中等),Dashed(虚线),Dotted(星罗棋布的),Thick(厚的),Double(双倍),Hair(头发)[上右下左顺序设置]
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.BorderLeft = BorderStyle.Thin;
        }

        //是否设置边框颜色
        if (isAddBorderColor)
        {
            //边框颜色[上右下左顺序设置]
            cellStyle.TopBorderColor = IndexedColors.Grey25Percent.Index;
            cellStyle.BottomBorderColor = IndexedColors.Grey25Percent.Index;
            cellStyle.LeftBorderColor = IndexedColors.Grey25Percent.Index;
            cellStyle.RightBorderColor = IndexedColors.Grey25Percent.Index;
        }


        //设置相关字体样式

        var cellStyleFont = (XSSFFont)workbook.CreateFont(); //创建字体

        //假如字体大小只需要是粗体的话直接使用下面该属性即可
        cellStyleFont.IsBold = isBold;
        cellStyleFont.FontHeightInPoints = fontHeightInPoints; //字体大小
        cellStyleFont.FontName = fontName;//字体（仿宋，楷体，宋体 ）
        cellStyleFont.Color = fontColor;//设置字体颜色
        cellStyleFont.IsItalic = isItalic;//是否将文字变为斜体
        cellStyleFont.Underline = underlineStyle;//字体下划线
        cellStyleFont.TypeOffset = typeOffset;//字体上标下标
        cellStyleFont.IsStrikeout = isStrikeout;//是否有删除线

        cellStyle.SetFont(cellStyleFont); //将字体绑定到样式

        // 设置单元格格式
        if (!string.IsNullOrEmpty(dataFormat))
        {
            IDataFormat format = workbook.CreateDataFormat();
            cellStyle.DataFormat = format.GetFormat(dataFormat);
        }

        return cellStyle;
    }


    /// <summary>
    /// 写入出库单到sheet
    /// </summary>
    /// <param name="sheet"></param>
    /// <param name="bill"></param>
    private static void WriteOutbillToExcel(ISheet sheet, ProductOutboundBill_View bill)
    {
        sheet.GetRow(1).GetCell(8).SetCellValue(bill.BillNo);
        sheet.GetRow(3).GetCell(8).SetCellValue(bill.CreateDate);
        sheet.GetRow(3).GetCell(2).SetCellValue(bill.Customer_view);
        sheet.GetRow(3).GetCell(5).SetCellValue(bill.Receiver_view);

        // 获取起始单元格的行和列索引
        int startRowIndex = 6;
        int startColIndex = 2;
        var data = bill.Details;
        // 写入数据
        for (int i = 0; i < data.Count; i++)
        {
            IRow row = sheet.GetRow(startRowIndex + i) ?? sheet.CreateRow(startRowIndex + i);
            var item = data[i];

            row.GetCell(startColIndex + 0).SetCellValue(item.OrderNo_view.ToString());
            row.GetCell(startColIndex + 1).SetCellValue(item.Product_view.ToString());
            row.GetCell(startColIndex + 2).SetCellValue(item.Spec_view != null ? item.Spec_view.ToString() : "");
            row.GetCell(startColIndex + 3).SetCellValue(item.Color.ToString());
            row.GetCell(startColIndex + 4).SetCellValue(item.LotNo.ToString());
            row.GetCell(startColIndex + 5).SetCellValue(item.Pcs);
            row.GetCell(startColIndex + 6).SetCellValue(Convert.ToDouble(item.Weight));
            row.GetCell(startColIndex + 7).SetCellValue(Convert.ToDouble(item.Meters));
        }
    }


    /// <summary>
    /// 写入梭织明细码单,单列米数或重量
    /// </summary>
    /// <param name="sheet"></param>
    /// <param name="bill"></param>
    /// <param name="maxRow"></param>
    /// <param name="maxCol"></param>
    /// <param name="startRowIndex"></param>
    /// <param name="startColIndex"></param>
    private static void WriteOutRollsToExcel(ISheet sheet, ProductOutboundBill_View bill, int maxRow, int maxCol, int startRowIndex, int startColIndex)
    {
        int currentPage = 0;
        List<ProductOutboundLot_View> dataList = bill.Details;//.OrderBy(x => x.OrderNo_view).ThenBy(x => x.Color).ThenBy(x => x.LotNo).ToList();

        //计算需占用总列数
        int totalCol = dataList.Sum(x => (int)((x.RollList.Count + 9) / maxRow));
        //计算总页数
        int totalPages = (int)Math.Ceiling((double)totalCol / maxCol);

        var distinctOrder = dataList.Select(x => x.OrderNo_view).Distinct().ToList();

        int distinctOrderCount = distinctOrder.Count();//订单数

        //复制码单页
        CopyAndAppendRows(sheet, 0, 24, totalPages - 1);
        //应该在一开始就将需要的页数全部复制出来,而不是到循环中去
        //进入循环之后在复制就是有数据的了,导致错误,调试1天时间


        //循环每个订单
        for (int i = 0; i < distinctOrderCount; i++)
        {
            //当前订单列表
            var list = dataList.Where(x => x.OrderNo_view == distinctOrder[i]).ToList();
            int listCount = list.Count();//缸数

            if (listCount == 0) continue;//订单无数据则跳过

            int colCount = list.Sum(x => (int)((x.RollList.Count + 9) / maxRow));//需占列数

            //需要页数
            //int pageCount = (int)Math.Ceiling((double)colCount / maxCol);

            WriteBillInfoToPerPage(sheet, bill, startRowIndex, startColIndex, list, currentPage);

            int currentcol = 1;//当前列

            //循环每个缸号
            for (int j = 0; j < listCount; j++)
            {
                //int lastRowIndex = sheet.LastRowNum;
                var lot = list[j];
                var n = lot.LotNo;
                var od = lot.OrderNo_view;
                int rollCount = lot.RollList.Count;//卷数

                //需要占用多少列
                int colsCount = (int)Math.Ceiling((double)rollCount / maxRow);

                //当前页剩余列数不够列数时,写到下一页
                if (currentcol + colsCount - 1 > maxCol)
                {
                    currentPage++;
                    startRowIndex += 25;
                    WriteBillInfoToPerPage(sheet, bill, startRowIndex, startColIndex, list, currentPage);
                    currentcol = 1;
                }

                int lotStartCol = currentcol + startColIndex;
                int lotEndCol = lotStartCol + colsCount - 1;

                int rowIndex = 0;
                int colorRow = startRowIndex + 6;
                int lotNoRow = startRowIndex + 7;
                int rollStartRow = startRowIndex + 8;//卷号起始行

                //写入颜色缸号
                for (int col = lotStartCol; col <= lotEndCol; col++)
                {
                    sheet.GetRow(colorRow).GetCell(col).SetCellValue(lot.Color);
                    sheet.GetRow(startRowIndex + 7).GetCell(col).SetCellValue(lot.LotNo);
                }

                //合并单元格,设置公式
                if (colsCount < 2)
                {
                    string formulaAddress = new CellRangeAddress(rollStartRow, startRowIndex + 17, lotStartCol, lotStartCol).FormatAsString();
                    sheet.GetRow(startRowIndex + 18).GetCell(lotStartCol).SetCellFormula($"Count({formulaAddress})");
                    sheet.GetRow(startRowIndex + 19).GetCell(lotStartCol).SetCellFormula($"SUM({formulaAddress})");
                }
                else
                {
                    //合并单元格:合并颜色缸号
                    sheet.AddMergedRegion(new CellRangeAddress(colorRow, colorRow, lotStartCol, lotEndCol));
                    sheet.AddMergedRegion(new CellRangeAddress(lotNoRow, lotNoRow, lotStartCol, lotEndCol));
                    //获取需要计算的单元格区域
                    string formulaAddress = new CellRangeAddress(rollStartRow, startRowIndex + 17, lotStartCol, lotEndCol).FormatAsString();
                    //设置公式
                    sheet.GetRow(startRowIndex + 18).GetCell(lotStartCol).SetCellFormula($"Count({formulaAddress})");
                    sheet.GetRow(startRowIndex + 19).GetCell(lotStartCol).SetCellFormula($"SUM({formulaAddress})");
                    //合并小计
                    sheet.AddMergedRegion(new CellRangeAddress(startRowIndex + 18, startRowIndex + 18, lotStartCol, lotEndCol));
                    sheet.AddMergedRegion(new CellRangeAddress(startRowIndex + 19, startRowIndex + 19, lotStartCol, lotEndCol));

                    //原先的公式在被覆盖设置时生成的文件打开报错:清除部分公式后才能打开
                    //var s = sheet.GetRow(startRowIndex + 18).GetCell(c);
                    //var s = sheet.GetRow(startRowIndex + 18).GetCell(c).CellFormula;
                    //sheet.GetRow(startRowIndex + 18).GetCell(c).SetCellFormula(null);
                    //}
                }

                //循环每个卷号
                for (int k = 0; k < lot.RollList.Count; k++)
                {
                    if (k != 0 && k % 10 == 0)
                    {
                        currentcol++;//换列
                        rowIndex = 0;
                    }

                    var roll = lot.RollList[k];

                    try
                    {
                        var cell = sheet.GetRow(rowIndex + rollStartRow).GetCell(currentcol + startColIndex);

                        //写入米数
                        cell.SetCellValue((double)roll.Meters);
                        //写入卷号
                        //cell.SetCellValue(roll.RollNo);
                        //写入重量
                        //cell.SetCellValue((double)roll.Weight);
                        //写入码数
                        //cell.SetCellValue((double)roll.Yards);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message + $"{rowIndex + rollStartRow}" + ":" + $"{currentcol + startColIndex}");
                    }
                    rowIndex++;
                }
                currentcol++;//每个缸号结束换列
            }

            currentPage++;//每个订单结束换页
            startRowIndex += 25;
        }

        // 手动刷新公式
        sheet.ForceFormulaRecalculation = true;
    }


    /// <summary>
    /// 将出库单数据写入到Excel的sheet中，按订单号分组写入到多个模板页
    /// 明细只有重量和长度两列,没有卷号
    /// </summary>
    /// <param name="sheet">Excel工作表</param>
    /// <param name="bill">出库单数据</param>
    /// <param name="maxLot">每页最大组数，默认6组</param>
    /// <param name="maxRow">每组最大行数，默认10行</param>
    private static void WriteOutRollsToExcelNew(ISheet sheet, ProductOutboundBill_View bill, bool isMeters = true, int maxLot = 6, int maxRow = 10, bool isRollNo = true)
    {
        if (bill.Details == null || bill.Details.Count == 0) return;

        // 按订单号分组
        var dataList = bill.Details;
        var distinctOrder = dataList.Select(x => x.OrderNo_view).Distinct().ToList();

        //设置码单长度显示单位
        //TODO:根据实际情况选择设置单位方式
        var firstUnitCell = sheet.GetRow(0).GetCell(0) ?? sheet.GetRow(0).CreateCell(0);
        firstUnitCell.SetCellValue(isMeters ? "米数" : "码数");

        //自动统计米数,如果米数为0则显示码数,否则显示米数
        var meters=dataList.Sum(y => y.Meters);
        if (meters == 0)
        {
            firstUnitCell.SetCellValue("码数");
        }
        else
        {
            firstUnitCell.SetCellValue("米数");
        }


            // 计算每个订单需要的组数和总页数
            int totalPages = 0;
        var orderPages = new Dictionary<string, int>();
        foreach (var orderNo in distinctOrder)
        {
            var orderLots = dataList.Where(x => x.OrderNo_view == orderNo).ToList();
            int totalGroups = orderLots.Sum(lot => (int)Math.Ceiling((double)lot.Pcs / maxRow));
            int pages = (int)Math.Ceiling((double)totalGroups / maxLot);
            orderPages[orderNo] = pages;
            totalPages += pages;
        }

        // 复制模板页
        if (totalPages > 1)
        {
            CopyAndAppendRows(sheet, 0, 24, totalPages - 1);
            //workbook.SetPrintArea(0, 1, 10, 0, 25); // 两栏设置打印区域为 B1:J26
            //根据sheet名称获取Index来设置打印区域,三栏有卷号
            sheet.Workbook.SetPrintArea(sheet.Workbook.GetSheetIndex(sheet.SheetName), $"B1:T{totalPages*25}");
        }



        // 当前页索引和起始行
        int currentPage = 0;
        int startRowIndex = 0;
        

        // 按订单号循环处理数据
        foreach (var orderNo in distinctOrder)
        {
            var orderLots = dataList.Where(x => x.OrderNo_view == orderNo).ToList();
            int currentLot = 0;

            // 写入订单信息
            var firstLot = orderLots.First();

            // 在新页写入订单信息
            WriteOrderInfoToSheet(sheet, bill, isRollNo, startRowIndex, firstLot);

            foreach (var lot in orderLots)
            {
                // 计算当前批次需要的组数
                int groupsNeeded = (int)Math.Ceiling((double)lot.RollList.Count / maxRow);

                lot.RollList= lot.RollList.OrderBy(x => x.RollNo).ToList();

                // 检查是否需要新页
                if (currentLot + groupsNeeded > maxLot)
                {
                    currentPage++;
                    startRowIndex += 25;
                    currentLot = 0;

                    // 在新页写入订单信息
                    WriteOrderInfoToSheet(sheet, bill, isRollNo, startRowIndex, firstLot);
                }


                // 写入每缸数据
                for (int g = 0; g < groupsNeeded; g++)
                {

                    int colGroupCount = isRollNo ? 3 : 2;
                    int startCol = 2 + currentLot * colGroupCount; // 每组占用2列，从C列开始

                    // 写入颜色和缸号
                    sheet.GetRow(startRowIndex + 6).GetCell(startCol).SetCellValue(lot.Color);
                    sheet.GetRow(startRowIndex + 7).GetCell(startCol).SetCellValue(lot.LotNo);


                    int rowOffset = 0;
                    // 写入卷序号
                    int startRollIndex = g * maxRow;
                    int endRollIndex = Math.Min(startRollIndex + maxRow, lot.RollList.Count);

                    for (int k = 0 + g * maxRow; k < endRollIndex; k++)
                    {

                        var roll = lot.RollList[k];
                        var row = sheet.GetRow(startRowIndex + 9 + rowOffset) ?? sheet.CreateRow(startRowIndex + 9 + rowOffset);


                        ICell IndexCell;
                        ICell weightCell;
                        ICell lenthCell;

                        if (isRollNo)
                        {

                            IndexCell = row.GetCell(startCol) ?? row.CreateCell(startCol);
                            weightCell = row.GetCell(startCol + 1) ?? row.CreateCell(startCol + 1);
                            lenthCell = row.GetCell(startCol + 2) ?? row.CreateCell(startCol + 2);
                            IndexCell.SetCellValue(roll.RollNo);
                        }
                        else
                        {
                            weightCell = row.GetCell(startCol ) ?? row.CreateCell(startCol);
                            lenthCell = row.GetCell(startCol + 1) ?? row.CreateCell(startCol + 1);
                        }
                        weightCell.SetCellValue(Convert.ToDouble(roll.Weight));

                        lenthCell.SetCellValue(isMeters ? Convert.ToDouble(roll.Meters) : Convert.ToDouble(roll.Yards));


                        rowOffset++;
                    }


                    currentLot++;
                }
            }

            // 移至下一页
            if (currentPage < totalPages - 1)
            {
                currentPage++;
                startRowIndex += 25;
                currentLot = 0;
            }
            //设置打印区域

            // 手动刷新公式
            sheet.ForceFormulaRecalculation = true;
            sheet.ProtectSheet("");
        }

        
    }

    private static void WriteOrderInfoToSheet(ISheet sheet, ProductOutboundBill_View bill, bool isRollNo, int startRowIndex, ProductOutboundLot_View firstLot)
    {
        if (!isRollNo)
        {
            sheet.GetRow(startRowIndex + 3).GetCell(2).SetCellValue(firstLot.OrderNo_view);
            sheet.GetRow(startRowIndex + 4).GetCell(2).SetCellValue(firstLot.Product_view);
            sheet.GetRow(startRowIndex + 4).GetCell(7).SetCellValue(firstLot.Spec_view);
            sheet.GetRow(startRowIndex + 4).GetCell(11).SetCellValue(bill.CreateDate);
            sheet.GetRow(startRowIndex + 0).GetCell(11).SetCellValue(bill.BillNo);
        }
        else
        {
            sheet.GetRow(startRowIndex + 3).GetCell(2).SetCellValue(firstLot.OrderNo_view);
            sheet.GetRow(startRowIndex + 4).GetCell(2).SetCellValue(firstLot.Product_view);
            sheet.GetRow(startRowIndex + 4).GetCell(10).SetCellValue(firstLot.Spec_view);
            sheet.GetRow(startRowIndex + 4).GetCell(16).SetCellValue(bill.CreateDate);
            sheet.GetRow(startRowIndex + 0).GetCell(16).SetCellValue(bill.BillNo);
        }
    }

    private static void WriteBillInfoToPerPage(ISheet sheet, ProductOutboundBill_View bill, int startRowIndex, int startColIndex, List<ProductOutboundLot_View> list, int currentPage)
    {
        int no = currentPage + 1;
        // 写入编号
        sheet.GetRow(startRowIndex + 1).GetCell(startColIndex + 9).SetCellValue(bill.BillNo + $"-{no}");

        // 写入订单号
        sheet.GetRow(startRowIndex + 3).GetCell(startColIndex + 1).SetCellValue(list[0].OrderNo_view);
        //写入日期
        sheet.GetRow(startRowIndex + 3).GetCell(startColIndex + 9).SetCellValue(bill.CreateDate);


        // 写入品种
        sheet.GetRow(startRowIndex + 4).GetCell(startColIndex + 1).SetCellValue(list[0].Product_view);
        // 写入规格
        //var cell=sheet.GetRow(startRowIndex + 4).GetCell(startColIndex + 5);
        if (list[0].Spec_view is not null)
        {
            sheet.GetRow(startRowIndex + 4).GetCell(startColIndex + 5).SetCellValue(list[0].Spec_view);
        }
        //写入款号
        //sheet.GetRow(startRowIndex+4).GetCell(startColIndex+9).SetCellValue(lot.StyleNo_view);
    }

    //TODO:转换成DataTable
    private static void ListToDataTable(List<ProductOutboundLot_View> list, int maxRow, int maxCol)
    {
        var result = new List<DataTable>();
        int totalCol = list.Sum(x => (int)((x.RollList.Count + 9) / maxRow));
        int pages = (int)Math.Ceiling(totalCol / (double)maxCol);

        for (int i = 0; i < pages; i++)
        {

        }

    }

    public static void ClearRange(ISheet sheet, int startRow, int endRow, int startCol, int endCol)
    {
        for (int i = startRow; i <= endRow; i++)
        {
            IRow row = sheet.GetRow(i) ?? sheet.CreateRow(i);
            for (int j = startCol; j <= endCol; j++)
            {
                ICell cell = row.GetCell(j) ?? row.CreateCell(j);
                cell.SetCellValue("");
            }
        }
    }

    public static void AppendToCellValue(ISheet sheet, int rowIndex, int colIndex, string appendValue)
    {
        IRow row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);
        ICell cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);
        string currentValue = cell.ToString() ?? string.Empty;
        cell.SetCellValue(currentValue + appendValue);
    }

    // 辅助方法：设置单元格的值
    private static void SetCellValue(ISheet sheet, int rowIndex, int colIndex, object value)
    {
        var row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);
        var cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);
        if (value is string)
            cell.SetCellValue((string)value);
        else if (value is double)
            cell.SetCellValue((double)value);
        else if (value is int)
            cell.SetCellValue((int)value);
        else if (value is decimal)
            cell.SetCellValue((double)(decimal)value);
        else
            cell.SetCellValue(value?.ToString() ?? string.Empty);
    }

    #region 导出为xlsx文件内部方法
    /// <summary>
    /// 从datatable 中导出到excel
    /// </summary>
    /// <param name="dtSource">datatable数据源</param>
    /// <param name="strHeaderText">表名</param>
    /// <param name="fs">文件流</param>
    /// <param name="readfs">内存流</param>
    /// <param name="sheetnum">sheet索引</param>
    static void ExportDataTable(DataTable dtSource, string strHeaderText, FileStream fs, MemoryStream readfs, Dictionary<string, string> dir, int sheetnum)
    {

        IWorkbook workbook = new XSSFWorkbook();
        if (readfs.Length > 0 && sheetnum > 0)
        {
            workbook = WorkbookFactory.Create(readfs);
        }
        ISheet sheet = null;
        ICellStyle dateStyle = workbook.CreateCellStyle();
        IDataFormat format = workbook.CreateDataFormat();
        dateStyle.DataFormat = format.GetFormat("yyyy-mm-dd");

        //取得列宽
        //int[] arrColWidth = new int[dtSource.Columns.Count];
        //foreach (DataColumn item in dtSource.Columns)
        //{
        //    arrColWidth[item.Ordinal] = Encoding.GetEncoding(936).GetBytes(item.ColumnName.ToString()).Length;
        //}
        //for (int i = 0; i < dtSource.Rows.Count; i++)
        //{
        //    for (int j = 0; j < dtSource.Columns.Count; j++)
        //    {
        //        int intTemp = Encoding.GetEncoding(936).GetBytes(dtSource.Rows[i][j].ToString()).Length;
        //        if (intTemp > arrColWidth[j])
        //        {
        //            arrColWidth[j] = intTemp;
        //        }
        //    }
        //}

        int rowIndex = 0;

        foreach (DataRow row in dtSource.Rows)
        {
            #region 新建表，填充表头，填充列头，样式

            if (rowIndex == 0)
            {
                #region 表头及样式
                {
                    string sheetName = strHeaderText + (sheetnum == 0 ? "" : sheetnum.ToString());
                    if (workbook.GetSheetIndex(sheetName) >= 0)
                    {
                        workbook.RemoveSheetAt(workbook.GetSheetIndex(sheetName));
                    }
                    sheet = workbook.CreateSheet(sheetName);
                    sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, dtSource.Columns.Count - 1));
                    IRow headerRow = sheet.CreateRow(0);
                    headerRow.HeightInPoints = 25;
                    headerRow.CreateCell(0).SetCellValue(strHeaderText);

                    ICellStyle headStyle = workbook.CreateCellStyle();
                    headStyle.Alignment = HorizontalAlignment.Center;
                    IFont font = workbook.CreateFont();
                    font.FontHeightInPoints = 20;
                    font.Boldweight = 700;
                    headStyle.SetFont(font);
                    headerRow.GetCell(0).CellStyle = headStyle;
                }
                #endregion

                #region 列头及样式
                {
                    IRow headerRow = sheet.CreateRow(1);
                    ICellStyle headStyle = workbook.CreateCellStyle();
                    headStyle.Alignment = HorizontalAlignment.Center;
                    IFont font = workbook.CreateFont();
                    font.FontHeightInPoints = 10;
                    font.Boldweight = 700;
                    headStyle.SetFont(font);


                    foreach (DataColumn column in dtSource.Columns)
                    {
                        headerRow.CreateCell(column.Ordinal).SetCellValue(dir[column.ColumnName]);
                        headerRow.GetCell(column.Ordinal).CellStyle = headStyle;
                        //设置列宽
                        //sheet.SetColumnWidth(column.Ordinal, (arrColWidth[column.Ordinal] + 1) * 256 * 2);
                    }
                }

                #endregion

                rowIndex = 2;
            }
            #endregion

            #region 填充内容
            IRow dataRow = sheet.CreateRow(rowIndex);
            foreach (DataColumn column in dtSource.Columns)
            {
                ICell newCell = dataRow.CreateCell(column.Ordinal);
                string drValue = row[column].ToString();
                switch (column.DataType.ToString())
                {
                    case "System.String": //字符串类型
                        double result;

                        if (isNumeric(drValue, out result))
                        {

                            double.TryParse(drValue, out result);
                            newCell.SetCellValue(result);
                            break;
                        }
                        else
                        {
                            newCell.SetCellValue(drValue);
                            break;
                        }
                    case "System.DateTime": //日期类型
                        DateTime dateV;
                        DateTime.TryParse(drValue, out dateV);
                        newCell.SetCellValue(dateV);

                        newCell.CellStyle = dateStyle; //格式化显示
                        break;
                    case "System.Boolean": //布尔型
                        bool boolV = false;
                        bool.TryParse(drValue, out boolV);
                        newCell.SetCellValue(boolV);
                        break;
                    case "System.Int16": //整型
                    case "System.Int32":
                    case "System.Int64":
                    case "System.Byte":
                        int intV = 0;
                        int.TryParse(drValue, out intV);
                        newCell.SetCellValue(intV);
                        break;
                    case "System.Decimal": //浮点型
                    case "System.Double":
                        double doubV = 0;
                        double.TryParse(drValue, out doubV);
                        newCell.SetCellValue(doubV);
                        break;
                    case "System.DBNull": //空值处理
                        newCell.SetCellValue("");
                        break;
                    default:
                        newCell.SetCellValue(drValue.ToString());
                        break;
                }
            }
            #endregion
            rowIndex++;
        }
        workbook.Write(fs);
        fs.Close();
    }
    #endregion

    /// <summary>
    /// 判断内容是否是数字
    /// </summary>
    /// <param name="message"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public static bool isNumeric(String message, out double result)
    {
        Regex rex = new Regex(@"^[-]?\d+[.]?\d*$");
        result = -1;
        if (rex.IsMatch(message))
        {
            result = double.Parse(message);
            return true;
        }
        else
            return false;
    }

    public static string ConvertR1C1ToA1(string r1c1Formula, int currentRow, int currentCol)
    {
        // 使用正则表达式匹配 R1C1 引用样式
        string pattern = @"R\[?(-?\d+)?\]?C\[?(-?\d+)?\]?";
        string result = Regex.Replace(r1c1Formula, pattern, match =>
        {
            int rowOffset = match.Groups[1].Success ? int.Parse(match.Groups[1].Value) : 0;
            int colOffset = match.Groups[2].Success ? int.Parse(match.Groups[2].Value) : 0;

            int row = currentRow + rowOffset;
            int col = currentCol + colOffset;

            string colLetter = GetColumnLetter(col);
            return $"{colLetter}{row}";
        });

        return result;
    }

    public static string GetColumnLetter(int col)
    {
        string columnLetter = "";
        while (col > 0)
        {
            int remainder = (col - 1) % 26;
            columnLetter = (char)('A' + remainder) + columnLetter;
            col = (col - 1) / 26;
        }
        return columnLetter;
    }

    // 辅助方法：设置单元格区域的边框
    static void SetCellRangeBorder(ISheet sheet, CellRangeAddress range, BorderStyle borderStyle)
    {
        IWorkbook workbook = sheet.Workbook;
        ICellStyle style = workbook.CreateCellStyle();
        style.BorderTop = borderStyle;
        style.BorderBottom = borderStyle;
        style.BorderLeft = borderStyle;
        style.BorderRight = borderStyle;

        for (int rowIndex = range.FirstRow; rowIndex <= range.LastRow; rowIndex++)
        {
            IRow row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);
            for (int colIndex = range.FirstColumn; colIndex <= range.LastColumn; colIndex++)
            {
                ICell cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);
                cell.CellStyle = style;
            }
        }
    }

    /// <summary>
    /// 设置附加样式,保留其他原有样式
    /// </summary>
    /// <param name="sheet"></param>
    /// <param name="range"></param>
    /// <param name="customStyle"></param>
    public static void SetCustomStyleToRange(ISheet sheet, CellRangeAddress range, ICellStyle customStyle)
    {
        IWorkbook workbook = sheet.Workbook;

        for (int rowIndex = range.FirstRow; rowIndex <= range.LastRow; rowIndex++)
        {
            IRow row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);

            for (int colIndex = range.FirstColumn; colIndex <= range.LastColumn; colIndex++)
            {
                ICell cell = row.GetCell(colIndex) ?? row.CreateCell(colIndex);
                ICellStyle originalStyle = cell.CellStyle;

                // 创建新的样式，保留原有样式属性并附加自定义样式
                ICellStyle newStyle = workbook.CreateCellStyle();
                newStyle.CloneStyleFrom(originalStyle);

                // 获取自定义样式的字体
                IFont customFont = customStyle.GetFont(workbook);
                IFont newFont = workbook.CreateFont();

                // 应用自定义样式的字体属性（仅非默认值）
                if (customFont.IsItalic) newFont.IsItalic = true;
                if (customFont.FontHeightInPoints != 11) newFont.FontHeightInPoints = customFont.FontHeightInPoints;
                if (customFont.Color != HSSFColor.Black.Index) newFont.Color = customFont.Color;
                if (customFont.IsBold) newFont.IsBold = true;
                if (customFont.Underline != FontUnderlineType.None) newFont.Underline = customFont.Underline;
                if (customFont.TypeOffset != FontSuperScript.None) newFont.TypeOffset = customFont.TypeOffset;
                if (customFont.Charset != 0) newFont.Charset = customFont.Charset;
                if (customFont.FontName != "Arial") newFont.FontName = customFont.FontName;

                newStyle.SetFont(newFont);

                // 应用自定义样式的其他属性（仅非默认值）
                if (customStyle.Alignment != HorizontalAlignment.General) newStyle.Alignment = customStyle.Alignment;
                if (customStyle.VerticalAlignment != VerticalAlignment.Bottom) newStyle.VerticalAlignment = customStyle.VerticalAlignment;
                if (customStyle.BorderTop != BorderStyle.None) newStyle.BorderTop = customStyle.BorderTop;
                if (customStyle.BorderBottom != BorderStyle.None) newStyle.BorderBottom = customStyle.BorderBottom;
                if (customStyle.BorderLeft != BorderStyle.None) newStyle.BorderLeft = customStyle.BorderLeft;
                if (customStyle.BorderRight != BorderStyle.None) newStyle.BorderRight = customStyle.BorderRight;
                if (customStyle.TopBorderColor != HSSFColor.Black.Index) newStyle.TopBorderColor = customStyle.TopBorderColor;
                if (customStyle.BottomBorderColor != HSSFColor.Black.Index) newStyle.BottomBorderColor = customStyle.BottomBorderColor;
                if (customStyle.LeftBorderColor != HSSFColor.Black.Index) newStyle.LeftBorderColor = customStyle.LeftBorderColor;
                if (customStyle.RightBorderColor != HSSFColor.Black.Index) newStyle.RightBorderColor = customStyle.RightBorderColor;
                if (customStyle.FillForegroundColor != HSSFColor.White.Index) newStyle.FillForegroundColor = customStyle.FillForegroundColor;
                if (customStyle.FillPattern != FillPattern.NoFill) newStyle.FillPattern = customStyle.FillPattern;
                if (customStyle.DataFormat != 0) newStyle.DataFormat = customStyle.DataFormat;
                if (customStyle.IsHidden) newStyle.IsHidden = true;
                if (customStyle.Indention != 0) newStyle.Indention = customStyle.Indention;
                if (customStyle.IsLocked) newStyle.IsLocked = true;
                if (customStyle.Rotation != 0) newStyle.Rotation = customStyle.Rotation;
                if (customStyle.ShrinkToFit) newStyle.ShrinkToFit = true;
                if (customStyle.WrapText) newStyle.WrapText = true;

                // 应用新样式到单元格
                cell.CellStyle = newStyle;
            }
        }
    }


    #region 复制追加行到末尾
    /// <summary>
    /// 复制追加行到末尾
    /// </summary>
    /// <param name="sheet">需要操作的sheet</param>
    /// <param name="startRow">起始行</param>
    /// <param name="endRow">结束行</param>
    /// <param name="appendCount">要追加的次数</param>
    /// <exception cref="ArgumentException"></exception>
    public static void CopyAndAppendRows(ISheet sheet, int startRow, int endRow, int appendCount)
    {
        if (sheet == null)
        {
            throw new ArgumentException("Sheet cannot be null.");
        }

        // 获取最后一行的索引
        int lastRowIndex = sheet.LastRowNum;
        // 存储原始合并单元格信息
        List<CellRangeAddress> originalMergedRegions = new List<CellRangeAddress>();
        for (int i = 0; i < sheet.NumMergedRegions; i++)
        {
            originalMergedRegions.Add(sheet.GetMergedRegion(i));
        }

        for (int i = 0; i < appendCount; i++)
        {
            // 复制合并单元格
            CopyMergedRegions(sheet, originalMergedRegions, startRow, endRow, lastRowIndex);

            for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
            {
                IRow sourceRow = sheet.GetRow(rowIndex);
                if (sourceRow == null) continue;

                IRow newRow = sheet.CreateRow(lastRowIndex + rowIndex + 1);
                CopyRow(sourceRow, newRow, startRow, endRow, lastRowIndex);
            }

            // 更新最后一行的索引
            lastRowIndex += (endRow - startRow + 1);
        }


    }

    public static void CopyAndAppendRows(string filePath, string sheetName, int startRow, int endRow, int appendCount)
    {
        // 打开现有的Excel文件
        using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite))
        {
            IWorkbook workbook = new XSSFWorkbook(file);
            ISheet sheet = workbook.GetSheet(sheetName);

            if (sheet == null)
            {
                throw new ArgumentException($"Sheet '{sheetName}' not found in the workbook.");
            }
            // 获取最后一行的索引
            int lastRowIndex = sheet.LastRowNum;

            // 存储原始合并单元格信息
            List<CellRangeAddress> originalMergedRegions = new List<CellRangeAddress>();
            for (int i = 0; i < sheet.NumMergedRegions; i++)
            {
                originalMergedRegions.Add(sheet.GetMergedRegion(i));
            }


            for (int i = 0; i < appendCount; i++)
            {
                // 复制合并单元格
                CopyMergedRegions(sheet, originalMergedRegions, startRow, endRow, lastRowIndex);


                for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
                {
                    IRow sourceRow = sheet.GetRow(rowIndex);
                    if (sourceRow == null) continue;

                    IRow newRow = sheet.CreateRow(lastRowIndex + rowIndex + 1);
                    CopyRow(sourceRow, newRow, startRow, endRow, lastRowIndex);
                }

                // 更新最后一行的索引
                lastRowIndex += (endRow - startRow + 1);
            }


            string templatePath = Path.Combine("D:\\Personal\\Desktop\\Excel", DateTime.Now.ToString("yymmddHHMMssff") + "梭织发货码单模板.xlsx");
            // 保存更改
            using (FileStream outputFile = new FileStream(templatePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(outputFile);
            }
        }
    }

    private static void CopyRow(IRow sourceRow, IRow newRow, int startRow, int endRow, int lastRowIndex)
    {
        newRow.Height = sourceRow.Height;

        for (int i = 0; i < sourceRow.LastCellNum; i++)
        {
            ICell sourceCell = sourceRow.GetCell(i);
            if (sourceCell == null) continue;

            ICell newCell = newRow.CreateCell(i);
            CopyCell(sourceCell, newCell, startRow, endRow, lastRowIndex);
        }
    }

    private static void CopyCell(ICell sourceCell, ICell newCell, int startRow, int endRow, int lastRowIndex)
    {
        switch (sourceCell.CellType)
        {
            case CellType.Numeric:
                newCell.SetCellValue(sourceCell.NumericCellValue);
                break;
            case CellType.String:
                newCell.SetCellValue(sourceCell.StringCellValue);
                break;
            case CellType.Boolean:
                newCell.SetCellValue(sourceCell.BooleanCellValue);
                break;
            case CellType.Formula:
                string adjustedFormula = AdjustFormula(sourceCell.CellFormula, startRow, endRow, lastRowIndex);
                newCell.SetCellFormula(adjustedFormula);
                break;
            case CellType.Blank:
                newCell.SetCellType(CellType.Blank);
                break;
            case CellType.Error:
                newCell.SetCellErrorValue(sourceCell.ErrorCellValue);
                break;
            default:
                newCell.SetCellValue(sourceCell.ToString());
                break;
        }

        // 复制单元格样式
        if (sourceCell.CellStyle != null)
        {
            newCell.CellStyle = sourceCell.CellStyle;
        }
    }
    private static string AdjustFormula(string formula, int startRow, int endRow, int lastRowIndex)
    {
        // 这里假设公式中的单元格引用是简单的A1:B2格式
        // 使用正则表达式匹配单元格引用
        string pattern = @"([A-Za-z]+)(\d+)";
        MatchCollection matches = Regex.Matches(formula, pattern);

        foreach (Match match in matches)
        {
            string column = match.Groups[1].Value;
            int row = int.Parse(match.Groups[2].Value);

            if (row >= startRow + 1 && row <= endRow + 1)
            {
                int newRow = row + lastRowIndex + 1;
                string newReference = $"{column}{newRow}";
                formula = formula.Replace(match.Value, newReference);
            }
        }

        return formula;
    }
    private static void CopyMergedRegions(ISheet sheet, List<CellRangeAddress> originalMergedRegions, int startRow, int endRow, int lastRowIndex)
    {
        foreach (var mergedRegion in originalMergedRegions)
        {
            if (mergedRegion.FirstRow >= startRow && mergedRegion.LastRow <= endRow)
            {
                CellRangeAddress newMergedRegion = new CellRangeAddress(
                    mergedRegion.FirstRow + lastRowIndex + 1,
                    mergedRegion.LastRow + lastRowIndex + 1,
                    mergedRegion.FirstColumn,
                    mergedRegion.LastColumn
                );
                sheet.AddMergedRegion(newMergedRegion);
            }
        }
    }


    #endregion
}
