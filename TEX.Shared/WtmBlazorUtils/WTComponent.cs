using System.Collections.Generic;
using BootstrapBlazor.Components;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using WalkingTec.Mvvm.Core;

namespace WtmBlazorUtils
{
    public class WTComponent : ComponentBase
    {
        /// <summary>
        /// 获得/设置 用户自定义属性
        /// </summary>
        [Parameter(CaptureUnmatchedValues = true)]
        public IDictionary<string, object> AdditionalAttributes { get; set; }

    }

    public class MyComponent : ComponentBase
    {
        [Inject]
        public WtmBlazorContext WtmBlazor { get; set; }
        [CascadingParameter]
        public LoginUserInfo UserInfo { get; set; }

        public async Task<QueryData<T>> StartSearch<T>(string url, BaseSearcher searcher, QueryPageOptions options) where T : class, new()
        {
            if (searcher != null)
            {
                searcher.IsEnumToString = false;
            }
            var rv = await WtmBlazor.Api.CallSearchApi<T>(url, searcher, options);
            QueryData<T> data = new QueryData<T>();
            if (rv.StatusCode == System.Net.HttpStatusCode.OK)
            {
                data.Items = rv.Data?.Data;
                data.TotalCount = rv.Data?.Count ?? 0;
            }
            if (rv.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                await WtmBlazor.Toast.Error(WtmBlazor.Localizer["Sys.Error"], WtmBlazor.Localizer["Sys.NoPrivilege"]);
            }
            return data;
        }

    }
}
