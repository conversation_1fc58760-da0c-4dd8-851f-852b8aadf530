using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using TEX.Model.Models;
using TEX.ViewModel.Models.ProductVMs;

namespace TEX.Shared;

public class NamedRuleHelper
{
    public static string GenerateProductCode(string rule, Product Model)
    {
        var result = rule;

        // 替换日期
        result = Regex.Replace(result, @"\{date:([^}]+)\}", match =>
        {
            return DateTime.Now.ToString(match.Groups[1].Value);
        });

        // 替换拼音
        result = Regex.Replace(result, @"\{pinyin:([^}]+)\}", match =>
        {
            var propertyName = match.Groups[1].Value;
            var propertyValue = typeof(Product).GetProperty(propertyName)?.GetValue(Model)?.ToString();
            return PinYinHelper.GetPinYinFirstLetters(propertyValue ?? "");
        });

        // 替换其他属性值
        result = Regex.Replace(result, @"\{([^}]+)\}", match =>
        {
            var propertyName = match.Groups[1].Value;
            var value= typeof(Product).GetProperty(propertyName)?.GetValue(Model)?.ToString() ?? "";
            if (propertyName.ToLower() == "productname")
            {
                //将产品名称中的"绒"字去掉
                value = value.Replace("绒", "");
            }
            
            if(propertyName.ToLower() == "width")
            {
                //只保留width的后两位数
                value = value.Substring(value.Length - 2);
            }
            if (propertyName.ToLower() == "gsm")
            {
                //只保留gsm的前两位数
                value = value.Substring(0, 2);
            }
            return value;
        });

        return result;
    }

    public static string GenerateKnittingProcessCode(string rule, KnittingProcess Model)
    {
        var result = rule;
        try { 
        // 替换日期
        result = Regex.Replace(result, @"\{date:([^}]+)\}", match =>
        {
            return DateTime.Now.ToString(match.Groups[1].Value);
        });

        // 替换拼音
        result = Regex.Replace(result, @"\{pinyin:([^}]+)\}", match =>
        {
            var propertyName = match.Groups[1].Value;
            if (propertyName.ToLower() == "productname")
            {
                //将产品名称中的"绒"字去掉
                propertyName = propertyName.Replace("绒", "");
            }
            var propertyValue = typeof(Product).GetProperty(propertyName)?.GetValue(Model)?.ToString();
            return PinYinHelper.GetPinYinFirstLetters(propertyValue ?? "");
        });

        // 替换其他属性值
        result = Regex.Replace(result, @"\{([^}]+)\}", match =>
        {
            var propertyName = match.Groups[1].Value;
            var value = typeof(Product).GetProperty(propertyName)?.GetValue(Model)?.ToString() ?? "";
            if (propertyName.ToLower() == "productname")
            {
                //将产品名称中的"绒"字去掉
                value = value.Replace("绒", "");
            }

            if (propertyName.ToLower() == "width")
            {
                //只保留width的后两位数
                value = value.Substring(value.Length - 2);
            }
            if (propertyName.ToLower() == "gsm")
            {
                //只保留gsm的前两位数
                value = value.Substring(0, 2);
            }
            return value;
        });
        }
        catch (Exception ex)
        {
            
            Console.WriteLine(ex.Message);
            return "生成失败";
        }
        return result;
    }
}

