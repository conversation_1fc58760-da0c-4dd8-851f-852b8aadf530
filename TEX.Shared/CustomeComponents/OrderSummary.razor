@using TEX.ViewModel.Models
@inject WtmBlazorContext WtmBlazor;
@inherits ComponentBase;


<br />
<div style="margin: 20px;">
    <h3 class="flex-fill text-dark fw-bold me-3">订单统计</h3>
    <div class="row" style="margin: 20px;">
        <div class="d-flex flex-wrap justify-content-between">
            <ProgressCardShow Title1="本周下单重量" Title2="本月下单重量" Value1="@DashData.POWeekTotalWeight" Value2="@DashData.POMonthTotalWeight" ColumnClass="col-md-3 col-lg-3" />
            <ProgressCardShow Title1="本月下单重量" Title2="本年下单重量" Value1="@DashData.POMonthTotalWeight" Value2="@DashData.POYearTotalWeight" ColumnClass="col-md-3 col-lg-3" />
            <ProgressCardShow Title1="本周下单金额" Title2="本月下单金额" Value1="@DashData.POWeekTotalAmount" Value2="@DashData.POMonthTotalAmount" ColumnClass="col-md-3 col-lg-3" />
            <ProgressCardShow Title1="本月下单金额" Title2="本年下单金额" Value1="@DashData.POMonthTotalAmount" Value2="@DashData.POYearTotalAmount" ColumnClass="col-md-3 col-lg-3" />
        </div>
    </div>
</div>

<br />
<div style="margin: 20px;">
    <h3 class="flex-fill text-dark fw-bold me-3">成品检验统计</h3>
    <div class="row" style="margin: 20px;">
        <div class="d-flex flex-wrap justify-content-between">
            <ProgressCardShow Title1="当天检验重量" Title2="本周检验重量" Value1="@InspectDashData.InspectTodayTotalWeight" Value2="@InspectDashData.InspectWeekTotalWeight" ColumnClass="col-md-2 col-lg-2" />
            <ProgressCardShow Title1="当天检验米数" Title2="本周检验米数" Value1="@InspectDashData.InspectTodayTotalMeters" Value2="@InspectDashData.InspectWeekTotalMeters" ColumnClass="col-md-2 col-lg-2" />
            <ProgressCardShow Title1="当天检验码数" Title2="本周检验码数" Value1="@InspectDashData.InspectTodayTotalYards" Value2="@InspectDashData.InspectWeekTotalYards" ColumnClass="col-md-2 col-lg-2" />
            <ProgressCardShow Title1="本周检验重量" Title2="本月检验重量" Value1="@InspectDashData.InspectWeekTotalWeight" Value2="@InspectDashData.InspectMonthTotalWeight" ColumnClass="col-md-2 col-lg-2" />
            <ProgressCardShow Title1="本周检验米数" Title2="本月检验米数" Value1="@InspectDashData.InspectWeekTotalMeters" Value2="@InspectDashData.InspectMonthTotalMeters" ColumnClass="col-md-2 col-lg-2" />
            <ProgressCardShow Title1="本周检验码数" Title2="本月检验码数" Value1="@InspectDashData.InspectWeekTotalYards" Value2="@InspectDashData.InspectMonthTotalYards" ColumnClass="col-md-2 col-lg-2" />
        </div>
    </div>
</div>

<br />
<div style="margin: 20px;">
    <h3 class="flex-fill text-dark fw-bold me-3">成品发货统计</h3>
    <div class="row" style="margin: 20px;">
        <div class="d-flex flex-wrap justify-content-between">
            <ProgressCardShow Title1="当天发货重量" Title2="本周发货重量" Value1="@InspectDashData.InspectTodayTotalWeight" Value2="@InspectDashData.InspectWeekTotalWeight" ColumnClass="col-md-3 col-lg-3" />
            <ProgressCardShow Title1="当天发货米数" Title2="本周发货米数" Value1="@InspectDashData.InspectTodayTotalMeters" Value2="@InspectDashData.InspectWeekTotalMeters" ColumnClass="col-md-3 col-lg-3" />
            <ProgressCardShow Title1="本周发货重量" Title2="本月发货重量" Value1="@InspectDashData.InspectWeekTotalWeight" Value2="@InspectDashData.InspectMonthTotalWeight" ColumnClass="col-md-3 col-lg-3" />
            <ProgressCardShow Title1="本周发货米数" Title2="本月发货米数" Value1="@InspectDashData.InspectWeekTotalMeters" Value2="@InspectDashData.InspectMonthTotalMeters" ColumnClass="col-md-3 col-lg-3" />
        </div>
    </div>
</div>
@code {
    private PODashboardData DashData { get; set; } = new();
    private InspectDashboardData InspectDashData { get; set; } = new();
    private double progressValue;
    private double yearProgressValue;

    private double progressAmountValue;
    private double yearProgressAmountValue;
    private IEnumerable<SelectedItem> YearItems { get; set; }
    private List<MonthlyTotalAmount> currentAmount { get; set; } = new();
    private List<MonthlyTotalWeight> currentWeight { get; set; } = new();

    private bool lenthUnitIsMeter = true;

    protected override async Task OnInitializedAsync()
    {
        await GetData();//异步获取数据可能会导致空引用
        await base.OnInitializedAsync();
    }


    private async Task GetData()
    {
        await Task.Delay(500);
        //var rv = await WtmBlazor.Api.CallAPI<PODashboardData>("/api/DashboardOrder/GetOrderData");
        var rv = await WtmBlazor.Api.CallAPI<PODashboardData>("/api/Models/PurchaseOrder/GetOrderData");
        if (rv == null || rv.Data == null) return;
        DashData = rv.Data;
        //DashData = service.GetData();
        progressValue = DashData.POMonthTotalWeight == 0m ? 0 : (double)(DashData.POWeekTotalWeight / DashData.POMonthTotalWeight) * 100;
        yearProgressValue = DashData.POYearTotalWeight == 0m ? 0 : (double)(DashData.POMonthTotalWeight / DashData.POYearTotalWeight) * 100;
        progressAmountValue = DashData.POMonthTotalAmount == 0m ? 0 : (double)(DashData.POWeekTotalAmount / DashData.POMonthTotalAmount) * 100;
        yearProgressAmountValue = DashData.POYearTotalAmount == 0m ? 0 : (double)(DashData.POMonthTotalAmount / DashData.POYearTotalAmount) * 100;

        YearItems = DashData.MonthlyTotalAmount
            .Select(x => x.YearMonth.Year)
            .Distinct()
            .OrderByDescending(year => year)
            .Select(year => new SelectedItem
                {
                    Text = year + "年",
                    Value = year + "-01"
                })
            .ToList();
        currentAmount = DashData.MonthlyTotalAmount.Where(x => x.YearMonth.Year == DateTime.Now.Year).ToList();
        currentWeight = DashData.MonthlyTotalWeight.Where(x => x.YearMonth.Year == DateTime.Now.Year).ToList();
        StateHasChanged();
    }

}
