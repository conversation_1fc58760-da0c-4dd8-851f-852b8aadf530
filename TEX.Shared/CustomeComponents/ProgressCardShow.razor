
<div class="@ColumnClass">
    <Card IsShadow="true" style="margin: 0 2px;">
        <BodyTemplate>
            <div class="row align-items-center no-gutters" style="font-size: 0.9rem;">
                <div class="col me-2">
                    <div class=" text-primary fw-bold mb-1 h7"><span>@Title1</span></div>
                    <div class="text-dark fw-bold h5">
                        <CountUp Value="@Value1"></CountUp>
                    </div>
                    <div class=" text-info fw-bold mb-1 h6"><span>@Title2</span></div>
                    <div class="row g-0 align-items-center">
                        <div class="col-auto">
                            <div class="text-dark fw-bold h5 me-3 mb-0">
                                <CountUp Value="@Value2"></CountUp>
                            </div>
                        </div>
                        <div class="col"><div class="db-progress"><Progress Value="@ProgressValue" IsShowValue="true" IsAnimated=true Color="Color.Info" IsStriped=true IsShowPercent=true Round="1"></Progress></div></div>
                    </div>
                </div>
                <div class="col-auto"><i class="fa-regular fa-calendar-check fa-2x"></i></div>
            </div>
        </BodyTemplate>
    </Card>
</div>


@code {

    [Parameter]
    public string Title1 { get; set; } = String.Empty;

    [Parameter]
    public string Title2 { get; set; } = String.Empty;

    [Parameter]
    public decimal Value1 { get; set; } = 0;

    [Parameter]
    public decimal Value2 { get; set; } = 0;

    [Parameter]
    public string ColumnClass { get; set; } = "col-md-3 col-lg-3";

    public double ProgressValue { get; set; } = 0;

    protected override Task OnInitializedAsync()
    {

        ProgressValue = Value1==0?0:(double)(Value2 / Value1 * 100);
        return base.OnInitializedAsync();
    }
}
