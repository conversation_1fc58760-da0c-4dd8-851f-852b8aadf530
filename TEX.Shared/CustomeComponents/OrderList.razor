@using TEX.ViewModel.Models.PurchaseOrderVMs;
@inherits BasePage


<Table @ref="dataTable" TItem="PurchaseOrder_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true"
       IsBordered="true" ShowRefresh="false" ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
       ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>
        <TableColumn @bind-Field="@context.PurchaseOrder_CreateDate" />
        <TableColumn @bind-Field="@context.PurchaseOrder_Customer" />
        <TableColumn @bind-Field="@context.PurchaseOrder_OrderNo" />
        <TableColumn @bind-Field="@context.PurchaseOrder_CustomerOrderNo" />
        <TableColumn @bind-Field="@context.PurchaseOrder_OrderType" />
        <TableColumn @bind-Field="@context.PurchaseOrder_Product" />

        <TableColumn @bind-Field="@context.PurchaseOrder_TotalMeters" />
        <TableColumn @bind-Field="@context.PurchaseOrder_TotalYards" />
        <TableColumn @bind-Field="@context.PurchaseOrder_TotalWeight" />
        
        @if (ValidToAmount)
        {
            <TableColumn @bind-Field="@context.TotalAmount" />
            <TableColumn @bind-Field="@context.PriceUnit" />
        }
        <TableColumn @bind-Field="@context.AuditStatus" />
        <TableColumn @bind-Field="@context.Remark" />
    </TableColumns>
    
</Table>

@code {
    [Parameter]
    public DateTime CreateDate { get; set; }
    private DateTime _createDate;

    [Parameter]
    public EventCallback<DateTime> CreateDateChanged { get; set; }

    private PurchaseOrderSearcher SearchModel = new PurchaseOrderSearcher();
    private Table<PurchaseOrder_View> dataTable;


    private bool ValidToAmount = false;//控制用户对金额的查看权限

    protected override async Task OnInitializedAsync()
    {
        ValidToAmount = UserInfo.Roles.Any(x => x.RoleCode.StartsWith("1"));//控制用户对金额的查看权限,角色编号1开头的用户有权限查看金额
        
        await base.OnInitializedAsync();
    }

    private async Task<QueryData<PurchaseOrder_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<PurchaseOrder_View>("/api/Models/PurchaseOrder/SearchPurchaseOrder", SearchModel, opts);
    }

    private void DoSearch()
    {
        if (dataTable!= null)
        dataTable.QueryAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_createDate != CreateDate)
        {
            _createDate = CreateDate;
            SearchModel.CreateDate = new DateRange(CreateDate, CreateDate.AddMonths(1).AddDays(-CreateDate.Day + 1).AddSeconds(-1));
            DoSearch();
            StateHasChanged();
            //await CreateDateChanged.InvokeAsync(CreateDate);
        }
        await Task.CompletedTask;
    }
}
