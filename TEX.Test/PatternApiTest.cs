using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.PatternVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class PatternApiTest
    {
        private PatternController _controller;
        private string _seed;

        public PatternApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<PatternController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new PatternSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            PatternVM vm = _controller.Wtm.CreateVM<PatternVM>();
            Pattern v = new Pattern();
            
            v.CreateDate = DateTime.Parse("2024-01-20 20:17:33");
            v.PatternName = "bwIPVRJnN";
            v.Customer = "0N1HCrig";
            v.Description = "UKSBt7";
            v.Requirements = "J0UU";
            v.Remark = "H13nlYpAkfpYOW8";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Pattern>().Find(v.ID);
                
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-01-20 20:17:33"));
                Assert.AreEqual(data.PatternName, "bwIPVRJnN");
                Assert.AreEqual(data.Customer, "0N1HCrig");
                Assert.AreEqual(data.Description, "UKSBt7");
                Assert.AreEqual(data.Requirements, "J0UU");
                Assert.AreEqual(data.Remark, "H13nlYpAkfpYOW8");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            Pattern v = new Pattern();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.CreateDate = DateTime.Parse("2024-01-20 20:17:33");
                v.PatternName = "bwIPVRJnN";
                v.Customer = "0N1HCrig";
                v.Description = "UKSBt7";
                v.Requirements = "J0UU";
                v.Remark = "H13nlYpAkfpYOW8";
                context.Set<Pattern>().Add(v);
                context.SaveChanges();
            }

            PatternVM vm = _controller.Wtm.CreateVM<PatternVM>();
            var oldID = v.ID;
            v = new Pattern();
            v.ID = oldID;
       		
            v.CreateDate = DateTime.Parse("2024-08-20 20:17:33");
            v.PatternName = "5mKubb8jHQA3";
            v.Customer = "Uxi2cSB9pGGe";
            v.Description = "dTJbh1ynnEU";
            v.Requirements = "KwlK";
            v.Remark = "O4YDs";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.CreateDate", "");
            vm.FC.Add("Entity.PatternName", "");
            vm.FC.Add("Entity.Customer", "");
            vm.FC.Add("Entity.Description", "");
            vm.FC.Add("Entity.Requirements", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Pattern>().Find(v.ID);
 				
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-08-20 20:17:33"));
                Assert.AreEqual(data.PatternName, "5mKubb8jHQA3");
                Assert.AreEqual(data.Customer, "Uxi2cSB9pGGe");
                Assert.AreEqual(data.Description, "dTJbh1ynnEU");
                Assert.AreEqual(data.Requirements, "KwlK");
                Assert.AreEqual(data.Remark, "O4YDs");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            Pattern v = new Pattern();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.CreateDate = DateTime.Parse("2024-01-20 20:17:33");
                v.PatternName = "bwIPVRJnN";
                v.Customer = "0N1HCrig";
                v.Description = "UKSBt7";
                v.Requirements = "J0UU";
                v.Remark = "H13nlYpAkfpYOW8";
                context.Set<Pattern>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            Pattern v1 = new Pattern();
            Pattern v2 = new Pattern();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.CreateDate = DateTime.Parse("2024-01-20 20:17:33");
                v1.PatternName = "bwIPVRJnN";
                v1.Customer = "0N1HCrig";
                v1.Description = "UKSBt7";
                v1.Requirements = "J0UU";
                v1.Remark = "H13nlYpAkfpYOW8";
                v2.CreateDate = DateTime.Parse("2024-08-20 20:17:33");
                v2.PatternName = "5mKubb8jHQA3";
                v2.Customer = "Uxi2cSB9pGGe";
                v2.Description = "dTJbh1ynnEU";
                v2.Requirements = "KwlK";
                v2.Remark = "O4YDs";
                context.Set<Pattern>().Add(v1);
                context.Set<Pattern>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<Pattern>().Find(v1.ID);
                var data2 = context.Set<Pattern>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }


    }
}
