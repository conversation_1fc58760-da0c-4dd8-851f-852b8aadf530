using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;

using TEX.Model.Models;
using TEX.DataAccess;
using TEX.Models.Controllers;
using TEX.ViewModel.Models.ProductVMs;


namespace TEX.Test
{
    [TestClass]
    public class ProductApiTest
    {
        private ProductController _controller;
        private string _seed;

        public ProductApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<ProductController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.SearchProduct(new ProductSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ProductVM vm = _controller.Wtm.CreateVM<ProductVM>();
            Product v = new Product();
            
            v.ProductCode = "ywDDT0F4By4D7grMMXDpRGY6PZX";
            v.ProductName = "WnGbYNM";
            //v.Category = TEX.Model.Models.FabricCategoryEnum.Satin;
            v.Contents = "5m0SrZ3CHrlFQXcSoQIwq36HN86uZsxbqaLNuCqOg9";
            v.Spec = "QbPPX9dCiP6qGlJnq";
            v.GSM = 7;
            v.Width = 89;
            v.PileLength = 1;
            v.DyeingProcess = "vqyvqqdrTzqZ";
            v.KnittingProcess = "JSqpF7dDLx2vyFxV";
            v.FinishingProcess = "gRZ8ql";
            v.ParentId = AddProduct();
            vm.Entity = v;
            var rv = _controller.Create(vm);
            Assert.IsInstanceOfType(rv.Result, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Product>().Find(v.ID);
                
                Assert.AreEqual(data.ProductCode, "ywDDT0F4By4D7grMMXDpRGY6PZX");
                Assert.AreEqual(data.ProductName, "WnGbYNM");
                //Assert.AreEqual(data.Category, TEX.Model.Models.FabricCategoryEnum.Satin);
                Assert.AreEqual(data.Contents, "5m0SrZ3CHrlFQXcSoQIwq36HN86uZsxbqaLNuCqOg9");
                Assert.AreEqual(data.Spec, "QbPPX9dCiP6qGlJnq");
                Assert.AreEqual(data.GSM, 7);
                Assert.AreEqual(data.Width, 89);
                Assert.AreEqual(data.PileLength, 1);
                Assert.AreEqual(data.DyeingProcess, "vqyvqqdrTzqZ");
                Assert.AreEqual(data.KnittingProcess, "JSqpF7dDLx2vyFxV");
                Assert.AreEqual(data.FinishingProcess, "gRZ8ql");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ProductCode = "ywDDT0F4By4D7grMMXDpRGY6PZX";
                v.ProductName = "WnGbYNM";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Satin;
                v.Contents = "5m0SrZ3CHrlFQXcSoQIwq36HN86uZsxbqaLNuCqOg9";
                v.Spec = "QbPPX9dCiP6qGlJnq";
                v.GSM = 7;
                v.Width = 89;
                v.PileLength = 1;
                v.DyeingProcess = "vqyvqqdrTzqZ";
                v.KnittingProcess = "JSqpF7dDLx2vyFxV";
                v.FinishingProcess = "gRZ8ql";
                v.ParentId = AddProduct();
                context.Set<Product>().Add(v);
                context.SaveChanges();
            }

            ProductVM vm = _controller.Wtm.CreateVM<ProductVM>();
            var oldID = v.ID;
            v = new Product();
            v.ID = oldID;
       		
            v.ProductCode = "z3dkOfMLaGv9NgOyKiEbg9xsPndCy5Jfw";
            v.ProductName = "ej1TygkYrw3gpS89qVe6nXlryrk7LEClDXG3T4PC74zrhokCUEnJdGe";
            //v.Category = TEX.Model.Models.FabricCategoryEnum.MicroSuede;
            v.Contents = "pESXhZnMcPaUBFeeCys8EjDVyhrGkjY73";
            v.Spec = "cIIaOm3Wvor8EaWbSnH1KOYUYoMsIS3AIpy16NDxGDoDJBv07SYhAjc6K9cVnStPwO7byAKrKp3S";
            v.GSM = 36;
            v.Width = 298;
            v.PileLength = 85;
            v.DyeingProcess = "MYQqcjxDMWvsdBIE8B";
            v.KnittingProcess = "7vw7ENt405UGmRN3f";
            v.FinishingProcess = "W6Xsy";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ProductCode", "");
            vm.FC.Add("Entity.ProductName", "");
            vm.FC.Add("Entity.Category", "");
            vm.FC.Add("Entity.Contents", "");
            vm.FC.Add("Entity.Spec", "");
            vm.FC.Add("Entity.GSM", "");
            vm.FC.Add("Entity.Width", "");
            vm.FC.Add("Entity.PileLength", "");
            vm.FC.Add("Entity.DyeingProcess", "");
            vm.FC.Add("Entity.KnittingProcess", "");
            vm.FC.Add("Entity.FinishingProcess", "");
            vm.FC.Add("Entity.ParentId", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv.Result, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Product>().Find(v.ID);
 				
                Assert.AreEqual(data.ProductCode, "z3dkOfMLaGv9NgOyKiEbg9xsPndCy5Jfw");
                Assert.AreEqual(data.ProductName, "ej1TygkYrw3gpS89qVe6nXlryrk7LEClDXG3T4PC74zrhokCUEnJdGe");
                //Assert.AreEqual(data.Category, TEX.Model.Models.FabricCategoryEnum.MicroSuede);
                Assert.AreEqual(data.Contents, "pESXhZnMcPaUBFeeCys8EjDVyhrGkjY73");
                Assert.AreEqual(data.Spec, "cIIaOm3Wvor8EaWbSnH1KOYUYoMsIS3AIpy16NDxGDoDJBv07SYhAjc6K9cVnStPwO7byAKrKp3S");
                Assert.AreEqual(data.GSM, 36);
                Assert.AreEqual(data.Width, 298);
                Assert.AreEqual(data.PileLength, 85);
                Assert.AreEqual(data.DyeingProcess, "MYQqcjxDMWvsdBIE8B");
                Assert.AreEqual(data.KnittingProcess, "7vw7ENt405UGmRN3f");
                Assert.AreEqual(data.FinishingProcess, "W6Xsy");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ProductCode = "ywDDT0F4By4D7grMMXDpRGY6PZX";
                v.ProductName = "WnGbYNM";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Satin;
                v.Contents = "5m0SrZ3CHrlFQXcSoQIwq36HN86uZsxbqaLNuCqOg9";
                v.Spec = "QbPPX9dCiP6qGlJnq";
                v.GSM = 7;
                v.Width = 89;
                v.PileLength = 1;
                v.DyeingProcess = "vqyvqqdrTzqZ";
                v.KnittingProcess = "JSqpF7dDLx2vyFxV";
                v.FinishingProcess = "gRZ8ql";
                v.ParentId = AddProduct();
                context.Set<Product>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            Product v1 = new Product();
            Product v2 = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ProductCode = "ywDDT0F4By4D7grMMXDpRGY6PZX";
                v1.ProductName = "WnGbYNM";
                //v1.Category = TEX.Model.Models.FabricCategoryEnum.Satin;
                v1.Contents = "5m0SrZ3CHrlFQXcSoQIwq36HN86uZsxbqaLNuCqOg9";
                v1.Spec = "QbPPX9dCiP6qGlJnq";
                v1.GSM = 7;
                v1.Width = 89;
                v1.PileLength = 1;
                v1.DyeingProcess = "vqyvqqdrTzqZ";
                v1.KnittingProcess = "JSqpF7dDLx2vyFxV";
                v1.FinishingProcess = "gRZ8ql";
                v1.ParentId = AddProduct();
                v2.ProductCode = "z3dkOfMLaGv9NgOyKiEbg9xsPndCy5Jfw";
                v2.ProductName = "ej1TygkYrw3gpS89qVe6nXlryrk7LEClDXG3T4PC74zrhokCUEnJdGe";
                //v2.Category = TEX.Model.Models.FabricCategoryEnum.MicroSuede;
                v2.Contents = "pESXhZnMcPaUBFeeCys8EjDVyhrGkjY73";
                v2.Spec = "cIIaOm3Wvor8EaWbSnH1KOYUYoMsIS3AIpy16NDxGDoDJBv07SYhAjc6K9cVnStPwO7byAKrKp3S";
                v2.GSM = 36;
                v2.Width = 298;
                v2.PileLength = 85;
                v2.DyeingProcess = "MYQqcjxDMWvsdBIE8B";
                v2.KnittingProcess = "7vw7ENt405UGmRN3f";
                v2.FinishingProcess = "W6Xsy";
                v2.ParentId = v1.ParentId; 
                context.Set<Product>().Add(v1);
                context.Set<Product>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<Product>().Find(v1.ID);
                var data2 = context.Set<Product>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "bSRhF5LDK1PfMJUvr3bPxRcmyjV";
                v.ProductName = "7cbqQ8Vql4F90pOlrD7BJuOQs1iKU3HxS5JfU";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Fakecotton;
                v.Contents = "pnJutSMk9Ixun4oH72MAiHmQaY";
                v.Spec = "OJ0MsDZUviD";
                v.GSM = 47;
                v.Width = 90;
                v.PileLength = 93;
                v.DyeingProcess = "b";
                v.KnittingProcess = "NeFSYqSNulQq";
                v.FinishingProcess = "jpvwH9tsBeO2";
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
