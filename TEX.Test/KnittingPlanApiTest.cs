using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.KnittingPlanVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class KnittingPlanApiTest
    {
        private KnittingPlanController _controller;
        private string _seed;

        public KnittingPlanApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<KnittingPlanController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new KnittingPlanSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            KnittingPlanVM vm = _controller.Wtm.CreateVM<KnittingPlanVM>();
            KnittingPlan v = new KnittingPlan();
            
            v.BillNo = "Pukr";
            v.PlanDate = DateTime.Parse("2025-08-19 13:47:32");
            v.PurchaseOrderId = AddPurchaseOrder();
            v.KnittingFactoryId = AddCompany();
            v.ProductName = "vl0QZcEHArZYaVJIe66E51fklHoJmGCNu4aWTpwL4PxJOfejZQvSAaktlvneBx7gws2RHigM8OukHQgKNSxMVqGKtEp";
            v.KnittingProcessId = AddKnittingProcess();
            v.PieceCount = 66;
            v.TotalWeight = 72;
            v.WeightPerPiece = 0;
            v.BatchNo = "w9Caan3D";
            v.DeliveryDate = DateTime.Parse("2025-08-02 13:47:32");
            v.Requirements = "4";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "ed";
            v.Remark = "SvXM4qRCpT";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<KnittingPlan>().Find(v.ID);
                
                Assert.AreEqual(data.BillNo, "Pukr");
                Assert.AreEqual(data.PlanDate, DateTime.Parse("2025-08-19 13:47:32"));
                Assert.AreEqual(data.ProductName, "vl0QZcEHArZYaVJIe66E51fklHoJmGCNu4aWTpwL4PxJOfejZQvSAaktlvneBx7gws2RHigM8OukHQgKNSxMVqGKtEp");
                Assert.AreEqual(data.PieceCount, 66);
                Assert.AreEqual(data.TotalWeight, 72);
                Assert.AreEqual(data.WeightPerPiece, 0);
                Assert.AreEqual(data.BatchNo, "w9Caan3D");
                Assert.AreEqual(data.DeliveryDate, DateTime.Parse("2025-08-02 13:47:32"));
                Assert.AreEqual(data.Requirements, "4");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "ed");
                Assert.AreEqual(data.Remark, "SvXM4qRCpT");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            KnittingPlan v = new KnittingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.BillNo = "Pukr";
                v.PlanDate = DateTime.Parse("2025-08-19 13:47:32");
                v.PurchaseOrderId = AddPurchaseOrder();
                v.KnittingFactoryId = AddCompany();
                v.ProductName = "vl0QZcEHArZYaVJIe66E51fklHoJmGCNu4aWTpwL4PxJOfejZQvSAaktlvneBx7gws2RHigM8OukHQgKNSxMVqGKtEp";
                v.KnittingProcessId = AddKnittingProcess();
                v.PieceCount = 66;
                v.TotalWeight = 72;
                v.WeightPerPiece = 0;
                v.BatchNo = "w9Caan3D";
                v.DeliveryDate = DateTime.Parse("2025-08-02 13:47:32");
                v.Requirements = "4";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "ed";
                v.Remark = "SvXM4qRCpT";
                context.Set<KnittingPlan>().Add(v);
                context.SaveChanges();
            }

            KnittingPlanVM vm = _controller.Wtm.CreateVM<KnittingPlanVM>();
            var oldID = v.ID;
            v = new KnittingPlan();
            v.ID = oldID;
       		
            v.BillNo = "J2RDNzF3";
            v.PlanDate = DateTime.Parse("2023-09-07 13:47:32");
            v.ProductName = "A4y26FLxMuagP7QxEVNDyAg1DCg";
            v.PieceCount = 79;
            v.TotalWeight = 2;
            v.WeightPerPiece = 33;
            v.BatchNo = "9lfKeZvf0FjK8Y4izqwdlRyUavu";
            v.DeliveryDate = DateTime.Parse("2024-04-25 13:47:32");
            v.Requirements = "q08scjC";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "i4sui3ZFs9GxYdVfmxE";
            v.Remark = "UPsJKWPjSLu5x8rJbC0";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.BillNo", "");
            vm.FC.Add("Entity.PlanDate", "");
            vm.FC.Add("Entity.PurchaseOrderId", "");
            vm.FC.Add("Entity.KnittingFactoryId", "");
            vm.FC.Add("Entity.ProductName", "");
            vm.FC.Add("Entity.KnittingProcessId", "");
            vm.FC.Add("Entity.PieceCount", "");
            vm.FC.Add("Entity.TotalWeight", "");
            vm.FC.Add("Entity.WeightPerPiece", "");
            vm.FC.Add("Entity.BatchNo", "");
            vm.FC.Add("Entity.DeliveryDate", "");
            vm.FC.Add("Entity.Requirements", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<KnittingPlan>().Find(v.ID);
 				
                Assert.AreEqual(data.BillNo, "J2RDNzF3");
                Assert.AreEqual(data.PlanDate, DateTime.Parse("2023-09-07 13:47:32"));
                Assert.AreEqual(data.ProductName, "A4y26FLxMuagP7QxEVNDyAg1DCg");
                Assert.AreEqual(data.PieceCount, 79);
                Assert.AreEqual(data.TotalWeight, 2);
                Assert.AreEqual(data.WeightPerPiece, 33);
                Assert.AreEqual(data.BatchNo, "9lfKeZvf0FjK8Y4izqwdlRyUavu");
                Assert.AreEqual(data.DeliveryDate, DateTime.Parse("2024-04-25 13:47:32"));
                Assert.AreEqual(data.Requirements, "q08scjC");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "i4sui3ZFs9GxYdVfmxE");
                Assert.AreEqual(data.Remark, "UPsJKWPjSLu5x8rJbC0");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            KnittingPlan v = new KnittingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.BillNo = "Pukr";
                v.PlanDate = DateTime.Parse("2025-08-19 13:47:32");
                v.PurchaseOrderId = AddPurchaseOrder();
                v.KnittingFactoryId = AddCompany();
                v.ProductName = "vl0QZcEHArZYaVJIe66E51fklHoJmGCNu4aWTpwL4PxJOfejZQvSAaktlvneBx7gws2RHigM8OukHQgKNSxMVqGKtEp";
                v.KnittingProcessId = AddKnittingProcess();
                v.PieceCount = 66;
                v.TotalWeight = 72;
                v.WeightPerPiece = 0;
                v.BatchNo = "w9Caan3D";
                v.DeliveryDate = DateTime.Parse("2025-08-02 13:47:32");
                v.Requirements = "4";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "ed";
                v.Remark = "SvXM4qRCpT";
                context.Set<KnittingPlan>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            KnittingPlan v1 = new KnittingPlan();
            KnittingPlan v2 = new KnittingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.BillNo = "Pukr";
                v1.PlanDate = DateTime.Parse("2025-08-19 13:47:32");
                v1.PurchaseOrderId = AddPurchaseOrder();
                v1.KnittingFactoryId = AddCompany();
                v1.ProductName = "vl0QZcEHArZYaVJIe66E51fklHoJmGCNu4aWTpwL4PxJOfejZQvSAaktlvneBx7gws2RHigM8OukHQgKNSxMVqGKtEp";
                v1.KnittingProcessId = AddKnittingProcess();
                v1.PieceCount = 66;
                v1.TotalWeight = 72;
                v1.WeightPerPiece = 0;
                v1.BatchNo = "w9Caan3D";
                v1.DeliveryDate = DateTime.Parse("2025-08-02 13:47:32");
                v1.Requirements = "4";
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v1.AuditedBy = "ed";
                v1.Remark = "SvXM4qRCpT";
                v2.BillNo = "J2RDNzF3";
                v2.PlanDate = DateTime.Parse("2023-09-07 13:47:32");
                v2.PurchaseOrderId = v1.PurchaseOrderId; 
                v2.KnittingFactoryId = v1.KnittingFactoryId; 
                v2.ProductName = "A4y26FLxMuagP7QxEVNDyAg1DCg";
                v2.KnittingProcessId = v1.KnittingProcessId; 
                v2.PieceCount = 79;
                v2.TotalWeight = 2;
                v2.WeightPerPiece = 33;
                v2.BatchNo = "9lfKeZvf0FjK8Y4izqwdlRyUavu";
                v2.DeliveryDate = DateTime.Parse("2024-04-25 13:47:32");
                v2.Requirements = "q08scjC";
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v2.AuditedBy = "i4sui3ZFs9GxYdVfmxE";
                v2.Remark = "UPsJKWPjSLu5x8rJbC0";
                context.Set<KnittingPlan>().Add(v1);
                context.Set<KnittingPlan>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<KnittingPlan>().Find(v1.ID);
                var data2 = context.Set<KnittingPlan>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "BGpxxdcrkL";
                v.CompanyName = "UaOAtzPJpYxpXF40LrKu0SyfojeSGzj0";
                v.CompanyFullName = "KlNfnvEpCmbTH3mL9EzRbEZyRVL9T";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.DyeingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "N3ISbnOC";
                v.Adress = "E1";
                v.TaxNO = "FF5eb9jEAVXO0Ro2WAJ";
                v.InvoiceInfo = "wuztDs9sD3chYWg";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "OqOpIeeIGZ17";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "Ah";
                v.MobilePhone = "Npgtcei";
                v.Address = "5AkJ";
                v.Phone = "BTBs6J1";
                v.Email = "jgKgWkf59Xm";
                v.WeChat = "U7oh";
                v.QQ = "Tykkix";
                v.Fax = "auAJlVwUTo";
                v.Remark = "Og4asNZLUGY";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictType()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 50;
                v.Name = "RcMQfzZHO8O";
                v.Description = "tsxytfJZ1N";
                v.DictOrder = 48;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictItem()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 51;
                v.DictTypeId = AddDictType();
                v.ItemName = "u3gqWUm8kpj";
                v.Description = "JRiQwF1BtvS3E3Stz";
                v.DictOrder = 81;
                v.Remark = "3o";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "G4et5gxOwZJ6JtTchoBjpc2pyDbjyWmbsnburQ";
                v.ProductName = "zyANRG1JVn2ob8BoWpj5iVouu1Xy";
                v.CategoryId = AddDictItem();
                v.Contents = "QOzuUILyCsY4O5KCagR4yJzhKkDEX12Cqnk3baQNTUPK5wE25oq0KSTsZXEMZRD";
                v.Spec = "1avn3Sn8hn1ig";
                v.GSM = 27;
                v.Width = 120;
                v.DyeingProductName = "QZ1mVU789mCk3Fkp";
                v.PileLength = 74;
                v.DyeingProcess = "ZKRQwfIfxx";
                v.KnittingProcess = "mJ2z2fJwnX";
                v.FinishingProcess = "4rc4ILbtgcK7k4vx2m";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2025-07-16 13:47:32");
                v.DeliveryDate = DateTime.Parse("2025-02-15 13:47:32");
                v.CustomerId = AddCompany();
                v.OrderNo = "JayOiqqzG8uDUgln";
                v.CustomerOrderNo = "d2E95OCU3bewcm3OiKBlLtHpyJcM37Z3r2MHW4gy09h";
                v.MerchandiserId = AddContact();
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.DyeingProductName = "zT";
                v.Light = TEX.Model.Models.LightEnum.D65_LED;
                v.Light2 = TEX.Model.Models.LightEnum.DayLight;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.KG;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.CNY;
                v.TotalMeters = 55;
                v.TotalYards = 15;
                v.TotalWeight = 31;
                v.TotalAmount = 75;
                v.CompletedStatus = TEX.Model.Models.CompletedStatusEnum.StandBy;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "9OjEo0AVsH";
                v.AuditedComment = "9qMz1A5HxMqzGtBs";
                v.Remark = "ABdmZV";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddKnittingProcess()
        {
            KnittingProcess v = new KnittingProcess();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProcessCode = "ehPh6IaIwyiuAwAHvczCcLEUEVJKF2gwU400";
                v.CategoryId = AddDictItem();
                v.MachineInfoJson = "P6uF";
                v.Weight = 17;
                v.Width = 66;
                v.PileLength = 28;
                v.FinishedWeight = 14;
                v.FinishedWidth = 11;
                v.FinishedPileHeight = 59;
                v.YarnInfoJson = "akzDkrGSA2dVJtF";
                v.Remark = "R40ftkrj";
                context.Set<KnittingProcess>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
