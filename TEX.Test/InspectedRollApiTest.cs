using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Finished.InspectedRollVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class InspectedRollApiTest
    {
        private InspectedRollController _controller;
        private string _seed;

        public InspectedRollApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<InspectedRollController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new InspectedRollSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            InspectedRollVM vm = _controller.Wtm.CreateVM<InspectedRollVM>();
            InspectedRoll v = new InspectedRoll();
            
            v.OrderDetailId = AddOrderDetail();
            v.InspectedLot = "kmRP";
            v.RollNo = 56;
            v.Weight = 58;
            v.Meters = 85;
            v.Yards = 13;
            v.Score = 67;
            v.TotalScore = 41;
            v.Grade = "F7MAIbEceQfQsHObltWJYAeJebE4isy2sqo1iySXcU4kJN7VzvQebtqWqjnmv";
            v.ProcessName = "jSjEsxPBTg6uumF306u24oTAX3YSaDZnPfGJ4C";
            v.DefectsRecord = "3r";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "c";
            v.AuditedComment = "1C6xANZU2uIu";
            v.Remark = "TOrjf";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<InspectedRoll>().Find(v.ID);
                
                Assert.AreEqual(data.InspectedLot, "kmRP");
                Assert.AreEqual(data.RollNo, 56);
                Assert.AreEqual(data.Weight, 58);
                Assert.AreEqual(data.Meters, 85);
                Assert.AreEqual(data.Yards, 13);
                Assert.AreEqual(data.Score, 67);
                Assert.AreEqual(data.TotalScore, 41);
                Assert.AreEqual(data.Grade, "F7MAIbEceQfQsHObltWJYAeJebE4isy2sqo1iySXcU4kJN7VzvQebtqWqjnmv");
                Assert.AreEqual(data.ProcessName, "jSjEsxPBTg6uumF306u24oTAX3YSaDZnPfGJ4C");
                Assert.AreEqual(data.DefectsRecord, "3r");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "c");
                Assert.AreEqual(data.AuditedComment, "1C6xANZU2uIu");
                Assert.AreEqual(data.Remark, "TOrjf");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            InspectedRoll v = new InspectedRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.OrderDetailId = AddOrderDetail();
                v.InspectedLot = "kmRP";
                v.RollNo = 56;
                v.Weight = 58;
                v.Meters = 85;
                v.Yards = 13;
                v.Score = 67;
                v.TotalScore = 41;
                v.Grade = "F7MAIbEceQfQsHObltWJYAeJebE4isy2sqo1iySXcU4kJN7VzvQebtqWqjnmv";
                v.ProcessName = "jSjEsxPBTg6uumF306u24oTAX3YSaDZnPfGJ4C";
                v.DefectsRecord = "3r";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "c";
                v.AuditedComment = "1C6xANZU2uIu";
                v.Remark = "TOrjf";
                context.Set<InspectedRoll>().Add(v);
                context.SaveChanges();
            }

            InspectedRollVM vm = _controller.Wtm.CreateVM<InspectedRollVM>();
            var oldID = v.ID;
            v = new InspectedRoll();
            v.ID = oldID;
       		
            v.InspectedLot = "s43IG7U0y";
            v.RollNo = 24;
            v.Weight = 92;
            v.Meters = 70;
            v.Yards = 51;
            v.Score = 0;
            v.TotalScore = 84;
            v.Grade = "vDBER5dW35ZeLcsW1eHNVOej0awWnlwxDbGwJvHJ6t";
            v.ProcessName = "COnmWD7Fuz9aBy18GA85Vy1cW";
            v.DefectsRecord = "NRsTRjVyx3jm";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
            v.AuditedBy = "kpwwxOaGmvTP8Up";
            v.AuditedComment = "h";
            v.Remark = "1ChIssK3Y";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.OrderDetailId", "");
            vm.FC.Add("Entity.InspectedLot", "");
            vm.FC.Add("Entity.RollNo", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Yards", "");
            vm.FC.Add("Entity.Score", "");
            vm.FC.Add("Entity.Totalscroe", "");
            vm.FC.Add("Entity.Grade", "");
            vm.FC.Add("Entity.Processname", "");
            vm.FC.Add("Entity.DefectsRecord", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.AuditedComment", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<InspectedRoll>().Find(v.ID);
 				
                Assert.AreEqual(data.InspectedLot, "s43IG7U0y");
                Assert.AreEqual(data.RollNo, 24);
                Assert.AreEqual(data.Weight, 92);
                Assert.AreEqual(data.Meters, 70);
                Assert.AreEqual(data.Yards, 51);
                Assert.AreEqual(data.Score, 0);
                //Assert.AreEqual(data.TotalScore, 84);//TODO 莫名奇妙和改之前一样,而实际页面修改是成功的
                Assert.AreEqual(data.Grade, "vDBER5dW35ZeLcsW1eHNVOej0awWnlwxDbGwJvHJ6t");
                Assert.AreEqual(data.ProcessName, "COnmWD7Fuz9aBy18GA85Vy1cW");
                Assert.AreEqual(data.DefectsRecord, "NRsTRjVyx3jm");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedFailed);
                Assert.AreEqual(data.AuditedBy, "kpwwxOaGmvTP8Up");
                Assert.AreEqual(data.AuditedComment, "h");
                Assert.AreEqual(data.Remark, "1ChIssK3Y");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

        [TestMethod]
        public void RollTest()
        {
            InspectedRoll v = new InspectedRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v.OrderDetailId = AddOrderDetail();
                v.InspectedLot = "kmRP";
                v.RollNo = 56;
                v.Weight = 58;
                v.Meters = 85;
                v.Yards = 13;
                v.Score = 67;
                v.TotalScore = 41;
                v.Grade = "1";
                v.ProcessName = "22";
                v.DefectsRecord = "3r";
            }
            var rv = _controller.Roll();
            Assert.IsNotNull(rv);
        }
            [TestMethod]
        public void GetTest()
        {
            InspectedRoll v = new InspectedRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.OrderDetailId = AddOrderDetail();
                v.InspectedLot = "kmRP";
                v.RollNo = 56;
                v.Weight = 58;
                v.Meters = 85;
                v.Yards = 13;
                v.Score = 67;
                v.TotalScore = 41;
                v.Grade = "F7MAIbEceQfQsHObltWJYAeJebE4isy2sqo1iySXcU4kJN7VzvQebtqWqjnmv";
                v.ProcessName = "jSjEsxPBTg6uumF306u24oTAX3YSaDZnPfGJ4C";
                v.DefectsRecord = "3r";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "c";
                v.AuditedComment = "1C6xANZU2uIu";
                v.Remark = "TOrjf";
                context.Set<InspectedRoll>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            InspectedRoll v1 = new InspectedRoll();
            InspectedRoll v2 = new InspectedRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.OrderDetailId = AddOrderDetail();
                v1.InspectedLot = "kmRP";
                v1.RollNo = 56;
                v1.Weight = 58;
                v1.Meters = 85;
                v1.Yards = 13;
                v1.Score = 67;
                v1.TotalScore = 41;
                v1.Grade = "F7MAIbEceQfQsHObltWJYAeJebE4isy2sqo1iySXcU4kJN7VzvQebtqWqjnmv";
                v1.ProcessName = "jSjEsxPBTg6uumF306u24oTAX3YSaDZnPfGJ4C";
                v1.DefectsRecord = "3r";
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v1.AuditedBy = "c";
                v1.AuditedComment = "1C6xANZU2uIu";
                v1.Remark = "TOrjf";
                v2.OrderDetailId = v1.OrderDetailId; 
                v2.InspectedLot = "s43IG7U0y";
                v2.RollNo = 24;
                v2.Weight = 92;
                v2.Meters = 70;
                v2.Yards = 51;
                v2.Score = 0;
                v2.TotalScore = 84;
                v2.Grade = "vDBER5dW35ZeLcsW1eHNVOej0awWnlwxDbGwJvHJ6t";
                v2.ProcessName = "COnmWD7Fuz9aBy18GA85Vy1cW";
                v2.DefectsRecord = "NRsTRjVyx3jm";
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v2.AuditedBy = "kpwwxOaGmvTP8Up";
                v2.AuditedComment = "h";
                v2.Remark = "1ChIssK3Y";
                context.Set<InspectedRoll>().Add(v1);
                context.Set<InspectedRoll>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<InspectedRoll>().Find(v1.ID);
                var data2 = context.Set<InspectedRoll>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "yxKuDAxJLRAku0WrYyxoHfeg";
                v.CompanyName = "30N9JXdHeC9hTs";
                v.CompanyFullName = "yyUgkmHtvb2XdY0VrsX79hsKysQu8LuLtEbBPKddAnWEO4ZNlsPl49B1";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.FinishingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                v.ContactPhone = "QgprY";
                v.Adress = "u";
                v.TaxNO = "4gn3";
                v.InvoiceInfo = "C6LfEzxc";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "wLUyFs5q0ENPd4ETPL";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "J7fi2";
                v.MobilePhone = "xtbuJ2ZzuX6oX";
                v.Address = "b6rsuQ23l3eRootxH9";
                v.Phone = "a";
                v.Email = "E7Q0DFKEm";
                v.WeChat = "pJXRnXsUO";
                v.QQ = "wa";
                v.Fax = "cfnBxV4hgXJ0D3o";
                v.Remark = "j9VS9ZD53pGCLS";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "v4DzLHZJqvMz66deITh1Tz0nc7evsorC";
                v.ProductName = "GACpRFjje0IxyiEKR1rMzUjh1bnNWwvfzag1YdzlQ4PlfZ0p";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Others;
                v.Contents = "n3ds7E487rgDTF8jt5UfrMbFe0gLFE9OE6cBFV8PxHuKSAIvI5NGhJp9x05XXtV";
                v.Spec = "5He9vz8NbiRIuCT0EcZ0YrNZR0a5897QWSqN4iBEVxh4iFF";
                v.GSM = 60;
                v.Width = 179;
                v.DyeingProductName = "v7NcMZKX";
                v.PileLength = 17;
                v.DyeingProcess = "g0T0";
                v.KnittingProcess = "U7O";
                v.FinishingProcess = "F17S7zS";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2023-01-06 19:19:23");
                v.DeliveryDate = DateTime.Parse("2025-03-12 19:19:23");
                v.CustomerId = AddCompany();
                v.OrderNo = "PVlSp9BLhVPki8vl8J9k3nTKuR21G9sWIujoALVKAVIVO2PlIsf7";
                v.CustomerOrderNo = "SXCbeDSx2WIY1kgTHmToD8HPu1pgVf317jesnTUo2nvOt6M7pOGNhvQATxC1";
                v.MerchandiserId = AddContact();
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Knitting;
                v.ProductId = AddProduct();
                v.DyeingProductName = "1ggTgcMpOmQgri4";
                v.Light = TEX.Model.Models.LightEnum.TL84;
                v.Light2 = TEX.Model.Models.LightEnum.D65_LED;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.GBP;
                v.TotalMeters = 17;
                v.TotalYards = 19;
                v.TotalWeight = 59;
                v.TotalAmount = 87;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "s7";
                v.AuditedComment = "E9qv919LJ3d2rO0i";
                v.Remark = "4sKGDlyqeIFu7g75i";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddOrderDetail()
        {
            OrderDetail v = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.PurchaseOrderId = AddPurchaseOrder();
                v.Color = "cVz6EkbRTRxe17P47WYbmhSj8xMpYyj";
                v.EngColor = "6GQhpcYmD2N";
                v.ColorCode = "BnA8JS73UhAk9u";
                v.Meters = 13;
                v.KG = 85;
                v.Yards = 16;
                v.Price = 34;
                v.Amount = 45;
                v.Remark = "dkqGSVcxSIut9c";
                context.Set<OrderDetail>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
