using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.PatternDetailVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class PatternDetailApiTest
    {
        private PatternDetailController _controller;
        private string _seed;

        public PatternDetailApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<PatternDetailController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new PatternDetailSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content) == false);
        }

        [TestMethod]
        public void CreateTest()
        {
            PatternDetailVM vm = _controller.Wtm.CreateVM<PatternDetailVM>();
            PatternDetail v = new PatternDetail();

            v.CreateDate = DateTime.Parse("2024-02-10 20:20:15");
            v.PatternId = AddPattern();
            v.PatternCode = 83;
            v.PatternVersion = 1;
            v.FabricCategory = "rJlmf";
            v.PatternRepeatWidth = 1;
            v.PatternRepeatHeight = 9;
            v.RequiredFullWidth = 24;
            v.RequiredGsm = 0;
            v.RequiredCuttableWidth = 79;
            v.MachineInch = 45;
            v.MachineTotalNeedles = 27;
            v.MachineSpec = "K0QV9fhdMY6yY";
            v.JacquardFeed = 48;
            v.PatternWeftPoint = 46;
            v.PatternWarpPoint = 67;
            v.GreigeRepeatWidth = 50;
            v.GreigeRepeatHeight = 71;
            v.GreigeWidth = 53;
            v.GreigeGsm = 30;
            v.FabricRepeatWidth = 21;
            v.FabricRepeatHeight = 54;
            v.FabricFullWidth = 89;
            v.FabricGsm = 90;
            v.VersionModified = "HQY5pNehjPlGE";
            v.Remark = "09oGahGEhQh6o";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<PatternDetail>().Find(v.ID);

                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-02-10 20:20:15"));
                Assert.AreEqual(data.PatternCode, 83);
                Assert.AreEqual(data.PatternVersion, 1);
                Assert.AreEqual(data.FabricCategory, "rJlmf");
                Assert.AreEqual(data.PatternRepeatWidth, 1);
                Assert.AreEqual(data.PatternRepeatHeight, 9);
                Assert.AreEqual(data.RequiredFullWidth, 24);
                Assert.AreEqual(data.RequiredGsm, 0);
                Assert.AreEqual(data.RequiredCuttableWidth, 79);
                Assert.AreEqual(data.MachineInch, 45);
                Assert.AreEqual(data.MachineTotalNeedles, 27);
                Assert.AreEqual(data.MachineSpec, "K0QV9fhdMY6yY");
                Assert.AreEqual(data.JacquardFeed, 48);
                Assert.AreEqual(data.PatternWeftPoint, 46);
                Assert.AreEqual(data.PatternWarpPoint, 67);
                Assert.AreEqual(data.GreigeRepeatWidth, 50);
                Assert.AreEqual(data.GreigeRepeatHeight, 71);
                Assert.AreEqual(data.GreigeWidth, 53);
                Assert.AreEqual(data.GreigeGsm, 30);
                Assert.AreEqual(data.FabricRepeatWidth, 21);
                Assert.AreEqual(data.FabricRepeatHeight, 54);
                Assert.AreEqual(data.FabricFullWidth, 89);
                Assert.AreEqual(data.FabricGsm, 90);
                Assert.AreEqual(data.VersionModified, "HQY5pNehjPlGE");
                Assert.AreEqual(data.Remark, "09oGahGEhQh6o");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            PatternDetail v = new PatternDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v.CreateDate = DateTime.Parse("2024-02-10 20:20:15");
                v.PatternId = AddPattern();
                v.PatternCode = 83;
                v.PatternVersion = 2;
                v.FabricCategory = "rJlmf";
                v.PatternRepeatWidth = 1;
                v.PatternRepeatHeight = 9;
                v.RequiredFullWidth = 24;
                v.RequiredGsm = 0;
                v.RequiredCuttableWidth = 79;
                v.MachineInch = 45;
                v.MachineTotalNeedles = 27;
                v.MachineSpec = "K0QV9fhdMY6yY";
                v.JacquardFeed = 48;
                v.PatternWeftPoint = 46;
                v.PatternWarpPoint = 67;
                v.GreigeRepeatWidth = 50;
                v.GreigeRepeatHeight = 71;
                v.GreigeWidth = 53;
                v.GreigeGsm = 30;
                v.FabricRepeatWidth = 21;
                v.FabricRepeatHeight = 54;
                v.FabricFullWidth = 89;
                v.FabricGsm = 90;
                v.VersionModified = "HQY5pNehjPlGE";
                v.Remark = "09oGahGEhQh6o";
                context.Set<PatternDetail>().Add(v);
                context.SaveChanges();
            }

            PatternDetailVM vm = _controller.Wtm.CreateVM<PatternDetailVM>();
            var oldID = v.ID;
            v = new PatternDetail();
            v.ID = oldID;

            v.CreateDate = DateTime.Parse("2024-10-20 20:20:15");
            v.PatternCode = 51;
            v.PatternVersion = 2;
            v.FabricCategory = "DTcc6rqulmLFKkSr";
            v.PatternRepeatWidth = 46;
            v.PatternRepeatHeight = 97;
            v.RequiredFullWidth = 69;
            v.RequiredGsm = 0;
            v.RequiredCuttableWidth = 13;
            v.MachineInch = 89;
            v.MachineTotalNeedles = 87;
            v.MachineSpec = "FNmsm5qP9yq6zjxk0o";
            v.JacquardFeed = 77;
            v.PatternWeftPoint = 55;
            v.PatternWarpPoint = 35;
            v.GreigeRepeatWidth = 75;
            v.GreigeRepeatHeight = 65;
            v.GreigeWidth = 0;
            v.GreigeGsm = 74;
            v.FabricRepeatWidth = 35;
            v.FabricRepeatHeight = 46;
            v.FabricFullWidth = 48;
            v.FabricGsm = 21;
            v.VersionModified = "c83utLDH";
            v.Remark = "fQf60s0UiVKgWdR2pA";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();

            vm.FC.Add("Entity.CreateDate", "");
            vm.FC.Add("Entity.PatternId", "");
            vm.FC.Add("Entity.PatternCode", "");
            vm.FC.Add("Entity.PatternVersion", "");
            vm.FC.Add("Entity.FabricCategory", "");
            vm.FC.Add("Entity.PatternRepeatWidth", "");
            vm.FC.Add("Entity.PatternRepeatHeight", "");
            vm.FC.Add("Entity.RequiredFullWidth", "");
            vm.FC.Add("Entity.RequiredGsm", "");
            vm.FC.Add("Entity.RequiredCuttableWidth", "");
            vm.FC.Add("Entity.MachineInch", "");
            vm.FC.Add("Entity.MachineTotalNeedles", "");
            vm.FC.Add("Entity.MachineSpec", "");
            vm.FC.Add("Entity.JacquardFeed", "");
            vm.FC.Add("Entity.PatternWeftPoint", "");
            vm.FC.Add("Entity.PatternWarpPoint", "");
            vm.FC.Add("Entity.GreigeRepeatWidth", "");
            vm.FC.Add("Entity.GreigeRepeatHeight", "");
            vm.FC.Add("Entity.GreigeWidth", "");
            vm.FC.Add("Entity.GreigeGsm", "");
            vm.FC.Add("Entity.FabricRepeatWidth", "");
            vm.FC.Add("Entity.FabricRepeatHeight", "");
            vm.FC.Add("Entity.FabricFullWidth", "");
            vm.FC.Add("Entity.FabricGsm", "");
            vm.FC.Add("Entity.VersionModified", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<PatternDetail>().Find(v.ID);

                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-10-20 20:20:15"));
                Assert.AreEqual(data.PatternCode, 51);
                Assert.AreEqual(data.PatternVersion, "75kWkWP4fIFkR6");
                Assert.AreEqual(data.FabricCategory, "DTcc6rqulmLFKkSr");
                Assert.AreEqual(data.PatternRepeatWidth, 46);
                Assert.AreEqual(data.PatternRepeatHeight, 97);
                Assert.AreEqual(data.RequiredFullWidth, 69);
                Assert.AreEqual(data.RequiredGsm, 0);
                Assert.AreEqual(data.RequiredCuttableWidth, 13);
                Assert.AreEqual(data.MachineInch, 89);
                Assert.AreEqual(data.MachineTotalNeedles, 87);
                Assert.AreEqual(data.MachineSpec, "FNmsm5qP9yq6zjxk0o");
                Assert.AreEqual(data.JacquardFeed, 77);
                Assert.AreEqual(data.PatternWeftPoint, 55);
                Assert.AreEqual(data.PatternWarpPoint, 35);
                Assert.AreEqual(data.GreigeRepeatWidth, 75);
                Assert.AreEqual(data.GreigeRepeatHeight, 65);
                Assert.AreEqual(data.GreigeWidth, 0);
                Assert.AreEqual(data.GreigeGsm, 74);
                Assert.AreEqual(data.FabricRepeatWidth, 35);
                Assert.AreEqual(data.FabricRepeatHeight, 46);
                Assert.AreEqual(data.FabricFullWidth, 48);
                Assert.AreEqual(data.FabricGsm, 21);
                Assert.AreEqual(data.VersionModified, "c83utLDH");
                Assert.AreEqual(data.Remark, "fQf60s0UiVKgWdR2pA");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

        [TestMethod]
        public void GetTest()
        {
            PatternDetail v = new PatternDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v.CreateDate = DateTime.Parse("2024-02-10 20:20:15");
                v.PatternId = AddPattern();
                v.PatternCode = 83;
                v.PatternVersion = 1;
                v.FabricCategory = "rJlmf";
                v.PatternRepeatWidth = 1;
                v.PatternRepeatHeight = 9;
                v.RequiredFullWidth = 24;
                v.RequiredGsm = 0;
                v.RequiredCuttableWidth = 79;
                v.MachineInch = 45;
                v.MachineTotalNeedles = 27;
                v.MachineSpec = "K0QV9fhdMY6yY";
                v.JacquardFeed = 48;
                v.PatternWeftPoint = 46;
                v.PatternWarpPoint = 67;
                v.GreigeRepeatWidth = 50;
                v.GreigeRepeatHeight = 71;
                v.GreigeWidth = 53;
                v.GreigeGsm = 30;
                v.FabricRepeatWidth = 21;
                v.FabricRepeatHeight = 54;
                v.FabricFullWidth = 89;
                v.FabricGsm = 90;
                v.VersionModified = "HQY5pNehjPlGE";
                v.Remark = "09oGahGEhQh6o";
                context.Set<PatternDetail>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            PatternDetail v1 = new PatternDetail();
            PatternDetail v2 = new PatternDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v1.CreateDate = DateTime.Parse("2024-02-10 20:20:15");
                v1.PatternId = AddPattern();
                v1.PatternCode = 83;
                v1.PatternVersion = 1;
                v1.FabricCategory = "rJlmf";
                v1.PatternRepeatWidth = 1;
                v1.PatternRepeatHeight = 9;
                v1.RequiredFullWidth = 24;
                v1.RequiredGsm = 0;
                v1.RequiredCuttableWidth = 79;
                v1.MachineInch = 45;
                v1.MachineTotalNeedles = 27;
                v1.MachineSpec = "K0QV9fhdMY6yY";
                v1.JacquardFeed = 48;
                v1.PatternWeftPoint = 46;
                v1.PatternWarpPoint = 67;
                v1.GreigeRepeatWidth = 50;
                v1.GreigeRepeatHeight = 71;
                v1.GreigeWidth = 53;
                v1.GreigeGsm = 30;
                v1.FabricRepeatWidth = 21;
                v1.FabricRepeatHeight = 54;
                v1.FabricFullWidth = 89;
                v1.FabricGsm = 90;
                v1.VersionModified = "HQY5pNehjPlGE";
                v1.Remark = "09oGahGEhQh6o";
                v2.CreateDate = DateTime.Parse("2024-10-20 20:20:15");
                v2.PatternId = v1.PatternId;
                v2.PatternCode = 51;
                v2.PatternVersion = 2;
                v2.FabricCategory = "DTcc6rqulmLFKkSr";
                v2.PatternRepeatWidth = 46;
                v2.PatternRepeatHeight = 97;
                v2.RequiredFullWidth = 69;
                v2.RequiredGsm = 0;
                v2.RequiredCuttableWidth = 13;
                v2.MachineInch = 89;
                v2.MachineTotalNeedles = 87;
                v2.MachineSpec = "FNmsm5qP9yq6zjxk0o";
                v2.JacquardFeed = 77;
                v2.PatternWeftPoint = 55;
                v2.PatternWarpPoint = 35;
                v2.GreigeRepeatWidth = 75;
                v2.GreigeRepeatHeight = 65;
                v2.GreigeWidth = 0;
                v2.GreigeGsm = 74;
                v2.FabricRepeatWidth = 35;
                v2.FabricRepeatHeight = 46;
                v2.FabricFullWidth = 48;
                v2.FabricGsm = 21;
                v2.VersionModified = "c83utLDH";
                v2.Remark = "fQf60s0UiVKgWdR2pA";
                context.Set<PatternDetail>().Add(v1);
                context.Set<PatternDetail>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<PatternDetail>().Find(v1.ID);
                var data2 = context.Set<PatternDetail>().Find(v2.ID);
                Assert.AreEqual(data1, null);
                Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] { });
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddPattern()
        {
            Pattern v = new Pattern();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {

                    v.CreateDate = DateTime.Parse("2025-04-22 20:20:15");
                    v.PatternName = "knRW191kStJJEACzEGS";
                    v.Customer = "fm7B59Tve2KWFC3";
                    v.Description = "1rnpGP";
                    v.Requirements = "Vcko";
                    v.Remark = "g7JJt0MmAExG";
                    context.Set<Pattern>().Add(v);
                    context.SaveChanges();
                }
                catch { }
            }
            return v.ID;
        }


    }
}
