using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.BasicInfo.ContactVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class ContactApiTest
    {
        private ContactController _controller;
        private string _seed;

        public ContactApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<ContactController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new ContactSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ContactVM vm = _controller.Wtm.CreateVM<ContactVM>();
            Contact v = new Contact();
            
            v.ContactName = "PJuI2lhPg33R43B0pd";
            v.AffiliationCompanyId = AddCompany();
            v.PositionTitle = "IfkP5F";
            v.MobilePhone = "74LRuh6K9FEz8aJXi0a";
            v.Address = "VZQ";
            v.Phone = "UEHJtttFL2MU";
            v.Email = "Ar00nGylssFuuN3";
            v.WeChat = "GWXkvBmx3LBZUNl";
            v.QQ = "TQmoJxzhqCTl7ocNPYg";
            v.Fax = "fs5Yg";
            v.Remark = "jI1tiZUXSI2L1rcwmj";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Contact>().Find(v.ID);
                
                Assert.AreEqual(data.ContactName, "PJuI2lhPg33R43B0pd");
                Assert.AreEqual(data.PositionTitle, "IfkP5F");
                Assert.AreEqual(data.MobilePhone, "74LRuh6K9FEz8aJXi0a");
                Assert.AreEqual(data.Address, "VZQ");
                Assert.AreEqual(data.Phone, "UEHJtttFL2MU");
                Assert.AreEqual(data.Email, "Ar00nGylssFuuN3");
                Assert.AreEqual(data.WeChat, "GWXkvBmx3LBZUNl");
                Assert.AreEqual(data.QQ, "TQmoJxzhqCTl7ocNPYg");
                Assert.AreEqual(data.Fax, "fs5Yg");
                Assert.AreEqual(data.Remark, "jI1tiZUXSI2L1rcwmj");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ContactName = "PJuI2lhPg33R43B0pd";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "IfkP5F";
                v.MobilePhone = "74LRuh6K9FEz8aJXi0a";
                v.Address = "VZQ";
                v.Phone = "UEHJtttFL2MU";
                v.Email = "Ar00nGylssFuuN3";
                v.WeChat = "GWXkvBmx3LBZUNl";
                v.QQ = "TQmoJxzhqCTl7ocNPYg";
                v.Fax = "fs5Yg";
                v.Remark = "jI1tiZUXSI2L1rcwmj";
                context.Set<Contact>().Add(v);
                context.SaveChanges();
            }

            ContactVM vm = _controller.Wtm.CreateVM<ContactVM>();
            var oldID = v.ID;
            v = new Contact();
            v.ID = oldID;
       		
            v.ContactName = "xlVJE";
            v.PositionTitle = "FhqD";
            v.MobilePhone = "wP0UWabp54Rlaz";
            v.Address = "e3aG";
            v.Phone = "C08";
            v.Email = "8vGgj9SEu6Ce5cvC";
            v.WeChat = "537tiKJKOdE5";
            v.QQ = "9eyi37";
            v.Fax = "4bdjvqAQDtvY";
            v.Remark = "8eRkLW86dAuIYM";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ContactName", "");
            vm.FC.Add("Entity.AffiliationCompanyId", "");
            vm.FC.Add("Entity.PositionTitle", "");
            vm.FC.Add("Entity.MobilePhone", "");
            vm.FC.Add("Entity.Address", "");
            vm.FC.Add("Entity.Phone", "");
            vm.FC.Add("Entity.Email", "");
            vm.FC.Add("Entity.WeChat", "");
            vm.FC.Add("Entity.QQ", "");
            vm.FC.Add("Entity.Fax", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Contact>().Find(v.ID);
 				
                Assert.AreEqual(data.ContactName, "xlVJE");
                Assert.AreEqual(data.PositionTitle, "FhqD");
                Assert.AreEqual(data.MobilePhone, "wP0UWabp54Rlaz");
                Assert.AreEqual(data.Address, "e3aG");
                Assert.AreEqual(data.Phone, "C08");
                Assert.AreEqual(data.Email, "8vGgj9SEu6Ce5cvC");
                Assert.AreEqual(data.WeChat, "537tiKJKOdE5");
                Assert.AreEqual(data.QQ, "9eyi37");
                Assert.AreEqual(data.Fax, "4bdjvqAQDtvY");
                Assert.AreEqual(data.Remark, "8eRkLW86dAuIYM");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ContactName = "PJuI2lhPg33R43B0pd";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "IfkP5F";
                v.MobilePhone = "74LRuh6K9FEz8aJXi0a";
                v.Address = "VZQ";
                v.Phone = "UEHJtttFL2MU";
                v.Email = "Ar00nGylssFuuN3";
                v.WeChat = "GWXkvBmx3LBZUNl";
                v.QQ = "TQmoJxzhqCTl7ocNPYg";
                v.Fax = "fs5Yg";
                v.Remark = "jI1tiZUXSI2L1rcwmj";
                context.Set<Contact>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            Contact v1 = new Contact();
            Contact v2 = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ContactName = "PJuI2lhPg33R43B0pd";
                v1.AffiliationCompanyId = AddCompany();
                v1.PositionTitle = "IfkP5F";
                v1.MobilePhone = "74LRuh6K9FEz8aJXi0a";
                v1.Address = "VZQ";
                v1.Phone = "UEHJtttFL2MU";
                v1.Email = "Ar00nGylssFuuN3";
                v1.WeChat = "GWXkvBmx3LBZUNl";
                v1.QQ = "TQmoJxzhqCTl7ocNPYg";
                v1.Fax = "fs5Yg";
                v1.Remark = "jI1tiZUXSI2L1rcwmj";
                v2.ContactName = "xlVJE";
                v2.AffiliationCompanyId = v1.AffiliationCompanyId; 
                v2.PositionTitle = "FhqD";
                v2.MobilePhone = "wP0UWabp54Rlaz";
                v2.Address = "e3aG";
                v2.Phone = "C08";
                v2.Email = "8vGgj9SEu6Ce5cvC";
                v2.WeChat = "537tiKJKOdE5";
                v2.QQ = "9eyi37";
                v2.Fax = "4bdjvqAQDtvY";
                v2.Remark = "8eRkLW86dAuIYM";
                context.Set<Contact>().Add(v1);
                context.Set<Contact>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<Contact>().Find(v1.ID);
                var data2 = context.Set<Contact>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "iirDk0KZntDxjFWkfS5fqCgNUaxQ";
                v.CompanyName = "qwiQI00cxdSbmcxpf1G5vqEy";
                v.CompanyFullName = "o0qfCqYF9VvfLR68dFDour1Rz25ne9dvb";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.FabricVender;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "zwZV1vqLH1VA";
                v.Adress = "0fKtI";
                v.TaxNO = "lPKzy";
                v.InvoiceInfo = "X86xmp93MkHL";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
