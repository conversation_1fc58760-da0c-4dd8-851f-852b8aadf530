using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.GreigeOutboundBillVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class GreigeOutboundBillApiTest
    {
        private GreigeOutboundBillController _controller;
        private string _seed;

        public GreigeOutboundBillApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<GreigeOutboundBillController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new GreigeOutboundBillSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            GreigeOutboundBillVM vm = _controller.Wtm.CreateVM<GreigeOutboundBillVM>();
            GreigeOutboundBill v = new GreigeOutboundBill();
            
            v.BillNo = "qzle1IC0ZYpHxVm3RqbYAcUVXNhMaCx";
            v.OutboundDate = DateTime.Parse("2026-11-04 15:10:27");
            v.Purpose = "9bbeFQ7kN6bTICOfDNmR72s8d836O0p5HfyTTH37D4o";
            v.ReceiverId = AddCompany();
            v.DyeingFactoryId = AddCompany();
            v.Warehouse = "Q96zEVPT5yvWdo0R4IkJcgUAgsoT0Rk9sJzGW";
            v.TransportMethod = "dpmEk2LelJR66";
            v.DeliveryAddress = "GBsNEXsGqNTCjqOtyFopWTOFEmzS8RKaHH4Jf87iNao8xnrpgkAQQXd0mGzOCTNH3iBNcrH9vjNjSyVa1d4c7cOPb3Wk09d3JMSPKDYCoALg9i0uUilfZfWUv8595HOh6KBQ1k1QYus";
            v.TotalRolls = 60;
            v.TotalWeight = 36;
            v.TotalMeters = 10;
            v.TotalYards = 73;
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
            v.AuditedBy = "hE";
            v.AuditedComment = "T9Q8uYKsEPS";
            v.Remark = "t8N3OItkoP8aylzbGIa";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<GreigeOutboundBill>().Find(v.ID);
                
                Assert.AreEqual(data.BillNo, "qzle1IC0ZYpHxVm3RqbYAcUVXNhMaCx");
                Assert.AreEqual(data.OutboundDate, DateTime.Parse("2026-11-04 15:10:27"));
                Assert.AreEqual(data.Purpose, "9bbeFQ7kN6bTICOfDNmR72s8d836O0p5HfyTTH37D4o");
                Assert.AreEqual(data.Warehouse, "Q96zEVPT5yvWdo0R4IkJcgUAgsoT0Rk9sJzGW");
                Assert.AreEqual(data.TransportMethod, "dpmEk2LelJR66");
                Assert.AreEqual(data.DeliveryAddress, "GBsNEXsGqNTCjqOtyFopWTOFEmzS8RKaHH4Jf87iNao8xnrpgkAQQXd0mGzOCTNH3iBNcrH9vjNjSyVa1d4c7cOPb3Wk09d3JMSPKDYCoALg9i0uUilfZfWUv8595HOh6KBQ1k1QYus");
                Assert.AreEqual(data.TotalRolls, 60);
                Assert.AreEqual(data.TotalWeight, 36);
                Assert.AreEqual(data.TotalMeters, 10);
                Assert.AreEqual(data.TotalYards, 73);
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedFailed);
                Assert.AreEqual(data.AuditedBy, "hE");
                Assert.AreEqual(data.AuditedComment, "T9Q8uYKsEPS");
                Assert.AreEqual(data.Remark, "t8N3OItkoP8aylzbGIa");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            GreigeOutboundBill v = new GreigeOutboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.BillNo = "qzle1IC0ZYpHxVm3RqbYAcUVXNhMaCx";
                v.OutboundDate = DateTime.Parse("2026-11-04 15:10:27");
                v.Purpose = "9bbeFQ7kN6bTICOfDNmR72s8d836O0p5HfyTTH37D4o";
                v.ReceiverId = AddCompany();
                v.DyeingFactoryId = AddCompany();
                v.Warehouse = "Q96zEVPT5yvWdo0R4IkJcgUAgsoT0Rk9sJzGW";
                v.TransportMethod = "dpmEk2LelJR66";
                v.DeliveryAddress = "GBsNEXsGqNTCjqOtyFopWTOFEmzS8RKaHH4Jf87iNao8xnrpgkAQQXd0mGzOCTNH3iBNcrH9vjNjSyVa1d4c7cOPb3Wk09d3JMSPKDYCoALg9i0uUilfZfWUv8595HOh6KBQ1k1QYus";
                v.TotalRolls = 60;
                v.TotalWeight = 36;
                v.TotalMeters = 10;
                v.TotalYards = 73;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "hE";
                v.AuditedComment = "T9Q8uYKsEPS";
                v.Remark = "t8N3OItkoP8aylzbGIa";
                context.Set<GreigeOutboundBill>().Add(v);
                context.SaveChanges();
            }

            GreigeOutboundBillVM vm = _controller.Wtm.CreateVM<GreigeOutboundBillVM>();
            var oldID = v.ID;
            v = new GreigeOutboundBill();
            v.ID = oldID;
       		
            v.BillNo = "XjN83Je3JdsaVwEaEWdq1Et44fWFIXqwr9CX3a440sPizJxQ72CNmdH1z1X4Mg";
            v.OutboundDate = DateTime.Parse("2024-06-24 15:10:27");
            v.Purpose = "qUwTAAsj";
            v.Warehouse = "UZNKVBKjUVeee";
            v.TransportMethod = "yZT33U0ttfJfMENJIrcZGElsut9H4z7unvP50";
            v.DeliveryAddress = "kop02xz9N7xUq1HIqnyCQpzWZu5so8wkho8X7pthQ93Bpd9aQNoGKNHbYFRgeEXuIrLEMFsFr29v2mF1prjpiaQVYBsIDxcEKOG4Tb79npUvfQvctbM4YdXeYxbDPRHYv4QMGe6SfUD7768GK0wjpVnPzQThTcBitjs";
            v.TotalRolls = 60;
            v.TotalWeight = 91;
            v.TotalMeters = 90;
            v.TotalYards = 98;
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "yc6ZQLq3gMHkVw72AB";
            v.AuditedComment = "T1";
            v.Remark = "0VggCP6KwnUPg";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.BillNo", "");
            vm.FC.Add("Entity.OutboundDate", "");
            vm.FC.Add("Entity.Purpose", "");
            vm.FC.Add("Entity.ReceiverId", "");
            vm.FC.Add("Entity.DyeingFactoryId", "");
            vm.FC.Add("Entity.Warehouse", "");
            vm.FC.Add("Entity.TransportMethod", "");
            vm.FC.Add("Entity.DeliveryAddress", "");
            vm.FC.Add("Entity.TotalRolls", "");
            vm.FC.Add("Entity.TotalWeight", "");
            vm.FC.Add("Entity.TotalMeters", "");
            vm.FC.Add("Entity.TotalYards", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.AuditedComment", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<GreigeOutboundBill>().Find(v.ID);
 				
                Assert.AreEqual(data.BillNo, "XjN83Je3JdsaVwEaEWdq1Et44fWFIXqwr9CX3a440sPizJxQ72CNmdH1z1X4Mg");
                Assert.AreEqual(data.OutboundDate, DateTime.Parse("2024-06-24 15:10:27"));
                Assert.AreEqual(data.Purpose, "qUwTAAsj");
                Assert.AreEqual(data.Warehouse, "UZNKVBKjUVeee");
                Assert.AreEqual(data.TransportMethod, "yZT33U0ttfJfMENJIrcZGElsut9H4z7unvP50");
                Assert.AreEqual(data.DeliveryAddress, "kop02xz9N7xUq1HIqnyCQpzWZu5so8wkho8X7pthQ93Bpd9aQNoGKNHbYFRgeEXuIrLEMFsFr29v2mF1prjpiaQVYBsIDxcEKOG4Tb79npUvfQvctbM4YdXeYxbDPRHYv4QMGe6SfUD7768GK0wjpVnPzQThTcBitjs");
                Assert.AreEqual(data.TotalRolls, 60);
                Assert.AreEqual(data.TotalWeight, 91);
                Assert.AreEqual(data.TotalMeters, 90);
                Assert.AreEqual(data.TotalYards, 98);
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "yc6ZQLq3gMHkVw72AB");
                Assert.AreEqual(data.AuditedComment, "T1");
                Assert.AreEqual(data.Remark, "0VggCP6KwnUPg");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            GreigeOutboundBill v = new GreigeOutboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.BillNo = "qzle1IC0ZYpHxVm3RqbYAcUVXNhMaCx";
                v.OutboundDate = DateTime.Parse("2026-11-04 15:10:27");
                v.Purpose = "9bbeFQ7kN6bTICOfDNmR72s8d836O0p5HfyTTH37D4o";
                v.ReceiverId = AddCompany();
                v.DyeingFactoryId = AddCompany();
                v.Warehouse = "Q96zEVPT5yvWdo0R4IkJcgUAgsoT0Rk9sJzGW";
                v.TransportMethod = "dpmEk2LelJR66";
                v.DeliveryAddress = "GBsNEXsGqNTCjqOtyFopWTOFEmzS8RKaHH4Jf87iNao8xnrpgkAQQXd0mGzOCTNH3iBNcrH9vjNjSyVa1d4c7cOPb3Wk09d3JMSPKDYCoALg9i0uUilfZfWUv8595HOh6KBQ1k1QYus";
                v.TotalRolls = 60;
                v.TotalWeight = 36;
                v.TotalMeters = 10;
                v.TotalYards = 73;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "hE";
                v.AuditedComment = "T9Q8uYKsEPS";
                v.Remark = "t8N3OItkoP8aylzbGIa";
                context.Set<GreigeOutboundBill>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            GreigeOutboundBill v1 = new GreigeOutboundBill();
            GreigeOutboundBill v2 = new GreigeOutboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.BillNo = "qzle1IC0ZYpHxVm3RqbYAcUVXNhMaCx";
                v1.OutboundDate = DateTime.Parse("2026-11-04 15:10:27");
                v1.Purpose = "9bbeFQ7kN6bTICOfDNmR72s8d836O0p5HfyTTH37D4o";
                v1.ReceiverId = AddCompany();
                v1.DyeingFactoryId = AddCompany();
                v1.Warehouse = "Q96zEVPT5yvWdo0R4IkJcgUAgsoT0Rk9sJzGW";
                v1.TransportMethod = "dpmEk2LelJR66";
                v1.DeliveryAddress = "GBsNEXsGqNTCjqOtyFopWTOFEmzS8RKaHH4Jf87iNao8xnrpgkAQQXd0mGzOCTNH3iBNcrH9vjNjSyVa1d4c7cOPb3Wk09d3JMSPKDYCoALg9i0uUilfZfWUv8595HOh6KBQ1k1QYus";
                v1.TotalRolls = 60;
                v1.TotalWeight = 36;
                v1.TotalMeters = 10;
                v1.TotalYards = 73;
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v1.AuditedBy = "hE";
                v1.AuditedComment = "T9Q8uYKsEPS";
                v1.Remark = "t8N3OItkoP8aylzbGIa";
                v2.BillNo = "XjN83Je3JdsaVwEaEWdq1Et44fWFIXqwr9CX3a440sPizJxQ72CNmdH1z1X4Mg";
                v2.OutboundDate = DateTime.Parse("2024-06-24 15:10:27");
                v2.Purpose = "qUwTAAsj";
                v2.ReceiverId = v1.ReceiverId; 
                v2.DyeingFactoryId = v1.DyeingFactoryId; 
                v2.Warehouse = "UZNKVBKjUVeee";
                v2.TransportMethod = "yZT33U0ttfJfMENJIrcZGElsut9H4z7unvP50";
                v2.DeliveryAddress = "kop02xz9N7xUq1HIqnyCQpzWZu5so8wkho8X7pthQ93Bpd9aQNoGKNHbYFRgeEXuIrLEMFsFr29v2mF1prjpiaQVYBsIDxcEKOG4Tb79npUvfQvctbM4YdXeYxbDPRHYv4QMGe6SfUD7768GK0wjpVnPzQThTcBitjs";
                v2.TotalRolls = 60;
                v2.TotalWeight = 91;
                v2.TotalMeters = 90;
                v2.TotalYards = 98;
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v2.AuditedBy = "yc6ZQLq3gMHkVw72AB";
                v2.AuditedComment = "T1";
                v2.Remark = "0VggCP6KwnUPg";
                context.Set<GreigeOutboundBill>().Add(v1);
                context.Set<GreigeOutboundBill>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<GreigeOutboundBill>().Find(v1.ID);
                var data2 = context.Set<GreigeOutboundBill>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "rlydfwBWCIjmcw";
                v.CompanyName = "HPdgEN1xi4A43";
                v.CompanyFullName = "8RjqBwV05CjWc5i9CEoYX2rYNENshI";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.FinishingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "vcX";
                v.Adress = "ZbBJ";
                v.TaxNO = "R4OTnonw335CUYMn";
                v.InvoiceInfo = "Fywbi0UmrDdh";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
