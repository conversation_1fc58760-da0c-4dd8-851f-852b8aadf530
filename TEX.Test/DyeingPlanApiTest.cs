using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Producttion.DyeingPlanVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class DyeingPlanApiTest
    {
        private DyeingPlanController _controller;
        private string _seed;

        public DyeingPlanApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<DyeingPlanController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new DyeingPlanSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            DyeingPlanVM vm = _controller.Wtm.CreateVM<DyeingPlanVM>();
            DyeingPlan v = new DyeingPlan();
            
            v.CreateDate = DateTime.Parse("2022-12-06 14:11:57");
            v.BillNo = "37";
            v.Version = 98;
            v.FinishingFactoryId = AddCompany();
            v.POrderId = AddPurchaseOrder();
            v.PlanBatch = "5aSWWlJGTWjHb2rBsyZERG2G2J32nrEDSW8D5a1kIrJH";
            v.FinishingProcess = "AAlZmViM2";
            //v.Width = "0LmP9U";
            //v.GSM = "FvvWR8Jc4WB0OBwpF";
            v.Light = TEX.Model.Models.LightEnum.U3000;
            v.Light2 = TEX.Model.Models.LightEnum.LED;
            v.GreigeBatch = "o2DQt";
            v.GreigeVenderId = AddCompany();
            v.DyeingDemand = "8uLvzg9Dx1QVD";
            v.PackDemand = "C1PVQjYXXOiH1Smj4H";
            v.AdditionalDemend = "CNbb5V9iq1";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "q7B";
            v.AuditedComment = "k7mHHVt5HFG4uASk";
            v.Remark = "E";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DyeingPlan>().Find(v.ID);
                
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2022-12-06 14:11:57"));
                Assert.AreEqual(data.BillNo, "37");
                Assert.AreEqual(data.Version, 98);
                Assert.AreEqual(data.PlanBatch, "5aSWWlJGTWjHb2rBsyZERG2G2J32nrEDSW8D5a1kIrJH");
                Assert.AreEqual(data.FinishingProcess, "AAlZmViM2");
                //Assert.AreEqual(data.Width, "0LmP9U");
                //Assert.AreEqual(data.GSM, "FvvWR8Jc4WB0OBwpF");
                Assert.AreEqual(data.Light, TEX.Model.Models.LightEnum.U3000);
                Assert.AreEqual(data.Light2, TEX.Model.Models.LightEnum.LED);
                Assert.AreEqual(data.GreigeBatch, "o2DQt");
                Assert.AreEqual(data.DyeingDemand, "8uLvzg9Dx1QVD");
                Assert.AreEqual(data.PackDemand, "C1PVQjYXXOiH1Smj4H");
                Assert.AreEqual(data.AdditionalDemend, "CNbb5V9iq1");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "q7B");
                Assert.AreEqual(data.AuditedComment, "k7mHHVt5HFG4uASk");
                Assert.AreEqual(data.Remark, "E");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            DyeingPlan v = new DyeingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.CreateDate = DateTime.Parse("2022-12-06 14:11:57");
                v.BillNo = "37";
                v.Version = 98;
                v.FinishingFactoryId = AddCompany();
                v.POrderId = AddPurchaseOrder();
                v.PlanBatch = "5aSWWlJGTWjHb2rBsyZERG2G2J32nrEDSW8D5a1kIrJH";
                v.FinishingProcess = "AAlZmViM2";
                //v.Width = "0LmP9U";
                //v.GSM = "FvvWR8Jc4WB0OBwpF";
                v.Light = TEX.Model.Models.LightEnum.U3000;
                v.Light2 = TEX.Model.Models.LightEnum.LED;
                v.GreigeBatch = "o2DQt";
                v.GreigeVenderId = AddCompany();
                v.DyeingDemand = "8uLvzg9Dx1QVD";
                v.PackDemand = "C1PVQjYXXOiH1Smj4H";
                v.AdditionalDemend = "CNbb5V9iq1";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "q7B";
                v.AuditedComment = "k7mHHVt5HFG4uASk";
                v.Remark = "E";
                context.Set<DyeingPlan>().Add(v);
                context.SaveChanges();
            }

            DyeingPlanVM vm = _controller.Wtm.CreateVM<DyeingPlanVM>();
            var oldID = v.ID;
            v = new DyeingPlan();
            v.ID = oldID;
       		
            v.CreateDate = DateTime.Parse("2024-07-28 14:11:57");
            v.BillNo = "47";
            v.Version = 81;
            v.PlanBatch = "u5R8w8JXHcEvo7SwynG2cANiURJQ11P2yunmFSLztnaazr1VYszTKx2xY";
            v.FinishingProcess = "7CV";
            //v.Width = "oeyQleTj7hWuG";
            //v.GSM = "nTjz7l930rTwM";
            v.Light = TEX.Model.Models.LightEnum.DayLight;
            v.Light2 = TEX.Model.Models.LightEnum.D65;
            v.GreigeBatch = "x85ISabUhLHNd6lDK";
            v.DyeingDemand = "PYKYyw8Q";
            v.PackDemand = "W2h7wUDdGToYC7vFZ";
            v.AdditionalDemend = "peyZotwTI6";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "Tgigq2UFRSomYZQ";
            v.AuditedComment = "sTMeJUfQHtuQi";
            v.Remark = "s11iD";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.CreateDate", "");
            vm.FC.Add("Entity.BillNo", "");
            vm.FC.Add("Entity.Version", "");
            vm.FC.Add("Entity.FinishingFactoryId", "");
            vm.FC.Add("Entity.POrderId", "");
            vm.FC.Add("Entity.PlanBatch", "");
            vm.FC.Add("Entity.FinishingProcess", "");
            vm.FC.Add("Entity.FabricId", "");
            vm.FC.Add("Entity.Width", "");
            vm.FC.Add("Entity.Gsm", "");
            vm.FC.Add("Entity.Light", "");
            vm.FC.Add("Entity.Light2", "");
            vm.FC.Add("Entity.GreigeBatch", "");
            vm.FC.Add("Entity.GreigeVenderId", "");
            vm.FC.Add("Entity.DyingDemand", "");
            vm.FC.Add("Entity.PackDemand", "");
            vm.FC.Add("Entity.AdditionalDemend", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.AuditedComment", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DyeingPlan>().Find(v.ID);
 				
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-07-28 14:11:57"));
                Assert.AreEqual(data.BillNo, "47");
                Assert.AreEqual(data.Version, 81);
                Assert.AreEqual(data.PlanBatch, "u5R8w8JXHcEvo7SwynG2cANiURJQ11P2yunmFSLztnaazr1VYszTKx2xY");
                Assert.AreEqual(data.FinishingProcess, "7CV");
                //Assert.AreEqual(data.Width, "oeyQleTj7hWuG");
                //Assert.AreEqual(data.GSM, "nTjz7l930rTwM");
                Assert.AreEqual(data.Light, TEX.Model.Models.LightEnum.DayLight);
                Assert.AreEqual(data.Light2, TEX.Model.Models.LightEnum.D65);
                Assert.AreEqual(data.GreigeBatch, "x85ISabUhLHNd6lDK");
                //Assert.AreEqual(data.DyeingDemand, "PYKYyw8Q");//莫名奇妙和改之前一样
                Assert.AreEqual(data.PackDemand, "W2h7wUDdGToYC7vFZ");
                Assert.AreEqual(data.AdditionalDemend, "peyZotwTI6");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "Tgigq2UFRSomYZQ");
                Assert.AreEqual(data.AuditedComment, "sTMeJUfQHtuQi");
                Assert.AreEqual(data.Remark, "s11iD");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            DyeingPlan v = new DyeingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.CreateDate = DateTime.Parse("2022-12-06 14:11:57");
                //v.BillNo = 37;
                v.Version = 98;
                v.FinishingFactoryId = AddCompany();
                v.POrderId = AddPurchaseOrder();
                v.PlanBatch = "5aSWWlJGTWjHb2rBsyZERG2G2J32nrEDSW8D5a1kIrJH";
                v.FinishingProcess = "AAlZmViM2";
                //v.Width = "0LmP9U";
                //v.GSM = "FvvWR8Jc4WB0OBwpF";
                v.Light = TEX.Model.Models.LightEnum.U3000;
                v.Light2 = TEX.Model.Models.LightEnum.LED;
                v.GreigeBatch = "o2DQt";
                v.GreigeVenderId = AddCompany();
                v.DyeingDemand = "8uLvzg9Dx1QVD";
                v.PackDemand = "C1PVQjYXXOiH1Smj4H";
                v.AdditionalDemend = "CNbb5V9iq1";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "q7B";
                v.AuditedComment = "k7mHHVt5HFG4uASk";
                v.Remark = "E";
                context.Set<DyeingPlan>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            DyeingPlan v1 = new DyeingPlan();
            DyeingPlan v2 = new DyeingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.CreateDate = DateTime.Parse("2022-12-06 14:11:57");
                //v1.BillNo = 37;
                v1.Version = 98;
                v1.FinishingFactoryId = AddCompany();
                v1.POrderId = AddPurchaseOrder();
                v1.PlanBatch = "5aSWWlJGTWjHb2rBsyZERG2G2J32nrEDSW8D5a1kIrJH";
                v1.FinishingProcess = "AAlZmViM2";
                //v1.Width = "0LmP9U";
                //v1.GSM = "FvvWR8Jc4WB0OBwpF";
                v1.Light = TEX.Model.Models.LightEnum.U3000;
                v1.Light2 = TEX.Model.Models.LightEnum.LED;
                v1.GreigeBatch = "o2DQt";
                v1.GreigeVenderId = AddCompany();
                v1.DyeingDemand = "8uLvzg9Dx1QVD";
                v1.PackDemand = "C1PVQjYXXOiH1Smj4H";
                v1.AdditionalDemend = "CNbb5V9iq1";
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v1.AuditedBy = "q7B";
                v1.AuditedComment = "k7mHHVt5HFG4uASk";
                v1.Remark = "E";
                v2.CreateDate = DateTime.Parse("2024-07-28 14:11:57");
                //v2.BillNo = 47;
                v2.Version = 81;
                v2.FinishingFactoryId = v1.FinishingFactoryId; 
                v2.POrderId = v1.POrderId; 
                v2.PlanBatch = "u5R8w8JXHcEvo7SwynG2cANiURJQ11P2yunmFSLztnaazr1VYszTKx2xY";
                v2.FinishingProcess = "7CV";
                //v2.Width = "oeyQleTj7hWuG";
                //v2.GSM = "nTjz7l930rTwM";
                v2.Light = TEX.Model.Models.LightEnum.DayLight;
                v2.Light2 = TEX.Model.Models.LightEnum.D65;
                v2.GreigeBatch = "x85ISabUhLHNd6lDK";
                v2.GreigeVenderId = v1.GreigeVenderId; 
                v2.DyeingDemand = "PYKYyw8Q";
                v2.PackDemand = "W2h7wUDdGToYC7vFZ";
                v2.AdditionalDemend = "peyZotwTI6";
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v2.AuditedBy = "Tgigq2UFRSomYZQ";
                v2.AuditedComment = "sTMeJUfQHtuQi";
                v2.Remark = "s11iD";
                context.Set<DyeingPlan>().Add(v1);
                context.Set<DyeingPlan>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<DyeingPlan>().Find(v1.ID);
                var data2 = context.Set<DyeingPlan>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "P1i4Esgmko42ruX6Ps4YQS3khVp01";
                v.CompanyName = "Yv1JxF06c0wqJeUXfUZjqxaRUNiceO";
                v.CompanyFullName = "Bw5p3bgK8aUznrMG1ONpyqhTnpBqkH8tz5ChCiBep7Q";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.FinishingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                v.ContactPhone = "6QDVb2";
                v.Adress = "x7Fx";
                v.TaxNO = "yOyQRIEI71KUKnfkgDooNcpFxempSQgz8lZO3PZ4OnAR";
                v.InvoiceInfo = "8UJKsLKxAunGIHC";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "UfYaAS6ke2kLNEYfKgpP8e";
                v.ProductName = "rfrD";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.NPTaffeta;
                v.Contents = "ZCHc9sITvU6mJ2xz8UUJRRZ9rTveYQmstxkgD1yWnEnaHytdL35MG";
                v.Spec = "hn8ixPP6dIK070n53tn16vORyp0vZy4R2mmOJPW2yzVTB5a9QyVCwzbVNKYQ8FqPfJc9AcY";
                v.GSM = 21;
                v.Width = 68;
                v.PileLength = 98;
                v.DyeingProcess = "qap0V6izp5YtL";
                v.KnittingProcess = "oN";
                v.FinishingProcess = "OueUkDHKg2DUJAjm";
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2022-08-02 14:11:57");
                v.DeliveryDate = DateTime.Parse("2022-08-08 14:11:57");
                v.CustomerId = AddCompany();
                v.OrderNo = "J5iBAQd1Didg3xA";
                v.CustomerOrderNo = "nyilZQ7aQM";
                //v.Merchandiser = "CTb7YMWstgI8f0";
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.Light = TEX.Model.Models.LightEnum.TL83;
                v.Light2 = TEX.Model.Models.LightEnum.D65_LED;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.USD;
                v.TotalMeters = 38;
                v.TotalYards = 2;
                v.TotalWeight = 88;
                v.TotalAmount = 47;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "5DBiLICxF5pGGJI";
                v.AuditedComment = "UBF";
                v.Remark = "y10daQZTHLcNJ";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
