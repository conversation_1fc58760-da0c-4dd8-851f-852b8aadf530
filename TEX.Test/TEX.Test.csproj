<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <Nullable>disable</Nullable>  
  </PropertyGroup>


  <ItemGroup>
	  <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.11" />
	  <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.11" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.8.3" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.1.2" />
    <PackageReference Include="MSTest.TestFramework" Version="2.1.2" />
	  <PackageReference Include="System.Net.Http.Json" Version="8.0.1" />
 </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\TEX\TEX.csproj" />
  </ItemGroup>

</Project>
