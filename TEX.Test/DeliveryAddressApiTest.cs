using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.BasicInfo.DeliveryAddressVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class DeliveryAddressApiTest
    {
        private DeliveryAddressController _controller;
        private string _seed;

        public DeliveryAddressApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<DeliveryAddressController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new DeliveryAddressSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            DeliveryAddressVM vm = _controller.Wtm.CreateVM<DeliveryAddressVM>();
            DeliveryAddress v = new DeliveryAddress();
            
            v.CompanyName = "DxKjszHFgCW";
            v.ContactName = "gnFAOi7SX3";
            v.Phone = "E3rEp";
            v.Address = "gRnCpK";
            v.AffiliationCompanyId = AddCompany();
            v.Remark = "J20";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DeliveryAddress>().Find(v.ID);
                
                Assert.AreEqual(data.CompanyName, "DxKjszHFgCW");
                Assert.AreEqual(data.ContactName, "gnFAOi7SX3");
                Assert.AreEqual(data.Phone, "E3rEp");
                Assert.AreEqual(data.Address, "gRnCpK");
                Assert.AreEqual(data.Remark, "J20");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            DeliveryAddress v = new DeliveryAddress();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.CompanyName = "DxKjszHFgCW";
                v.ContactName = "gnFAOi7SX3";
                v.Phone = "E3rEp";
                v.Address = "gRnCpK";
                v.AffiliationCompanyId = AddCompany();
                v.Remark = "J20";
                context.Set<DeliveryAddress>().Add(v);
                context.SaveChanges();
            }

            DeliveryAddressVM vm = _controller.Wtm.CreateVM<DeliveryAddressVM>();
            var oldID = v.ID;
            v = new DeliveryAddress();
            v.ID = oldID;
       		
            v.CompanyName = "kJpWVMhMrcMlWSKmU";
            v.ContactName = "PzbhV7EEe7kN";
            v.Phone = "WqZxX1BqL8A";
            v.Address = "NKah7GxdPIf3WppP";
            v.Remark = "JmqhP6e";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.CompanyName", "");
            vm.FC.Add("Entity.ContactName", "");
            vm.FC.Add("Entity.Phone", "");
            vm.FC.Add("Entity.Address", "");
            vm.FC.Add("Entity.AffiliationCompanyId", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DeliveryAddress>().Find(v.ID);
 				
                Assert.AreEqual(data.CompanyName, "kJpWVMhMrcMlWSKmU");
                Assert.AreEqual(data.ContactName, "PzbhV7EEe7kN");
                Assert.AreEqual(data.Phone, "WqZxX1BqL8A");
                Assert.AreEqual(data.Address, "NKah7GxdPIf3WppP");
                Assert.AreEqual(data.Remark, "JmqhP6e");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            DeliveryAddress v = new DeliveryAddress();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.CompanyName = "DxKjszHFgCW";
                v.ContactName = "gnFAOi7SX3";
                v.Phone = "E3rEp";
                v.Address = "gRnCpK";
                v.AffiliationCompanyId = AddCompany();
                v.Remark = "J20";
                context.Set<DeliveryAddress>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            DeliveryAddress v1 = new DeliveryAddress();
            DeliveryAddress v2 = new DeliveryAddress();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.CompanyName = "DxKjszHFgCW";
                v1.ContactName = "gnFAOi7SX3";
                v1.Phone = "E3rEp";
                v1.Address = "gRnCpK";
                v1.AffiliationCompanyId = AddCompany();
                v1.Remark = "J20";
                v2.CompanyName = "kJpWVMhMrcMlWSKmU";
                v2.ContactName = "PzbhV7EEe7kN";
                v2.Phone = "WqZxX1BqL8A";
                v2.Address = "NKah7GxdPIf3WppP";
                v2.AffiliationCompanyId = v1.AffiliationCompanyId; 
                v2.Remark = "JmqhP6e";
                context.Set<DeliveryAddress>().Add(v1);
                context.Set<DeliveryAddress>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<DeliveryAddress>().Find(v1.ID);
                var data2 = context.Set<DeliveryAddress>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "Uxg9bJ3Pn8VBqisDAUNE8L";
                v.CompanyName = "P9fgMl8fZFpJbmcSgOfb2QDpGPKJxezZWGu06S";
                v.CompanyFullName = "hyQ2EeMIlh1v";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.DyeingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                v.ContactPhone = "8vZvX12kESJ3P4BEI0";
                v.Adress = "CY";
                v.TaxNO = "K1Dh5MZthuTEvKjpwGIORLCSehrGV0oWedMRnt";
                v.InvoiceInfo = "xlU4K";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
