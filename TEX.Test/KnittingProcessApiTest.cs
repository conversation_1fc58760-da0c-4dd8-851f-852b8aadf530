using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.KnittingProcessVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class KnittingProcessApiTest
    {
        private KnittingProcessController _controller;
        private string _seed;

        public KnittingProcessApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<KnittingProcessController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new KnittingProcessSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            KnittingProcessVM vm = _controller.Wtm.CreateVM<KnittingProcessVM>();
            KnittingProcess v = new KnittingProcess();
            
            v.ProcessCode = "idmuAhnQc9yY";
            v.MachineInfoJson = "uuQb30";
            v.Weight = 55;
            v.Width = 90;
            v.PileLength = 85;
            v.FinishedWeight = 76;
            v.FinishedWidth = 87;
            v.FinishedPileHeight = 77;
            v.YarnInfoJson = "yCil2pV3zuY";
            v.Remark = "kjE9e";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<KnittingProcess>().Find(v.ID);
                
                Assert.AreEqual(data.ProcessCode, "idmuAhnQc9yY");
                Assert.AreEqual(data.MachineInfoJson, "uuQb30");
                Assert.AreEqual(data.Weight, 55);
                Assert.AreEqual(data.Width, 90);
                Assert.AreEqual(data.PileLength, 85);
                Assert.AreEqual(data.FinishedWeight, 76);
                Assert.AreEqual(data.FinishedWidth, 87);
                Assert.AreEqual(data.FinishedPileHeight, 77);
                Assert.AreEqual(data.YarnInfoJson, "yCil2pV3zuY");
                Assert.AreEqual(data.Remark, "kjE9e");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            KnittingProcess v = new KnittingProcess();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ProcessCode = "idmuAhnQc9yY";
                v.MachineInfoJson = "uuQb30";
                v.Weight = 55;
                v.Width = 90;
                v.PileLength = 85;
                v.FinishedWeight = 76;
                v.FinishedWidth = 87;
                v.FinishedPileHeight = 77;
                v.YarnInfoJson = "yCil2pV3zuY";
                v.Remark = "kjE9e";
                context.Set<KnittingProcess>().Add(v);
                context.SaveChanges();
            }

            KnittingProcessVM vm = _controller.Wtm.CreateVM<KnittingProcessVM>();
            var oldID = v.ID;
            v = new KnittingProcess();
            v.ID = oldID;
       		
            v.ProcessCode = "Rp3qsLJcOl2yzSycVJCEdGcMjiYH5fa";
            v.MachineInfoJson = "VADwufM7mH9hOeAwPla";
            v.Weight = 22;
            v.Width = 42;
            v.PileLength = 60;
            v.FinishedWeight = 28;
            v.FinishedWidth = 51;
            v.FinishedPileHeight = 31;
            v.YarnInfoJson = "YgXYgOomzpIPdiQqxn";
            v.Remark = "HrR4RsCg0Hb1";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ProcessCode", "");
            vm.FC.Add("Entity.MachineInfoJson", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Width", "");
            vm.FC.Add("Entity.PileHeight", "");
            vm.FC.Add("Entity.FinishedWeight", "");
            vm.FC.Add("Entity.FinishedWidth", "");
            vm.FC.Add("Entity.FinishedPileHeight", "");
            vm.FC.Add("Entity.YarnInfoJson", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<KnittingProcess>().Find(v.ID);
 				
                Assert.AreEqual(data.ProcessCode, "Rp3qsLJcOl2yzSycVJCEdGcMjiYH5fa");
                Assert.AreEqual(data.MachineInfoJson, "VADwufM7mH9hOeAwPla");
                Assert.AreEqual(data.Weight, 22);
                Assert.AreEqual(data.Width, 42);
                Assert.AreEqual(data.PileLength, 60);
                Assert.AreEqual(data.FinishedWeight, 28);
                Assert.AreEqual(data.FinishedWidth, 51);
                Assert.AreEqual(data.FinishedPileHeight, 31);
                Assert.AreEqual(data.YarnInfoJson, "YgXYgOomzpIPdiQqxn");
                Assert.AreEqual(data.Remark, "HrR4RsCg0Hb1");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            KnittingProcess v = new KnittingProcess();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ProcessCode = "idmuAhnQc9yY";
                v.MachineInfoJson = "uuQb30";
                v.Weight = 55;
                v.Width = 90;
                v.PileLength = 85;
                v.FinishedWeight = 76;
                v.FinishedWidth = 87;
                v.FinishedPileHeight = 77;
                v.YarnInfoJson = "yCil2pV3zuY";
                v.Remark = "kjE9e";
                context.Set<KnittingProcess>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            KnittingProcess v1 = new KnittingProcess();
            KnittingProcess v2 = new KnittingProcess();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ProcessCode = "idmuAhnQc9yY";
                v1.MachineInfoJson = "uuQb30";
                v1.Weight = 55;
                v1.Width = 90;
                v1.PileLength = 85;
                v1.FinishedWeight = 76;
                v1.FinishedWidth = 87;
                v1.FinishedPileHeight = 77;
                v1.YarnInfoJson = "yCil2pV3zuY";
                v1.Remark = "kjE9e";
                v2.ProcessCode = "Rp3qsLJcOl2yzSycVJCEdGcMjiYH5fa";
                v2.MachineInfoJson = "VADwufM7mH9hOeAwPla";
                v2.Weight = 22;
                v2.Width = 42;
                v2.PileLength = 60;
                v2.FinishedWeight = 28;
                v2.FinishedWidth = 51;
                v2.FinishedPileHeight = 31;
                v2.YarnInfoJson = "YgXYgOomzpIPdiQqxn";
                v2.Remark = "HrR4RsCg0Hb1";
                context.Set<KnittingProcess>().Add(v1);
                context.Set<KnittingProcess>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<KnittingProcess>().Find(v1.ID);
                var data2 = context.Set<KnittingProcess>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }


    }
}
