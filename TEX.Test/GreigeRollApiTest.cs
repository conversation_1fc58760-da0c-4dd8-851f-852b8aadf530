using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.GreigeRollVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class GreigeRollApiTest
    {
        private GreigeRollController _controller;
        private string _seed;

        public GreigeRollApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<GreigeRollController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new GreigeRollSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            GreigeRollVM vm = _controller.Wtm.CreateVM<GreigeRollVM>();
            GreigeRoll v = new GreigeRoll();
            
            v.ParentId = AddGreigeRoll();
            v.ProductId = AddProduct();
            v.KnittingPlanId = AddKnittingPlan();
            v.InboundBillId = AddGreigeInboundBill();
            v.OutboundBillId = AddGreigeOutboundBill();
            v.OperationType = TEX.Model.Models.RollOperationEnum.Original;
            v.OperationRemark = "aqOORbisGQNjO01m8cUZ7DOEW1E6kc6W";
            v.BatchNo = "hXv6HqEGsh0N5iWkjvVle5L3XKau9v4LyKcKVhDUcc";
            v.MachineId = "tJva0N4hBkmLv4VYi2DQLofrYTZxumbw1ZuUY";
            v.RollNo = 11;
            v.InboundWeight = 3;
            v.InboundMeters = 25;
            v.InboundYards = 48;
            v.InboundBillNo = "nqoDbf5YDagTxQHRLJoZ1XhDv2vWHQl2PwKP0RNmvXTjSmu5hX7dlwSfm2";
            v.QcWeight = 73;
            v.QcMeters = 69;
            v.QcYards = 87;
            v.OutboundWeight = 3;
            v.OutboundMeters = 87;
            v.OutboundYards = 21;
            v.OutboundBillNo = "vXNbMcG8G";
            v.Grade = "hbtBtdzKRJFeqtt";
            v.Worker = "5Uj53";
            v.Inspector = "BVFCdrf0VcB";
            v.Status = "Ui9fPmKWkRuP";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<GreigeRoll>().Find(v.ID);
                
                Assert.AreEqual(data.OperationType, TEX.Model.Models.RollOperationEnum.Original);
                Assert.AreEqual(data.OperationRemark, "aqOORbisGQNjO01m8cUZ7DOEW1E6kc6W");
                Assert.AreEqual(data.BatchNo, "hXv6HqEGsh0N5iWkjvVle5L3XKau9v4LyKcKVhDUcc");
                Assert.AreEqual(data.MachineId, "tJva0N4hBkmLv4VYi2DQLofrYTZxumbw1ZuUY");
                Assert.AreEqual(data.RollNo, 11);
                Assert.AreEqual(data.InboundWeight, 3);
                Assert.AreEqual(data.InboundMeters, 25);
                Assert.AreEqual(data.InboundYards, 48);
                Assert.AreEqual(data.InboundBillNo, "nqoDbf5YDagTxQHRLJoZ1XhDv2vWHQl2PwKP0RNmvXTjSmu5hX7dlwSfm2");
                Assert.AreEqual(data.QcWeight, 73);
                Assert.AreEqual(data.QcMeters, 69);
                Assert.AreEqual(data.QcYards, 87);
                Assert.AreEqual(data.OutboundWeight, 3);
                Assert.AreEqual(data.OutboundMeters, 87);
                Assert.AreEqual(data.OutboundYards, 21);
                Assert.AreEqual(data.OutboundBillNo, "vXNbMcG8G");
                Assert.AreEqual(data.Grade, "hbtBtdzKRJFeqtt");
                Assert.AreEqual(data.Worker, "5Uj53");
                Assert.AreEqual(data.Inspector, "BVFCdrf0VcB");
                Assert.AreEqual(data.Status, "Ui9fPmKWkRuP");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            GreigeRoll v = new GreigeRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ParentId = AddGreigeRoll();
                v.ProductId = AddProduct();
                v.KnittingPlanId = AddKnittingPlan();
                v.InboundBillId = AddGreigeInboundBill();
                v.OutboundBillId = AddGreigeOutboundBill();
                v.OperationType = TEX.Model.Models.RollOperationEnum.Original;
                v.OperationRemark = "aqOORbisGQNjO01m8cUZ7DOEW1E6kc6W";
                v.BatchNo = "hXv6HqEGsh0N5iWkjvVle5L3XKau9v4LyKcKVhDUcc";
                v.MachineId = "tJva0N4hBkmLv4VYi2DQLofrYTZxumbw1ZuUY";
                v.RollNo = 11;
                v.InboundWeight = 3;
                v.InboundMeters = 25;
                v.InboundYards = 48;
                v.InboundBillNo = "nqoDbf5YDagTxQHRLJoZ1XhDv2vWHQl2PwKP0RNmvXTjSmu5hX7dlwSfm2";
                v.QcWeight = 73;
                v.QcMeters = 69;
                v.QcYards = 87;
                v.OutboundWeight = 3;
                v.OutboundMeters = 87;
                v.OutboundYards = 21;
                v.OutboundBillNo = "vXNbMcG8G";
                v.Grade = "hbtBtdzKRJFeqtt";
                v.Worker = "5Uj53";
                v.Inspector = "BVFCdrf0VcB";
                v.Status = "Ui9fPmKWkRuP";
                context.Set<GreigeRoll>().Add(v);
                context.SaveChanges();
            }

            GreigeRollVM vm = _controller.Wtm.CreateVM<GreigeRollVM>();
            var oldID = v.ID;
            v = new GreigeRoll();
            v.ID = oldID;
       		
            v.OperationType = TEX.Model.Models.RollOperationEnum.Original;
            v.OperationRemark = "LIch1TV64ZyfTNvRMuV6jq2eaaJqrszK0Z6e8Gdclp";
            v.BatchNo = "xI6fLrKknzSLxN3mHXmDoYVOMiKszFWSzWtfsoa9WcPMmBxBVAlDrpaedloky";
            v.MachineId = "so0FV258pP4vkRq8Kc4XtksnqlHHRPgQ3PycuRMFYiGzUJ1K5PruL";
            v.RollNo = 82;
            v.InboundWeight = 77;
            v.InboundMeters = 47;
            v.InboundYards = 41;
            v.InboundBillNo = "1hUO3HYmfDAETLcjX7DyuBCCJbhheWc63VbZyMEe6f3DLUYQ4Q9d3st8x";
            v.QcWeight = 90;
            v.QcMeters = 76;
            v.QcYards = 30;
            v.OutboundWeight = 39;
            v.OutboundMeters = 96;
            v.OutboundYards = 2;
            v.OutboundBillNo = "NUlBLZIxXriS9pmBoBp2oJ79ArL6TIiu6emqcNkzhegg33kwZXxC9nRBa0k1";
            v.Grade = "R1Sfevo";
            v.Worker = "RRw7BfeeeXSI7pSMK1Z";
            v.Inspector = "m";
            v.Status = "E";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ParentId", "");
            vm.FC.Add("Entity.ProductId", "");
            vm.FC.Add("Entity.KnittingPlanId", "");
            vm.FC.Add("Entity.InboundBillId", "");
            vm.FC.Add("Entity.OutboundBillId", "");
            vm.FC.Add("Entity.OperationType", "");
            vm.FC.Add("Entity.OperationRemark", "");
            vm.FC.Add("Entity.BatchNo", "");
            vm.FC.Add("Entity.MachineId", "");
            vm.FC.Add("Entity.RollNo", "");
            vm.FC.Add("Entity.InboundWeight", "");
            vm.FC.Add("Entity.InboundMeters", "");
            vm.FC.Add("Entity.InboundYards", "");
            vm.FC.Add("Entity.InboundBillNo", "");
            vm.FC.Add("Entity.QcWeight", "");
            vm.FC.Add("Entity.QcMeters", "");
            vm.FC.Add("Entity.QcYards", "");
            vm.FC.Add("Entity.OutboundWeight", "");
            vm.FC.Add("Entity.OutboundMeters", "");
            vm.FC.Add("Entity.OutboundYards", "");
            vm.FC.Add("Entity.OutboundBillNo", "");
            vm.FC.Add("Entity.Grade", "");
            vm.FC.Add("Entity.Worker", "");
            vm.FC.Add("Entity.Inspector", "");
            vm.FC.Add("Entity.Status", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<GreigeRoll>().Find(v.ID);
 				
                Assert.AreEqual(data.OperationType, TEX.Model.Models.RollOperationEnum.Original);
                Assert.AreEqual(data.OperationRemark, "LIch1TV64ZyfTNvRMuV6jq2eaaJqrszK0Z6e8Gdclp");
                Assert.AreEqual(data.BatchNo, "xI6fLrKknzSLxN3mHXmDoYVOMiKszFWSzWtfsoa9WcPMmBxBVAlDrpaedloky");
                Assert.AreEqual(data.MachineId, "so0FV258pP4vkRq8Kc4XtksnqlHHRPgQ3PycuRMFYiGzUJ1K5PruL");
                Assert.AreEqual(data.RollNo, 82);
                Assert.AreEqual(data.InboundWeight, 77);
                Assert.AreEqual(data.InboundMeters, 47);
                Assert.AreEqual(data.InboundYards, 41);
                Assert.AreEqual(data.InboundBillNo, "1hUO3HYmfDAETLcjX7DyuBCCJbhheWc63VbZyMEe6f3DLUYQ4Q9d3st8x");
                Assert.AreEqual(data.QcWeight, 90);
                Assert.AreEqual(data.QcMeters, 76);
                Assert.AreEqual(data.QcYards, 30);
                Assert.AreEqual(data.OutboundWeight, 39);
                Assert.AreEqual(data.OutboundMeters, 96);
                Assert.AreEqual(data.OutboundYards, 2);
                Assert.AreEqual(data.OutboundBillNo, "NUlBLZIxXriS9pmBoBp2oJ79ArL6TIiu6emqcNkzhegg33kwZXxC9nRBa0k1");
                Assert.AreEqual(data.Grade, "R1Sfevo");
                Assert.AreEqual(data.Worker, "RRw7BfeeeXSI7pSMK1Z");
                Assert.AreEqual(data.Inspector, "m");
                Assert.AreEqual(data.Status, "E");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            GreigeRoll v = new GreigeRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ParentId = AddGreigeRoll();
                v.ProductId = AddProduct();
                v.KnittingPlanId = AddKnittingPlan();
                v.InboundBillId = AddGreigeInboundBill();
                v.OutboundBillId = AddGreigeOutboundBill();
                v.OperationType = TEX.Model.Models.RollOperationEnum.Original;
                v.OperationRemark = "aqOORbisGQNjO01m8cUZ7DOEW1E6kc6W";
                v.BatchNo = "hXv6HqEGsh0N5iWkjvVle5L3XKau9v4LyKcKVhDUcc";
                v.MachineId = "tJva0N4hBkmLv4VYi2DQLofrYTZxumbw1ZuUY";
                v.RollNo = 11;
                v.InboundWeight = 3;
                v.InboundMeters = 25;
                v.InboundYards = 48;
                v.InboundBillNo = "nqoDbf5YDagTxQHRLJoZ1XhDv2vWHQl2PwKP0RNmvXTjSmu5hX7dlwSfm2";
                v.QcWeight = 73;
                v.QcMeters = 69;
                v.QcYards = 87;
                v.OutboundWeight = 3;
                v.OutboundMeters = 87;
                v.OutboundYards = 21;
                v.OutboundBillNo = "vXNbMcG8G";
                v.Grade = "hbtBtdzKRJFeqtt";
                v.Worker = "5Uj53";
                v.Inspector = "BVFCdrf0VcB";
                v.Status = "Ui9fPmKWkRuP";
                context.Set<GreigeRoll>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            GreigeRoll v1 = new GreigeRoll();
            GreigeRoll v2 = new GreigeRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ParentId = AddGreigeRoll();
                v1.ProductId = AddProduct();
                v1.KnittingPlanId = AddKnittingPlan();
                v1.InboundBillId = AddGreigeInboundBill();
                v1.OutboundBillId = AddGreigeOutboundBill();
                v1.OperationType = TEX.Model.Models.RollOperationEnum.Original;
                v1.OperationRemark = "aqOORbisGQNjO01m8cUZ7DOEW1E6kc6W";
                v1.BatchNo = "hXv6HqEGsh0N5iWkjvVle5L3XKau9v4LyKcKVhDUcc";
                v1.MachineId = "tJva0N4hBkmLv4VYi2DQLofrYTZxumbw1ZuUY";
                v1.RollNo = 11;
                v1.InboundWeight = 3;
                v1.InboundMeters = 25;
                v1.InboundYards = 48;
                v1.InboundBillNo = "nqoDbf5YDagTxQHRLJoZ1XhDv2vWHQl2PwKP0RNmvXTjSmu5hX7dlwSfm2";
                v1.QcWeight = 73;
                v1.QcMeters = 69;
                v1.QcYards = 87;
                v1.OutboundWeight = 3;
                v1.OutboundMeters = 87;
                v1.OutboundYards = 21;
                v1.OutboundBillNo = "vXNbMcG8G";
                v1.Grade = "hbtBtdzKRJFeqtt";
                v1.Worker = "5Uj53";
                v1.Inspector = "BVFCdrf0VcB";
                v1.Status = "Ui9fPmKWkRuP";
                v2.ParentId = v1.ParentId; 
                v2.ProductId = v1.ProductId; 
                v2.KnittingPlanId = v1.KnittingPlanId; 
                v2.InboundBillId = v1.InboundBillId; 
                v2.OutboundBillId = v1.OutboundBillId; 
                v2.OperationType = TEX.Model.Models.RollOperationEnum.Original;
                v2.OperationRemark = "LIch1TV64ZyfTNvRMuV6jq2eaaJqrszK0Z6e8Gdclp";
                v2.BatchNo = "xI6fLrKknzSLxN3mHXmDoYVOMiKszFWSzWtfsoa9WcPMmBxBVAlDrpaedloky";
                v2.MachineId = "so0FV258pP4vkRq8Kc4XtksnqlHHRPgQ3PycuRMFYiGzUJ1K5PruL";
                v2.RollNo = 82;
                v2.InboundWeight = 77;
                v2.InboundMeters = 47;
                v2.InboundYards = 41;
                v2.InboundBillNo = "1hUO3HYmfDAETLcjX7DyuBCCJbhheWc63VbZyMEe6f3DLUYQ4Q9d3st8x";
                v2.QcWeight = 90;
                v2.QcMeters = 76;
                v2.QcYards = 30;
                v2.OutboundWeight = 39;
                v2.OutboundMeters = 96;
                v2.OutboundYards = 2;
                v2.OutboundBillNo = "NUlBLZIxXriS9pmBoBp2oJ79ArL6TIiu6emqcNkzhegg33kwZXxC9nRBa0k1";
                v2.Grade = "R1Sfevo";
                v2.Worker = "RRw7BfeeeXSI7pSMK1Z";
                v2.Inspector = "m";
                v2.Status = "E";
                context.Set<GreigeRoll>().Add(v1);
                context.Set<GreigeRoll>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<GreigeRoll>().Find(v1.ID);
                var data2 = context.Set<GreigeRoll>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Int32 AddDictType()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 98;
                v.Name = "aqdQPVUqiURsc";
                v.Description = "QYDCHsMYP";
                v.DictOrder = 72;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictItem()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 16;
                v.DictTypeId = AddDictType();
                v.ItemName = "i";
                v.Description = "ezTdGfKHy";
                v.DictOrder = 78;
                v.Remark = "r2kSzkD8ksA";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "hcgKR4TRASKTE54IBwXrIoPQrBctZwX4";
                v.ProductName = "QqNq0hQdCUcZAKLtYymiMWjLZsOOWl5L97hNIZdUK7";
                v.CategoryId = AddDictItem();
                v.Contents = "bEenFLpZt0879Cr2d0F8jqUxtoFUIer8UYdnbD";
                v.Spec = "J3js2430B6CzdgGxk3eOoSBf0Ccagr5f34rSMZznPJ1KO9lqk0VZDfuCYCJcjGFeSVGBX8awO2re3CL3UMaKjbBML52O5NZfsl8";
                v.GSM = 16;
                v.Width = 298;
                v.FullWidth = 122;
                v.DyeingProductName = "d2CGv";
                v.PileLength = 56;
                v.DyeingProcess = "cMa4GIZ6WGYlqmAW2";
                v.KnittingProcess = "vgIpMi3";
                v.FinishingProcess = "feA4scwFkn";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.Cancelled;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "O6RFFfOkdngvhVk969qw2fgaR";
                v.CompanyName = "jWyyCmgTIDtzJkFbsceAI9NWBrGeEvg";
                v.CompanyFullName = "BNE61DqwCS8BHUzYnyYhAe6sLZb2k57r";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.GreigeVender;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "GWVtLen3fJKm3Y9zwu";
                v.Adress = "NXiMybEprYCyUE9pec";
                v.TaxNO = "OFn53rtE9gwAsG63GClomS5CCBaY78jdZZh0tr8jRzg732eS8qbv6";
                v.InvoiceInfo = "3vD9a1eEifPHaI562";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.Deleted;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "tHttigrR";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "0dlQsF7lM9LT";
                v.MobilePhone = "IY5rDsxQer0";
                v.Address = "1Ayvu8y7L";
                v.Phone = "4";
                v.Email = "HjRG6";
                v.WeChat = "8FT";
                v.QQ = "89ic4JPyJqlmZS92";
                v.Fax = "XK9";
                v.Remark = "JJ6";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2024-06-12 14:56:35");
                v.DeliveryDate = DateTime.Parse("2026-06-12 14:56:35");
                v.CustomerId = AddCompany();
                v.OrderNo = "xWywwacj8zrdpYUbNlVfuW7tj4NXuYMYskPr38yLBl2M";
                v.CustomerOrderNo = "Lf8VBQkcU0vG9YAsW1RcqBni57hlUOrec6pWA";
                v.MerchandiserId = AddContact();
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.DyeingProductName = "MdWsZ4abiB";
                v.Light = TEX.Model.Models.LightEnum.D65_LED;
                v.Light2 = TEX.Model.Models.LightEnum.TL84;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.GBP;
                v.TotalMeters = 59;
                v.TotalYards = 32;
                v.TotalWeight = 44;
                v.TotalAmount = 66;
                v.CompletedStatus = TEX.Model.Models.CompletedStatusEnum.Completed;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.Cancelled;
                v.AuditedBy = "Jbu5w";
                v.AuditedComment = "VS";
                v.Remark = "W0";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddKnittingProcess()
        {
            KnittingProcess v = new KnittingProcess();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProcessCode = "TrnXBLZTm3c8rhPrJW6GTaYMwgg";
                v.CategoryId = AddDictItem();
                v.MachineInfoJson = "0L8U48LBviVzAh1gN";
                v.Weight = 87;
                v.Width = 9;
                v.PileLength = 6;
                v.FinishedWeight = 19;
                v.FinishedWidth = 68;
                v.FinishedPileHeight = 14;
                v.YarnInfoJson = "XakeDF";
                v.Remark = "oWV";
                context.Set<KnittingProcess>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddKnittingPlan()
        {
            KnittingPlan v = new KnittingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.BillNo = "h2yEbB3dWaHDw4";
                v.PlanDate = DateTime.Parse("2025-09-13 14:56:35");
                v.PurchaseOrderId = AddPurchaseOrder();
                v.KnittingFactoryId = AddCompany();
                v.ProductName = "qFkveP";
                v.KnittingProcessId = AddKnittingProcess();
                v.PieceCount = 39;
                v.TotalWeight = 60;
                v.WeightPerPiece = 78;
                v.BatchNo = "2qlAgNN1t";
                v.DeliveryDate = DateTime.Parse("2025-04-11 14:56:35");
                v.Requirements = "5yO";
                v.Status = TEX.Model.Models.PlanStatusEnum.Completed;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "6yf35y4KJGNKM";
                v.Remark = "RZ";
                context.Set<KnittingPlan>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddGreigeInboundBill()
        {
            GreigeInboundBill v = new GreigeInboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.BillNo = "Y6Krqd0PkhZVGm3ZT2epZBA0CN87LdxUwqKaEEWta5SLGkbLBrn";
                v.InboundDate = DateTime.Parse("2024-09-23 14:56:35");
                v.KnittingFactoryId = AddCompany();
                v.Warehouse = "DoEjtdY692ustEHYXt9hYMDRl7W";
                v.TotalRolls = 34;
                v.TotalWeight = 53;
                v.TotalMeters = 72;
                v.TotalYards = 76;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "X9hai";
                v.AuditedComment = "4U7Y5M907fpCgX";
                v.Remark = "pX8N68lIY1xU";
                context.Set<GreigeInboundBill>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddGreigeOutboundBill()
        {
            GreigeOutboundBill v = new GreigeOutboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.BillNo = "zw9JDI4YYLR58QwGNSgQCCLZIT0AQ4KBozBiUBm";
                v.OutboundDate = DateTime.Parse("2026-04-29 14:56:35");
                v.Purpose = "NmKrza";
                v.ReceiverId = AddCompany();
                v.DyeingFactoryId = AddCompany();
                v.Warehouse = "KSaluNE8l1QTrsup7kcaVXABkTauRPI4XGkCg7hqTYNiIkq0MmPffovLuIoWu";
                v.TransportMethod = "BrFDxLSyrJuA6JR6FULnoXQ";
                v.DeliveryAddress = "AjQbIjPuufcuFfsOCwfD0xUkJV2DWf9xg8J0O3zhx1vzmaB7dt3Gm6NZ4LAF";
                v.TotalRolls = 55;
                v.TotalWeight = 27;
                v.TotalMeters = 86;
                v.TotalYards = 19;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "Nh5Lt1";
                v.AuditedComment = "MEAUOVv8";
                v.Remark = "YQROfJ";
                context.Set<GreigeOutboundBill>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddGreigeRoll()
        {
            GreigeRoll v = new GreigeRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductId = AddProduct();
                v.KnittingPlanId = AddKnittingPlan();
                v.InboundBillId = AddGreigeInboundBill();
                v.OutboundBillId = AddGreigeOutboundBill();
                v.OperationType = TEX.Model.Models.RollOperationEnum.Merged;
                v.OperationRemark = "WLC283W4HzmwnR6Fcc4AvIgT4mDOnHjfUqYYODPnwMxlrH0JVJqEFTrpwE94ojiyYj7Ca4m83sNRLnfImJxC3x3JNpMOXc6Q67uadOkDzxGi2OBM3UWMJDsBRpOmhTsZImHXzphJgwK6PTAkWB8rKnlHlQpCZB6OTBadnCidT9u7BeUKpX1ufXa43rZxFdzS";
                v.BatchNo = "BCDMo1tpvJdFqFBpNTfPBJqDQ4Gf8rPbJ8eOpIUeDpyYxnSd";
                v.MachineId = "GUSudEqdMyd4039ksN9Yasp9laK7eTnaY19zMAQyCeKF0I36ST60s5mGJ";
                v.RollNo = 50;
                v.InboundWeight = 45;
                v.InboundMeters = 34;
                v.InboundYards = 28;
                v.InboundBillNo = "uxyWbIDSiSxVaQ";
                v.QcWeight = 73;
                v.QcMeters = 39;
                v.QcYards = 90;
                v.OutboundWeight = 34;
                v.OutboundMeters = 46;
                v.OutboundYards = 5;
                v.OutboundBillNo = "VjOXJUbpPbXgDZ5oEhAGNz5MtpFRdU7hYg9";
                v.Grade = "KRdoAetgDgD";
                v.Worker = "O2mvnzzOQC";
                v.Inspector = "xQH";
                v.Status = "OEKpv8ttJXnmfy9Y";
                context.Set<GreigeRoll>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
