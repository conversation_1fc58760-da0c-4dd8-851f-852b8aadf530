using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Finished.ProductStockVMs;
using TEX.Model.Finished;
using TEX.DataAccess;
using TEX.Model.Models;


namespace TEX.Test
{
    [TestClass]
    public class ProductStockApiTest
    {
        private ProductStockController _controller;
        private string _seed;

        public ProductStockApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<ProductStockController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new ProductStockSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ProductStockVM vm = _controller.Wtm.CreateVM<ProductStockVM>();
            ProductStock v = new ProductStock();
            
            v.OrderDetailId = AddOrderDetail();
            v.TotalPcs = 1;
            v.TotalWeight = 4;
            v.TotalMeters = 80;
            v.TotalYards = 5;
            v.Wearhouse = "IGh4d7Bbc8oaa4KDItn3iJsXXGrKUxLp8p";
            v.Location = "6EY3V1vBb6Ovfdnm3AzucPdf3Ch3cZBeCG4ts35kAe6IPDrEOr";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductStock>().Find(v.ID);
                
                Assert.AreEqual(data.TotalPcs, 1);
                Assert.AreEqual(data.TotalWeight, 4);
                Assert.AreEqual(data.TotalMeters, 80);
                Assert.AreEqual(data.TotalYards, 5);
                Assert.AreEqual(data.Wearhouse, "IGh4d7Bbc8oaa4KDItn3iJsXXGrKUxLp8p");
                Assert.AreEqual(data.Location, "6EY3V1vBb6Ovfdnm3AzucPdf3Ch3cZBeCG4ts35kAe6IPDrEOr");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            ProductStock v = new ProductStock();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.OrderDetailId = AddOrderDetail();
                v.TotalPcs = 1;
                v.TotalWeight = 4;
                v.TotalMeters = 80;
                v.TotalYards = 5;
                v.Wearhouse = "IGh4d7Bbc8oaa4KDItn3iJsXXGrKUxLp8p";
                v.Location = "6EY3V1vBb6Ovfdnm3AzucPdf3Ch3cZBeCG4ts35kAe6IPDrEOr";
                context.Set<ProductStock>().Add(v);
                context.SaveChanges();
            }

            ProductStockVM vm = _controller.Wtm.CreateVM<ProductStockVM>();
            var oldID = v.ID;
            v = new ProductStock();
            v.ID = oldID;
       		
            v.TotalPcs = 31;
            v.TotalWeight = 1;
            v.TotalMeters = 98;
            v.TotalYards = 10;
            v.Wearhouse = "DdoMrqpq1tfJr8hGIg1B9T69";
            v.Location = "GqVQ66IOvQvv6R8gSErOymtk5Fu372Z9y47pIyLJcskj";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.OrderDetailId", "");
            vm.FC.Add("Entity.TotalPcs", "");
            vm.FC.Add("Entity.TotalWeight", "");
            vm.FC.Add("Entity.TotalMeters", "");
            vm.FC.Add("Entity.TotalYards", "");
            vm.FC.Add("Entity.Wearhouse", "");
            vm.FC.Add("Entity.Location", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductStock>().Find(v.ID);
 				
                Assert.AreEqual(data.TotalPcs, 31);
                Assert.AreEqual(data.TotalWeight, 1);
                Assert.AreEqual(data.TotalMeters, 98);
                Assert.AreEqual(data.TotalYards, 10);
                Assert.AreEqual(data.Wearhouse, "DdoMrqpq1tfJr8hGIg1B9T69");
                Assert.AreEqual(data.Location, "GqVQ66IOvQvv6R8gSErOymtk5Fu372Z9y47pIyLJcskj");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            ProductStock v = new ProductStock();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.OrderDetailId = AddOrderDetail();
                v.TotalPcs = 1;
                v.TotalWeight = 4;
                v.TotalMeters = 80;
                v.TotalYards = 5;
                v.Wearhouse = "IGh4d7Bbc8oaa4KDItn3iJsXXGrKUxLp8p";
                v.Location = "6EY3V1vBb6Ovfdnm3AzucPdf3Ch3cZBeCG4ts35kAe6IPDrEOr";
                context.Set<ProductStock>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            ProductStock v1 = new ProductStock();
            ProductStock v2 = new ProductStock();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.OrderDetailId = AddOrderDetail();
                v1.TotalPcs = 1;
                v1.TotalWeight = 4;
                v1.TotalMeters = 80;
                v1.TotalYards = 5;
                v1.Wearhouse = "IGh4d7Bbc8oaa4KDItn3iJsXXGrKUxLp8p";
                v1.Location = "6EY3V1vBb6Ovfdnm3AzucPdf3Ch3cZBeCG4ts35kAe6IPDrEOr";
                v2.OrderDetailId = v1.OrderDetailId; 
                v2.TotalPcs = 31;
                v2.TotalWeight = 1;
                v2.TotalMeters = 98;
                v2.TotalYards = 10;
                v2.Wearhouse = "DdoMrqpq1tfJr8hGIg1B9T69";
                v2.Location = "GqVQ66IOvQvv6R8gSErOymtk5Fu372Z9y47pIyLJcskj";
                context.Set<ProductStock>().Add(v1);
                context.Set<ProductStock>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<ProductStock>().Find(v1.ID);
                var data2 = context.Set<ProductStock>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "cr0DlsUGf7BWIMbq5c";
                v.CompanyName = "okNMFvELaL7ezo4wS9CPir6kZn";
                v.CompanyFullName = "zcevNVPGN0HugW7Zc";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.TradingCompany;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "xpiDwjdQnvz";
                v.Adress = "ZbBLRFlRutlFyc3";
                v.TaxNO = "l9wyurtZwHKsce6zTxtfXrtyMIgMvLnJhFVG9Q";
                v.InvoiceInfo = "dvufl7R3";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "3jv20VffqwWcaN9MdmD";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "rXR";
                v.MobilePhone = "vOzv94tOr46p6G";
                v.Address = "p9";
                v.Phone = "zx0mzRa1WIVSbbgaT";
                v.Email = "I";
                v.WeChat = "q8UjPelA51jZxZNiO";
                v.QQ = "MgtQEt";
                v.Fax = "XCCkGGTONWivp0v";
                v.Remark = "NTd8kqu";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictType()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 34;
                v.Name = "j8VLKNS8QyYO7F7Z";
                v.Description = "UmQUCwKYqG0IdhINwBI";
                v.DictOrder = 74;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictItem()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 64;
                v.DictTypeId = AddDictType();
                v.ItemName = "uFFh3Sr";
                v.Description = "5Z53R12";
                v.DictOrder = 55;
                v.Remark = "yZnKXWCNarDY";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "NhERy8F7vGqPgaQh85i8pm4LsNlMBM973lPiQfeHP7uli20Hl1z";
                v.ProductName = "avcRv9";
                v.CategoryId = AddDictItem();
                v.Contents = "hkMajTOCuaij4TH01fYVpd";
                v.Spec = "NCt6hIUX4WYgcaw64tQUovBrYLsFYLEcVy6pUYfXGT15LXsBbiIPvSzYZ6kkArsTlaQa5XTLgNcEblUtw3";
                v.GSM = 74;
                v.Width = 7;
                v.DyeingProductName = "I6yA2dIxvhpXWAHgrf9we27zsUxZfNd1udNnPEZdYTkWke";
                v.PileLength = 41;
                v.DyeingProcess = "XHFI2oW4mmJg";
                v.KnittingProcess = "35LnIu1";
                v.FinishingProcess = "ndljTueph";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2025-09-28 16:48:12");
                v.DeliveryDate = DateTime.Parse("2024-05-25 16:48:12");
                v.CustomerId = AddCompany();
                v.OrderNo = "IU7jjXPPY8Nk4kMmrD1oVV1";
                v.CustomerOrderNo = "EXN8ijHU4ptx1KtLM1";
                v.MerchandiserId = AddContact();
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.DyeingProductName = "0yzajrnXrQPUEgMS";
                v.Light = TEX.Model.Models.LightEnum.CWF;
                v.Light2 = TEX.Model.Models.LightEnum.D65;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.GBP;
                v.TotalMeters = 71;
                v.TotalYards = 16;
                v.TotalWeight = 60;
                v.TotalAmount = 5;
                v.CompletedStatus = TEX.Model.Models.CompletedStatusEnum.Cancelled;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "CSivvo2Se5lRKNeSX";
                v.AuditedComment = "eLQ0iEyhsmDQ";
                v.Remark = "PyPzOG";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddOrderDetail()
        {
            OrderDetail v = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.PurchaseOrderId = AddPurchaseOrder();
                v.Color = "0CoC4bw79LpP4";
                v.EngColor = "9KvC4c3ltBrXbD9fd7T";
                v.ColorCode = "wAl67sb8YyBlw59ULPYaBb";
                v.Meters = 73;
                v.KG = 26;
                v.Yards = 43;
                v.Price = 21;
                v.Amount = 24;
                v.Remark = "odgtj";
                context.Set<OrderDetail>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
