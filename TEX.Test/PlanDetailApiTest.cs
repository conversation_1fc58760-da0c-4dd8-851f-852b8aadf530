using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Producttion.PlanDetailVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class PlanDetailApiTest
    {
        private PlanDetailController _controller;
        private string _seed;

        public PlanDetailApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<PlanDetailController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new PlanDetailSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            PlanDetailVM vm = _controller.Wtm.CreateVM<PlanDetailVM>();
            PlanDetail v = new PlanDetail();
            
            v.DyeingPlanId = AddDyeingPlan();
            v.OrderDetailId = AddOrderDetail();
            v.Color = "YuWxsIW9Nx42jbAgqskNFSQg4c6g5eXhjFeMGxXjSBN8x8LFA8ytrQgWrHLoh";
            v.ColorCode = "AmAhQ1pAAqWvB3KM2xRc";
            v.Pcs = 70;
            v.Qty = 6;
            v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.Y;
            v.DeliveryDate = DateTime.Parse("2024-02-14 19:18:26");
            v.FinishingPrice = 36;
            v.GreigeBatch = "u";
            v.DyeingProcess = "DjUcnzvHtEhXIDW";
            v.Remark = "e";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<PlanDetail>().Find(v.ID);
                
                Assert.AreEqual(data.Color, "YuWxsIW9Nx42jbAgqskNFSQg4c6g5eXhjFeMGxXjSBN8x8LFA8ytrQgWrHLoh");
                Assert.AreEqual(data.ColorCode, "AmAhQ1pAAqWvB3KM2xRc");
                Assert.AreEqual(data.Pcs, 70);
                Assert.AreEqual(data.Qty, 6);
                Assert.AreEqual(data.QtyUnit, TEX.Model.Models.AccountingUnitEnum.Y);
                Assert.AreEqual(data.DeliveryDate, DateTime.Parse("2024-02-14 19:18:26"));
                Assert.AreEqual(data.FinishingPrice, 36);
                Assert.AreEqual(data.GreigeBatch, "u");
                Assert.AreEqual(data.DyeingProcess, "DjUcnzvHtEhXIDW");
                Assert.AreEqual(data.Remark, "e");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            PlanDetail v = new PlanDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.DyeingPlanId = AddDyeingPlan();
                v.OrderDetailId = AddOrderDetail();
                v.Color = "YuWxsIW9Nx42jbAgqskNFSQg4c6g5eXhjFeMGxXjSBN8x8LFA8ytrQgWrHLoh";
                v.ColorCode = "AmAhQ1pAAqWvB3KM2xRc";
                v.Pcs = 70;
                v.Qty = 6;
                v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v.DeliveryDate = DateTime.Parse("2024-02-14 19:18:26");
                v.FinishingPrice = 36;
                v.GreigeBatch = "u";
                v.DyeingProcess = "DjUcnzvHtEhXIDW";
                v.Remark = "e";
                context.Set<PlanDetail>().Add(v);
                context.SaveChanges();
            }

            PlanDetailVM vm = _controller.Wtm.CreateVM<PlanDetailVM>();
            var oldID = v.ID;
            v = new PlanDetail();
            v.ID = oldID;
       		
            v.Color = "xXXHF0s2LJIrD7RoBjM7wKmpVANJ88iv1CJPseOqRU241xY9K014JGxFZHu";
            v.ColorCode = "X8gS8cukHUaJIjr5Du";
            v.Pcs = 73;
            v.Qty = 96;
            v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.KG;
            v.DeliveryDate = DateTime.Parse("2025-05-03 19:18:26");
            v.FinishingPrice = 61;
            v.GreigeBatch = "wNPygsFNHtsVry7d9H";
            v.DyeingProcess = "VfXeYSrJWgEg1Qa";
            v.Remark = "RFjGhzO";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.DyeingPlanId", "");
            vm.FC.Add("Entity.OrderDetailId", "");
            vm.FC.Add("Entity.Color", "");
            vm.FC.Add("Entity.ColorCode", "");
            vm.FC.Add("Entity.Pcs", "");
            vm.FC.Add("Entity.Qty", "");
            vm.FC.Add("Entity.QtyUnit", "");
            vm.FC.Add("Entity.DeliveryDate", "");
            vm.FC.Add("Entity.FinishingPrice", "");
            vm.FC.Add("Entity.GreigeBatch", "");
            vm.FC.Add("Entity.DyeingProcess", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<PlanDetail>().Find(v.ID);
 				
                Assert.AreEqual(data.Color, "xXXHF0s2LJIrD7RoBjM7wKmpVANJ88iv1CJPseOqRU241xY9K014JGxFZHu");
                Assert.AreEqual(data.ColorCode, "X8gS8cukHUaJIjr5Du");
                Assert.AreEqual(data.Pcs, 73);
                Assert.AreEqual(data.Qty, 96);
                Assert.AreEqual(data.QtyUnit, TEX.Model.Models.AccountingUnitEnum.KG);
                Assert.AreEqual(data.DeliveryDate, DateTime.Parse("2025-05-03 19:18:26"));
                Assert.AreEqual(data.FinishingPrice, 61);
                Assert.AreEqual(data.GreigeBatch, "wNPygsFNHtsVry7d9H");
                Assert.AreEqual(data.DyeingProcess, "VfXeYSrJWgEg1Qa");
                Assert.AreEqual(data.Remark, "RFjGhzO");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            PlanDetail v = new PlanDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.DyeingPlanId = AddDyeingPlan();
                v.OrderDetailId = AddOrderDetail();
                v.Color = "YuWxsIW9Nx42jbAgqskNFSQg4c6g5eXhjFeMGxXjSBN8x8LFA8ytrQgWrHLoh";
                v.ColorCode = "AmAhQ1pAAqWvB3KM2xRc";
                v.Pcs = 70;
                v.Qty = 6;
                v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v.DeliveryDate = DateTime.Parse("2024-02-14 19:18:26");
                v.FinishingPrice = 36;
                v.GreigeBatch = "u";
                v.DyeingProcess = "DjUcnzvHtEhXIDW";
                v.Remark = "e";
                context.Set<PlanDetail>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            PlanDetail v1 = new PlanDetail();
            PlanDetail v2 = new PlanDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.DyeingPlanId = AddDyeingPlan();
                v1.OrderDetailId = AddOrderDetail();
                v1.Color = "YuWxsIW9Nx42jbAgqskNFSQg4c6g5eXhjFeMGxXjSBN8x8LFA8ytrQgWrHLoh";
                v1.ColorCode = "AmAhQ1pAAqWvB3KM2xRc";
                v1.Pcs = 70;
                v1.Qty = 6;
                v1.QtyUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v1.DeliveryDate = DateTime.Parse("2024-02-14 19:18:26");
                v1.FinishingPrice = 36;
                v1.GreigeBatch = "u";
                v1.DyeingProcess = "DjUcnzvHtEhXIDW";
                v1.Remark = "e";
                v2.DyeingPlanId = v1.DyeingPlanId; 
                v2.OrderDetailId = v1.OrderDetailId; 
                v2.Color = "xXXHF0s2LJIrD7RoBjM7wKmpVANJ88iv1CJPseOqRU241xY9K014JGxFZHu";
                v2.ColorCode = "X8gS8cukHUaJIjr5Du";
                v2.Pcs = 73;
                v2.Qty = 96;
                v2.QtyUnit = TEX.Model.Models.AccountingUnitEnum.KG;
                v2.DeliveryDate = DateTime.Parse("2025-05-03 19:18:26");
                v2.FinishingPrice = 61;
                v2.GreigeBatch = "wNPygsFNHtsVry7d9H";
                v2.DyeingProcess = "VfXeYSrJWgEg1Qa";
                v2.Remark = "RFjGhzO";
                context.Set<PlanDetail>().Add(v1);
                context.Set<PlanDetail>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<PlanDetail>().Find(v1.ID);
                var data2 = context.Set<PlanDetail>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "z0nGjTSP8lfWzN0g9fNEoO5";
                v.CompanyName = "1rffq4Uuvo5mlxzRdfh";
                v.CompanyFullName = "4sp";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.WovenFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                v.ContactPhone = "nVy9AWbdw";
                v.Adress = "8t5KcpXyN";
                v.TaxNO = "5Uv0INaGnE19AdK7MHYufK";
                v.InvoiceInfo = "MSSRoT6TVLrzbM";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "nBsRjw";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "fMXvl";
                v.MobilePhone = "iI";
                v.Address = "9J";
                v.Phone = "EzuyMdrgb";
                v.Email = "xJxJCG";
                v.WeChat = "IPNOKb8XNkZqXnwb0";
                v.QQ = "SSPUcNgFy";
                v.Fax = "RAMHd2e";
                v.Remark = "0c97tGcE7vRnpFgwOT";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictType()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 31;
                v.Name = "2MqWhi24GxIoW1Rwwg1";
                v.Description = "DF2b3ZpUS";
                v.DictOrder = 24;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictItem()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 18;
                v.DictTypeId = AddDictType();
                v.ItemName = "VV4g3";
                v.Description = "0vCa5lW4fiy4LbOEk";
                v.DictOrder = 61;
                v.Remark = "vmOYC";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "zM5sZjSqXWGSw8G2guvR";
                v.ProductName = "guqiJdgzAfKt7r8RR71fY8xsWdIi";
                v.CategoryId = AddDictItem();
                v.Contents = "IpqcrAUBPXNFYybQ2tp2KHila0k";
                v.Spec = "YxsNr7zsb7ig5qBKC6yBrsdMiJe7GIeUSboZKTinrhEv6VS34BPpTQycRGfcthIxUI0ymvFSlWm";
                v.GSM = 42;
                v.Width = 81;
                v.DyeingProductName = "0iak0ZKASWriMmhn5x3akrYWem7PaQTdtDd5KMEBeZ9FBzL6xTdxz6m";
                v.PileLength = 27;
                v.DyeingProcess = "8kHGsP4TMKiL";
                v.KnittingProcess = "LMb";
                v.FinishingProcess = "7jybcsK1";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2025-11-06 19:18:26");
                v.DeliveryDate = DateTime.Parse("2025-01-10 19:18:26");
                v.CustomerId = AddCompany();
                v.OrderNo = "UTB8499OQwKlpUj2BCxwRvI9nZXEtf";
                v.CustomerOrderNo = "bdTz6UEkQ3wkYpd1QEPN3kh5EUyS4khSuebGvDU8JPC15OW6lOuWkThU4O";
                v.MerchandiserId = AddContact();
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Greige;
                v.ProductId = AddProduct();
                v.DyeingProductName = "E58";
                v.Light = TEX.Model.Models.LightEnum.D65;
                v.Light2 = TEX.Model.Models.LightEnum.D65_LED;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.CNY;
                v.TotalMeters = 47;
                v.TotalYards = 65;
                v.TotalWeight = 33;
                v.TotalAmount = 6;
                v.CompletedStatus = TEX.Model.Models.CompletedStatusEnum.Completed;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "BHQevm";
                v.AuditedComment = "Z8Aeh5nSFh9gRPzMpb";
                v.Remark = "D9YzPr8VFCNEL";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddDyeingPlan()
        {
            DyeingPlan v = new DyeingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2023-10-31 19:18:26");
                v.BillNo = "7FEr6FKGHu7VDp9";
                v.Version = 1;
                v.FinishingFactoryId = AddCompany();
                v.POrderId = AddPurchaseOrder();
                v.PlanBatch = "Q69435QOThdSd2C";
                v.FinishingProcess = "X";
                //v.Width = "Y9jWW";
                //v.GSM = "gxrddqTA";
                v.Light = TEX.Model.Models.LightEnum.DayLight;
                v.Light2 = TEX.Model.Models.LightEnum.TL83;
                v.Pcs = 46;
                v.TotalQty = 5;
                v.GreigeBatch = "Bv";
                v.GreigeVenderId = AddCompany();
                v.DyeingDemand = "R4T";
                v.PackDemand = "H3ew0IOR";
                v.AdditionalDemend = "9vi47DlIH6WNlf";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "S53qSKsSi";
                v.AuditedComment = "RHpfrMOaEivaq3L1BG";
                v.Remark = "2ahlBg0";
                context.Set<DyeingPlan>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddOrderDetail()
        {
            OrderDetail v = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.PurchaseOrderId = AddPurchaseOrder();
                v.Color = "oMVtm0sX0FzGpa2CcWDvIUe";
                v.EngColor = "b0065KymLX9IbsMDGvOx";
                v.ColorCode = "knizF";
                v.Meters = 29;
                v.KG = 48;
                v.Yards = 15;
                v.Price = 43;
                v.Amount = 23;
                v.Remark = "z0xnlRVqlrB64XIGJL";
                context.Set<OrderDetail>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
