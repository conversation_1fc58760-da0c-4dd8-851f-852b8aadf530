using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Producttion.LotAllocateVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class LotAllocateApiTest
    {
        private LotAllocateController _controller;
        private string _seed;

        public LotAllocateApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<LotAllocateController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new LotAllocateSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            LotAllocateVM vm = _controller.Wtm.CreateVM<LotAllocateVM>();
            LotAllocate v = new LotAllocate();
            
            v.PlanDetailId = AddPlanDetail();
            v.CreateDate = DateTime.Parse("2024-07-08 10:04:14");
            v.ProductName = "53";
            v.Color = "aMIR0nmBM6kBCQ1epENXIX5aCC6Ko";
            v.ColorCode = "JpRldaqBx1NpWUi8IGh";
            v.LotNo = "ge51vlFjZUXK2Wsh5ixjbjQgKx9ythORDGpurB";
            v.Pcs = 55;
            v.Meters = 89;
            v.Weight = 72;
            //v.DyeingProcess = "QIkb6fwd";
            v.Remark = "Yu9UUJ6OGVP9pQsQrH2";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<LotAllocate>().Find(v.ID);
                
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-07-08 10:04:14"));
                Assert.AreEqual(data.ProductName, "53");
                Assert.AreEqual(data.Color, "aMIR0nmBM6kBCQ1epENXIX5aCC6Ko");
                Assert.AreEqual(data.ColorCode, "JpRldaqBx1NpWUi8IGh");
                Assert.AreEqual(data.LotNo, "ge51vlFjZUXK2Wsh5ixjbjQgKx9ythORDGpurB");
                Assert.AreEqual(data.Pcs, 55);
                Assert.AreEqual(data.Meters, 89);
                Assert.AreEqual(data.Weight, 72);
                //Assert.AreEqual(data.DyeingProcess, "QIkb6fwd");
                Assert.AreEqual(data.Remark, "Yu9UUJ6OGVP9pQsQrH2");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            LotAllocate v = new LotAllocate();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.PlanDetailId = AddPlanDetail();
                v.CreateDate = DateTime.Parse("2024-07-08 10:04:14");
                v.ProductName = "53";
                v.Color = "aMIR0nmBM6kBCQ1epENXIX5aCC6Ko";
                v.ColorCode = "JpRldaqBx1NpWUi8IGh";
                v.LotNo = "ge51vlFjZUXK2Wsh5ixjbjQgKx9ythORDGpurB";
                v.Pcs = 55;
                v.Meters = 89;
                v.Weight = 72;
                //v.DyeingProcess = "QIkb6fwd";
                v.Remark = "Yu9UUJ6OGVP9pQsQrH2";
                context.Set<LotAllocate>().Add(v);
                context.SaveChanges();
            }

            LotAllocateVM vm = _controller.Wtm.CreateVM<LotAllocateVM>();
            var oldID = v.ID;
            v = new LotAllocate();
            v.ID = oldID;
       		
            v.CreateDate = DateTime.Parse("2024-01-21 10:04:14");
            v.ProductName = "5zl3wjODXNpdbAal5";
            v.Color = "R4tGlo0qAebsZnzFiWDS8fNU1p07c5OpC1mNm2PcOj2YALDnwWpjoS";
            v.ColorCode = "Kdh9c0MUW1yWxDFn9iRBBRczy1rZED04KfmF6x";
            v.LotNo = "W38CYl1pRqJucXteFAXRWpOZpZxaSv0J3oSMlTcO7BX1afSfnPJ8qep";
            v.Pcs = 68;
            v.Meters = 59;
            v.Weight = 47;
            //v.DyeingProcess = "jmtyD";
            v.Remark = "MGQzS";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.PlanDetailId", "");
            vm.FC.Add("Entity.CreateDate", "");
            vm.FC.Add("Entity.ProductName", "");
            vm.FC.Add("Entity.Color", "");
            vm.FC.Add("Entity.ColorCode", "");
            vm.FC.Add("Entity.LotNo", "");
            vm.FC.Add("Entity.Pcs", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.DyeingProcess", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<LotAllocate>().Find(v.ID);
 				
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-01-21 10:04:14"));
                Assert.AreEqual(data.ProductName, "5zl3wjODXNpdbAal5");
                Assert.AreEqual(data.Color, "R4tGlo0qAebsZnzFiWDS8fNU1p07c5OpC1mNm2PcOj2YALDnwWpjoS");
                Assert.AreEqual(data.ColorCode, "Kdh9c0MUW1yWxDFn9iRBBRczy1rZED04KfmF6x");
                Assert.AreEqual(data.LotNo, "W38CYl1pRqJucXteFAXRWpOZpZxaSv0J3oSMlTcO7BX1afSfnPJ8qep");
                Assert.AreEqual(data.Pcs, 68);
                Assert.AreEqual(data.Meters, 59);
                Assert.AreEqual(data.Weight, 47);
                //Assert.AreEqual(data.DyeingProcess, "jmtyD");
                Assert.AreEqual(data.Remark, "MGQzS");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            LotAllocate v = new LotAllocate();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.PlanDetailId = AddPlanDetail();
                v.CreateDate = DateTime.Parse("2024-07-08 10:04:14");
                v.ProductName = "53";
                v.Color = "aMIR0nmBM6kBCQ1epENXIX5aCC6Ko";
                v.ColorCode = "JpRldaqBx1NpWUi8IGh";
                v.LotNo = "ge51vlFjZUXK2Wsh5ixjbjQgKx9ythORDGpurB";
                v.Pcs = 55;
                v.Meters = 89;
                v.Weight = 72;
                //v.DyeingProcess = "QIkb6fwd";
                v.Remark = "Yu9UUJ6OGVP9pQsQrH2";
                context.Set<LotAllocate>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            LotAllocate v1 = new LotAllocate();
            LotAllocate v2 = new LotAllocate();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.PlanDetailId = AddPlanDetail();
                v1.CreateDate = DateTime.Parse("2024-07-08 10:04:14");
                v1.ProductName = "53";
                v1.Color = "aMIR0nmBM6kBCQ1epENXIX5aCC6Ko";
                v1.ColorCode = "JpRldaqBx1NpWUi8IGh";
                v1.LotNo = "ge51vlFjZUXK2Wsh5ixjbjQgKx9ythORDGpurB";
                v1.Pcs = 55;
                v1.Meters = 89;
                v1.Weight = 72;
                //v1.DyeingProcess = "QIkb6fwd";
                v1.Remark = "Yu9UUJ6OGVP9pQsQrH2";
                v2.PlanDetailId = v1.PlanDetailId; 
                v2.CreateDate = DateTime.Parse("2024-01-21 10:04:14");
                v2.ProductName = "5zl3wjODXNpdbAal5";
                v2.Color = "R4tGlo0qAebsZnzFiWDS8fNU1p07c5OpC1mNm2PcOj2YALDnwWpjoS";
                v2.ColorCode = "Kdh9c0MUW1yWxDFn9iRBBRczy1rZED04KfmF6x";
                v2.LotNo = "W38CYl1pRqJucXteFAXRWpOZpZxaSv0J3oSMlTcO7BX1afSfnPJ8qep";
                v2.Pcs = 68;
                v2.Meters = 59;
                v2.Weight = 47;
                //v2.DyeingProcess = "jmtyD";
                v2.Remark = "MGQzS";
                context.Set<LotAllocate>().Add(v1);
                context.Set<LotAllocate>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<LotAllocate>().Find(v1.ID);
                var data2 = context.Set<LotAllocate>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "BZFTnvz7Xbj";
                v.CompanyName = "KZ4Yv3rBv4x1mBWtJ";
                v.CompanyFullName = "fQoW1wuVZ1usqbH";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.FabricVender;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "r4";
                v.Adress = "WZuP";
                v.TaxNO = "Z7jc3UvOHGXrrIxS4OM";
                v.InvoiceInfo = "OCaZd73b";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "ypEmQgoE";
                v.ProductName = "oVEdefta85vrV7B9bBL9nCjSDU1XeLLbVDvBbDzucwi1MiDzpPVvYnQsSBL";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Others;
                v.Contents = "w9tY0YbTwcxs8cA2jdLLlXcVvI6v8l2YA0y0z3JZwIdiZFr";
                v.Spec = "ojvz5S5A5h8DP1BIqySpQTU6SsCYdBNYPdGs8snASen63yEZOg6wsAFb";
                v.GSM = 22;
                v.Width = 94;
                v.PileLength = 27;
                v.DyeingProcess = "8xEgC";
                v.KnittingProcess = "uwTFxusLQXUMH8";
                v.FinishingProcess = "EwT8RBxDvlw";
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2022-12-30 10:04:14");
                v.DeliveryDate = DateTime.Parse("2025-04-12 10:04:14");
                v.CustomerId = AddCompany();
                v.OrderNo = "GUQ";
                v.CustomerOrderNo = "vyOBLVDHpAjLTsCwD5xlZOLQCtZowucKiW0XEypYnQp1";
                //v.Merchandiser = "NLyKjAOZ";
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.Light = TEX.Model.Models.LightEnum.LED;
                v.Light2 = TEX.Model.Models.LightEnum.D65;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.KG;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.HKD;
                v.TotalMeters = 28;
                v.TotalYards = 76;
                v.TotalWeight = 53;
                v.TotalAmount = 10;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "mYUmTCt4Z";
                v.AuditedComment = "2fYCb3d8TNjq4AW1lz2";
                v.Remark = "cUO";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddDyeingPlan()
        {
            DyeingPlan v = new DyeingPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2024-02-12 10:04:14");
                //v.BillNo = 2;
                v.Version = 66;
                v.FinishingFactoryId = AddCompany();
                v.POrderId = AddPurchaseOrder();
                v.PlanBatch = "Q2FVRTZWDSEfzjkBpnM08B9ZRmMFuPHFh0T21OQG5qjLVmNa07T4";
                v.FinishingProcess = "IY6yS72kcqZ7j";
                //v.Width = "TTLfp0Pe5CQq";
                //v.GSM = "9fkqDZPXx5dZkDZDCDU";
                v.Light = TEX.Model.Models.LightEnum.D65;
                v.Light2 = TEX.Model.Models.LightEnum.DayLight;
                v.Pcs = 90;
                v.TotalQty = 31;
                v.GreigeBatch = "Dj3vui7azdn3QCGi";
                v.GreigeVenderId = AddCompany();
                v.DyeingDemand = "xvSWc";
                v.PackDemand = "Y2zNE8IJ";
                v.AdditionalDemend = "f9qjjZA4j";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "hFGzP";
                v.AuditedComment = "TPPkKquFUbepaLt";
                v.Remark = "Og";
                context.Set<DyeingPlan>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPlanDetail()
        {
            PlanDetail v = new PlanDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.DyeingPlanId = AddDyeingPlan();
                v.Color = "zkuet1n7f93ggtdlWWBlNkXVJE";
                v.ColorCode = "rgurJU6oumqxSnZon5s5RxYEjldggA4VbL1FX";
                v.Pcs = 8;
                v.Qty = 64;
                v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v.DeliveryDate = DateTime.Parse("2024-12-12 10:04:14");
                v.FinishingPrice = 8;
                //v.GreigeVenderId = AddCompany();
                v.GreigeBatch = "OFyruz9NJNxAz9pY";
                v.Remark = "giuOnBDD";
                context.Set<PlanDetail>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
