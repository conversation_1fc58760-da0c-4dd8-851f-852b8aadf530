using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Greige.GreigeInboundBillVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class GreigeInboundBillApiTest
    {
        private GreigeInboundBillController _controller;
        private string _seed;

        public GreigeInboundBillApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<GreigeInboundBillController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new GreigeInboundBillSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            GreigeInboundBillVM vm = _controller.Wtm.CreateVM<GreigeInboundBillVM>();
            GreigeInboundBill v = new GreigeInboundBill();
            
            v.BillNo = "49K";
            v.InboundDate = DateTime.Parse("2025-04-21 15:08:59");
            v.KnittingFactoryId = AddCompany();
            v.Warehouse = "bcHOaiFXJHGMpbZcU690A";
            v.TotalRolls = 26;
            v.TotalWeight = 19;
            v.TotalMeters = 80;
            v.TotalYards = 30;
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
            v.AuditedBy = "2JIcAW0yOMLRlhax6xg";
            v.AuditedComment = "f0P";
            v.Remark = "x2GlmM0ZZi";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<GreigeInboundBill>().Find(v.ID);
                
                Assert.AreEqual(data.BillNo, "49K");
                Assert.AreEqual(data.InboundDate, DateTime.Parse("2025-04-21 15:08:59"));
                Assert.AreEqual(data.Warehouse, "bcHOaiFXJHGMpbZcU690A");
                Assert.AreEqual(data.TotalRolls, 26);
                Assert.AreEqual(data.TotalWeight, 19);
                Assert.AreEqual(data.TotalMeters, 80);
                Assert.AreEqual(data.TotalYards, 30);
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedFailed);
                Assert.AreEqual(data.AuditedBy, "2JIcAW0yOMLRlhax6xg");
                Assert.AreEqual(data.AuditedComment, "f0P");
                Assert.AreEqual(data.Remark, "x2GlmM0ZZi");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            GreigeInboundBill v = new GreigeInboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.BillNo = "49K";
                v.InboundDate = DateTime.Parse("2025-04-21 15:08:59");
                v.KnittingFactoryId = AddCompany();
                v.Warehouse = "bcHOaiFXJHGMpbZcU690A";
                v.TotalRolls = 26;
                v.TotalWeight = 19;
                v.TotalMeters = 80;
                v.TotalYards = 30;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "2JIcAW0yOMLRlhax6xg";
                v.AuditedComment = "f0P";
                v.Remark = "x2GlmM0ZZi";
                context.Set<GreigeInboundBill>().Add(v);
                context.SaveChanges();
            }

            GreigeInboundBillVM vm = _controller.Wtm.CreateVM<GreigeInboundBillVM>();
            var oldID = v.ID;
            v = new GreigeInboundBill();
            v.ID = oldID;
       		
            v.BillNo = "xOaLbJIf";
            v.InboundDate = DateTime.Parse("2026-01-28 15:08:59");
            v.Warehouse = "fLMo3VinYoldO4Db2MXe75Fk8e6uogskek4ctSBF3vc9sfZHAM1t1Z8DaBkHHC6";
            v.TotalRolls = 96;
            v.TotalWeight = 85;
            v.TotalMeters = 40;
            v.TotalYards = 30;
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "G0UsVwUegagvuvbm";
            v.AuditedComment = "zpoAgwy1SRF4";
            v.Remark = "qwSv9k";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.BillNo", "");
            vm.FC.Add("Entity.InboundDate", "");
            vm.FC.Add("Entity.KnittingFactoryId", "");
            vm.FC.Add("Entity.Warehouse", "");
            vm.FC.Add("Entity.TotalRolls", "");
            vm.FC.Add("Entity.TotalWeight", "");
            vm.FC.Add("Entity.TotalMeters", "");
            vm.FC.Add("Entity.TotalYards", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.AuditedComment", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<GreigeInboundBill>().Find(v.ID);
 				
                Assert.AreEqual(data.BillNo, "xOaLbJIf");
                Assert.AreEqual(data.InboundDate, DateTime.Parse("2026-01-28 15:08:59"));
                Assert.AreEqual(data.Warehouse, "fLMo3VinYoldO4Db2MXe75Fk8e6uogskek4ctSBF3vc9sfZHAM1t1Z8DaBkHHC6");
                Assert.AreEqual(data.TotalRolls, 96);
                Assert.AreEqual(data.TotalWeight, 85);
                Assert.AreEqual(data.TotalMeters, 40);
                Assert.AreEqual(data.TotalYards, 30);
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "G0UsVwUegagvuvbm");
                Assert.AreEqual(data.AuditedComment, "zpoAgwy1SRF4");
                Assert.AreEqual(data.Remark, "qwSv9k");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            GreigeInboundBill v = new GreigeInboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.BillNo = "49K";
                v.InboundDate = DateTime.Parse("2025-04-21 15:08:59");
                v.KnittingFactoryId = AddCompany();
                v.Warehouse = "bcHOaiFXJHGMpbZcU690A";
                v.TotalRolls = 26;
                v.TotalWeight = 19;
                v.TotalMeters = 80;
                v.TotalYards = 30;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "2JIcAW0yOMLRlhax6xg";
                v.AuditedComment = "f0P";
                v.Remark = "x2GlmM0ZZi";
                context.Set<GreigeInboundBill>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            GreigeInboundBill v1 = new GreigeInboundBill();
            GreigeInboundBill v2 = new GreigeInboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.BillNo = "49K";
                v1.InboundDate = DateTime.Parse("2025-04-21 15:08:59");
                v1.KnittingFactoryId = AddCompany();
                v1.Warehouse = "bcHOaiFXJHGMpbZcU690A";
                v1.TotalRolls = 26;
                v1.TotalWeight = 19;
                v1.TotalMeters = 80;
                v1.TotalYards = 30;
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v1.AuditedBy = "2JIcAW0yOMLRlhax6xg";
                v1.AuditedComment = "f0P";
                v1.Remark = "x2GlmM0ZZi";
                v2.BillNo = "xOaLbJIf";
                v2.InboundDate = DateTime.Parse("2026-01-28 15:08:59");
                v2.KnittingFactoryId = v1.KnittingFactoryId; 
                v2.Warehouse = "fLMo3VinYoldO4Db2MXe75Fk8e6uogskek4ctSBF3vc9sfZHAM1t1Z8DaBkHHC6";
                v2.TotalRolls = 96;
                v2.TotalWeight = 85;
                v2.TotalMeters = 40;
                v2.TotalYards = 30;
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v2.AuditedBy = "G0UsVwUegagvuvbm";
                v2.AuditedComment = "zpoAgwy1SRF4";
                v2.Remark = "qwSv9k";
                context.Set<GreigeInboundBill>().Add(v1);
                context.Set<GreigeInboundBill>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<GreigeInboundBill>().Find(v1.ID);
                var data2 = context.Set<GreigeInboundBill>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "FYC97Kq7eqs";
                v.CompanyName = "VTprofeo06H3gQd";
                v.CompanyFullName = "IBjHveJX3Vq6KEbJ8B3bFTclpYym5noPkt1PBqgESeuffLdTQC";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.FinishingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "HqvO";
                v.Adress = "LP9VxYaj4";
                v.TaxNO = "w7vyJlSp5IMtwVAQ513OzoZL6Fg8Ja30w9z0BkyPih2ZvmjTkqHamMlqd";
                v.InvoiceInfo = "cakYHF39XH0IUgJyHh2";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
