using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Models.DictTypeVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class DictTypeApiTest
    {
        private DictTypeController _controller;
        private string _seed;

        public DictTypeApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<DictTypeController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new DictTypeSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            DictTypeVM vm = _controller.Wtm.CreateVM<DictTypeVM>();
            DictType v = new DictType();
            
            v.ID = 40;
            v.Name = "4C2NVbP0eBsgBe8";
            v.Description = "YjVAItvD2";
            v.DictOrder = 99;
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DictType>().Find(v.ID);
                
                Assert.AreEqual(data.ID, 40);
                Assert.AreEqual(data.Name, "4C2NVbP0eBsgBe8");
                Assert.AreEqual(data.Description, "YjVAItvD2");
                Assert.AreEqual(data.DictOrder, 99);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ID = 40;
                v.Name = "4C2NVbP0eBsgBe8";
                v.Description = "YjVAItvD2";
                v.DictOrder = 99;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
            }

            DictTypeVM vm = _controller.Wtm.CreateVM<DictTypeVM>();
            var oldID = v.ID;
            v = new DictType();
            v.ID = oldID;
       		
            v.Name = "gGEHwtDfDol";
            v.Description = "SmTpssN7";
            v.DictOrder = 41;
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ID", "");
            vm.FC.Add("Entity.Name", "");
            vm.FC.Add("Entity.Description", "");
            vm.FC.Add("Entity.DictOrder", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DictType>().Find(v.ID);
 				
                Assert.AreEqual(data.Name, "gGEHwtDfDol");
                Assert.AreEqual(data.Description, "SmTpssN7");
                Assert.AreEqual(data.DictOrder, 41);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ID = 40;
                v.Name = "4C2NVbP0eBsgBe8";
                v.Description = "YjVAItvD2";
                v.DictOrder = 99;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            DictType v1 = new DictType();
            DictType v2 = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ID = 40;
                v1.Name = "4C2NVbP0eBsgBe8";
                v1.Description = "YjVAItvD2";
                v1.DictOrder = 99;
                v2.ID = 60;
                v2.Name = "gGEHwtDfDol";
                v2.Description = "SmTpssN7";
                v2.DictOrder = 41;
                context.Set<DictType>().Add(v1);
                context.Set<DictType>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<DictType>().Find(v1.ID);
                var data2 = context.Set<DictType>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }


    }
}
