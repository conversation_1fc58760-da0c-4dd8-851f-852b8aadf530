using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Producttion.WIPDetailVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class WIPDetailApiTest
    {
        private WIPDetailController _controller;
        private string _seed;

        public WIPDetailApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<WIPDetailController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new WIPDetailSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            WIPDetailVM vm = _controller.Wtm.CreateVM<WIPDetailVM>();
            WIPDetail v = new WIPDetail();
            
            v.Color = "sFoz2fdnIqXBxN051c";
            v.LotNo = "wt536WTG5xifedEjVE7C7xyXHSb5iR";
            v.Pcs = 62;
            v.Meters = 49;
            v.Weight = 93;
            v.Remark = "JQhyP0TFe";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<WIPDetail>().Find(v.ID);
                
                Assert.AreEqual(data.Color, "sFoz2fdnIqXBxN051c");
                Assert.AreEqual(data.LotNo, "wt536WTG5xifedEjVE7C7xyXHSb5iR");
                Assert.AreEqual(data.Pcs, 62);
                Assert.AreEqual(data.Meters, 49);
                Assert.AreEqual(data.Weight, 93);
                Assert.AreEqual(data.Remark, "JQhyP0TFe");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            WIPDetail v = new WIPDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.Color = "sFoz2fdnIqXBxN051c";
                v.LotNo = "wt536WTG5xifedEjVE7C7xyXHSb5iR";
                v.Pcs = 62;
                v.Meters = 49;
                v.Weight = 93;
                v.Remark = "JQhyP0TFe";
                context.Set<WIPDetail>().Add(v);
                context.SaveChanges();
            }

            WIPDetailVM vm = _controller.Wtm.CreateVM<WIPDetailVM>();
            var oldID = v.ID;
            v = new WIPDetail();
            v.ID = oldID;
       		
            v.Color = "ZAFtz5xV0GlzK2uDR2k7y2LccYaWAt4sq8kgmt1Ox9k6w7KsOjtABqQcBOO";
            v.LotNo = "X6RwfaVZFRsa1AWiMKAcpAf5jG4CitbfMAGFcrGr7pP";
            v.Pcs = 47;
            v.Meters = 28;
            v.Weight = 36;
            v.Remark = "6zQEyBdPQJF6lo1bZ3h";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.Color", "");
            vm.FC.Add("Entity.LotNo", "");
            vm.FC.Add("Entity.Pcs", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<WIPDetail>().Find(v.ID);
 				
                Assert.AreEqual(data.Color, "ZAFtz5xV0GlzK2uDR2k7y2LccYaWAt4sq8kgmt1Ox9k6w7KsOjtABqQcBOO");
                Assert.AreEqual(data.LotNo, "X6RwfaVZFRsa1AWiMKAcpAf5jG4CitbfMAGFcrGr7pP");
                Assert.AreEqual(data.Pcs, 47);
                Assert.AreEqual(data.Meters, 28);
                Assert.AreEqual(data.Weight, 36);
                Assert.AreEqual(data.Remark, "6zQEyBdPQJF6lo1bZ3h");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            WIPDetail v = new WIPDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.Color = "sFoz2fdnIqXBxN051c";
                v.LotNo = "wt536WTG5xifedEjVE7C7xyXHSb5iR";
                v.Pcs = 62;
                v.Meters = 49;
                v.Weight = 93;
                v.Remark = "JQhyP0TFe";
                context.Set<WIPDetail>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            WIPDetail v1 = new WIPDetail();
            WIPDetail v2 = new WIPDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.Color = "sFoz2fdnIqXBxN051c";
                v1.LotNo = "wt536WTG5xifedEjVE7C7xyXHSb5iR";
                v1.Pcs = 62;
                v1.Meters = 49;
                v1.Weight = 93;
                v1.Remark = "JQhyP0TFe";
                v2.Color = "ZAFtz5xV0GlzK2uDR2k7y2LccYaWAt4sq8kgmt1Ox9k6w7KsOjtABqQcBOO";
                v2.LotNo = "X6RwfaVZFRsa1AWiMKAcpAf5jG4CitbfMAGFcrGr7pP";
                v2.Pcs = 47;
                v2.Meters = 28;
                v2.Weight = 36;
                v2.Remark = "6zQEyBdPQJF6lo1bZ3h";
                context.Set<WIPDetail>().Add(v1);
                context.Set<WIPDetail>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<WIPDetail>().Find(v1.ID);
                var data2 = context.Set<WIPDetail>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }


    }
}
