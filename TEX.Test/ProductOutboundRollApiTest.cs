using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Finished.ProductOutboundRollVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class ProductOutboundRollApiTest
    {
        private ProductOutboundRollController _controller;
        private string _seed;

        public ProductOutboundRollApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<ProductOutboundRollController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new ProductOutboundRollSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ProductOutboundRollVM vm = _controller.Wtm.CreateVM<ProductOutboundRollVM>();
            ProductOutboundRoll v = new ProductOutboundRoll();
            
            v.LotId = AddProductOutboundLot();
            v.RollNo = 63;
            v.Weight = 5;
            v.Meters = 81;
            v.Yards = 52;
            v.Grade = "lTuj8uY";
            v.Remark = "U45pngx";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductOutboundRoll>().Find(v.ID);
                
                Assert.AreEqual(data.RollNo, 63);
                Assert.AreEqual(data.Weight, 5);
                Assert.AreEqual(data.Meters, 81);
                Assert.AreEqual(data.Yards, 52);
                Assert.AreEqual(data.Grade, "lTuj8uY");
                Assert.AreEqual(data.Remark, "U45pngx");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            ProductOutboundRoll v = new ProductOutboundRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.LotId = AddProductOutboundLot();
                v.RollNo = 63;
                v.Weight = 5;
                v.Meters = 81;
                v.Yards = 52;
                v.Grade = "lTuj8uY";
                v.Remark = "U45pngx";
                context.Set<ProductOutboundRoll>().Add(v);
                context.SaveChanges();
            }

            ProductOutboundRollVM vm = _controller.Wtm.CreateVM<ProductOutboundRollVM>();
            var oldID = v.ID;
            v = new ProductOutboundRoll();
            v.ID = oldID;
       		
            v.RollNo = 68;
            v.Weight = 32;
            v.Meters = 63;
            v.Yards = 58;
            v.Grade = "xm4oa6X500VM";
            v.Remark = "Q02KFZwMmjHF";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.LotId", "");
            vm.FC.Add("Entity.RollNo", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Yards", "");
            vm.FC.Add("Entity.Grade", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductOutboundRoll>().Find(v.ID);
 				
                Assert.AreEqual(data.RollNo, 68);
                Assert.AreEqual(data.Weight, 32);
                Assert.AreEqual(data.Meters, 63);
                Assert.AreEqual(data.Yards, 58);
                Assert.AreEqual(data.Grade, "xm4oa6X500VM");
                Assert.AreEqual(data.Remark, "Q02KFZwMmjHF");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            ProductOutboundRoll v = new ProductOutboundRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.LotId = AddProductOutboundLot();
                v.RollNo = 63;
                v.Weight = 5;
                v.Meters = 81;
                v.Yards = 52;
                v.Grade = "lTuj8uY";
                v.Remark = "U45pngx";
                context.Set<ProductOutboundRoll>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            ProductOutboundRoll v1 = new ProductOutboundRoll();
            ProductOutboundRoll v2 = new ProductOutboundRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.LotId = AddProductOutboundLot();
                v1.RollNo = 63;
                v1.Weight = 5;
                v1.Meters = 81;
                v1.Yards = 52;
                v1.Grade = "lTuj8uY";
                v1.Remark = "U45pngx";
                v2.LotId = v1.LotId; 
                v2.RollNo = 68;
                v2.Weight = 32;
                v2.Meters = 63;
                v2.Yards = 58;
                v2.Grade = "xm4oa6X500VM";
                v2.Remark = "Q02KFZwMmjHF";
                context.Set<ProductOutboundRoll>().Add(v1);
                context.Set<ProductOutboundRoll>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<ProductOutboundRoll>().Find(v1.ID);
                var data2 = context.Set<ProductOutboundRoll>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "TDmJIZ7sQS0k1xCKah5twsM5JTJxk";
                v.CompanyName = "2fvbGnbGDP0chRlzsq3fqlI8vXcnVegw";
                v.CompanyFullName = "QcFw0EvMM4NFNEBq0iCjG4AzNScXVWOIZXOZXyArnJon6UT9oM6tVud0HnH";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.GarmentFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                v.ContactPhone = "4Lmj";
                v.Adress = "Iz9UpepbBl7rXHDK";
                v.TaxNO = "9zpV8d1DqcUWeY";
                v.InvoiceInfo = "mpTyO";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProductOutboundBill()
        {
            ProductOutboundBill v = new ProductOutboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2022-09-29 16:32:28");
                v.BillNo = "CpHca4t61rn";
                v.CustomerId = AddCompany();
                v.ReceiverId = AddCompany();
                //v.CustomerOrderNo = "Qa4fcN";
                v.Pcs = 97;
                v.Weight = 23;
                v.Meters = 91;
                v.Yards = 73;
                v.Remark = "6DookOzbq7M";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "SE6b1p5KWjDVrizQbji";
                v.AuditedComment = "YMEnTj2o3ZqLVFJfsqx";
                context.Set<ProductOutboundBill>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "Y3r3tXb6VaWcCZWlyUXDDdk2B30mN";
                v.ProductName = "copnBLDLVbcaCRZPr3lElqQKbDLRxIkgUGbtPIEwx1egyNanOQzk9";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.NylonTaffeta;
                v.Contents = "lV375WxAB4A";
                v.Spec = "nTiHwMPLKoHwNe3GRDZ3uF9UZQGsQKrydl2tAwECE9YC";
                v.GSM = 67;
                v.Width = 155;
                v.PileLength = 33;
                v.DyeingProcess = "2MfnGbwi2E0P0X3";
                v.KnittingProcess = "WaN6lUOBRI1";
                v.FinishingProcess = "b";
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2023-01-01 16:32:28");
                v.DeliveryDate = DateTime.Parse("2023-06-10 16:32:28");
                v.CustomerId = AddCompany();
                v.OrderNo = "RHljZRHly10dGpldTkmKHV05A4Zr4hqG88ypDWD660KrGNL";
                v.CustomerOrderNo = "2WyWE6DDWZBs";
                //v.Merchandiser = "MpyVk";
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.Light = TEX.Model.Models.LightEnum.CWF;
                v.Light2 = TEX.Model.Models.LightEnum.A;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.GBP;
                v.TotalMeters = 26;
                v.TotalYards = 83;
                v.TotalWeight = 28;
                v.TotalAmount = 34;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "lpbmiGPhrHRvshYkeN9";
                v.AuditedComment = "DbjCl";
                v.Remark = "70KVS";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProductOutboundLot()
        {
            ProductOutboundLot v = new ProductOutboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.OutboundBillId = AddProductOutboundBill();
                v.Color = "pgSzO3sV12OeHL8yUUTt";
                v.ColorCode = "I";
                v.LotNo = "UFIFVc3myiydd";
                v.Pcs = 90;
                v.Weight = 25;
                v.Meters = 92;
                v.Yards = 3;
                v.Remark = "KjsdBrlpnmkX6";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "4olabwfkQbhgvky2eu";
                v.AuditedComment = "zXF";
                context.Set<ProductOutboundLot>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
