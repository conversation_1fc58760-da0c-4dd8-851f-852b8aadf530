using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Producttion.WIPVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class WIPApiTest
    {
        private WIPController _controller;
        private string _seed;

        public WIPApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<WIPController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new WIPSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            WIPVM vm = _controller.Wtm.CreateVM<WIPVM>();
            WIP v = new WIP();
            
            v.BillNo = "T";
            v.CreateDate = DateTime.Parse("2022-11-12 12:52:41");
            v.POrderId = AddPurchaseOrder();
            v.ShipperId = AddCompany();
            v.ReceiverId = AddCompany();
            v.Pcs = 97;
            v.Meters = 20;
            v.Weight = 58;
            v.Procedure = TEX.Model.Models.ProcedureEnum.Coating;
            v.FinishingProcess = "egZyDMNx";
            v.Remark = "FnL6UXa";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "I6Ef";
            v.AuditedComment = "4YPdars4WFL";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<WIP>().Find(v.ID);
                
                Assert.AreEqual(data.BillNo, "T");
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2022-11-12 12:52:41"));
                Assert.AreEqual(data.Pcs, 97);
                Assert.AreEqual(data.Meters, 20);
                Assert.AreEqual(data.Weight, 58);
                Assert.AreEqual(data.Procedure, TEX.Model.Models.ProcedureEnum.Coating);
                Assert.AreEqual(data.FinishingProcess, "egZyDMNx");
                Assert.AreEqual(data.Remark, "FnL6UXa");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "I6Ef");
                Assert.AreEqual(data.AuditedComment, "4YPdars4WFL");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            WIP v = new WIP();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.BillNo = "T";
                v.CreateDate = DateTime.Parse("2022-11-12 12:52:41");
                v.POrderId = AddPurchaseOrder();
                v.ShipperId = AddCompany();
                v.ReceiverId = AddCompany();
                v.Pcs = 97;
                v.Meters = 20;
                v.Weight = 58;
                v.Procedure = TEX.Model.Models.ProcedureEnum.Coating;
                v.FinishingProcess = "egZyDMNx";
                v.Remark = "FnL6UXa";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "I6Ef";
                v.AuditedComment = "4YPdars4WFL";
                context.Set<WIP>().Add(v);
                context.SaveChanges();
            }

            WIPVM vm = _controller.Wtm.CreateVM<WIPVM>();
            var oldID = v.ID;
            v = new WIP();
            v.ID = oldID;
       		
            v.BillNo = "yfSOqldry13WWw";
            v.CreateDate = DateTime.Parse("2022-10-19 12:52:41");
            v.Pcs = 66;
            v.Meters = 19;
            v.Weight = 22;
            v.Procedure = TEX.Model.Models.ProcedureEnum.StickingFilm;
            v.FinishingProcess = "N";
            v.Remark = "UXK9UtPxs7jGp7o";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
            v.AuditedBy = "YPp464";
            v.AuditedComment = "wlkyikNsBUtbGid";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.BillNo", "");
            vm.FC.Add("Entity.CreateDate", "");
            vm.FC.Add("Entity.POrderId", "");
            vm.FC.Add("Entity.ShipperId", "");
            vm.FC.Add("Entity.ReceiverId", "");
            vm.FC.Add("Entity.Pcs", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Procedure", "");
            vm.FC.Add("Entity.FinishingProcess", "");
            vm.FC.Add("Entity.Remark", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.AuditedComment", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<WIP>().Find(v.ID);
 				
                Assert.AreEqual(data.BillNo, "yfSOqldry13WWw");
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2022-10-19 12:52:41"));
                Assert.AreEqual(data.Pcs, 66);
                Assert.AreEqual(data.Meters, 19);
                Assert.AreEqual(data.Weight, 22);
                Assert.AreEqual(data.Procedure, TEX.Model.Models.ProcedureEnum.StickingFilm);
                Assert.AreEqual(data.FinishingProcess, "N");
                Assert.AreEqual(data.Remark, "UXK9UtPxs7jGp7o");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedApproved);
                Assert.AreEqual(data.AuditedBy, "YPp464");
                Assert.AreEqual(data.AuditedComment, "wlkyikNsBUtbGid");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            WIP v = new WIP();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.BillNo = "T";
                v.CreateDate = DateTime.Parse("2022-11-12 12:52:41");
                v.POrderId = AddPurchaseOrder();
                v.ShipperId = AddCompany();
                v.ReceiverId = AddCompany();
                v.Pcs = 97;
                v.Meters = 20;
                v.Weight = 58;
                v.Procedure = TEX.Model.Models.ProcedureEnum.Coating;
                v.FinishingProcess = "egZyDMNx";
                v.Remark = "FnL6UXa";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "I6Ef";
                v.AuditedComment = "4YPdars4WFL";
                context.Set<WIP>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            WIP v1 = new WIP();
            WIP v2 = new WIP();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.BillNo = "T";
                v1.CreateDate = DateTime.Parse("2022-11-12 12:52:41");
                v1.POrderId = AddPurchaseOrder();
                v1.ShipperId = AddCompany();
                v1.ReceiverId = AddCompany();
                v1.Pcs = 97;
                v1.Meters = 20;
                v1.Weight = 58;
                v1.Procedure = TEX.Model.Models.ProcedureEnum.Coating;
                v1.FinishingProcess = "egZyDMNx";
                v1.Remark = "FnL6UXa";
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v1.AuditedBy = "I6Ef";
                v1.AuditedComment = "4YPdars4WFL";
                v2.BillNo = "yfSOqldry13WWw";
                v2.CreateDate = DateTime.Parse("2022-10-19 12:52:41");
                v2.POrderId = v1.POrderId; 
                v2.ShipperId = v1.ShipperId; 
                v2.ReceiverId = v1.ReceiverId; 
                v2.Pcs = 66;
                v2.Meters = 19;
                v2.Weight = 22;
                v2.Procedure = TEX.Model.Models.ProcedureEnum.StickingFilm;
                v2.FinishingProcess = "N";
                v2.Remark = "UXK9UtPxs7jGp7o";
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v2.AuditedBy = "YPp464";
                v2.AuditedComment = "wlkyikNsBUtbGid";
                context.Set<WIP>().Add(v1);
                context.Set<WIP>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<WIP>().Find(v1.ID);
                var data2 = context.Set<WIP>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "Dki9";
                v.CompanyName = "1GxuLtK00Z";
                v.CompanyFullName = "3c1WY";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.KnittingFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "I2JREPxDwUhAA0UqsDG";
                v.Adress = "ZIo";
                v.TaxNO = "54LFrYrQNp5scKDzUcH1exuvMNI";
                v.InvoiceInfo = "lcqn4sAtwA6l";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "t10Ii5hHAmZ4HNtEX1R5mchGd4bTN4mUo2akoAG";
                v.ProductName = "j9n6w2";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Fakecotton;
                v.Contents = "D1mITV3onv2q2j7mgSaVMaD08dHCInpwv";
                v.Spec = "hZIvBeDGHVWTYvaUDWX7tfgEEG1Mz7";
                v.GSM = 44;
                v.Width = 7;
                v.PileLength = 90;
                v.DyeingProcess = "eMXIY";
                v.KnittingProcess = "PFxNiuMNLQjn2Tdk";
                v.FinishingProcess = "9wm6A";
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2022-09-12 12:52:41");
                v.DeliveryDate = DateTime.Parse("2023-06-19 12:52:41");
                v.CustomerId = AddCompany();
                v.OrderNo = "E5QFtnOSxhw48QQ709yNyT4MrFgE9rKp1jql";
                v.CustomerOrderNo = "IxvR6DV07";
                //v.Merchandiser = "nM8";
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.Light = TEX.Model.Models.LightEnum.TL84;
                v.Light2 = TEX.Model.Models.LightEnum.D65;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.Y;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.HKD;
                v.TotalMeters = 0;
                v.TotalYards = 69;
                v.TotalWeight = 47;
                v.TotalAmount = 74;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "tEa1ruStqNX";
                v.AuditedComment = "fpYpfhjZsrofyJ7I9";
                v.Remark = "tM6mD7oELhB89Or";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
