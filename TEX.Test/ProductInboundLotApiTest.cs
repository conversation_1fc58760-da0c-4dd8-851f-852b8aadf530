using Esprima.Ast;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TEX.Controllers;
using TEX.DataAccess;
using TEX.Model.Finished;
using TEX.Model.Models;
using TEX.ViewModel.Finished.ProductInboundBillVMs;
using TEX.ViewModel.Finished.ProductInboundLotVMs;
using WalkingTec.Mvvm.Core;


namespace TEX.Test
{
    [TestClass]
    public class ProductInboundLotApiTest
    {
        private ProductInboundLotController _controller;
        private ProductInboundBillController _billcontroller;
        private string _seed;
        private DataContext context;

        public ProductInboundLotApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            context = new DataContext(_seed, DBTypeEnum.Memory);
            _controller = MockController.CreateApi<ProductInboundLotController>(context, "user");
            _billcontroller = MockController.CreateApi<ProductInboundBillController>(context, "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new ProductInboundLotSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content) == false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ProductInboundLotVM vm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot v = new ProductInboundLot();
            //_controller.Wtm.LoginUserInfo.CurrentTenant = "test";

            var lotId = Guid.NewGuid();
            v.ID = lotId;
            v.InboundBillId = AddProductInboundBill();
            v.OrderDetailId = AddOrderDetail();
            v.Color = "YXFMifnlp37SzfgIJUrXZx";
            v.ColorCode = "XyS6DulX98PCjm267Ml3flQd9td8VVhWOXldxNnFBySti";
            v.LotNo = "S51QHK8HmsIt9dayAh";
            v.Pcs = 2;
            v.Weight = 100;
            v.Meters = 200;
            v.Yards = 180;
            v.Location = "PZ9pdcN5zPR5m26DNkOfngCtqrF8vXnDyuTDBjSYFnPVExE4C";
            v.Remark = "lhWmX";

            v.RollList = new List<ProductInboundRoll>
                {

                    new ProductInboundRoll
                    {
                        ID = Guid.NewGuid(),
                        LotId = lotId,
                        LotNo = "LOT001_UPDATED",
                        RollNo = 1,
                        Meters = 120,
                        Weight = 60,
                        Yards = 108,
                        TenantCode = "test"
                    },
                    new ProductInboundRoll
                    {
                        ID = Guid.NewGuid(),
                        LotId = lotId,
                        LotNo = "LOT001_UPDATED",
                        RollNo = 3,
                        Meters = 80,
                        Weight = 40,
                        Yards = 72,
                        TenantCode = "test"
                    }
                };

            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductInboundLot>().Include(Roll => Roll.RollList).FirstOrDefault(x => x.ID == v.ID);
                var data2 = context.Set<ProductInboundRoll>().ToList();
                var data3 = context.Set<ProductStock>().FirstOrDefault(x => x.OrderDetailId == v.OrderDetailId);

                Assert.AreEqual(data.Color, "YXFMifnlp37SzfgIJUrXZx");
                Assert.AreEqual(data.ColorCode, "XyS6DulX98PCjm267Ml3flQd9td8VVhWOXldxNnFBySti");
                Assert.AreEqual(data.LotNo, "S51QHK8HmsIt9dayAh");
                Assert.AreEqual(data.Pcs, 2);
                Assert.AreEqual(data.Weight, 100);
                Assert.AreEqual(data.Meters, 200);
                Assert.AreEqual(data.Yards, 180);
                Assert.AreEqual(data.Location, "PZ9pdcN5zPR5m26DNkOfngCtqrF8vXnDyuTDBjSYFnPVExE4C");
                Assert.AreEqual(data.Remark, "lhWmX");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);

                Assert.AreEqual(data.RollList.Count, 2);
                Assert.AreEqual(data.RollList[0].LotNo, "LOT001_UPDATED");
                Assert.AreEqual(data.RollList[0].RollNo, 1);
                Assert.AreEqual(data.RollList[0].Meters, 120);
                Assert.AreEqual(data.RollList[0].Weight, 60);
                Assert.AreEqual(data.RollList[0].Yards, 108);
                Assert.AreEqual(data.RollList[1].LotNo, "LOT001_UPDATED");
                Assert.AreEqual(data.RollList[1].RollNo, 3);
                Assert.AreEqual(data.RollList[1].Meters, 80);
                Assert.AreEqual(data.RollList[1].Weight, 40);
                Assert.AreEqual(data.RollList[1].Yards, 72);

                Assert.AreEqual(data3.TotalPcs, 2);
                Assert.AreEqual(data3.TotalWeight, 100);
                Assert.AreEqual(data3.TotalMeters, 200);
                Assert.AreEqual(data3.TotalYards, 180);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            ProductInboundLot v = new ProductInboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v.InboundBillId = AddProductInboundBill();
                v.OrderDetailId = AddOrderDetail();
                v.Color = "YXFMifnlp37SzfgIJUrXZx";
                v.ColorCode = "XyS6DulX98PCjm267Ml3flQd9td8VVhWOXldxNnFBySti";
                v.LotNo = "S51QHK8HmsIt9dayAh";
                v.Pcs = 62;
                v.Weight = 80;
                v.Meters = 75;
                v.Yards = 13;
                v.Location = "PZ9pdcN5zPR5m26DNkOfngCtqrF8vXnDyuTDBjSYFnPVExE4C";
                v.Remark = "lhWmX";
                context.Set<ProductInboundLot>().Add(v);
                context.SaveChanges();
            }

            ProductInboundLotVM vm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            var oldID = v.ID;
            v = new ProductInboundLot();
            v.ID = oldID;

            v.Color = "zb76skNiIewXkSo8T5kV02e35wpbHV8s1r";
            v.ColorCode = "dsLI8zJBRzZYlWw2MKlW";
            v.LotNo = "C";
            v.Pcs = 10;
            v.Weight = 79;
            v.Meters = 48;
            v.Yards = 69;
            v.Location = "F8UWIJPDXI9DQLkT9566iCOWYeXjKPlyDvEoimxjrGk7YKD5c61RLVNTKKc";
            v.Remark = "48ADAW";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();

            vm.FC.Add("Entity.InboundBillId", "");
            vm.FC.Add("Entity.OrderDetailId", "");
            vm.FC.Add("Entity.Color", "");
            vm.FC.Add("Entity.ColorCode", "");
            vm.FC.Add("Entity.LotNo", "");
            vm.FC.Add("Entity.Pcs", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Yards", "");
            vm.FC.Add("Entity.Location", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            //EditVM会自动统计RollList的数量来更新Lot的Pcs、Weight、Meters、Yards字段
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductInboundLot>().Find(v.ID);

                Assert.AreEqual(data.Color, "zb76skNiIewXkSo8T5kV02e35wpbHV8s1r");
                Assert.AreEqual(data.ColorCode, "dsLI8zJBRzZYlWw2MKlW");
                Assert.AreEqual(data.LotNo, "C");
                Assert.AreEqual(data.Pcs, 0);
                Assert.AreEqual(data.Weight, 0);
                Assert.AreEqual(data.Meters, 0);
                Assert.AreEqual(data.Yards, 0);
                Assert.AreEqual(data.Location, "F8UWIJPDXI9DQLkT9566iCOWYeXjKPlyDvEoimxjrGk7YKD5c61RLVNTKKc");
                Assert.AreEqual(data.Remark, "48ADAW");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

        [TestMethod]
        public void GetTest()
        {
            ProductInboundLot v = new ProductInboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v.InboundBillId = AddProductInboundBill();
                v.OrderDetailId = AddOrderDetail();
                v.Color = "YXFMifnlp37SzfgIJUrXZx";
                v.ColorCode = "XyS6DulX98PCjm267Ml3flQd9td8VVhWOXldxNnFBySti";
                v.LotNo = "S51QHK8HmsIt9dayAh";
                v.Pcs = 62;
                v.Weight = 80;
                v.Meters = 75;
                v.Yards = 13;
                v.Location = "PZ9pdcN5zPR5m26DNkOfngCtqrF8vXnDyuTDBjSYFnPVExE4C";
                v.Remark = "lhWmX";
                context.Set<ProductInboundLot>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            ProductInboundLot v1 = new ProductInboundLot();
            ProductInboundLot v2 = new ProductInboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {

                v1.InboundBillId = AddProductInboundBill();
                v1.OrderDetailId = AddOrderDetail();
                v1.Color = "YXFMifnlp37SzfgIJUrXZx";
                v1.ColorCode = "XyS6DulX98PCjm267Ml3flQd9td8VVhWOXldxNnFBySti";
                v1.LotNo = "S51QHK8HmsIt9dayAh";
                v1.Pcs = 62;
                v1.Weight = 80;
                v1.Meters = 75;
                v1.Yards = 13;
                v1.Location = "PZ9pdcN5zPR5m26DNkOfngCtqrF8vXnDyuTDBjSYFnPVExE4C";
                v1.Remark = "lhWmX";
                v2.InboundBillId = v1.InboundBillId;
                v2.OrderDetailId = v1.OrderDetailId;
                v2.Color = "zb76skNiIewXkSo8T5kV02e35wpbHV8s1r";
                v2.ColorCode = "dsLI8zJBRzZYlWw2MKlW";
                v2.LotNo = "C";
                v2.Pcs = 10;
                v2.Weight = 79;
                v2.Meters = 48;
                v2.Yards = 69;
                v2.Location = "F8UWIJPDXI9DQLkT9566iCOWYeXjKPlyDvEoimxjrGk7YKD5c61RLVNTKKc";
                v2.Remark = "48ADAW";
                context.Set<ProductInboundLot>().Add(v1);
                context.Set<ProductInboundLot>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<ProductInboundLot>().Find(v1.ID);
                var data2 = context.Set<ProductInboundLot>().Find(v2.ID);
                Assert.AreEqual(data1, null);
                Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] { });
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        /// <summary>
        /// 测试ProductInboundLotVM.DoAdd方法的两级联动和库存更新功能
        /// 验证：Roll汇总到Lot、库存创建/更新、Bill统计更新
        /// </summary>
        [TestMethod]
        public void DoAddTest_TwoLevelCascadeAndStockUpdate()
        {
            // {{ AURA-X: Add - 测试ProductInboundLotVM.DoAdd方法的两级联动（Lot-Roll）及库存更新功能. Confirmed via 寸止 }}

            ProductInboundLotVM vm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot v = new ProductInboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductInboundBill();
            var orderDetailId = AddOrderDetail();

            v.ID = lotId;
            v.InboundBillId = billId;
            v.OrderDetailId = orderDetailId;
            v.Color = "测试颜色";
            v.ColorCode = "TEST001";
            v.LotNo = "LOT_DOADD_TEST_001";
            v.Location = "A区01号";
            v.Remark = "DoAdd测试";

            // 创建Roll数据，用于测试汇总功能
            v.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_DOADD_TEST_001",
                    RollNo = 1,
                    Meters = 120,
                    Weight = 60,
                    Yards = 108,
                    TenantCode = "test"
                },
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_DOADD_TEST_001",
                    RollNo = 2,
                    Meters = 80,
                    Weight = 40,
                    Yards = 72,
                    TenantCode = "test"
                },
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_DOADD_TEST_001",
                    RollNo = 3,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                }
            };

            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            // 验证DoAdd方法的执行结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 1. 验证Lot数据汇总（基于Roll计算）
                var lotData = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData, "Lot数据应该被正确保存");
                Assert.AreEqual(3, lotData.Pcs, "Pcs应该等于Roll数量：3");
                Assert.AreEqual(300, lotData.Meters, "Meters应该是Roll的总和：120+80+100=300");
                Assert.AreEqual(150, lotData.Weight, "Weight应该是Roll的总和：60+40+50=150");
                Assert.AreEqual(270, lotData.Yards, "Yards应该是Roll的总和：108+72+90=270");

                // 2. 验证Roll数据正确保存
                Assert.AreEqual(3, lotData.RollList.Count, "应该有3个Roll");
                var rollData = lotData.RollList.OrderBy(x => x.RollNo).ToList();
                Assert.AreEqual(1, rollData[0].RollNo);
                Assert.AreEqual(120, rollData[0].Meters);
                Assert.AreEqual(2, rollData[1].RollNo);
                Assert.AreEqual(80, rollData[1].Meters);
                Assert.AreEqual(3, rollData[2].RollNo);
                Assert.AreEqual(100, rollData[2].Meters);

                // 3. 验证库存更新
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(stockData, "库存记录应该被创建或更新");
                Assert.AreEqual(3, stockData.TotalPcs, "库存件数应该增加3");
                Assert.AreEqual(150, stockData.TotalWeight, "库存重量应该增加150");
                Assert.AreEqual(300, stockData.TotalMeters, "库存米数应该增加300");
                Assert.AreEqual(270, stockData.TotalYards, "库存码数应该增加270");


                // 4. 验证Bill统计字段更新
                var billData = context.Set<ProductInboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);

                Assert.IsNotNull(billData, "Bill数据应该存在");
                // Bill的统计应该是所有Lot的汇总
                Assert.AreEqual(billData.LotList.Sum(x => x.Pcs), billData.Pcs, "Bill的Pcs应该是所有Lot的总和");
                Assert.AreEqual(billData.LotList.Sum(x => x.Meters), billData.Meters, "Bill的Meters应该是所有Lot的总和");
                Assert.AreEqual(billData.LotList.Sum(x => x.Weight), billData.Weight, "Bill的Weight应该是所有Lot的总和");
                Assert.AreEqual(billData.LotList.Sum(x => x.Yards), billData.Yards, "Bill的Yards应该是所有Lot的总和");

            }
        }

        /// <summary>
        /// 测试ProductInboundLotVM.DoAdd方法的边界情况
        /// 验证：空Roll列表、库存累加等场景
        /// </summary>
        [TestMethod]
        public void DoAddTest_EdgeCases()
        {
            // {{ AURA-X: Add - 测试ProductInboundLotVM.DoAdd方法的边界情况，包括空Roll列表和库存累加. Confirmed via 寸止 }}

            // 测试场景1：空Roll列表
            ProductInboundLotVM vm1 = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot v1 = new ProductInboundLot();

            var lotId1 = Guid.NewGuid();
            var billId = AddProductInboundBill();
            var orderDetailId = AddOrderDetail();

            v1.ID = lotId1;
            v1.InboundBillId = billId;
            v1.OrderDetailId = orderDetailId;
            v1.Color = "空Roll测试";
            v1.ColorCode = "EMPTY001";
            v1.LotNo = "LOT_EMPTY_ROLL_TEST";
            v1.Location = "A区02号";
            v1.Remark = "空Roll列表测试";
            v1.RollList = new List<ProductInboundRoll>(); // 空列表

            vm1.Entity = v1;
            var rv1 = _controller.Add(vm1);
            Assert.IsInstanceOfType(rv1, typeof(OkObjectResult));

            // 验证空Roll列表的处理
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId1);

                Assert.IsNotNull(lotData);
                Assert.AreEqual(0, lotData.Pcs, "空Roll列表时Pcs应该为0");
                Assert.AreEqual(0, lotData.Meters, "空Roll列表时Meters应该为0");
                Assert.AreEqual(0, lotData.Weight, "空Roll列表时Weight应该为0");
                Assert.AreEqual(0, lotData.Yards, "空Roll列表时Yards应该为0");
                Assert.AreEqual(0, lotData.RollList.Count, "Roll列表应该为空");
            }

            // 测试场景2：库存累加（同一OrderDetail的多次入库）
            ProductInboundLotVM vm2 = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot v2 = new ProductInboundLot();

            var lotId2 = Guid.NewGuid();
            v2.ID = lotId2;
            v2.InboundBillId = billId;
            v2.OrderDetailId = orderDetailId; // 使用相同的OrderDetailId
            v2.Color = "累加测试";
            v2.ColorCode = "ACCUM001";
            v2.LotNo = "LOT_ACCUMULATE_TEST";
            v2.Location = "A区03号";
            v2.Remark = "库存累加测试";

            v2.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId2,
                    LotNo = "LOT_ACCUMULATE_TEST",
                    RollNo = 1,
                    Meters = 50,
                    Weight = 25,
                    Yards = 45,
                    TenantCode = "test"
                }
            };

            vm2.Entity = v2;
            var rv2 = _controller.Add(vm2);
            Assert.IsInstanceOfType(rv2, typeof(OkObjectResult));

            // 验证库存累加
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(stockData);
                // 库存应该是两次入库的累加：第一次0，第二次1件
                Assert.AreEqual(1, stockData.TotalPcs, "库存件数应该累加：0+1=1");
                Assert.AreEqual(25, stockData.TotalWeight, "库存重量应该累加：0+25=25");
                Assert.AreEqual(50, stockData.TotalMeters, "库存米数应该累加：0+50=50");
                Assert.AreEqual(45, stockData.TotalYards, "库存码数应该累加：0+45=45");
            }
        }

        /// <summary>
        /// 测试ProductInboundLotVM.DoEdit方法的两级联动修改功能
        /// 验证：Roll修改汇总到Lot、库存差异更新、Bill统计重新计算
        /// </summary>
        [TestMethod]
        public void DoEditTest_TwoLevelCascadeAndStockDifference()
        {
            // {{ AURA-X: Add - 测试ProductInboundLotVM.DoEdit方法的两级联动修改及库存差异更新功能. Confirmed via 寸止 }}

            // 第一步：创建初始数据
            ProductInboundLotVM createVm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot initialLot = new ProductInboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductInboundBill();
            var orderDetailId = AddOrderDetail();

            initialLot.ID = lotId;
            initialLot.InboundBillId = billId;
            initialLot.OrderDetailId = orderDetailId;
            initialLot.Color = "初始颜色";
            initialLot.ColorCode = "INIT001";
            initialLot.LotNo = "LOT_EDIT_TEST_001";
            initialLot.Location = "B区01号";
            initialLot.Remark = "DoEdit测试初始数据";

            // 创建初始Roll数据
            initialLot.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_EDIT_TEST_001",
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                },
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_EDIT_TEST_001",
                    RollNo = 2,
                    Meters = 80,
                    Weight = 40,
                    Yards = 72,
                    TenantCode = "test"
                }
            };

            createVm.Entity = initialLot;
            var createResult = _controller.Add(createVm);
            Assert.IsInstanceOfType(createResult, typeof(OkObjectResult));

            // 验证初始数据
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);
                Assert.IsNotNull(initialStock);
                Assert.AreEqual(2, initialStock.TotalPcs, "初始库存件数应该为2");
                Assert.AreEqual(90, initialStock.TotalWeight, "初始库存重量应该为90");
                Assert.AreEqual(180, initialStock.TotalMeters, "初始库存米数应该为180");
                Assert.AreEqual(162, initialStock.TotalYards, "初始库存码数应该为162");
            }

            // 第二步：执行DoEdit修改
            ProductInboundLotVM editVm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            editVm.SetEntityById(lotId);

            // 修改Lot基本信息
            editVm.Entity.Color = "修改后颜色";
            editVm.Entity.ColorCode = "EDIT001";
            editVm.Entity.Location = "B区02号";
            editVm.Entity.Remark = "DoEdit测试修改后";

            // 修改Roll数据：删除一个Roll，修改一个Roll，新增一个Roll
            var existingRollIds = initialLot.RollList.Select(x => x.ID).ToList();
            editVm.Entity.RollList = new List<ProductInboundRoll>
            {
                // 保留第一个Roll但修改数据
                new ProductInboundRoll
                {
                    ID = existingRollIds[0],
                    LotId = lotId,
                    LotNo = "LOT_EDIT_TEST_001",
                    RollNo = 1,
                    Meters = 120, // 从100改为120
                    Weight = 60,  // 从50改为60
                    Yards = 108,  // 从90改为108
                    TenantCode = "test"
                },
                // 删除第二个Roll（不包含在新列表中）
                // 新增第三个Roll
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_EDIT_TEST_001",
                    RollNo = 3,
                    Meters = 90,
                    Weight = 45,
                    Yards = 81,
                    TenantCode = "test"
                }
            };

            var editResult = _controller.Edit(editVm);
            Assert.IsInstanceOfType(editResult, typeof(OkObjectResult));

            // 第三步：验证DoEdit结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 1. 验证Lot数据更新（基于新Roll重新计算）
                var updatedLot = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(updatedLot);
                Assert.AreEqual("修改后颜色", updatedLot.Color, "Lot颜色应该被更新");
                Assert.AreEqual("EDIT001", updatedLot.ColorCode, "Lot色号应该被更新");
                Assert.AreEqual("B区02号", updatedLot.Location, "Lot库位应该被更新");
                Assert.AreEqual(2, updatedLot.Pcs, "Lot件数应该重新计算：2个Roll");
                Assert.AreEqual(210, updatedLot.Meters, "Lot米数应该重新计算：120+90=210");
                Assert.AreEqual(105, updatedLot.Weight, "Lot重量应该重新计算：60+45=105");
                Assert.AreEqual(189, updatedLot.Yards, "Lot码数应该重新计算：108+81=189");

                // 2. 验证Roll数据更新
                Assert.AreEqual(2, updatedLot.RollList.Count, "应该有2个Roll");
                var rollData = updatedLot.RollList.OrderBy(x => x.RollNo).ToList();
                Assert.AreEqual(1, rollData[0].RollNo);
                Assert.AreEqual(120, rollData[0].Meters, "第一个Roll的米数应该被更新");
                Assert.AreEqual(3, rollData[1].RollNo);
                Assert.AreEqual(90, rollData[1].Meters, "第三个Roll应该被新增");

                // 3. 验证库存差异更新
                var updatedStock = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(updatedStock);
                // 库存变化：原来2件(90kg,180m,162y) -> 现在2件(105kg,210m,189y)
                // 差异：0件(+15kg,+30m,+27y)
                Assert.AreEqual(2, updatedStock.TotalPcs, "库存件数应该保持2");
                Assert.AreEqual(105, updatedStock.TotalWeight, "库存重量应该更新为105");
                Assert.AreEqual(210, updatedStock.TotalMeters, "库存米数应该更新为210");
                Assert.AreEqual(189, updatedStock.TotalYards, "库存码数应该更新为189");
                Assert.AreEqual("user", updatedStock.UpdateBy, "应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(updatedStock.UpdateTime.Value).Seconds < 10, "更新时间应该是最近的");

                // 4. 验证Bill统计字段重新计算
                var updatedBill = context.Set<ProductInboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);

                Assert.IsNotNull(updatedBill);
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Pcs), updatedBill.Pcs, "Bill的Pcs应该重新计算");
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Meters), updatedBill.Meters, "Bill的Meters应该重新计算");
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Weight), updatedBill.Weight, "Bill的Weight应该重新计算");
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Yards), updatedBill.Yards, "Bill的Yards应该重新计算");
                Assert.AreEqual("user", updatedBill.UpdateBy, "Bill应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(updatedBill.UpdateTime.Value).Seconds < 10, "Bill更新时间应该是最近的");
            }
        }

        /// <summary>
        /// 测试ProductInboundLotVM.DoEdit方法的边界情况
        /// 验证：空Roll列表、null Roll列表、库存负数等场景
        /// </summary>
        [TestMethod]
        public void DoEditTest_EdgeCases()
        {
            // {{ AURA-X: Add - 测试ProductInboundLotVM.DoEdit方法的边界情况，包括空Roll列表和库存负数. Confirmed via 寸止 }}

            // 第一步：创建初始数据
            ProductInboundLotVM createVm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot initialLot = new ProductInboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductInboundBill();
            var orderDetailId = AddOrderDetail();

            initialLot.ID = lotId;
            initialLot.InboundBillId = billId;
            initialLot.OrderDetailId = orderDetailId;
            initialLot.Color = "边界测试";
            initialLot.ColorCode = "EDGE001";
            initialLot.LotNo = "LOT_EDGE_TEST_001";
            initialLot.Location = "C区01号";
            initialLot.Remark = "边界情况测试";

            initialLot.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_EDGE_TEST_001",
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                }
            };

            createVm.Entity = initialLot;
            var createResult = _controller.Add(createVm);
            Assert.IsInstanceOfType(createResult, typeof(OkObjectResult));

            // 测试场景1：清空所有Roll（设置为空列表）
            ProductInboundLotVM editVm1 = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            editVm1.SetEntityById(lotId);
            editVm1.Entity.RollList = new List<ProductInboundRoll>(); // 空列表
            editVm1.Entity.Remark = "清空Roll测试";

            var editResult1 = _controller.Edit(editVm1);
            Assert.IsInstanceOfType(editResult1, typeof(OkObjectResult));

            // 验证清空Roll的结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData);
                Assert.AreEqual(0, lotData.Pcs, "清空Roll后Pcs应该为0");
                Assert.AreEqual(0, lotData.Meters, "清空Roll后Meters应该为0");
                Assert.AreEqual(0, lotData.Weight, "清空Roll后Weight应该为0");
                Assert.AreEqual(0, lotData.Yards, "清空Roll后Yards应该为0");
                Assert.AreEqual(0, lotData.RollList.Count, "Roll列表应该为空");

                // 验证库存更新（应该减少到0或负数）
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);
                Assert.IsNotNull(stockData);
                // 库存变化：原来1件(50kg,100m,90y) -> 现在0件(0kg,0m,0y)
                Assert.AreEqual(0, stockData.TotalPcs, "库存件数应该减少到0");
                Assert.AreEqual(0, stockData.TotalWeight, "库存重量应该减少到0");
                Assert.AreEqual(0, stockData.TotalMeters, "库存米数应该减少到0");
                Assert.AreEqual(0, stockData.TotalYards, "库存码数应该减少到0");
            }

            // 测试场景2：设置Roll列表为null
            ProductInboundLotVM editVm2 = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            editVm2.SetEntityById(lotId);
            editVm2.Entity.RollList = null; // null列表
            editVm2.Entity.Remark = "null Roll测试";

            var editResult2 = _controller.Edit(editVm2);
            Assert.IsInstanceOfType(editResult2, typeof(OkObjectResult));

            // 验证null Roll列表的处理
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData);
                // DoEdit方法会将null转换为空列表
                Assert.AreEqual(0, lotData.Pcs, "null Roll列表时Pcs应该为0");
                Assert.AreEqual(0, lotData.Meters, "null Roll列表时Meters应该为0");
                Assert.AreEqual(0, lotData.Weight, "null Roll列表时Weight应该为0");
                Assert.AreEqual(0, lotData.Yards, "null Roll列表时Yards应该为0");
            }

            // 测试场景3：重新添加Roll数据（从0恢复）
            ProductInboundLotVM editVm3 = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            editVm3.SetEntityById(lotId);
            editVm3.Entity.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_EDGE_TEST_001",
                    RollNo = 1,
                    Meters = 150,
                    Weight = 75,
                    Yards = 135,
                    TenantCode = "test"
                },
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_EDGE_TEST_001",
                    RollNo = 2,
                    Meters = 120,
                    Weight = 60,
                    Yards = 108,
                    TenantCode = "test"
                }
            };
            editVm3.Entity.Remark = "恢复Roll测试";

            var editResult3 = _controller.Edit(editVm3);
            Assert.IsInstanceOfType(editResult3, typeof(OkObjectResult));

            // 验证恢复Roll的结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData);
                Assert.AreEqual(2, lotData.Pcs, "恢复后Pcs应该为2");
                Assert.AreEqual(270, lotData.Meters, "恢复后Meters应该为270");
                Assert.AreEqual(135, lotData.Weight, "恢复后Weight应该为135");
                Assert.AreEqual(243, lotData.Yards, "恢复后Yards应该为243");
                Assert.AreEqual(2, lotData.RollList.Count, "应该有2个Roll");

                // 验证库存恢复
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);
                Assert.IsNotNull(stockData);
                Assert.AreEqual(2, stockData.TotalPcs, "库存件数应该恢复到2");
                Assert.AreEqual(135, stockData.TotalWeight, "库存重量应该恢复到135");
                Assert.AreEqual(270, stockData.TotalMeters, "库存米数应该恢复到270");
                Assert.AreEqual(243, stockData.TotalYards, "库存码数应该恢复到243");
            }
        }

        /// <summary>
        /// 测试ProductInboundLotVM.DoEdit方法的事务处理
        /// 验证：内存数据库兼容性、实体跟踪处理
        /// </summary>
        [TestMethod]
        public void DoEditTest_TransactionHandling()
        {
            // {{ AURA-X: Add - 测试ProductInboundLotVM.DoEdit方法的事务处理和内存数据库兼容性. Confirmed via 寸止 }}

            // 创建测试数据
            ProductInboundLotVM createVm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            ProductInboundLot initialLot = new ProductInboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductInboundBill();
            var orderDetailId = AddOrderDetail();

            initialLot.ID = lotId;
            initialLot.InboundBillId = billId;
            initialLot.OrderDetailId = orderDetailId;
            initialLot.Color = "事务测试";
            initialLot.ColorCode = "TRANS001";
            initialLot.LotNo = "LOT_TRANSACTION_TEST";
            initialLot.Location = "D区01号";
            initialLot.Remark = "事务处理测试";

            initialLot.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_TRANSACTION_TEST",
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                }
            };

            createVm.Entity = initialLot;
            var createResult = _controller.Add(createVm);
            Assert.IsInstanceOfType(createResult, typeof(OkObjectResult));

            // 执行DoEdit操作，测试内存数据库的事务处理
            ProductInboundLotVM editVm = _controller.Wtm.CreateVM<ProductInboundLotVM>();
            editVm.SetEntityById(lotId);
            editVm.Entity.Color = "事务测试修改";
            editVm.Entity.RollList = new List<ProductInboundRoll>
            {
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_TRANSACTION_TEST",
                    RollNo = 1,
                    Meters = 120,
                    Weight = 60,
                    Yards = 108,
                    TenantCode = "test"
                },
                new ProductInboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    LotNo = "LOT_TRANSACTION_TEST",
                    RollNo = 2,
                    Meters = 80,
                    Weight = 40,
                    Yards = 72,
                    TenantCode = "test"
                }
            };

            var editResult = _controller.Edit(editVm);
            Assert.IsInstanceOfType(editResult, typeof(OkObjectResult), "DoEdit操作应该成功完成");

            // 验证事务处理结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 验证数据一致性
                var lotData = context.Set<ProductInboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData, "Lot数据应该存在");
                Assert.AreEqual("事务测试修改", lotData.Color, "Lot颜色应该被更新");
                Assert.AreEqual(2, lotData.RollList.Count, "应该有2个Roll");
                Assert.AreEqual(2, lotData.Pcs, "Lot统计应该正确");
                Assert.AreEqual(200, lotData.Meters, "Lot米数应该正确");
                Assert.AreEqual(100, lotData.Weight, "Lot重量应该正确");
                Assert.AreEqual(180, lotData.Yards, "Lot码数应该正确");

                // 验证库存数据一致性
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(stockData, "库存数据应该存在");
                Assert.AreEqual(2, stockData.TotalPcs, "库存件数应该正确");
                Assert.AreEqual(100, stockData.TotalWeight, "库存重量应该正确");
                Assert.AreEqual(200, stockData.TotalMeters, "库存米数应该正确");
                Assert.AreEqual(180, stockData.TotalYards, "库存码数应该正确");

                // 验证Bill数据一致性
                var billData = context.Set<ProductInboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);

                Assert.IsNotNull(billData, "Bill数据应该存在");
                Assert.AreEqual(billData.LotList.Sum(x => x.Pcs), billData.Pcs, "Bill统计应该与Lot汇总一致");
                Assert.AreEqual(billData.LotList.Sum(x => x.Meters), billData.Meters, "Bill米数应该与Lot汇总一致");
                Assert.AreEqual(billData.LotList.Sum(x => x.Weight), billData.Weight, "Bill重量应该与Lot汇总一致");
                Assert.AreEqual(billData.LotList.Sum(x => x.Yards), billData.Yards, "Bill码数应该与Lot汇总一致");
            }
        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {

                    v.CompanyCode = "MrDU1O";
                    v.CompanyName = "FpDIyoSWPMwRaMl4Gfx0I93mCFhOiojvwLgkmlFPvzu26OivDUy10fD1e1b5dR";
                    v.CompanyFullName = "ZAgsAodXETBXkYWUGew5JP44azrUU5csCJ9ZHkXBzF3DPgfkE53";
                    v.CompanyType = TEX.Model.Models.CompanyTypeEnum.KnittingFactory;
                    v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                    v.ContactPhone = "agb6U0sf9XwW";
                    v.Adress = "2BZ2dOQ6LZgO4Bl";
                    v.TaxNO = "1qJ";
                    v.InvoiceInfo = "qlk";
                    v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                    context.Set<Company>().Add(v);
                    context.SaveChanges();
                }
                catch { }
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {

                    v.ContactName = "T";
                    v.AffiliationCompanyId = AddCompany();
                    v.PositionTitle = "C6AjLGSWHTG3MX";
                    v.MobilePhone = "70lzsxkBbfZpddTWC";
                    v.Address = "0nCCt4kodEMvlYD";
                    v.Phone = "fS";
                    v.Email = "LiZ1KbA5x49w2i";
                    v.WeChat = "ViaJyjlcvrvHQMo";
                    v.QQ = "IAaoB";
                    v.Fax = "2HFbwErH";
                    v.Remark = "swB";
                    v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                    context.Set<Contact>().Add(v);
                    context.SaveChanges();
                }
                catch { }
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {

                    v.ProductCode = "MtyvJSm1HZX";
                    v.ProductName = "7BnKUa";
                    //v.Category = TEX.Model.Models.FabricCategoryEnum.Taslan;
                    v.Contents = "Qk7vCSFUGUrAB9Q2oXuwpsl0Fx21nRghTJRRQPqtHLNR8r6c9TGQEs5juYgPa";
                    v.Spec = "mCoa5AOl1XOcHyWT3LzrDneiEuM94wtxI7FpfrZICTqJ49rsn8j9jascbiY1rLuxPxj9mvqf4FR798HfVWAzs6Wq";
                    v.GSM = 2;
                    v.Width = 100;
                    v.DyeingProductName = "FHj47kDJs2bSL67g2NAlCHCLEOb8fuFWA3vAZIaJvtjRzuKgC2PC6d";
                    v.PileLength = 26;
                    v.DyeingProcess = "L3LpQY";
                    v.KnittingProcess = "vWkAaT";
                    v.FinishingProcess = "odksoEvtnmvT6O1m";
                    v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                    context.Set<Product>().Add(v);
                    context.SaveChanges();
                }
                catch { }
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            OrderDetail v1 = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {
                    v.CreateDate = DateTime.Parse("2025-05-10 18:47:19");
                    v.DeliveryDate = DateTime.Parse("2024-09-16 18:47:19");
                    v.CustomerId = AddCompany();
                    v.OrderNo = "HpztUZHDbsWra8xKdSXyCXQk1hMBo5PFbrvuaoCmJD3vEbK473xyFVLCVi";
                    v.CustomerOrderNo = "a6gYi3ONIBg9lL6Gu3TPb5X5WDVSnbyTL6LkHubRC0EF9AGcGVf6DX4MFmssdJ";
                    v.MerchandiserId = AddContact();
                    v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                    v.ProductId = AddProduct();
                    v.DyeingProductName = "Cl";
                    v.Light = TEX.Model.Models.LightEnum.D65_LED;
                    v.Light2 = TEX.Model.Models.LightEnum.D65;
                    v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                    v.PriceUnit = TEX.Model.Models.CurrencyEnum.HKD;
                    v.TotalMeters = 33;
                    v.TotalYards = 41;
                    v.TotalWeight = 43;
                    v.TotalAmount = 32;
                    v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                    v.AuditedBy = "a3R";
                    v.AuditedComment = "ysjNlex8OFQD5bP2F";
                    v.Remark = "ysQ02";
                    context.Set<PurchaseOrder>().Add(v);


                    v1.PurchaseOrderId = v.ID;
                    v1.Color = "8BtEnuLGYU084sVpwpS";
                    v1.EngColor = "eswlPttmHOIsCPqSuj8BDH44";
                    v1.ColorCode = "lXZJAN7oh6ZBujT7f";
                    v1.Meters = 76;
                    v1.KG = 68;
                    v1.Yards = 53;
                    v1.Price = 51;
                    v1.Amount = 48;
                    v1.Remark = "f4ApfVEn";
                    context.Set<OrderDetail>().Add(v1);
                    context.SaveChanges();
                }
                catch { }
            }
            return v.ID;
        }
        private Guid AddOrderDetail()
        {
            OrderDetail v = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                if(context.Set<OrderDetail>().Count() == 0)
                {
                    AddPurchaseOrder();
                }
                return context.Set<OrderDetail>().FirstOrDefault()!.ID; ;
            }
        }
        private Guid AddProductInboundBill()
        {
            ProductInboundBill v = new ProductInboundBill();

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {

                    v.CreateDate = DateTime.Parse("2025-01-09 18:47:19");
                    v.BillNo = "HhOs59mIOCKm482dnB";
                    v.POrderId = AddPurchaseOrder();
                    v.FinishingFactoryId = AddCompany();
                    v.Warehouse = "QzS0HBuwYaFsTnDj8sHEcBvuheYfZIPLCNnXmWeeFWoUhxQC";
                    v.Pcs = 22;
                    v.Weight = 52;
                    v.Meters = 82;
                    v.Yards = 24;
                    v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                    v.AuditedBy = "ihpN2a";
                    v.AuditedComment = "YA";
                    v.Remark = "C0cIYw4XF";
                    context.Set<ProductInboundBill>().Add(v);
                    context.SaveChanges();


                }
                catch { }
            }
            return v.ID;
        }

       



    }
}
