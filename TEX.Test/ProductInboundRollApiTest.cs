using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Finished.ProductInboundRollVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class ProductInboundRollApiTest
    {
        private ProductInboundRollController _controller;
        private string _seed;

        public ProductInboundRollApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<ProductInboundRollController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new ProductInboundRollSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ProductInboundRollVM vm = _controller.Wtm.CreateVM<ProductInboundRollVM>();
            ProductInboundRoll v = new ProductInboundRoll();
            
            v.LotId = AddProductInboundLot();
            v.RollNo = 38;
            v.Weight = 28;
            v.Meters = 34;
            v.Yards = 42;
            v.Grade = "Yz1CBQluX";
            v.Remark = "UPNtcqDXErUCcWfaY";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductInboundRoll>().Find(v.ID);
                
                Assert.AreEqual(data.RollNo, 38);
                Assert.AreEqual(data.Weight, 28);
                Assert.AreEqual(data.Meters, 34);
                Assert.AreEqual(data.Yards, 42);
                Assert.AreEqual(data.Grade, "Yz1CBQluX");
                Assert.AreEqual(data.Remark, "UPNtcqDXErUCcWfaY");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            ProductInboundRoll v = new ProductInboundRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.LotId = AddProductInboundLot();
                v.RollNo = 38;
                v.Weight = 28;
                v.Meters = 34;
                v.Yards = 42;
                v.Grade = "Yz1CBQluX";
                v.Remark = "UPNtcqDXErUCcWfaY";
                context.Set<ProductInboundRoll>().Add(v);
                context.SaveChanges();
            }

            ProductInboundRollVM vm = _controller.Wtm.CreateVM<ProductInboundRollVM>();
            var oldID = v.ID;
            v = new ProductInboundRoll();
            v.ID = oldID;
       		
            v.RollNo = 50;
            v.Weight = 3;
            v.Meters = 45;
            v.Yards = 68;
            v.Grade = "Ij7cHmAJySBL";
            v.Remark = "P";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.LotId", "");
            vm.FC.Add("Entity.RollNo", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Yards", "");
            vm.FC.Add("Entity.Grade", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductInboundRoll>().Find(v.ID);
 				
                Assert.AreEqual(data.RollNo, 50);
                Assert.AreEqual(data.Weight, 3);
                Assert.AreEqual(data.Meters, 45);
                Assert.AreEqual(data.Yards, 68);
                Assert.AreEqual(data.Grade, "Ij7cHmAJySBL");
                Assert.AreEqual(data.Remark, "P");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            ProductInboundRoll v = new ProductInboundRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.LotId = AddProductInboundLot();
                v.RollNo = 38;
                v.Weight = 28;
                v.Meters = 34;
                v.Yards = 42;
                v.Grade = "Yz1CBQluX";
                v.Remark = "UPNtcqDXErUCcWfaY";
                context.Set<ProductInboundRoll>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            ProductInboundRoll v1 = new ProductInboundRoll();
            ProductInboundRoll v2 = new ProductInboundRoll();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.LotId = AddProductInboundLot();
                v1.RollNo = 38;
                v1.Weight = 28;
                v1.Meters = 34;
                v1.Yards = 42;
                v1.Grade = "Yz1CBQluX";
                v1.Remark = "UPNtcqDXErUCcWfaY";
                v2.LotId = v1.LotId; 
                v2.RollNo = 50;
                v2.Weight = 3;
                v2.Meters = 45;
                v2.Yards = 68;
                v2.Grade = "Ij7cHmAJySBL";
                v2.Remark = "P";
                context.Set<ProductInboundRoll>().Add(v1);
                context.Set<ProductInboundRoll>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<ProductInboundRoll>().Find(v1.ID);
                var data2 = context.Set<ProductInboundRoll>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "w1QbSYdD7yqkxYPWS87mFS2wqf73SjW";
                v.CompanyName = "Plr1WQY6JolpxJZ033HxYgsMTeBpfIwZNXuec8KznehfXnzSUJoamGN";
                v.CompanyFullName = "6E5k";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.TradingCompany;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Customer;
                v.ContactPhone = "3";
                v.Adress = "e2YbnR";
                v.TaxNO = "YBD6lVAf1gswnj82Wb5ZqD8lXDgxdP83vnBy";
                v.InvoiceInfo = "MioEkgM";
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "z0brzUbnFIDm6OikEDUPRuhaocPOw";
                v.ProductName = "Nv5mExMl8me2OVVAoLQgPhdikOfdp";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.Satin;
                v.Contents = "qGzBHaZjSuqCVZsyvxvnyCPmDu4Iw7xtKghGfVBTWvJwYrlrHN5cowh9KgD";
                v.Spec = "NRNOpRr4DEUjAlRbb9Dfwzlkq2g4oBhhtqN7Gl0FZ6V1YUTwcjrwbx5goEKRgjKU0pRNeQXRsCItSVAP9BQh0L4mHmaNCdtiszOxkDIOttHbrc";
                v.GSM = 47;
                v.Width = 255;
                v.PileLength = 40;
                v.DyeingProcess = "enRmwFuOwqgehqbX";
                v.KnittingProcess = "Hv2FLkc";
                v.FinishingProcess = "x";
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2024-05-27 14:30:52");
                v.DeliveryDate = DateTime.Parse("2022-07-26 14:30:52");
                v.CustomerId = AddCompany();
                v.OrderNo = "BeO6z9cFT5ZEkK247STAV2rpVqQk2pYFGxRtXGi4bP54Rz5Swf0eVTO7kl";
                v.CustomerOrderNo = "RVgedjvgX7xYX";
                //v.Merchandiser = "uzWA3tPWUeLQtl";
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Greige;
                v.ProductId = AddProduct();
                v.Light = TEX.Model.Models.LightEnum.CWF;
                v.Light2 = TEX.Model.Models.LightEnum.TL84;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.HKD;
                v.TotalMeters = 62;
                v.TotalYards = 68;
                v.TotalWeight = 22;
                v.TotalAmount = 86;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "b";
                v.AuditedComment = "i";
                v.Remark = "3UlhYL1VQB0ghwo4od";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProductInboundBill()
        {
            ProductInboundBill v = new ProductInboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2022-09-03 14:30:52");
                //v.BillNo = 59;
                v.FinishingFactoryId = AddCompany();

                v.Pcs = 49;
                v.Weight = 64;
                v.Meters = 13;
                v.Yards = 28;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                v.AuditedBy = "qJQiE1UHtPQK8CS";
                v.AuditedComment = "fW";
                v.Remark = "QTkiV8KmE3s";
                context.Set<ProductInboundBill>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProductInboundLot()
        {
            ProductInboundLot v = new ProductInboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.InboundBillId = AddProductInboundBill();

                v.Color = "KS4q8MgjY";
                v.ColorCode = "jwdoWFpRA";
                v.LotNo = "fjJacADMEJJ7oE";
                v.Pcs = 89;
                v.Weight = 94;
                v.Meters = 57;
                v.Yards = 64;
                v.Location = "u2uf9E80Q2abC9fB7fI04mwo5HepMssEsgZz3rxWbZ";
 
                v.Remark = "18P1T642IVEYGEn";
                context.Set<ProductInboundLot>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
