using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Finished.ProductOutboundLotVMs;
using TEX.Model.Models;
using TEX.DataAccess;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TEX.Model.Finished;


namespace TEX.Test
{
    [TestClass]
    public class ProductOutboundLotApiTest
    {
        private ProductOutboundLotController _controller;
        private string _seed;

        public ProductOutboundLotApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<ProductOutboundLotController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new ProductOutboundLotSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            ProductOutboundLotVM vm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot v = new ProductOutboundLot();
            
            v.OutboundBillId = AddProductOutboundBill();
            v.OrderDetailId = AddOrderDetail();
            v.Color = "jei6O5tvtHSgxCof9lANXZtioOImvx9WT";
            v.ColorCode = "JY9";
            v.LotNo = "jRaqfV";
            v.Pcs = 1;
            v.Weight = 95;
            v.Meters = 85;
            v.Yards = 97;
            v.Remark = "QUy";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
            v.AuditedBy = "tlKezSMvizH1WLP";
            v.AuditedComment = "oGQQf";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductOutboundLot>().Find(v.ID);
                
                Assert.AreEqual(data.Color, "jei6O5tvtHSgxCof9lANXZtioOImvx9WT");
                Assert.AreEqual(data.ColorCode, "JY9");
                Assert.AreEqual(data.LotNo, "jRaqfV");
                Assert.AreEqual(data.Pcs, 0);//没有RollList,所以为0,默认会根据RollList来统计数据
                Assert.AreEqual(data.Weight, 0);
                Assert.AreEqual(data.Meters, 0);
                Assert.AreEqual(data.Yards, 0);
                Assert.AreEqual(data.Remark, "QUy");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedFailed);
                Assert.AreEqual(data.AuditedBy, "tlKezSMvizH1WLP");
                Assert.AreEqual(data.AuditedComment, "oGQQf");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            ProductOutboundLot v = new ProductOutboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.OutboundBillId = AddProductOutboundBill();
                v.OrderDetailId = AddOrderDetail();
                v.Color = "jei6O5tvtHSgxCof9lANXZtioOImvx9WT";
                v.ColorCode = "JY9";
                v.LotNo = "jRaqfV";
                v.Pcs = 1;
                v.Weight = 95;
                v.Meters = 85;
                v.Yards = 97;
                v.Remark = "QUy";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "tlKezSMvizH1WLP";
                v.AuditedComment = "oGQQf";
                context.Set<ProductOutboundLot>().Add(v);
                context.SaveChanges();
            }

            ProductOutboundLotVM vm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            var oldID = v.ID;
            v = new ProductOutboundLot();
            v.ID = oldID;
       		
            v.Color = "7ADZquXxHWmL5mYndUKi3emvQo0JbhctS";
            v.ColorCode = "QhcNwW9oHOOIQM7laxcSfXwXX850DNGaSKNM6D5a";
            v.LotNo = "U6TW0sxfzKFus";
            v.Pcs = 60;
            v.Weight = 98;
            v.Meters = 10;
            v.Yards = 94;
            v.Remark = "nQJVWRlMKUw";
            v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
            v.AuditedBy = "EYNqNl0";
            v.AuditedComment = "W";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.OutboundBillId", "");
            vm.FC.Add("Entity.OrderDetailId", "");
            vm.FC.Add("Entity.Color", "");
            vm.FC.Add("Entity.ColorCode", "");
            vm.FC.Add("Entity.LotNo", "");
            vm.FC.Add("Entity.Pcs", "");
            vm.FC.Add("Entity.Weight", "");
            vm.FC.Add("Entity.Meters", "");
            vm.FC.Add("Entity.Yards", "");
            vm.FC.Add("Entity.Remark", "");
            vm.FC.Add("Entity.AuditStatus", "");
            vm.FC.Add("Entity.AuditedBy", "");
            vm.FC.Add("Entity.AuditedComment", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<ProductOutboundLot>().Find(v.ID);
 				
                Assert.AreEqual(data.Color, "7ADZquXxHWmL5mYndUKi3emvQo0JbhctS");
                Assert.AreEqual(data.ColorCode, "QhcNwW9oHOOIQM7laxcSfXwXX850DNGaSKNM6D5a");
                Assert.AreEqual(data.LotNo, "U6TW0sxfzKFus");
                Assert.AreEqual(data.Pcs, 0);//会自动根据RollList统计,RollList为空则为0
                Assert.AreEqual(data.Weight, 0);
                Assert.AreEqual(data.Meters, 0);
                Assert.AreEqual(data.Yards, 0);
                Assert.AreEqual(data.Remark, "nQJVWRlMKUw");
                Assert.AreEqual(data.AuditStatus, TEX.Model.Models.AuditStatusEnum.AuditedFailed);
                //Assert.AreEqual(data.AuditedBy, "EYNqNl0");
                //Assert.AreEqual(data.AuditedComment, "W");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            ProductOutboundLot v = new ProductOutboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.OutboundBillId = AddProductOutboundBill();
                v.OrderDetailId = AddOrderDetail();
                v.Color = "jei6O5tvtHSgxCof9lANXZtioOImvx9WT";
                v.ColorCode = "JY9";
                v.LotNo = "jRaqfV";
                v.Pcs = 1;
                v.Weight = 95;
                v.Meters = 85;
                v.Yards = 97;
                v.Remark = "QUy";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v.AuditedBy = "tlKezSMvizH1WLP";
                v.AuditedComment = "oGQQf";
                context.Set<ProductOutboundLot>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            ProductOutboundLot v1 = new ProductOutboundLot();
            ProductOutboundLot v2 = new ProductOutboundLot();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.OutboundBillId = AddProductOutboundBill();
                v1.OrderDetailId = AddOrderDetail();
                v1.Color = "jei6O5tvtHSgxCof9lANXZtioOImvx9WT";
                v1.ColorCode = "JY9";
                v1.LotNo = "jRaqfV";
                v1.Pcs = 1;
                v1.Weight = 95;
                v1.Meters = 85;
                v1.Yards = 97;
                v1.Remark = "QUy";
                v1.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v1.AuditedBy = "tlKezSMvizH1WLP";
                v1.AuditedComment = "oGQQf";
                v2.OutboundBillId = v1.OutboundBillId; 
                v2.OrderDetailId = v1.OrderDetailId; 
                v2.Color = "7ADZquXxHWmL5mYndUKi3emvQo0JbhctS";
                v2.ColorCode = "QhcNwW9oHOOIQM7laxcSfXwXX850DNGaSKNM6D5a";
                v2.LotNo = "U6TW0sxfzKFus";
                v2.Pcs = 60;
                v2.Weight = 98;
                v2.Meters = 10;
                v2.Yards = 94;
                v2.Remark = "nQJVWRlMKUw";
                v2.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                v2.AuditedBy = "EYNqNl0";
                v2.AuditedComment = "W";
                context.Set<ProductOutboundLot>().Add(v1);
                context.Set<ProductOutboundLot>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<ProductOutboundLot>().Find(v1.ID);
                var data2 = context.Set<ProductOutboundLot>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        /// <summary>
        /// 测试ProductOutboundLotBatchVM.DoBatchDelete方法的完整功能
        /// 验证：Lot软删除、Roll软删除、库存恢复、Bill统计更新
        /// </summary>
        [TestMethod]
        public void DoBatchDeleteTest_CompleteFunction()
        {
            // {{ AURA-X: Modify - 修复测试方式，使用VM的Add方法创建出库数据，而不是手动更新库存. Confirmed via 寸止 }}

            // 第一步：创建初始库存
            var orderDetailId1 = AddOrderDetail();
            var orderDetailId2 = AddOrderDetail();

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 创建初始库存
                var initialStock1 = new ProductStock
                {
                    OrderDetailId = orderDetailId1,
                    TotalPcs = 5,
                    TotalWeight = 250,
                    TotalMeters = 500,
                    TotalYards = 450,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                var initialStock2 = new ProductStock
                {
                    OrderDetailId = orderDetailId2,
                    TotalPcs = 8,
                    TotalWeight = 400,
                    TotalMeters = 800,
                    TotalYards = 720,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(initialStock1);
                context.Set<ProductStock>().Add(initialStock2);
                context.SaveChanges();

                var stock1 = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId1);
                Assert.AreEqual(stock1.TotalPcs, 5);
                Assert.AreEqual(stock1.TotalWeight, 250);
                Assert.AreEqual(stock1.TotalMeters, 500);
            }

            // 第二步：使用VM的Add方法创建出库数据（这样库存会自动同步更新）
            var billId = AddProductOutboundBill();
            var lot1Id = Guid.NewGuid();
            var lot2Id = Guid.NewGuid();
            var lot3Id = Guid.NewGuid();

            // 创建第一个Lot及其Roll（使用VM方法）
            ProductOutboundLotVM vm1 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot lot1 = new ProductOutboundLot
            {
                ID = lot1Id,
                OutboundBillId = billId,
                OrderDetailId = orderDetailId1,
                Color = "批量删除测试1",
                ColorCode = "BATCH001",
                LotNo = "LOT_BATCH_DELETE_001",
                Remark = "批量删除测试Lot1",
                RollList = new List<ProductOutboundRoll>
                {
                    new ProductOutboundRoll
                    {
                        ID = Guid.NewGuid(),
                        LotId = lot1Id,
                        RollNo = 1,
                        Meters = 120,
                        Weight = 60,
                        Yards = 108,
                        TenantCode = "test"
                    },
                    new ProductOutboundRoll
                    {
                        ID = Guid.NewGuid(),
                        LotId = lot1Id,
                        RollNo = 2,
                        Meters = 80,
                        Weight = 40,
                        Yards = 72,
                        TenantCode = "test"
                    }
                }
            };

            vm1.Entity = lot1;
            var rv1 = _controller.Add(vm1);
            Assert.IsInstanceOfType(rv1, typeof(OkObjectResult), "创建Lot1应该成功");

            // 创建第二个Lot及其Roll（使用VM方法）
            ProductOutboundLotVM vm2 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot lot2 = new ProductOutboundLot
            {
                ID = lot2Id,
                OutboundBillId = billId,
                OrderDetailId = orderDetailId1, // 同一个OrderDetail
                Color = "批量删除测试2",
                ColorCode = "BATCH002",
                LotNo = "LOT_BATCH_DELETE_002",
                Remark = "批量删除测试Lot2",
                RollList = new List<ProductOutboundRoll>
                {
                    new ProductOutboundRoll
                    {
                        ID = Guid.NewGuid(),
                        LotId = lot2Id,
                        RollNo = 1,
                        Meters = 100,
                        Weight = 50,
                        Yards = 90,
                        TenantCode = "test"
                    }
                }
            };

            vm2.Entity = lot2;
            var rv2 = _controller.Add(vm2);
            Assert.IsInstanceOfType(rv2, typeof(OkObjectResult), "创建Lot2应该成功");

            // 验证出库后的库存状态（VM自动更新的库存）
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var stock1 = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId1);
                var stock2 = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId2);

                // 库存1：原5件 - lot1(2件) - lot2(1件) = 2件
                // 重量：原250 - lot1(60+40=100) - lot2(50) = 100
                // 米数：原500 - lot1(120+80=200) - lot2(100) = 200
                // 码数：原450 - lot1(108+72=180) - lot2(90) = 180
                Assert.AreEqual(2, stock1.TotalPcs, "出库后库存1件数：5-2-1=2");
                Assert.AreEqual(100, stock1.TotalWeight, "出库后库存1重量：250-100-50=100");
                Assert.AreEqual(200, stock1.TotalMeters, "出库后库存1米数：500-200-100=200");
                Assert.AreEqual(180, stock1.TotalYards, "出库后库存1码数：450-180-90=180");

            }

            // 创建第3个Lot及其Roll（使用VM方法）
            ProductOutboundLotVM vm3 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot lot3 = new ProductOutboundLot
            {
                ID = lot3Id,
                OutboundBillId = billId,
                OrderDetailId = orderDetailId1, // 同一个OrderDetail
                Color = "批量删除测试2",
                ColorCode = "BATCH002",
                LotNo = "LOT_BATCH_DELETE_002",
                Remark = "批量删除测试Lot2",
                RollList = new List<ProductOutboundRoll>
                {
                    new ProductOutboundRoll
                    {
                        ID = Guid.NewGuid(),
                        LotId = lot3Id,
                        RollNo = 1,
                        Meters = 100,
                        Weight = 50,
                        Yards = 90,
                        TenantCode = "test"
                    }
                }
            };

            vm2.Entity = lot3;
            var rv3 = _controller.Add(vm2);



            // 第三步：执行批量删除（删除lot1和lot3）
            var rv = _controller.BatchDelete(new string[] { lot1Id.ToString(), lot3Id.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult), "批量删除应该成功");

            // 第四步：验证删除结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 1. 验证Lot软删除
                var deletedLot1 = context.Set<ProductOutboundLot>().IgnoreQueryFilters().FirstOrDefault(x => x.ID == lot1Id);
                var remainingLot2 = context.Set<ProductOutboundLot>().IgnoreQueryFilters().FirstOrDefault(x => x.ID == lot2Id);


                Assert.IsNotNull(deletedLot1, "Lot1应该存在（软删除）");
                Assert.IsFalse(deletedLot1.IsValid, "Lot1应该被软删除");
                Assert.AreEqual("user", deletedLot1.UpdateBy, "Lot1应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(deletedLot1.UpdateTime.Value).Seconds < 10, "Lot1更新时间应该是最近的");

                Assert.IsNotNull(remainingLot2, "Lot2应该存在（未删除）");
                Assert.IsTrue(remainingLot2.IsValid, "Lot2应该仍然有效");


                // 2. 验证Roll软删除
                var rolls = context.Set<ProductOutboundRoll>().IgnoreQueryFilters()
                    .Where(x => x.LotId == lot1Id || x.LotId == lot3Id)
                    .ToList();

                Assert.AreEqual(3, rolls.Count, "应该有3个Roll（lot1的2个 + lot3的1个）");
                Assert.IsTrue(rolls.All(x => !x.IsValid), "所有相关Roll都应该被软删除");
                Assert.IsTrue(rolls.All(x => x.UpdateBy == "user"), "所有Roll都应该记录更新人");
                Assert.IsTrue(rolls.All(x => DateTime.Now.Subtract(x.UpdateTime.Value).Seconds < 10), "所有Roll更新时间应该是最近的");

                // 验证lot2的Roll未被删除
                var lot2Rolls = context.Set<ProductOutboundRoll>().IgnoreQueryFilters()
                    .Where(x => x.LotId == lot2Id)
                    .ToList();
                Assert.AreEqual(1, lot2Rolls.Count, "Lot2应该有1个Roll");
                Assert.IsTrue(lot2Rolls.All(x => x.IsValid), "Lot2的Roll应该仍然有效");

                // 3. 验证库存恢复
                var stock1 = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId1);

                // 库存1恢复：删除了lot1(2件,100kg,200m,180y)，保留lot2(1件,50kg,100m,90y)
                // 删除前库存：2件,100kg,200m,180y
                // 删除lot1恢复：+2件,+100kg,+200m,+180y
                // 恢复后库存：4件,200kg,400m,360y
                Assert.AreEqual(4, stock1.TotalPcs, "库存1件数恢复：2+2=4");
                Assert.AreEqual(200, stock1.TotalWeight, "库存1重量恢复：100+100=200");
                Assert.AreEqual(400, stock1.TotalMeters, "库存1米数恢复：200+200=400");
                Assert.AreEqual(360, stock1.TotalYards, "库存1码数恢复：180+180=360");

                Assert.AreEqual("user", stock1.UpdateBy, "库存1应该记录更新人");

                Assert.IsTrue(DateTime.Now.Subtract(stock1.UpdateTime.Value).Seconds < 10, "库存1更新时间应该是最近的");

                // 4. 验证Bill统计字段更新
                var bill = context.Set<ProductOutboundBill>()
                    .Include(x => x.LotList)
                    .First(x => x.ID == billId);

                // Bill统计应该只包含有效的Lot（lot2）
                var validLots = bill.LotList.Where(x => x.IsValid).ToList();
                Assert.AreEqual(1, validLots.Count, "Bill应该只有1个有效Lot");
                Assert.AreEqual(validLots.Sum(x => x.Pcs), bill.Pcs, "Bill的Pcs应该等于有效Lot的总和");
                Assert.AreEqual(validLots.Sum(x => x.Meters), bill.Meters, "Bill的Meters应该等于有效Lot的总和");
                Assert.AreEqual(validLots.Sum(x => x.Weight), bill.Weight, "Bill的Weight应该等于有效Lot的总和");
                Assert.AreEqual(validLots.Sum(x => x.Yards), bill.Yards, "Bill的Yards应该等于有效Lot的总和");
                Assert.AreEqual("user", bill.UpdateBy, "Bill应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(bill.UpdateTime.Value).Seconds < 10, "Bill更新时间应该是最近的");
            }
        }

        /// <summary>
        /// 测试ProductOutboundLotBatchVM.DoBatchDelete方法的边界情况
        /// 验证：空ID列表、不存在的Lot、已删除的Lot等场景
        /// </summary>
        [TestMethod]
        public void DoBatchDeleteTest_EdgeCases()
        {
            // {{ AURA-X: Add - 测试ProductOutboundLotBatchVM.DoBatchDelete方法的边界情况. Confirmed via 寸止 }}

            // 测试场景1：空ID列表
            var rv1 = _controller.BatchDelete(new string[] { });
            Assert.IsInstanceOfType(rv1, typeof(OkResult), "空ID列表应该返回成功");

            // 测试场景2：不存在的Lot ID
            var nonExistentId = Guid.NewGuid();
            var rv2 = _controller.BatchDelete(new string[] { nonExistentId.ToString() });
            Assert.IsInstanceOfType(rv2, typeof(OkObjectResult), "不存在的ID应该返回成功（无操作）");

            // 测试场景3：已经被软删除的Lot
            var billId = AddProductOutboundBill();
            var orderDetailId = AddOrderDetail();
            var lotId = Guid.NewGuid();

            // 创建初始库存
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = new ProductStock
                {
                    OrderDetailId = orderDetailId,
                    TotalPcs = 5,
                    TotalWeight = 250,
                    TotalMeters = 500,
                    TotalYards = 450,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(initialStock);
                context.SaveChanges();
            }

            // 创建已软删除的Lot
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var deletedLot = new ProductOutboundLot
                {
                    ID = lotId,
                    OutboundBillId = billId,
                    OrderDetailId = orderDetailId,
                    Color = "已删除测试",
                    ColorCode = "DELETED001",
                    LotNo = "LOT_DELETED_TEST",
                    Pcs = 1,
                    Weight = 50,
                    Meters = 100,
                    Yards = 90,
                    Remark = "已删除的Lot",
                    IsValid = false, // 已经被软删除
                    CreateTime = DateTime.Now,
                    CreateBy = "user"
                };

                var deletedRoll = new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    IsValid = false, // 已经被软删除
                    CreateTime = DateTime.Now,
                    CreateBy = "user",
                    TenantCode = "test"
                };

                context.Set<ProductOutboundLot>().Add(deletedLot);
                context.Set<ProductOutboundRoll>().Add(deletedRoll);
                context.SaveChanges();
            }

            // 尝试删除已经被软删除的Lot
            var rv3 = _controller.BatchDelete(new string[] { lotId.ToString() });
            Assert.IsInstanceOfType(rv3, typeof(OkObjectResult), "已删除的Lot应该返回成功（无操作）");

            // 验证库存没有变化（因为Lot已经是软删除状态，不应该影响库存）
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var stock = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId);
                Assert.AreEqual(5, stock.TotalPcs, "库存不应该变化");
                Assert.AreEqual(250, stock.TotalWeight, "库存不应该变化");
                Assert.AreEqual(500, stock.TotalMeters, "库存不应该变化");
                Assert.AreEqual(450, stock.TotalYards, "库存不应该变化");
            }

            // 测试场景4：混合场景（存在的、不存在的、已删除的）
            var validLotId = Guid.NewGuid();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var validLot = new ProductOutboundLot
                {
                    ID = validLotId,
                    OutboundBillId = billId,
                    OrderDetailId = orderDetailId,
                    Color = "有效测试",
                    ColorCode = "VALID001",
                    LotNo = "LOT_VALID_TEST",
                    Pcs = 1,
                    Weight = 50,
                    Meters = 100,
                    Yards = 90,
                    Remark = "有效的Lot",
                    IsValid = true,
                    CreateTime = DateTime.Now,
                    CreateBy = "user"
                };

                context.Set<ProductOutboundLot>().Add(validLot);
                context.SaveChanges();

                // 模拟出库扣减库存
                var stock = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId);
                stock.TotalPcs -= 1;
                stock.TotalWeight -= 50;
                stock.TotalMeters -= 100;
                stock.TotalYards -= 90;
                context.SaveChanges();
            }

            // 混合删除：有效的、不存在的、已删除的
            var mixedIds = new string[]
            {
                validLotId.ToString(),      // 有效的
                Guid.NewGuid().ToString(),  // 不存在的
                lotId.ToString()            // 已删除的
            };

            var rv4 = _controller.BatchDelete(mixedIds);
            Assert.IsInstanceOfType(rv4, typeof(OkObjectResult), "混合删除应该成功");

            // 验证只有有效的Lot被处理
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var validLot = context.Set<ProductOutboundLot>().IgnoreQueryFilters().FirstOrDefault(x => x.ID == validLotId);
                Assert.IsNotNull(validLot, "有效Lot应该存在");
                Assert.IsFalse(validLot.IsValid, "有效Lot应该被软删除");

                // 验证库存恢复（只恢复有效Lot的库存）
                var stock = context.Set<ProductStock>().First(x => x.OrderDetailId == orderDetailId);
                Assert.AreEqual(5, stock.TotalPcs, "库存应该恢复：4+1=5");
                Assert.AreEqual(250, stock.TotalWeight, "库存应该恢复：200+50=250");
                Assert.AreEqual(500, stock.TotalMeters, "库存应该恢复：400+100=500");
                Assert.AreEqual(450, stock.TotalYards, "库存应该恢复：360+90=450");
            }
        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "P";
                v.CompanyName = "nUIrdbgNv1UAH7Vp";
                v.CompanyFullName = "bNqSzbCfKwgWzn9GHy1T5";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.GreigeVender;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "1MFmI";
                v.Adress = "E4wpVUUcg";
                v.TaxNO = "Lb9l";
                v.InvoiceInfo = "OQYAWu5bfEa97t2C";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddDeliveryAddress()
        {
            DeliveryAddress v = new DeliveryAddress();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyName = "ydon";
                v.ContactName = "Kuu7GgpA";
                v.Phone = "ule";
                v.Address = "KoLUDlo";
                v.AffiliationCompanyId = AddCompany();
                v.Remark = "dotqIVHfQGJ";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedFailed;
                context.Set<DeliveryAddress>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProductOutboundBill()
        {
            ProductOutboundBill v = new ProductOutboundBill();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2023-05-16 11:34:22");
                v.BillNo = "0";
                v.CustomerId = AddCompany();
                v.ReceiverId = AddDeliveryAddress();
                //v.CustomerOrderNo = "gllhl0nOpo";
                v.Pcs = 41;
                v.Weight = 95;
                v.Meters = 78;
                v.Yards = 97;
                v.Remark = "7hVVOVZO";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "NYPkNM72uIqESSUl";
                v.AuditedComment = "JaYK0UgKbA2";
                context.Set<ProductOutboundBill>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "CAO0GiqGYXl3hY";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "mgFq96K53nxNRfCiiK";
                v.MobilePhone = "wOuD4KPNYzN1cEToDR";
                v.Address = "1HiL3Z";
                v.Phone = "I";
                v.Email = "5V";
                v.WeChat = "01OW";
                v.QQ = "BwEB9Ryh7";
                v.Fax = "bVnkjiRtCseh7qteFr";
                v.Remark = "oWHd5j";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "kvfjd13sHKaAKqukhw1tTrP4UJGhLIzpj2Wyc";
                v.ProductName = "IbFa9nrAjSBrybOa3pytuGl6TNVQjjgJ0UTiY3uU4y44WntKzUVIrI9e5v";
                //v.Category = TEX.Model.Models.FabricCategoryEnum.NylonTaffeta;
                v.Contents = "Fb6q644UGC2L5z5drNpsdx4wxzV0cpb";
                v.Spec = "b7K1PjcO2sasFgnc8ipNiuerSL8oF0hFNqLyQF0h9ARAXmxs4jS5G5Yze";
                v.GSM = 7;
                v.Width = 1;
                v.DyeingProductName = "YF";
                v.PileLength = 11;
                v.DyeingProcess = "8zXml0hU";
                v.KnittingProcess = "tYbZllMnf0X";
                v.FinishingProcess = "rWKRQ6ZCfqJ";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            OrderDetail v1 = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try
                {
                    v.CreateDate = DateTime.Parse("2025-05-10 18:47:19");
                    v.DeliveryDate = DateTime.Parse("2024-09-16 18:47:19");
                    v.CustomerId = AddCompany();
                    v.OrderNo = "HpztUZHDbsWra8xKdSXyCXQk1hMBo5PFbrvuaoCmJD3vEbK473xyFVLCVi";
                    v.CustomerOrderNo = "a6gYi3ONIBg9lL6Gu3TPb5X5WDVSnbyTL6LkHubRC0EF9AGcGVf6DX4MFmssdJ";
                    v.MerchandiserId = AddContact();
                    v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                    v.ProductId = AddProduct();
                    v.DyeingProductName = "Cl";
                    v.Light = TEX.Model.Models.LightEnum.D65_LED;
                    v.Light2 = TEX.Model.Models.LightEnum.D65;
                    v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                    v.PriceUnit = TEX.Model.Models.CurrencyEnum.HKD;
                    v.TotalMeters = 33;
                    v.TotalYards = 41;
                    v.TotalWeight = 43;
                    v.TotalAmount = 32;
                    v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                    v.AuditedBy = "a3R";
                    v.AuditedComment = "ysjNlex8OFQD5bP2F";
                    v.Remark = "ysQ02";
                    context.Set<PurchaseOrder>().Add(v);


                    v1.PurchaseOrderId = v.ID;
                    v1.Color = "8BtEnuLGYU084sVpwpS";
                    v1.EngColor = "eswlPttmHOIsCPqSuj8BDH44";
                    v1.ColorCode = "lXZJAN7oh6ZBujT7f";
                    v1.Meters = 76;
                    v1.KG = 68;
                    v1.Yards = 53;
                    v1.Price = 51;
                    v1.Amount = 48;
                    v1.Remark = "f4ApfVEn";
                    context.Set<OrderDetail>().Add(v1);
                    context.SaveChanges();
                }
                catch { }
            }
            return v.ID;
        }
        private Guid AddOrderDetail()
        {
            OrderDetail v = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                if(context.Set<OrderDetail>().Count() == 0)
                {
                    AddPurchaseOrder();
                }
                return context.Set<OrderDetail>().FirstOrDefault()!.ID; ;
            }
        }

        /// <summary>
        /// 测试ProductOutboundLotVM.DoAdd方法的两级联动和库存扣减功能
        /// 验证：Roll汇总到Lot、库存扣减、Bill统计更新
        /// </summary>
        [TestMethod]
        public void DoAddTest_TwoLevelCascadeAndStockDeduction()
        {
            // {{ AURA-X: Add - 测试ProductOutboundLotVM.DoAdd方法的两级联动（Lot-Roll）及库存扣减功能. Confirmed via 寸止 }}

            ProductOutboundLotVM vm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot v = new ProductOutboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductOutboundBill();
            var orderDetailId = AddOrderDetail();

            // 先创建初始库存
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = new ProductStock
                {
                    OrderDetailId = orderDetailId,
                    TotalPcs = 10,
                    TotalWeight = 500,
                    TotalMeters = 1000,
                    TotalYards = 900,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(initialStock);
                context.SaveChanges();
            }

            v.ID = lotId;
            v.OutboundBillId = billId;
            v.OrderDetailId = orderDetailId;
            v.Color = "出库测试颜色";
            v.ColorCode = "OUT001";
            v.LotNo = "LOT_OUT_DOADD_TEST_001";
            v.Remark = "DoAdd出库测试";

            // 创建Roll数据，用于测试汇总功能
            v.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 120,
                    Weight = 60,
                    Yards = 108,
                    TenantCode = "test"
                },
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 2,
                    Meters = 80,
                    Weight = 40,
                    Yards = 72,
                    TenantCode = "test"
                },
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 3,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                }
            };

            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            // 验证DoAdd方法的执行结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 1. 验证Lot数据汇总（基于Roll计算）
                var lotData = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData, "Lot数据应该被正确保存");
                Assert.AreEqual(3, lotData.Pcs, "Pcs应该等于Roll数量：3");
                Assert.AreEqual(300, lotData.Meters, "Meters应该是Roll的总和：120+80+100=300");
                Assert.AreEqual(150, lotData.Weight, "Weight应该是Roll的总和：60+40+50=150");
                Assert.AreEqual(270, lotData.Yards, "Yards应该是Roll的总和：108+72+90=270");

                // 2. 验证Roll数据正确保存
                Assert.AreEqual(3, lotData.RollList.Count, "应该有3个Roll");
                var rollData = lotData.RollList.OrderBy(x => x.RollNo).ToList();
                Assert.AreEqual(1, rollData[0].RollNo);
                Assert.AreEqual(120, rollData[0].Meters);
                Assert.AreEqual(2, rollData[1].RollNo);
                Assert.AreEqual(80, rollData[1].Meters);
                Assert.AreEqual(3, rollData[2].RollNo);
                Assert.AreEqual(100, rollData[2].Meters);

                // 3. 验证库存扣减（出库减少库存）
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(stockData, "库存记录应该存在");
                Assert.AreEqual(7, stockData.TotalPcs, "库存件数应该减少3：10-3=7");
                Assert.AreEqual(350, stockData.TotalWeight, "库存重量应该减少150：500-150=350");
                Assert.AreEqual(700, stockData.TotalMeters, "库存米数应该减少300：1000-300=700");
                Assert.AreEqual(630, stockData.TotalYards, "库存码数应该减少270：900-270=630");
                Assert.AreEqual("user", stockData.UpdateBy, "应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(stockData.UpdateTime.Value).Seconds < 10, "更新时间应该是最近的");

                // 4. 验证Bill统计字段更新
                var billData = context.Set<ProductOutboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);

                Assert.IsNotNull(billData, "Bill数据应该存在");
                // Bill的统计应该是所有Lot的汇总
                Assert.AreEqual(billData.LotList.Sum(x => x.Pcs), billData.Pcs, "Bill的Pcs应该是所有Lot的总和");
                Assert.AreEqual(billData.LotList.Sum(x => x.Meters), billData.Meters, "Bill的Meters应该是所有Lot的总和");
                Assert.AreEqual(billData.LotList.Sum(x => x.Weight), billData.Weight, "Bill的Weight应该是所有Lot的总和");
                Assert.AreEqual(billData.LotList.Sum(x => x.Yards), billData.Yards, "Bill的Yards应该是所有Lot的总和");
                Assert.AreEqual("user", billData.UpdateBy, "Bill应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(billData.UpdateTime.Value).Seconds < 10, "Bill更新时间应该是最近的");
            }
        }

        /// <summary>
        /// 测试ProductOutboundLotVM.DoAdd方法的边界情况
        /// 验证：空Roll列表、库存不足、负库存等场景
        /// </summary>
        [TestMethod]
        public void DoAddTest_EdgeCases()
        {
            // {{ AURA-X: Add - 测试ProductOutboundLotVM.DoAdd方法的边界情况，包括空Roll列表和库存不足. Confirmed via 寸止 }}

            // 测试场景1：空Roll列表
            ProductOutboundLotVM vm1 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot v1 = new ProductOutboundLot();

            var lotId1 = Guid.NewGuid();
            var billId = AddProductOutboundBill();
            var orderDetailId = AddOrderDetail();

            v1.ID = lotId1;
            v1.OutboundBillId = billId;
            v1.OrderDetailId = orderDetailId;
            v1.Color = "空Roll测试";
            v1.ColorCode = "EMPTY001";
            v1.LotNo = "LOT_EMPTY_ROLL_OUT_TEST";
            v1.Remark = "空Roll列表出库测试";
            v1.RollList = new List<ProductOutboundRoll>(); // 空列表

            vm1.Entity = v1;
            var rv1 = _controller.Add(vm1);
            Assert.IsInstanceOfType(rv1, typeof(OkObjectResult));

            // 验证空Roll列表的处理
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId1);

                Assert.IsNotNull(lotData);
                Assert.AreEqual(0, lotData.Pcs, "空Roll列表时Pcs应该为0");
                Assert.AreEqual(0, lotData.Meters, "空Roll列表时Meters应该为0");
                Assert.AreEqual(0, lotData.Weight, "空Roll列表时Weight应该为0");
                Assert.AreEqual(0, lotData.Yards, "空Roll列表时Yards应该为0");
                Assert.AreEqual(0, lotData.RollList.Count, "Roll列表应该为空");
            }

            // 测试场景2：库存不足导致负库存（出库大于现有库存）
            ProductOutboundLotVM vm2 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot v2 = new ProductOutboundLot();

            var lotId2 = Guid.NewGuid();
            var orderDetailId2 = AddOrderDetail();

            // 创建少量初始库存
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var limitedStock = new ProductStock
                {
                    OrderDetailId = orderDetailId2,
                    TotalPcs = 1,
                    TotalWeight = 50,
                    TotalMeters = 100,
                    TotalYards = 90,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(limitedStock);
                context.SaveChanges();
            }

            v2.ID = lotId2;
            v2.OutboundBillId = billId;
            v2.OrderDetailId = orderDetailId2;
            v2.Color = "库存不足测试";
            v2.ColorCode = "INSUFFICIENT001";
            v2.LotNo = "LOT_INSUFFICIENT_TEST";
            v2.Remark = "库存不足出库测试";

            v2.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId2,
                    RollNo = 1,
                    Meters = 150, // 超过库存的100
                    Weight = 75,  // 超过库存的50
                    Yards = 135,  // 超过库存的90
                    TenantCode = "test"
                }
            };

            vm2.Entity = v2;
            var rv2 = _controller.Add(vm2);
            Assert.IsInstanceOfType(rv2, typeof(OkObjectResult));

            // 验证负库存处理
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId2);

                Assert.IsNotNull(stockData);
                // 出库后库存变为负数（系统允许负库存）
                Assert.AreEqual(0, stockData.TotalPcs, "库存件数：1-1=0");
                Assert.AreEqual(-25, stockData.TotalWeight, "库存重量：50-75=-25（负库存）");
                Assert.AreEqual(-50, stockData.TotalMeters, "库存米数：100-150=-50（负库存）");
                Assert.AreEqual(-45, stockData.TotalYards, "库存码数：90-135=-45（负库存）");
            }
        }

        /// <summary>
        /// 测试ProductOutboundLotVM.DoEdit方法的两级联动修改功能
        /// 验证：Roll修改汇总到Lot、库存差异更新、Bill统计重新计算
        /// </summary>
        [TestMethod]
        public void DoEditTest_TwoLevelCascadeAndStockDifference()
        {
            // {{ AURA-X: Add - 测试ProductOutboundLotVM.DoEdit方法的两级联动修改及库存差异更新功能. Confirmed via 寸止 }}

            // 第一步：创建初始库存
            var orderDetailId = AddOrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = new ProductStock
                {
                    OrderDetailId = orderDetailId,
                    TotalPcs = 10,
                    TotalWeight = 500,
                    TotalMeters = 1000,
                    TotalYards = 900,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(initialStock);
                context.SaveChanges();
            }

            // 第二步：创建初始出库数据
            ProductOutboundLotVM createVm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot initialLot = new ProductOutboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductOutboundBill();

            initialLot.ID = lotId;
            initialLot.OutboundBillId = billId;
            initialLot.OrderDetailId = orderDetailId;
            initialLot.Color = "初始出库颜色";
            initialLot.ColorCode = "INIT_OUT001";
            initialLot.LotNo = "LOT_OUT_EDIT_TEST_001";
            initialLot.Remark = "DoEdit出库测试初始数据";

            // 创建初始Roll数据
            initialLot.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                },
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 2,
                    Meters = 80,
                    Weight = 40,
                    Yards = 72,
                    TenantCode = "test"
                }
            };

            createVm.Entity = initialLot;
            var createResult = _controller.Add(createVm);
            Assert.IsInstanceOfType(createResult, typeof(OkObjectResult));

            // 验证初始数据和库存扣减
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);
                Assert.IsNotNull(initialStock);
                Assert.AreEqual(8, initialStock.TotalPcs, "初始出库后库存件数：10-2=8");
                Assert.AreEqual(410, initialStock.TotalWeight, "初始出库后库存重量：500-90=410");
                Assert.AreEqual(820, initialStock.TotalMeters, "初始出库后库存米数：1000-180=820");
                Assert.AreEqual(738, initialStock.TotalYards, "初始出库后库存码数：900-162=738");

                var initialBill = context.Set<ProductOutboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);
                Assert.IsNotNull(initialBill);
                Assert.AreEqual(1, initialBill.LotList.Count, "初始出库后Bill的Lot列表数量：1");
                Assert.AreEqual(2, initialBill.Pcs, "初始出库后Bill的Pcs：2");
                Assert.AreEqual(180, initialBill.Meters, "初始出库后Bill的Meters：180");
                Assert.AreEqual(90, initialBill.Weight, "初始出库后Bill的Weight：90");
                Assert.AreEqual(162, initialBill.Yards, "初始出库后Bill的Yards：162");
            
            }

            // 第三步：执行DoEdit修改
            ProductOutboundLotVM editVm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            editVm.SetEntityById(lotId);

            // 修改Lot基本信息
            editVm.Entity.Color = "修改后出库颜色";
            editVm.Entity.ColorCode = "EDIT_OUT001";
            editVm.Entity.Remark = "DoEdit出库测试修改后";

            // 修改Roll数据：删除一个Roll，修改一个Roll，新增一个Roll
            var existingRollIds = initialLot.RollList.Select(x => x.ID).ToList();
            editVm.Entity.RollList = new List<ProductOutboundRoll>
            {
                // 保留第一个Roll但修改数据
                new ProductOutboundRoll
                {
                    ID = existingRollIds[0],
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 120, // 从100改为120
                    Weight = 60,  // 从50改为60
                    Yards = 108,  // 从90改为108
                    TenantCode = "test"
                },
                // 删除第二个Roll（不包含在新列表中）
                // 新增第三个Roll
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 3,
                    Meters = 90,
                    Weight = 45,
                    Yards = 81,
                    TenantCode = "test"
                }
            };

            var editResult = _controller.Edit(editVm);
            Assert.IsInstanceOfType(editResult, typeof(OkObjectResult));

            // 第四步：验证DoEdit结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 1. 验证Lot数据更新（基于新Roll重新计算）
                var updatedLot = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(updatedLot);
                Assert.AreEqual("修改后出库颜色", updatedLot.Color, "Lot颜色应该被更新");
                Assert.AreEqual("EDIT_OUT001", updatedLot.ColorCode, "Lot色号应该被更新");
                Assert.AreEqual(2, updatedLot.Pcs, "Lot件数应该重新计算：2个Roll");
                Assert.AreEqual(210, updatedLot.Meters, "Lot米数应该重新计算：120+90=210");
                Assert.AreEqual(105, updatedLot.Weight, "Lot重量应该重新计算：60+45=105");
                Assert.AreEqual(189, updatedLot.Yards, "Lot码数应该重新计算：108+81=189");

                // 2. 验证Roll数据更新
                Assert.AreEqual(2, updatedLot.RollList.Count, "应该有2个Roll");
                var rollData = updatedLot.RollList.OrderBy(x => x.RollNo).ToList();
                Assert.AreEqual(1, rollData[0].RollNo);
                Assert.AreEqual(120, rollData[0].Meters, "第一个Roll的米数应该被更新");
                Assert.AreEqual(3, rollData[1].RollNo);
                Assert.AreEqual(90, rollData[1].Meters, "第三个Roll应该被新增");

                // 3. 验证库存差异更新
                var updatedStock = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(updatedStock);
                // 库存变化计算：
                // 原出库：2件(90kg,180m,162y) -> 新出库：2件(105kg,210m,189y)
                // 差异：0件(+15kg,+30m,+27y) - 出库增加，库存应该进一步减少
                // 原库存：8件(410kg,820m,738y) -> 新库存：8件(395kg,790m,711y)
                Assert.AreEqual(8, updatedStock.TotalPcs, "库存件数应该保持8");
                Assert.AreEqual(395, updatedStock.TotalWeight, "库存重量：410-15=395");
                Assert.AreEqual(790, updatedStock.TotalMeters, "库存米数：820-30=790");
                Assert.AreEqual(711, updatedStock.TotalYards, "库存码数：738-27=711");
                Assert.AreEqual("user", updatedStock.UpdateBy, "应该记录更新人");
                //Assert.IsTrue(DateTime.Now.Subtract(updatedStock.UpdateTime.Value).Seconds < 10, "更新时间应该是最近的");

                // 4. 验证Bill统计字段重新计算
                var updatedBill = context.Set<ProductOutboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);

                Assert.IsNotNull(updatedBill);
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Pcs), updatedBill.Pcs, "Bill的Pcs应该重新计算");
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Meters), updatedBill.Meters, "Bill的Meters应该重新计算");
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Weight), updatedBill.Weight, "Bill的Weight应该重新计算");
                Assert.AreEqual(updatedBill.LotList.Sum(x => x.Yards), updatedBill.Yards, "Bill的Yards应该重新计算");
                Assert.AreEqual("user", updatedBill.UpdateBy, "Bill应该记录更新人");
                Assert.IsTrue(DateTime.Now.Subtract(updatedBill.UpdateTime.Value).Seconds < 10, "Bill更新时间应该是最近的");
            }
        }

        /// <summary>
        /// 测试ProductOutboundLotVM.DoEdit方法的边界情况
        /// 验证：空Roll列表、null Roll列表、库存负数等场景
        /// </summary>
        [TestMethod]
        public void DoEditTest_EdgeCases()
        {
            // {{ AURA-X: Add - 测试ProductOutboundLotVM.DoEdit方法的边界情况，包括空Roll列表和库存负数. Confirmed via 寸止 }}

            // 第一步：创建初始库存和出库数据
            var orderDetailId = AddOrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = new ProductStock
                {
                    OrderDetailId = orderDetailId,
                    TotalPcs = 5,
                    TotalWeight = 250,
                    TotalMeters = 500,
                    TotalYards = 450,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(initialStock);
                context.SaveChanges();
            }

            ProductOutboundLotVM createVm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot initialLot = new ProductOutboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductOutboundBill();

            initialLot.ID = lotId;
            initialLot.OutboundBillId = billId;
            initialLot.OrderDetailId = orderDetailId;
            initialLot.Color = "边界测试";
            initialLot.ColorCode = "EDGE_OUT001";
            initialLot.LotNo = "LOT_OUT_EDGE_TEST_001";
            initialLot.Remark = "边界情况出库测试";

            initialLot.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                }
            };

            createVm.Entity = initialLot;
            var createResult = _controller.Add(createVm);
            Assert.IsInstanceOfType(createResult, typeof(OkObjectResult));

            // 测试场景1：清空所有Roll（设置为空列表）
            ProductOutboundLotVM editVm1 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            editVm1.SetEntityById(lotId);
            editVm1.Entity.RollList = new List<ProductOutboundRoll>(); // 空列表
            editVm1.Entity.Remark = "清空Roll出库测试";

            var editResult1 = _controller.Edit(editVm1);
            Assert.IsInstanceOfType(editResult1, typeof(OkObjectResult));

            // 验证清空Roll的结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData);
                Assert.AreEqual(0, lotData.Pcs, "清空Roll后Pcs应该为0");
                Assert.AreEqual(0, lotData.Meters, "清空Roll后Meters应该为0");
                Assert.AreEqual(0, lotData.Weight, "清空Roll后Weight应该为0");
                Assert.AreEqual(0, lotData.Yards, "清空Roll后Yards应该为0");
                Assert.AreEqual(0, lotData.RollList.Count, "Roll列表应该为空");

                // 验证库存更新（出库减少，清空Roll相当于减少出库，库存应该增加）
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);
                Assert.IsNotNull(stockData);
                // 原出库：1件(50kg,100m,90y) -> 新出库：0件(0kg,0m,0y)
                // 差异：-1件(-50kg,-100m,-90y) - 出库减少，库存应该增加
                // 原库存：4件(200kg,400m,360y) -> 新库存：5件(250kg,500m,450y)
                Assert.AreEqual(5, stockData.TotalPcs, "库存件数应该恢复到5");
                Assert.AreEqual(250, stockData.TotalWeight, "库存重量应该恢复到250");
                Assert.AreEqual(500, stockData.TotalMeters, "库存米数应该恢复到500");
                Assert.AreEqual(450, stockData.TotalYards, "库存码数应该恢复到450");
            }

            // 测试场景2：设置Roll列表为null
            ProductOutboundLotVM editVm2 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            editVm2.SetEntityById(lotId);
            editVm2.Entity.RollList = null; // null列表
            editVm2.Entity.Remark = "null Roll出库测试";

            var editResult2 = _controller.Edit(editVm2);
            Assert.IsInstanceOfType(editResult2, typeof(OkObjectResult));

            // 验证null Roll列表的处理
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData);
                // DoEdit方法会将null转换为空列表
                Assert.AreEqual(0, lotData.Pcs, "null Roll列表时Pcs应该为0");
                Assert.AreEqual(0, lotData.Meters, "null Roll列表时Meters应该为0");
                Assert.AreEqual(0, lotData.Weight, "null Roll列表时Weight应该为0");
                Assert.AreEqual(0, lotData.Yards, "null Roll列表时Yards应该为0");
            }

            // 测试场景3：重新添加Roll数据（从0恢复）
            ProductOutboundLotVM editVm3 = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            editVm3.SetEntityById(lotId);
            editVm3.Entity.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 150,
                    Weight = 75,
                    Yards = 135,
                    TenantCode = "test"
                },
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 2,
                    Meters = 120,
                    Weight = 60,
                    Yards = 108,
                    TenantCode = "test"
                }
            };
            editVm3.Entity.Remark = "恢复Roll出库测试";

            var editResult3 = _controller.Edit(editVm3);
            Assert.IsInstanceOfType(editResult3, typeof(OkObjectResult));

            // 验证恢复Roll的结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var lotData = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData);
                Assert.AreEqual(2, lotData.Pcs, "恢复后Pcs应该为2");
                Assert.AreEqual(270, lotData.Meters, "恢复后Meters应该为270");
                Assert.AreEqual(135, lotData.Weight, "恢复后Weight应该为135");
                Assert.AreEqual(243, lotData.Yards, "恢复后Yards应该为243");
                Assert.AreEqual(2, lotData.RollList.Count, "应该有2个Roll");

                // 验证库存更新（出库增加，库存减少）
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);
                Assert.IsNotNull(stockData);
                // 原出库：0件(0kg,0m,0y) -> 新出库：2件(135kg,270m,243y)
                // 差异：+2件(+135kg,+270m,+243y) - 出库增加，库存应该减少
                // 原库存：5件(250kg,500m,450y) -> 新库存：3件(115kg,230m,207y)
                Assert.AreEqual(3, stockData.TotalPcs, "库存件数：5-2=3");
                Assert.AreEqual(115, stockData.TotalWeight, "库存重量：250-135=115");
                Assert.AreEqual(230, stockData.TotalMeters, "库存米数：500-270=230");
                Assert.AreEqual(207, stockData.TotalYards, "库存码数：450-243=207");
            }
        }

        /// <summary>
        /// 测试ProductOutboundLotVM.DoEdit方法的事务处理
        /// 验证：内存数据库兼容性、实体跟踪处理
        /// </summary>
        [TestMethod]
        public void DoEditTest_TransactionHandling()
        {
            // {{ AURA-X: Add - 测试ProductOutboundLotVM.DoEdit方法的事务处理和内存数据库兼容性. Confirmed via 寸止 }}

            // 创建初始库存和测试数据
            var orderDetailId = AddOrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var initialStock = new ProductStock
                {
                    OrderDetailId = orderDetailId,
                    TotalPcs = 10,
                    TotalWeight = 500,
                    TotalMeters = 1000,
                    TotalYards = 900,
                    CreateTime = DateTime.Now,
                    CreateBy = "system"
                };
                context.Set<ProductStock>().Add(initialStock);
                context.SaveChanges();
            }

            ProductOutboundLotVM createVm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            ProductOutboundLot initialLot = new ProductOutboundLot();

            var lotId = Guid.NewGuid();
            var billId = AddProductOutboundBill();

            initialLot.ID = lotId;
            initialLot.OutboundBillId = billId;
            initialLot.OrderDetailId = orderDetailId;
            initialLot.Color = "事务测试";
            initialLot.ColorCode = "TRANS_OUT001";
            initialLot.LotNo = "LOT_OUT_TRANSACTION_TEST";
            initialLot.Remark = "事务处理出库测试";

            initialLot.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 100,
                    Weight = 50,
                    Yards = 90,
                    TenantCode = "test"
                }
            };

            createVm.Entity = initialLot;
            var createResult = _controller.Add(createVm);
            Assert.IsInstanceOfType(createResult, typeof(OkObjectResult));

            // 执行DoEdit操作，测试内存数据库的事务处理
            ProductOutboundLotVM editVm = _controller.Wtm.CreateVM<ProductOutboundLotVM>();
            editVm.SetEntityById(lotId);
            editVm.Entity.Color = "事务测试修改";
            editVm.Entity.RollList = new List<ProductOutboundRoll>
            {
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 1,
                    Meters = 120,
                    Weight = 60,
                    Yards = 108,
                    TenantCode = "test"
                },
                new ProductOutboundRoll
                {
                    ID = Guid.NewGuid(),
                    LotId = lotId,
                    RollNo = 2,
                    Meters = 80,
                    Weight = 40,
                    Yards = 72,
                    TenantCode = "test"
                }
            };

            var editResult = _controller.Edit(editVm);
            Assert.IsInstanceOfType(editResult, typeof(OkObjectResult), "DoEdit操作应该成功完成");

            // 验证事务处理结果
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                // 验证数据一致性
                var lotData = context.Set<ProductOutboundLot>()
                    .Include(x => x.RollList)
                    .FirstOrDefault(x => x.ID == lotId);

                Assert.IsNotNull(lotData, "Lot数据应该存在");
                Assert.AreEqual("事务测试修改", lotData.Color, "Lot颜色应该被更新");
                Assert.AreEqual(2, lotData.RollList.Count, "应该有2个Roll");
                Assert.AreEqual(2, lotData.Pcs, "Lot统计应该正确");
                Assert.AreEqual(200, lotData.Meters, "Lot米数应该正确");
                Assert.AreEqual(100, lotData.Weight, "Lot重量应该正确");
                Assert.AreEqual(180, lotData.Yards, "Lot码数应该正确");

                // 验证库存数据一致性
                var stockData = context.Set<ProductStock>()
                    .FirstOrDefault(x => x.OrderDetailId == orderDetailId);

                Assert.IsNotNull(stockData, "库存数据应该存在");
                // 原出库：1件(50kg,100m,90y) -> 新出库：2件(100kg,200m,180y)
                // 差异：+1件(+50kg,+100m,+90y) - 出库增加，库存减少
                // 原库存：9件(450kg,900m,810y) -> 新库存：8件(400kg,800m,720y)
                Assert.AreEqual(8, stockData.TotalPcs, "库存件数应该正确");
                Assert.AreEqual(400, stockData.TotalWeight, "库存重量应该正确");
                Assert.AreEqual(800, stockData.TotalMeters, "库存米数应该正确");
                Assert.AreEqual(720, stockData.TotalYards, "库存码数应该正确");

                // 验证Bill数据一致性
                var billData = context.Set<ProductOutboundBill>()
                    .Include(x => x.LotList)
                    .FirstOrDefault(x => x.ID == billId);

                Assert.IsNotNull(billData, "Bill数据应该存在");
                Assert.AreEqual(billData.LotList.Sum(x => x.Pcs), billData.Pcs, "Bill统计应该与Lot汇总一致");
                Assert.AreEqual(billData.LotList.Sum(x => x.Meters), billData.Meters, "Bill米数应该与Lot汇总一致");
                Assert.AreEqual(billData.LotList.Sum(x => x.Weight), billData.Weight, "Bill重量应该与Lot汇总一致");
                Assert.AreEqual(billData.LotList.Sum(x => x.Yards), billData.Yards, "Bill码数应该与Lot汇总一致");
            }
        }



    }
}
