using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Finished.InspectPlanVMs;
using TEX.Model.Finished;
using TEX.DataAccess;
using TEX.Model.Models;


namespace TEX.Test
{
    [TestClass]
    public class InspectPlanApiTest
    {
        private InspectPlanController _controller;
        private string _seed;

        public InspectPlanApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<InspectPlanController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new InspectPlanSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            InspectPlanVM vm = _controller.Wtm.CreateVM<InspectPlanVM>();
            InspectPlan v = new InspectPlan();
            
            v.CreateDate = DateTime.Parse("2024-03-26 19:27:37");
            v.OrderDetailId = AddOrderDetail();
            v.PlanNo = "hTK5o";
            v.PlanBatch = "7P";
            v.InspectionStandard = TEX.Model.Finished.InspectionStandardEnum.FourPoint;
            v.PlanQty = 60;
            v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.KG;
            v.InspectStatus = TEX.Model.Finished.InspectStatusEnum.Finished;
            v.PlanFinishDate = DateTime.Parse("2024-05-02 19:27:37");
            v.Remark = "v2m";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<InspectPlan>().Find(v.ID);
                
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-03-26 19:27:37"));
                Assert.AreEqual(data.PlanNo, "hTK5o");
                Assert.AreEqual(data.PlanBatch, "7P");
                Assert.AreEqual(data.InspectionStandard, TEX.Model.Finished.InspectionStandardEnum.FourPoint);
                Assert.AreEqual(data.PlanQty, 60);
                Assert.AreEqual(data.QtyUnit, TEX.Model.Models.AccountingUnitEnum.KG);
                Assert.AreEqual(data.InspectStatus, TEX.Model.Finished.InspectStatusEnum.Finished);
                Assert.AreEqual(data.PlanFinishDate, DateTime.Parse("2024-05-02 19:27:37"));
                Assert.AreEqual(data.Remark, "v2m");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            InspectPlan v = new InspectPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.CreateDate = DateTime.Parse("2024-03-26 19:27:37");
                v.OrderDetailId = AddOrderDetail();
                v.PlanNo = "hTK5o";
                v.PlanBatch = "7P";
                v.InspectionStandard = TEX.Model.Finished.InspectionStandardEnum.FourPoint;
                v.PlanQty = 60;
                v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.KG;
                v.InspectStatus = TEX.Model.Finished.InspectStatusEnum.Finished;
                v.PlanFinishDate = DateTime.Parse("2024-05-02 19:27:37");
                v.Remark = "v2m";
                context.Set<InspectPlan>().Add(v);
                context.SaveChanges();
            }

            InspectPlanVM vm = _controller.Wtm.CreateVM<InspectPlanVM>();
            var oldID = v.ID;
            v = new InspectPlan();
            v.ID = oldID;
       		
            v.CreateDate = DateTime.Parse("2024-05-07 19:27:37");
            v.PlanNo = "kalfwwvblS6";
            v.PlanBatch = "EEj";
            v.InspectionStandard = TEX.Model.Finished.InspectionStandardEnum.FourPoint;
            v.PlanQty = 9;
            v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.M;
            v.InspectStatus = TEX.Model.Finished.InspectStatusEnum.Finished;
            v.PlanFinishDate = DateTime.Parse("2025-08-07 19:27:37");
            v.Remark = "CNom76g8MUoy34OpWY";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.CreateDate", "");
            vm.FC.Add("Entity.OrderDetailId", "");
            vm.FC.Add("Entity.PlanNo", "");
            vm.FC.Add("Entity.OrderNo", "");
            vm.FC.Add("Entity.ProductName", "");
            vm.FC.Add("Entity.Color", "");
            vm.FC.Add("Entity.ColorCode", "");
            vm.FC.Add("Entity.PlanBatch", "");
            vm.FC.Add("Entity.InspectionStandard", "");
            vm.FC.Add("Entity.PlanQty", "");
            vm.FC.Add("Entity.QtyUnit", "");
            vm.FC.Add("Entity.InspectStatus", "");
            vm.FC.Add("Entity.PlanFinishDate", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<InspectPlan>().Find(v.ID);
 				
                Assert.AreEqual(data.CreateDate, DateTime.Parse("2024-05-07 19:27:37"));
                Assert.AreEqual(data.PlanNo, "kalfwwvblS6");
                Assert.AreEqual(data.PlanBatch, "EEj");
                Assert.AreEqual(data.InspectionStandard, TEX.Model.Finished.InspectionStandardEnum.FourPoint);
                Assert.AreEqual(data.PlanQty, 9);
                Assert.AreEqual(data.QtyUnit, TEX.Model.Models.AccountingUnitEnum.M);
                Assert.AreEqual(data.InspectStatus, TEX.Model.Finished.InspectStatusEnum.Finished);
                Assert.AreEqual(data.PlanFinishDate, DateTime.Parse("2025-08-07 19:27:37"));
                Assert.AreEqual(data.Remark, "CNom76g8MUoy34OpWY");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            InspectPlan v = new InspectPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.CreateDate = DateTime.Parse("2024-03-26 19:27:37");
                v.OrderDetailId = AddOrderDetail();
                v.PlanNo = "hTK5o";
                v.PlanBatch = "7P";
                v.InspectionStandard = TEX.Model.Finished.InspectionStandardEnum.FourPoint;
                v.PlanQty = 60;
                v.QtyUnit = TEX.Model.Models.AccountingUnitEnum.KG;
                v.InspectStatus = TEX.Model.Finished.InspectStatusEnum.Finished;
                v.PlanFinishDate = DateTime.Parse("2024-05-02 19:27:37");
                v.Remark = "v2m";
                context.Set<InspectPlan>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            InspectPlan v1 = new InspectPlan();
            InspectPlan v2 = new InspectPlan();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.CreateDate = DateTime.Parse("2024-03-26 19:27:37");
                v1.OrderDetailId = AddOrderDetail();
                v1.PlanNo = "hTK5o";
                v1.PlanBatch = "7P";
                v1.InspectionStandard = TEX.Model.Finished.InspectionStandardEnum.FourPoint;
                v1.PlanQty = 60;
                v1.QtyUnit = TEX.Model.Models.AccountingUnitEnum.KG;
                v1.InspectStatus = TEX.Model.Finished.InspectStatusEnum.Finished;
                v1.PlanFinishDate = DateTime.Parse("2024-05-02 19:27:37");
                v1.Remark = "v2m";
                v2.CreateDate = DateTime.Parse("2024-05-07 19:27:37");
                v2.OrderDetailId = v1.OrderDetailId; 
                v2.PlanNo = "kalfwwvblS6";
                v2.PlanBatch = "EEj";
                v2.InspectionStandard = TEX.Model.Finished.InspectionStandardEnum.FourPoint;
                v2.PlanQty = 9;
                v2.QtyUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v2.InspectStatus = TEX.Model.Finished.InspectStatusEnum.Finished;
                v2.PlanFinishDate = DateTime.Parse("2025-08-07 19:27:37");
                v2.Remark = "CNom76g8MUoy34OpWY";
                context.Set<InspectPlan>().Add(v1);
                context.Set<InspectPlan>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<InspectPlan>().Find(v1.ID);
                var data2 = context.Set<InspectPlan>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddCompany()
        {
            Company v = new Company();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CompanyCode = "vWPHK";
                v.CompanyName = "BQXSrAfsjrOKi2hmgwgTDnQ9mvX3ZDSqiAdQ4yGsQ1PhGi7J";
                v.CompanyFullName = "L1TNhJczU0kDhRSjyDrVfOt6hRQZC8B9fV6YuQ4bHKJdZ96";
                v.CompanyType = TEX.Model.Models.CompanyTypeEnum.WovenFactory;
                v.Relationship = TEX.Model.Models.RelationshipEnum.Vender;
                v.ContactPhone = "SMs1Ariz";
                v.Adress = "DLbAuCwr215";
                v.TaxNO = "2ucLrhjAygat50BT8s5mHRqsS";
                v.InvoiceInfo = "hsKxmdIWxP1av7lNnAF";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Company>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddContact()
        {
            Contact v = new Contact();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ContactName = "iy";
                v.AffiliationCompanyId = AddCompany();
                v.PositionTitle = "naJQKCdl0BOQBOEpS";
                v.MobilePhone = "gVE2IZI1w95lz";
                v.Address = "KqLD";
                v.Phone = "NdOIvpSMCy";
                v.Email = "gy";
                v.WeChat = "4Ag1d4";
                v.QQ = "trrNM";
                v.Fax = "zxlZmyD";
                v.Remark = "1q7ei";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Contact>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictType()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 93;
                v.Name = "IGaC17p2";
                v.Description = "TCH";
                v.DictOrder = 73;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Int32 AddDictItem()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 84;
                v.DictTypeId = AddDictType();
                v.ItemName = "Bt";
                v.Description = "j5krw8CWg4WLcn";
                v.DictOrder = 77;
                v.Remark = "yEFCpDf4w3l";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddProduct()
        {
            Product v = new Product();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ProductCode = "LZmzSwNKI1YmkmXzLp4ublSH77bjhINt616WgQT";
                v.ProductName = "H";
                v.CategoryId = AddDictItem();
                v.Contents = "fICZ4urckLSvBCwzq8d3UjQROMJFTTKQww5VKIBaqgK5CbuxCJcQvcgdHVLPW";
                v.Spec = "3a4QYIZvbHleX4iSi6UUWE";
                v.GSM = 6;
                v.Width = 196;
                v.DyeingProductName = "PU4Yq8M";
                v.PileLength = 0;
                v.DyeingProcess = "n2iCQ9V9";
                v.KnittingProcess = "qlJfuYqPlv2G27SJ9UI";
                v.FinishingProcess = "voN";
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.NotAudited;
                context.Set<Product>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddPurchaseOrder()
        {
            PurchaseOrder v = new PurchaseOrder();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.CreateDate = DateTime.Parse("2024-10-06 19:27:37");
                v.DeliveryDate = DateTime.Parse("2025-05-11 19:27:37");
                v.CustomerId = AddCompany();
                v.OrderNo = "DgT7Nf0w71nX2Y5RJSpkPY8qIpb2FEeFi";
                v.CustomerOrderNo = "DtPCsU7q2MzlcBik1a3CrECfP93KRP7J2WW4vEvqs9eRd7EVlnQhxPWa1feuHE";
                v.MerchandiserId = AddContact();
                v.OrderType = TEX.Model.Models.OrderTypeEnum.Fabric;
                v.ProductId = AddProduct();
                v.DyeingProductName = "zusVPPu8mM7bCL7id";
                v.Light = TEX.Model.Models.LightEnum.D65_LED;
                v.Light2 = TEX.Model.Models.LightEnum.LED;
                v.AccountUnit = TEX.Model.Models.AccountingUnitEnum.M;
                v.PriceUnit = TEX.Model.Models.CurrencyEnum.GBP;
                v.TotalMeters = 84;
                v.TotalYards = 69;
                v.TotalWeight = 84;
                v.TotalAmount = 16;
                v.CompletedStatus = TEX.Model.Models.CompletedStatusEnum.StandBy;
                v.AuditStatus = TEX.Model.Models.AuditStatusEnum.AuditedApproved;
                v.AuditedBy = "cmwHHqTKztnslaCk0WE";
                v.AuditedComment = "QY0L86N";
                v.Remark = "ik";
                context.Set<PurchaseOrder>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddOrderDetail()
        {
            OrderDetail v = new OrderDetail();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.PurchaseOrderId = AddPurchaseOrder();
                v.Color = "TtMcJldbDFz";
                v.EngColor = "f80s";
                v.ColorCode = "VVBa93B";
                v.Meters = 31;
                v.KG = 88;
                v.Yards = 8;
                v.Price = 0;
                v.Amount = 1;
                v.Remark = "enilqyLzCs4lMp";
                context.Set<OrderDetail>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
