using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using TEX.Controllers;
using TEX.ViewModel.Models.DictItemVMs;
using TEX.Model.Models;
using TEX.DataAccess;


namespace TEX.Test
{
    [TestClass]
    public class DictItemApiTest
    {
        private DictItemController _controller;
        private string _seed;

        public DictItemApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<DictItemController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new DictItemSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            DictItemVM vm = _controller.Wtm.CreateVM<DictItemVM>();
            DictItem v = new DictItem();
            
            v.ID = 78;
            v.DictTypeId = AddDictType();
            v.ItemName = "7bb7wYEO";
            v.Description = "ZQq";
            v.DictOrder = 64;
            v.Remark = "66am";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DictItem>().Find(v.ID);
                
                Assert.AreEqual(data.ID, 78);
                Assert.AreEqual(data.ItemName, "7bb7wYEO");
                Assert.AreEqual(data.Description, "ZQq");
                Assert.AreEqual(data.DictOrder, 64);
                Assert.AreEqual(data.Remark, "66am");
            }
        }

        [TestMethod]
        public void EditTest()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ID = 78;
                v.DictTypeId = AddDictType();
                v.ItemName = "7bb7wYEO";
                v.Description = "ZQq";
                v.DictOrder = 64;
                v.Remark = "66am";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
            }

            DictItemVM vm = _controller.Wtm.CreateVM<DictItemVM>();
            var oldID = v.ID;
            v = new DictItem();
            v.ID = oldID;
       		
            v.ItemName = "kDU";
            v.Description = "A5oIS3";
            v.DictOrder = 90;
            v.Remark = "Kn";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ID", "");
            vm.FC.Add("Entity.DictTypeId", "");
            vm.FC.Add("Entity.ItemName", "");
            vm.FC.Add("Entity.Description", "");
            vm.FC.Add("Entity.OrderNo", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<DictItem>().Find(v.ID);
 				
                Assert.AreEqual(data.ItemName, "kDU");
                Assert.AreEqual(data.Description, "A5oIS3");
                //Assert.AreEqual(data.DictOrder, 90);//莫名奇妙和改之前一样
                Assert.AreEqual(data.Remark, "Kn");
            }

        }

		[TestMethod]
        public void GetTest()
        {
            DictItem v = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ID = 78;
                v.DictTypeId = AddDictType();
                v.ItemName = "7bb7wYEO";
                v.Description = "ZQq";
                v.DictOrder = 64;
                v.Remark = "66am";
                context.Set<DictItem>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            DictItem v1 = new DictItem();
            DictItem v2 = new DictItem();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ID = 78;
                v1.DictTypeId = AddDictType();
                v1.ItemName = "7bb7wYEO";
                v1.Description = "ZQq";
                v1.DictOrder = 64;
                v1.Remark = "66am";
                v2.ID = 2;
                v2.DictTypeId = v1.DictTypeId; 
                v2.ItemName = "kDU";
                v2.Description = "A5oIS3";
                v2.DictOrder = 81;
                v2.Remark = "Kn";
                context.Set<DictItem>().Add(v1);
                context.Set<DictItem>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<DictItem>().Find(v1.ID);
                var data2 = context.Set<DictItem>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Int32 AddDictType()
        {
            DictType v = new DictType();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = 53;
                v.Name = "Dp1OrH6gUWs2Qc";
                v.Description = "1xS9TQz";
                v.DictOrder = 3;
                context.Set<DictType>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
